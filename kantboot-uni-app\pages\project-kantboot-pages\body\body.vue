<template>
	<view class="content">
    <view :class="clazz.bg"></view>

    <view :class="clazz.viewBox">
      <view class="view-left">
           <body-tabbar ref="bodyTabbar" :selected="tabbarSelected"
                        @changeTabbar="changeTabbar"></body-tabbar>
      </view>
      <view class="view-center">
        <page-home v-show="tabbarSelected === 'home'"></page-home>
        <PageAI v-show="tabbarSelected === 'ai'"></PageAI>
        <PagePost v-show="tabbarSelected === 'post'"></PagePost>
        <PageMessage v-show="tabbarSelected === 'message'"></PageMessage>
        <page-mine v-show="tabbarSelected === 'mine'"></page-mine>
    </view>
      <view :class="clazz.viewRight"></view>

    </view>



	</view>
</template>

<script>
  import bodyTabbar from './components/bodyTabbar.vue'
  import PageHome from "./pageComponents/pageHome.vue";
  import PageMine from "./pageComponents/pageMine.vue";
  import PagePost from "./pageComponents/pagePost.vue";
  import PageMessage from "./pageComponents/pageMessage.vue";
  import PageAI from "./pageComponents/pageAI.vue";

	export default {
	components: {
		bodyTabbar,
    PageHome,
    PageAI,
    PageMine,
    PagePost,
    PageMessage,
	},
		data() {
			return {
        mineInfo: {},
        tabbarSelected: 'home',
        clazz:{
          bg: this.$kt.style.toggleClass("bg"),
          viewBox: this.$kt.style.toggleClass("view-box"),
          viewRight: this.$kt.style.toggleClass("view-right"),
        }

			}
		},
		// 分享给好友
		onShareAppMessage() {
			return {
				title: '',
			}
		},
		onLoad(query) {
			//页面传参tabbarSelected=all
			if (query && query.tabbarSelected) {
				this.tabbarSelected = query.tabbarSelected;
				this.changeTabbar(query.tabbarSelected);
			}
			this.changeTabbar("home");
		},
    mounted() {
      let colorScheme = this.$kt.style.getMode().colorScheme;
      if(colorScheme === 'dark'){
        // 将状态栏调成白色
        uni.setNavigationBarColor({
          frontColor: '#ffffff',
          backgroundColor: '#ffffff',
          animation: {
            duration: 0,
            timingFunc: 'easeIn'
          }
        });
      } else{
        // 将状态栏调成黑色
        uni.setNavigationBarColor({
          frontColor: '#000000',
          backgroundColor: '#000000',
          animation: {
            duration: 0,
            timingFunc: 'easeIn'
          }
        });
      }
    },
		methods: {

      changeTabbar(code) {
        this.tabbarSelected = code;
        if(code.endsWith('Meet')){
          this.clazz.viewBox = this.$kt.style.toggleClass("view-box-meet");
          // 重新渲染
        }else{
          this.clazz.viewBox = this.$kt.style.toggleClass("view-box");
        }
        this.$forceUpdate();
        console.log(code)
      }
		},

	}
</script>

<style lang="scss" scoped>
  .bg {
    position: fixed;
    height: 100vh;
    width: 100vw;
    top:0;
    left:0;
    z-index: -1;
  }

  .bg-mode-color-scheme-light{
    background-color: #ffffff;
  }

  .bg-mode-color-scheme-dark{
    background-color: #191919;
  }



  .show{
    opacity: 1;
    z-index: 10;
  }

  .hide{
    opacity: 0;
    z-index: -1;
  }

  .content{

  }

  //.view-box{
  //  position: fixed;
  //  left:0;
  //  top:0;
  //  width: 1200px;
  //  height: 100vh;
  //  background-color: #ff9900;
  //  vertical-align: top;
  //}
  //
  //.view-center{
  //  width: 600px;
  //  display: inline-block;
  //}

  .view-box-mode-device-pc{
    position: fixed;
    //background-color: #FF0000;
    width: 1200px;
    height: 100vh;
    top:0;
    left:50%;
    transform: translateX(-50%);

    .view-center{
      width: 600px;
    }

    .view-right{
      position: absolute;
      display: inline-block;
      width: 360px;
      height: 100%;
      right: 0;
      top:0;
      z-index: 10;
    }
    .view-right-mode-color-scheme-light {
      background-color: #ffffff;
    }
    .view-right-mode-color-scheme-dark{
      background-color: #191919;
    }

  }

  .view-box-meet-mode-device-pc{
    position: fixed;
    //background-color: #FF0000;
    width: 1200px;
    height: 100vh;
    top:0;
    left:50%;
    transform: translateX(-50%);

    .view-center{
      width: 960px;
    }

    .view-right{
      position: absolute;
      display: inline-block;
      width: 5px;
      height: 100%;
      right: 0;
      top:0;
      z-index: 99;
    }
    .view-right-mode-color-scheme-light {
      background-color: #ffffff;
    }
    .view-right-mode-color-scheme-dark{
      background-color: #191919;
    }
  }

</style>
