@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.card-list.data-v-1de8c056 {
  text-align: center;
}
.card-list .card.data-v-1de8c056 {
  display: inline-block;
  margin: 10rpx;
}
.card-list.data-v-1de8c056::-webkit-scrollbar {
  display: none;
}
.panel .panel-header.data-v-1de8c056 {
  padding: 20rpx;
}
.panel-card-section.data-v-1de8c056 {
  padding: 20rpx 30rpx 20rpx 30rpx;
  text-align: left;
}
.panel-card-section .panel-card-section-item.data-v-1de8c056 {
  display: inline-block;
  margin-right: 30rpx;
}
.panel-card-section .panel-card-section-item-selected.data-v-1de8c056 {
  font-weight: bold;
  color: #000000;
}
.panel-privileges.data-v-1de8c056 {
  padding: 20rpx 30rpx 20rpx 30rpx;
}
.panel-privileges .panel-privileges-box.data-v-1de8c056 {
  border-radius: 10rpx;
}
.panel-privileges .panel-privileges-box .panel-privileges-box-title.data-v-1de8c056 {
  font-size: 28rpx;
  font-weight: bold;
  color: #000000;
}
.panel-privileges-card.data-v-1de8c056 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20rpx 0;
}
.panel-mode-device-pc .panel-card-section.data-v-1de8c056 {
  margin-left: 130rpx;
}
.panel-mode-device-pc .panel-card-section .panel-card-section-item.data-v-1de8c056 {
  cursor: pointer;
}
.panel-mode-device-pc .card-list.data-v-1de8c056 {
  display: flex;
  justify-content: space-evenly;
}
.panel-mode-device-pc .panel-header.data-v-1de8c056 {
  display: flex;
  justify-content: center;
}
.panel-mode-device-pc .panel-privileges.data-v-1de8c056 {
  text-align: center;
}
.panel-mode-device-pc .panel-privileges .pay-btn.data-v-1de8c056 {
  display: inline-block;
  width: 300px;
}
