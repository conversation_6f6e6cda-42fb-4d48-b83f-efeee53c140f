@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.image-box.data-v-7c11b8c8 {
  white-space: nowrap;
  overflow-x: auto;
  scrollbar-width: thin;
}
.image-box .image-box-add.data-v-7c11b8c8 {
  position: relative;
  display: inline-block;
  width: 200rpx;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  justify-content: center;
  align-items: center;
  margin-right: 30rpx;
}
.image-box .image-box-add .image-box-add-image.data-v-7c11b8c8 {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  opacity: 0.8;
}
.image-box .image-box-loading.data-v-7c11b8c8 {
  position: relative;
  display: inline-block;
  width: 200rpx;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  justify-content: center;
  align-items: center;
  margin-right: 30rpx;
}
.image-box .image-box-loading .image-box-add-loading.data-v-7c11b8c8 {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  opacity: 0.8;
  -webkit-animation: loading-data-v-7c11b8c8 1s linear infinite;
          animation: loading-data-v-7c11b8c8 1s linear infinite;
}
.image-box .image-box-image.data-v-7c11b8c8 {
  position: relative;
  display: inline-block;
  width: 200rpx;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  margin-right: 30rpx;
}
.image-box .image-box-image .image-box-image-image.data-v-7c11b8c8 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 50%;
  left: 50%;
  border-radius: 20rpx;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  opacity: 0.8;
}
.image-box .image-box-image .image-box-image-bottom.data-v-7c11b8c8 {
  position: absolute;
  z-index: 1;
  bottom: 50rpx;
  left: 10rpx;
  width: 100%;
  box-sizing: border-box;
  border-radius: 0 0 20rpx 20rpx;
}
.image-box .image-box-image .image-box-image-bottom .image-box-image-icon.data-v-7c11b8c8 {
  padding: 10rpx;
  background-color: rgba(255, 255, 255, 0.5);
  width: 28rpx;
  height: 28rpx;
  opacity: 0.8;
  border-radius: 55%;
  margin-right: 20rpx;
  -webkit-filter: invert(1);
          filter: invert(1);
}
.image-box .image-box-image .image-box-image-bottom .image-box-image-icon-left.data-v-7c11b8c8 {
  position: absolute;
  left: 0;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.image-box .image-box-image .image-box-image-bottom .image-box-image-icon-right.data-v-7c11b8c8 {
  position: absolute;
  right: 0;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
}
.image-box .image-box-image .image-box-image-remove.data-v-7c11b8c8 {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  z-index: 2;
}
.image-box .image-box-image .image-box-image-remove .image-box-image-remove-icon.data-v-7c11b8c8 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 50%;
  height: 50%;
  opacity: 0.8;
  -webkit-filter: invert(1);
          filter: invert(1);
}
@-webkit-keyframes loading-data-v-7c11b8c8 {
0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
            transform: translate(-50%, -50%) rotate(0deg);
}
100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
            transform: translate(-50%, -50%) rotate(360deg);
}
}
@keyframes loading-data-v-7c11b8c8 {
0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
            transform: translate(-50%, -50%) rotate(0deg);
}
100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
            transform: translate(-50%, -50%) rotate(360deg);
}
}
.image-box-add.data-v-7c11b8c8:active {
  -webkit-transform: scale(0.97);
          transform: scale(0.97);
}
.image-box.data-v-7c11b8c8::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
.image-box-mode-device-pc .image-box-add.data-v-7c11b8c8 {
  cursor: pointer;
}
