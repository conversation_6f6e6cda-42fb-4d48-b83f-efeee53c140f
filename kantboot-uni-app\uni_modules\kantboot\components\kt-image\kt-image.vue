<template>
  <view class="box">

    <image
        v-show="isLoad"
        v-if="!fileId"
        class="img"
        :mode="mode"
        :style="{
          borderRadius: borderRadius
        }"
        :src="src" @load="loadBySrc(src)">
    </image>
    <image
        v-show="isLoad"
        v-if="fileId"
        class="img"
        :mode="mode"
        :style="{
          borderRadius: borderRadius
        }"
        :src="file.visit(fileId)" @load="loadBySrc(fileId)">
    </image>


  </view>
</template>


<script>
import file from "@/uni_modules/kantboot/libs/file";

export default {
  props: {
    src: {
      type: String,
      default: ""
    },
    fileId: {
      type: String,
      default: ""
    },
    mode: {
      type: String,
      default: "aspectFit"
    },
    borderRadius: {
      type: String,
      default: ""
    },

  },
  data() {
    return {
      file,
      // 是否加载完成
      isLoad: false
    };
  },
  created() {
    // this.$kt.event.on(`ktImageLoad:${this.src}`, () => {
    //   this.$emit("load");
    // });
    // this.$kt.event.on(`ktImageLoad:${this.fileId}`, () => {
    //   this.$emit("load");
    // });
    uni.$on(`ktImageLoad:${this.src}`, () => {
      this.$emit("load");
    });
    uni.$on(`ktImageLoad:${this.fileId}`, () => {
      this.$emit("load");
    });
  },
  methods: {
    loadBySrc(src) {
      // this.$kt.event.emit(`ktImageLoad:${src}`);
      uni.$emit(`ktImageLoad:${src}`);
      console.log("loadBySrc", src);
      this.isLoad = true;
    },
    loadByFileId(fileId) {
      // this.$kt.event.emit(`ktImageLoad:${fileId}`);
      uni.$emit(`ktImageLoad:${fileId}`);
      console.log("loadByFileId", fileId);
      this.isLoad = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 100% !important;
  height: 100% !important;

  .img {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }
}

</style>
