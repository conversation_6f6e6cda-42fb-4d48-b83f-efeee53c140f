@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-6391b582 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
}
.text_hide.data-v-6391b582 {
  position: absolute;
  display: inline-block;
  z-index: -1;
  opacity: 0;
  white-space: nowrap;
}
.text-normal.data-v-6391b582 {
  display: inline-block;
}
.text-scroll.data-v-6391b582 {
  white-space: nowrap;
  width: 100%;
}
.text-scroll-not-ar.data-v-6391b582 {
  -webkit-animation: scroll-data-v-6391b582 5s linear infinite;
          animation: scroll-data-v-6391b582 5s linear infinite;
}
.text-scroll-is-ar.data-v-6391b582 {
  -webkit-animation: scrollAr-data-v-6391b582 5s linear infinite;
          animation: scrollAr-data-v-6391b582 5s linear infinite;
}
@-webkit-keyframes scroll-data-v-6391b582 {
0% {
    -webkit-transform: translateX(40%);
            transform: translateX(40%);
    opacity: 0.2;
}
10% {
    -webkit-transform: translateX(20%);
            transform: translateX(20%);
    opacity: 1;
}
50% {
    -webkit-transform: translateX(-20%);
            transform: translateX(-20%);
    opacity: 1;
}
100% {
    -webkit-transform: translateX(-70%);
            transform: translateX(-70%);
    opacity: 0.4;
}
}
@keyframes scroll-data-v-6391b582 {
0% {
    -webkit-transform: translateX(40%);
            transform: translateX(40%);
    opacity: 0.2;
}
10% {
    -webkit-transform: translateX(20%);
            transform: translateX(20%);
    opacity: 1;
}
50% {
    -webkit-transform: translateX(-20%);
            transform: translateX(-20%);
    opacity: 1;
}
100% {
    -webkit-transform: translateX(-70%);
            transform: translateX(-70%);
    opacity: 0.4;
}
}
@-webkit-keyframes scrollAr-data-v-6391b582 {
0% {
    -webkit-transform: translateX(-80%);
            transform: translateX(-80%);
}
100% {
    -webkit-transform: translateX(50%);
            transform: translateX(50%);
}
}
@keyframes scrollAr-data-v-6391b582 {
0% {
    -webkit-transform: translateX(-80%);
            transform: translateX(-80%);
}
100% {
    -webkit-transform: translateX(50%);
            transform: translateX(50%);
}
}
.text.data-v-6391b582 {
  overflow: hidden;
}
