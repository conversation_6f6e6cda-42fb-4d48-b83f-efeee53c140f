<template>
  <view>
    <kt-popup
        ref="ktPopup"
        @close="close"
        :zIndex="999999999"
    >

      <view :class="clazz.box">
        <view class="title">{{$i18n.zhToGlobal("修改密码")}}</view>

        <view class="in-box">
          <!-- 原密码 -->
          <view
              v-if="!noHasOldPassword"
              class="in-box-input">
            <image
                class="in-box-input-icon"
                :src="$kt.file.byPath('icon/key.svg')"></image>
            <input
                class="in-box-input-input"
                type="password"
                v-model="requestParams.oldPassword"
                @input="requestParams.oldPassword = $event.detail.value"
                v-if="!oldPasswordShow"
                :placeholder="$i18n.zhToGlobal('输入原密码')"></input>
            <input
                class="in-box-input-input"
                type="text"
                v-model="requestParams.oldPassword"
                @input="requestParams.oldPassword = $event.detail.value"
                v-if="oldPasswordShow"
                :placeholder="$i18n.zhToGlobal('输入原密码')"></input>
            <image
                class="in-box-input-icon"
                v-if="!oldPasswordShow"
                :src="$kt.file.byPath('icon/eye.svg')"></image>
            <image
                class="in-box-input-icon"
                v-if="oldPasswordShow"
                :src="$kt.file.byPath('icon/eyeOff.svg')"></image>
            <view
                class="in-box-input-show-change"
                @click="oldPasswordShowChange()">
            </view>
        </view>
        </view>
        <view class="in-box">
          <view class="in-box-input">
            <image
                class="in-box-input-icon"
                :src="$kt.file.byPath('icon/key.svg')"></image>
            <input
                class="in-box-input-input"
                type="password"
                v-model="requestParams.password"
                @input="requestParams.password = $event.detail.value"
                v-if="!passwordShow"
                :placeholder="$i18n.zhToGlobal('输入新密码')"></input>
            <input
                class="in-box-input-input"
                type="text"
                v-model="requestParams.password"
                @input="requestParams.password = $event.detail.value"
                v-if="passwordShow"
                :placeholder="$i18n.zhToGlobal('输入新密码')"></input>
            <image
                class="in-box-input-icon"
                v-if="!passwordShow"
                :src="$kt.file.byPath('icon/eye.svg')"></image>
            <image
                class="in-box-input-icon"
                v-if="passwordShow"
                :src="$kt.file.byPath('icon/eyeOff.svg')"></image>
            <view
                class="in-box-input-show-change"
                @click="passwordShowChange()">
            </view>
          </view>
        </view>

        <view class="in-box">
          <view class="in-box-input">
            <image
                class="in-box-input-icon"
                :src="$kt.file.byPath('icon/key.svg')"></image>
            <input
                class="in-box-input-input"
                type="password"
                v-model="passwordConfirm"
                @input="passwordConfirm = $event.detail.value"
                v-if="!passwordConfirmShow"
                :placeholder="$i18n.zhToGlobal('确认新密码')"></input>
            <input
                class="in-box-input-input"
                type="text"
                v-model="passwordConfirm"
                @input="passwordConfirm = $event.detail.value"
                v-if="passwordConfirmShow"
                :placeholder="$i18n.zhToGlobal('确认输入新密码')"></input>
            <image
                class="in-box-input-icon"
                v-if="!passwordConfirmShow"
                :src="$kt.file.byPath('icon/eye.svg')"></image>
            <image
                class="in-box-input-icon"
                v-if="passwordConfirmShow"
                :src="$kt.file.byPath('icon/eyeOff.svg')"></image>
            <view
                class="in-box-input-show-change"
                @click="passwordConfirmShowChange()"
            ></view>
          </view>
        </view>

        <view style="height: 30rpx"></view>

        <kt-button
            ref="ktButton"
            @click="toBind()">
          {{$i18n.zhToGlobal("确定修改")}}
        </kt-button>
      </view>

    </kt-popup>
  </view>
</template>

<script>
export default {
  props: {
    /**
     * 是否不需要原密码
     */
    noHasOldPassword: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      oldPasswordShow: '',
      passwordShow: false,
      passwordConfirmShow: false,
      requestParams: {
        oldPassword: '',
        password: '',
      },
      passwordConfirm: '',
      clazz: {
        box: this.$kt.style.toggleClass("box"),
      },
    };
  },
  mounted() {

  },
  methods: {
    open() {
      this.$refs.ktPopup.open();
      this.requestParams.oldPassword = '';
      this.requestParams.password = '';
      this.passwordConfirm = '';
      this.oldPasswordShow = false;
      this.passwordShow = false;
      this.passwordConfirmShow = false;
    },
    oldPasswordShowChange(){
      this.oldPasswordShow = !this.oldPasswordShow;
      try {
        this.$forceUpdate();
      }catch (e){

      }
    },
    passwordShowChange(){
      this.passwordShow = !this.passwordShow;
      try {
        this.$forceUpdate();
      }catch (e){

      }
    },
    passwordConfirmShowChange(){
      this.passwordConfirmShow = !this.passwordConfirmShow;
      try {
        this.$forceUpdate();
      }catch (e){
      }
    },
    toBind(){
      this.$refs.ktButton.loading(null, 999999);
      if (this.requestParams.password.length < 6) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("密码长度不能小于6位"));
        return;
      }
      if (this.requestParams.password !== this.passwordConfirm) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("两次输入的密码不一致"));
        return;
      }

      if(this.noHasOldPassword){

        this.$request.post("/user-account-web/userAccount/changePasswordNoHasOldPassword",{
          data: {
            newPassword: this.requestParams.password,
          },
        }).then(res => {
          this.$refs.ktButton.success(res.msg);
          this.$refs.ktPopup.close();
          this.$kt.userAccount.requestSelf();
          this.$emit("phone:bindSuccess");
          this.$kt.event.emit("phone:bindSuccess", this.requestParams);
        }).catch(err => {
          this.$refs.ktButton.error(err.errMsg);
        })

      }

      this.$request.post("/user-account-web/userAccount/changePassword",{
        data: {
          oldPassword: this.requestParams.oldPassword,
          newPassword: this.requestParams.password,
        },
      }).then(res => {
        this.$refs.ktButton.success(res.msg);
        this.$refs.ktPopup.close();
        this.$kt.userAccount.requestSelf();
        this.$emit("phone:bindSuccess");
        this.$kt.event.emit("phone:bindSuccess", this.requestParams);
      }).catch(err => {
        this.$refs.ktButton.error(err.errMsg);
      })

    },
  },
}
</script>

<style lang="scss" scoped>
.box{
  .title{
    font-size: 32rpx;
    color: #333333;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
  padding: 50rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  .in-box{
    padding: 20rpx;
    .in-box-input{
      position: relative;
      background-color: #f0f0f0;
      padding: 30rpx;
      border-radius: 20rpx;
      .in-box-input-icon{
        width: 40rpx;
        height: 40rpx;
        display: inline-block;
        vertical-align: middle;
      }
      .in-box-input-input{
        position: relative;
        vertical-align: top;
        display: inline-block;
        letter-spacing: 2rpx;
        margin-left: 10rpx;
        width: calc(100% - 100rpx);
        border: none;
        font-size: 28rpx;
        color: #333333;
        z-index:2;
      }
      .in-box-input-show-change{
        position: absolute;
        right: 0;
        top: 0;
        width: 100rpx;
        height: 100%;
        opacity: 0.1;
        z-index: 99999;
      }
    }
  }
}

.box-mode-device-pc{
  border-radius: 20rpx;
}
</style>
