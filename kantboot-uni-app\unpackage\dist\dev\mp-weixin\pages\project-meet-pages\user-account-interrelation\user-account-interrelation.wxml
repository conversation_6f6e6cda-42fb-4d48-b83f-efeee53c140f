<view class="data-v-53a101cb"><view id="headerInUserAccountInterrelation" class="data-v-53a101cb"><kt-nav-bar vue-id="207fb89b-1" title="{{$root.g0}}" class="data-v-53a101cb" bind:__l="__l"></kt-nav-bar></view><view class="box data-v-53a101cb"><kt-user-account-interrelation-panel vue-id="207fb89b-2" user-account-id="{{userAccountId}}" init-code="{{initCode}}" height="{{'calc(100vh - '+headerHeight+'rpx - 160rpx)'}}" data-ref="userAccountInterrelationPanel" data-event-opts="{{[['^select',[['toUserInfoPage']]]]}}" bind:select="__e" class="data-v-53a101cb vue-ref" bind:__l="__l"></kt-user-account-interrelation-panel></view></view>