<template>
  <scroll-view class="user-recommend-panel" scroll-y="true" s
               croll-with-animation="true"
               @scrolltolower="getAfter"
               :show-scrollbar="false"
               :refresher-triggered="refresherTriggered"
               :refresher-enabled="refresherEnabled"
               @refresherrefresh="onRefresherrefresh"
               :lower-threshold="$kt.util.rpxToPx(500)"
               :style="{height}">
    <view class="user-list-box">
      <view
          v-for="item in userList"
          :class="clazz.userBox"
          @click="toUserInfoPage(item)"
      >
        <project-hometown-user-info-out-card
            :userInfo="item"
        ></project-hometown-user-info-out-card>
      </view>
      <u-loading-icon
          v-if="alfterLoading"
          mode="circle"
          size="50rpx"
      ></u-loading-icon>
      <view v-if="isBottom"
            style="text-align: center; color: #999999; font-size: 24rpx;">
        {{$kt.i18n.zhToGlobal('没有更多了')}}
      </view>
      <view style="height: 50rpx;"></view>
    </view>
  </scroll-view>
</template>

<script>
export default {
  props: {
    keyword:{
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: "100vh"
    }
  },
  data() {
    return {
      isLoading: false,
      refresherEnabled: true,
      refresherTriggered: true,
      userList: [],
      requestParam: {
        maxId: 0,
        minId: 0,
      },
      alfterLoading: false,
      rowLength: 20,
      // 是否见底了
      isBottom: false,
      clazz:{
        userBox: this.$kt.style.toggleClass("user-box"),
      }
    }
  },
  watch:{
    keyword(newVal) {
      if (newVal) {
        this.getInitList();
      }
    }
  },
  created() {
    this.getInitList();
  },
  methods: {
    async onRefresherrefresh() {
      if(this.refreshing){
        // this.refresherTriggered = false;
        return;
      }
      this.isLoading = true;
      this.refresherTriggered = true;
      await this.getInitList();
      setTimeout(()=>{
        this.refresherTriggered = false;
      },1000);
    },
    /**
     * 计算最大id和最小id
     */
    calculateMaxAndMinId() {
      let maxId = 0;
      let minId = 0;
      for (let i = 0; i < this.userList.length; i++) {
        let item = this.userList[i];
        if (i === 0) {
          maxId = item.id;
          minId = item.id;
        } else {
          if (item.id > maxId) {
            maxId = item.id;
          }
          if (item.id < minId) {
            minId = item.id;
          }
        }
      }
      this.requestParam.maxId = minId;
      this.requestParam.minId = maxId;
    },

    /**
     * 是否已存在
     */
    isExist(id) {
      for (let i = 0; i < this.userList.length; i++) {
        let item = this.userList[i];
        if (item.id === id) {
          return true;
        }
      }
      return false;
    },
    pushUserList(userAccount) {
      // 先判断是否存在
      if (this.isExist(userAccount.id)) {
        return;
      }
      this.userList.push(userAccount);
    },

    toUserInfoPage(userAccount) {
      this.$kt.router.navTo('/pages/project-make-friends-pages/user-info/user-info', {'userAccountId': userAccount.id})
    },
    // /project-make-friends-web/userAccount/getRecommendList
    getInitList() {
      // 先从存储中获取
      let userList = this.$kt.storage.get('userRecommendList');
      if (userList) {
        this.userList = userList;
        this.calculateMaxAndMinId();
      }

      this.$request.post('/project-make-friends-web/userAccount/getRecommendList',{
        data:{
          keyword: this.keyword,
        }
      }).then(res => {
        this.userList = res.data;
        this.calculateMaxAndMinId();
        // 添加进存储
        this.$kt.storage.set('userRecommendList', res.data);
      }).catch(err => {
      });
    },
    getAfter(){
      if (this.isBottom) {
        return;
      }
      if (this.alfterLoading) {
        return;
      }
      this.alfterLoading = true;
      this.$request.post("/project-make-friends-web/userAccount/getRecommendList",{data:
            {
              maxId:this.requestParam.maxId,
              keyword: this.keyword,
            }})
          .then(res => {
            let list = res.data;
            if (list.length < this.rowLength) {
              this.alfterLoading = false;
              this.isBottom = true;
              return;
            }
            for (let i = 0; i < list.length; i++) {
              let item = list[i];
              this.pushUserList(item);
            }
            this.calculateMaxAndMinId();
            this.alfterLoading = false;
          });
    },

  }
}
</script>


<style lang="scss" scoped>
// 取消显示滚动条
.user-recommend-panel {
  overflow: hidden;
  &::-webkit-scrollbar {
    display: none;
  }
}

// 滚动条不显示
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
// 不显示滚动条
.user-recommend-panel::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;}

// 滚动
.user-recommend-panel::-webkit-scrollbar-thumb {
  background-color: #fff;
  border-radius: 0;
}

.user-list-box {
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}

.user-box {
  //border-radius: 20rpx;
  padding: 10rpx 30rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  //border: 1px solid #f0f0f0;
  border-bottom: 2rpx dashed #bbbbbb;

}

:active {
  // transform: scale(.97);
}

.user-box-mode-color-scheme-dark {
  background-color: #1f1f1f;
}
</style>