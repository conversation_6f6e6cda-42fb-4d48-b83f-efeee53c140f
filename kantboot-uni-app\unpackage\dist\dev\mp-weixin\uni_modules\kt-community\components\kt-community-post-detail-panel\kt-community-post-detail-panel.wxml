<view class="kt-community-post-detail-panel data-v-619f8fab" style="{{'height:'+(height)+';'}}"><scroll-view class="scroll-view data-v-619f8fab" style="{{'height:'+(height)+';'}}" scroll-y="{{true}}"><block wx:if="{{postId}}"><view class="box data-v-619f8fab"><kt-community-post-card vue-id="5b5a328a-1" post-id="{{postId}}" data-ref="projectAcmCard" data-event-opts="{{[['^dotClick',[['cardDotClick']]]]}}" bind:dotClick="__e" class="data-v-619f8fab vue-ref" bind:__l="__l"></kt-community-post-card><kt-community-post-operate-popup bind:removeSuccess="__e" vue-id="5b5a328a-2" data-ref="communityPostOperatePopup" data-event-opts="{{[['^removeSuccess',[['removeSuccess']]]]}}" class="data-v-619f8fab vue-ref" bind:__l="__l"></kt-community-post-operate-popup></view></block><view class="data-v-619f8fab"><view class="{{['data-v-619f8fab',clazz.box]}}"><view class="box-title data-v-619f8fab">{{$root.g0}}</view><view hidden="{{!(loading)}}" class="data-v-619f8fab"><view style="height:100rpx;" class="data-v-619f8fab"></view><block wx:if="{{loading}}"><u-loading-icon vue-id="5b5a328a-3" mode="circle" size="{{50}}" class="data-v-619f8fab" bind:__l="__l"></u-loading-icon></block></view><block wx:if="{{$root.g1}}"><view class="no-data data-v-619f8fab"><view class="data-v-619f8fab"><image class="no-data-image data-v-619f8fab" src="{{$root.g2}}"></image></view><view class="no-data-text data-v-619f8fab">{{$root.g3+''}}</view></view></block><block wx:for="{{list}}" wx:for-item="item" wx:for-index="__i0__"><view class="box-item data-v-619f8fab"><kt-community-post-card vue-id="{{'5b5a328a-4-'+__i0__}}" is-transparent="{{true}}" border-radius="0" has-dot="{{false}}" has-bottom-operation="{{false}}" post="{{item}}" class="data-v-619f8fab" bind:__l="__l"></kt-community-post-card></view></block></view></view><view style="height:400rpx;" class="data-v-619f8fab"></view></scroll-view><view class="bottom data-v-619f8fab"><block wx:if="{{$root.g4>0}}"><kt-image-select bind:input="__e" vue-id="5b5a328a-5" file-group-code="fp" value="{{bindParams.imageIds}}" data-event-opts="{{[['^input',[['__set_model',['$0','imageIds','$event',[]],['bindParams']]]]]}}" class="data-v-619f8fab" bind:__l="__l"></kt-image-select></block><kt-send-input vue-id="5b5a328a-6" placeholder="{{placeholder}}" mode="panel" has-video="{{false}}" has-voice="{{false}}" has-user-account="{{false}}" data-ref="sendInput" data-event-opts="{{[['^send',[['send']]]]}}" bind:send="__e" class="data-v-619f8fab vue-ref" bind:__l="__l"></kt-send-input></view></view>