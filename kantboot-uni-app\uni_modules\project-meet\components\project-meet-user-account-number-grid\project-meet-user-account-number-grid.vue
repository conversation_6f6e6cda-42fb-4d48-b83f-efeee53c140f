<template>
  <view class="box">
    <view class="grid">
      <view
          @click="toInterrelation('follow')"
          class="grid-item">
        <view class="num">{{getInterrelation(userAccount).followCount || '0'}}</view>
        <view class="text">{{$i18n.zhToGlobal("关注")}}</view>
      </view>
      <view class="grid-item"
        @click="toPostSelf()"
      >
        <view class="num">{{userAccountRelationship.postCount || '0'}}</view>
        <view class="text">{{$i18n.zhToGlobal("动态")}}</view>
      </view>
       <view
           @click="toInterrelation('followed')"
           class="grid-item">
        <view class="num">{{getInterrelation(userAccount).followedCount || '0'}}</view>
        <view class="text">{{$i18n.zhToGlobal("粉丝")}}</view>
      </view>
       <view
           @click="toLike('like')"
           class="grid-item">
        <view class="num">{{userAccountRelationship.likeCount || '0'}}</view>
        <view class="text">{{$i18n.zhToGlobal("点赞")}}</view>
      </view>
       <view
           @click="toVisit()"
           class="grid-item">
        <view class="num">{{userAccountRelationship.visitCount || '0'}}</view>
        <view class="text">{{$i18n.zhToGlobal("访客")}}</view>
      </view>
    </view>
    <kt-user-account-interrelation-popup
    ref="ktUserAccountInterrelationPopup"
    @select="selectUserAccount"
    ></kt-user-account-interrelation-popup>
    <project-meet-user-info-popup
    ref="projectMeetUserInfoPopup"
    ></project-meet-user-info-popup>
    <project-meet-like-list-popup
    ref="projectMeetLikeListPopup"
    ></project-meet-like-list-popup>
    <project-meet-post-self-popup
    ref="projectMeetPostSelfPopup"
    ></project-meet-post-self-popup>
    <project-meet-visit-popup
    ref="projectMeetVisitPopup"
    ></project-meet-visit-popup>
  </view>
</template>

<script>
export default {
  props:{
    userAccount:{
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      interrelation:{},
      isSvip:false,
      isVip:false,
      userAccountRelationship:{
        visitCount: 0,
        likeCount: 0,
        postCount: 0
      }
    };
  },
  watch: {
    userAccount: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.getInterrelationByUserAccountId();
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.getUserAccountRelationship();
    this.$kt.event.on("changeTabbar:mineMeet",()=>{
      this.getUserAccountRelationship();
    });
  },
  methods: {
    toVisit(){
      let currentTime = new Date().getTime();

      this.isSvip = this.userAccount.gmtSvipExpire && this.userAccount.gmtSvipExpire > currentTime;
      this.isVip = this.userAccount.gmtVipExpire && this.userAccount.gmtVipExpire > currentTime;

      if(!this.isVip&&!this.isSvip){
        uni.showToast({
          title: this.$i18n.zhToGlobal("开启VIP，享受专属特权"),
          icon: 'none'
        });
        return;
      }
      if(this.$kt.style.detectDeviceType()==="pc"){
        this.$refs.projectMeetVisitPopup.open();
        return;
      }
      this.$kt.router.navTo('/pages/project-meet-pages/user-visit/user-visit');
    },
    toLike(){
      let currentTime = new Date().getTime();
      this.isSvip = this.userAccount.gmtSvipExpire && this.userAccount.gmtSvipExpire > currentTime;
      this.isVip = this.userAccount.gmtVipExpire && this.userAccount.gmtVipExpire > currentTime;

      if(!this.isVip&&!this.isSvip){
        uni.showToast({
          title: this.$i18n.zhToGlobal("开启VIP，享受专属特权"),
          icon: 'none'
        });
        return;
      }
      // projectMeetLikeListPopup
      if(this.$kt.style.detectDeviceType()==="pc"){
        this.$refs.projectMeetLikeListPopup.open();
        return;
      }
      this.$kt.router.navTo('/pages/project-meet-pages/like-list/like-list');
    },
    toPostSelf(){
      // projectMeetLikeListPopup
      if(this.$kt.style.detectDeviceType()==="pc"){
        this.$refs.projectMeetPostSelfPopup.open();
        return;
      }

      this.$kt.router.navTo('/pages/project-meet-pages/post-self/post-self');
    },
    // /fp-community-web/userAccountRelationship/getSelf
    async getUserAccountRelationship() {
      await this.$request.post('/fp-community-web/userAccountRelationship/getSelf', {
        data: {}
      }).then(res => {
        this.userAccountRelationship = res.data;
      }).catch(err => {
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        });
      });
    },
    selectUserAccount(userAccount){
      this.$refs.projectMeetUserInfoPopup.open(userAccount.id)
    },
    toInterrelation(code){
      let currentTime = new Date().getTime();
      this.isSvip = this.userAccount.gmtSvipExpire && this.userAccount.gmtSvipExpire > currentTime;
      this.isVip = this.userAccount.gmtVipExpire && this.userAccount.gmtVipExpire > currentTime;

      if(!this.isVip&&!this.isSvip){
        uni.showToast({
          title: this.$i18n.zhToGlobal("开启VIP，享受专属特权"),
          icon: 'none'
        });
        return;
      }


      if(this.$kt.style.detectDeviceType()==="pc"){
        this.$refs.ktUserAccountInterrelationPopup.open(code);
        return;
      }
      this.$kt.router.navTo('/pages/project-meet-pages/user-account-interrelation/user-account-interrelation?code='+code+"&userAccountId="+this.userAccount.id);
    },
    async getInterrelationByUserAccountId() {
      await this.$request.post('/user-interrelation-web/interrelation/getByUserAccountId', {
        data: { userAccountId: this.userAccount.id }
      }).then(res => {
        this.interrelation = res.data;
        this.userAccount.interrelation = this.interrelation;
      }).catch(err => {
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        });
      });
    },
    getInterrelation(userAccount){
      let interrelation = {
        followCount: 0,
        followedCount: 0,
        mutualFollowCount: 0

      }
      if(this.interrelation){
        interrelation = this.interrelation;
        if(!interrelation.mutualFollowCount) {
          interrelation.mutualFollowCount = 0;
        }
        if(!interrelation.followCount) {
          interrelation.followCount = 0;
        }
        if(!interrelation.followedCount) {
          interrelation.followedCount = 0;
        }
      }
      return interrelation;
    },
  },
}
</script>

<style lang="scss" scoped>
.box{
  padding: 20rpx;
  box-sizing: border-box;
}
.num {
  font-size: 28rpx;
  color: #000;
  font-weight: bold;
}
.text {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}
.grid{
  .grid-item{
    text-align: center;
    display: inline-block;
    width: 20%;
    vertical-align: top;
    cursor: pointer;
  }
}
</style>
