let result = {};
/**
 * 验证码邮箱格式
 */
result.checkEmail = (email) => {
    // 邮箱格式正则
    let reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/;
    return new Promise((resolve, reject) => {
        if (reg.test(email)) {
            resolve();
        } else {
            reject();
        }
    })
}

/**
 * 验证码手机格式
 */
result.checkPhone = (phoneAreaCode,phone) => {
    let map = {
        "86": /^1[3456789]\d{9}$/,
        "852": /^[569]\d{7}$/,
        "853": /^[569]\d{7}$/,
        "886": /^09\d{8}$/,
        "1": /^[0-9]{10}$/,
    }
    return new Promise((resolve, reject) => {
        if (map[phoneAreaCode].test(phone)) {
            resolve();
        } else {
            reject();
        }
    });
}

export default result;