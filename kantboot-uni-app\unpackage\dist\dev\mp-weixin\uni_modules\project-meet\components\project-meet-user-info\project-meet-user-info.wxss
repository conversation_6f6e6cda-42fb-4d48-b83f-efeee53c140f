
.body {
  position: relative;
  top: -20rpx;
  background-color: white;
  border-radius: 10px 10px 0 0;
}
.body_title {
  /* background-color: aqua; */
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.body_title_left {
  margin-top: 20rpx;
  margin-left: 20rpx;
  display: flex;
  align-items: flex-end;
}
.body_title_right {
  margin-top: 20rpx;
  margin-right: 20rpx;
  display: flex;
  color: rgb(133, 133, 133);
  font-size: 24rpx;
}
.body_title_name {
  font-size: 32rpx;
  font-weight: bold;
}
.body_title_age {
  margin-left: 20rpx;
  background: rgb(238, 238, 238);
  border-radius: 14rpx;
  font-size: 20rpx;
  min-width: 60rpx;
  padding: 10rpx 0 10rpx 0;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}
.body_title_age_female {
  background: #ff5f79;
}
.body_title_age_male {
  background: #5a7ef6;
}
.body_info {
  display: flex;
  font-size: 22rpx;
  margin-top: 20rpx;
  margin-left: 20rpx;
}
.body_adjective {
  margin-top: 40rpx;
  padding-left: 20rpx;
  margin-bottom: 30rpx;
}
.body_adjective_title {
  display: flex;
  align-items: center;
}
.body_adjective_icn {
  height: 32rpx;
  width: 8rpx;
  background: #ff5f79;
  border-radius: 4rpx;
}
.body_adjective_text {
  margin-left: 15rpx;
  color: rgba(0, 0, 0, 0.9);
  font-weight: 700;
  font-size: 32rpx;
}
.body_adjective_body {
  margin-top: 30rpx;
}
.adjective_body_icon {
  display: inline-block;
  margin: 10rpx;
  padding: 16rpx 28rpx;
  background-color: #eef2fe;
  font-size: 28rpx;
  color: #5a7ef6;
  border-radius: 30rpx;
}
.foot_box {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 200rpx;
  border-radius: 10rpx;
  background: white;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 16px 0px;
  display: flex;
}
.foot_btn {
  width: 224rpx;
  height: 86rpx;
  background: #5a7ef6;
  border-radius: 42rpx;
  text-align: center;
  line-height: 86rpx;
  font-size: 30rpx;
  color: #fff;
  font-weight: 700;
}
.no-data-text {
  font-size: 28rpx;
  color: #999;
}
.foot_btn {
  cursor: pointer;
}
.foot_box_fixed{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}
.tag-v{
  display: inline-block;
  padding: 0 10rpx;
  font-size: 24rpx;
  margin-left: 10rpx;
  border-radius: 20rpx;
}
.tag-v-vip{
  background-color: #ff5f79;
  color: #fff;
}
.tag-v-svip{
  background-color: #d4af37;
  color: #fff;
}
.online-box{
  position: absolute;
  bottom: 30rpx;
  z-index: 10000;
  /* 绿色
 */
  background-color: rgba(0, 118, 0, 0.7);
  color: #FFFFFF;
  border: 2rpx solid rgba(0, 118, 0, 1);
  border-radius: 20rpx;
  left: 20rpx;
  padding: 0 20rpx 0 20rpx;
  font-size: 24rpx;
}
.offline-box{
  background-color: rgba(255, 0, 0, 0.7);
  color: #FFFFFF;
  border: 2rpx solid rgba(255, 0, 0, 1);
}


