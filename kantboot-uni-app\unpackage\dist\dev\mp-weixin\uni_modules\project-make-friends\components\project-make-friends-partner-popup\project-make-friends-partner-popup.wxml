<view class="data-v-97fa71f4"><kt-popup vue-id="6752ca80-1" overlayClose="{{false}}" zIndex="{{999999999}}" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-97fa71f4 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-97fa71f4"><view data-event-opts="{{[['tap',[['closePop',['$event']]]]]}}" class="close-btn data-v-97fa71f4" bindtap="__e">×</view><view class="title data-v-97fa71f4">{{isEdit?$root.g0:$root.g1}}</view><block wx:if="{{userData}}"><view class="user-info data-v-97fa71f4"><view class="info-section data-v-97fa71f4"><view class="section-title data-v-97fa71f4">{{$root.g2}}</view><view class="user-card data-v-97fa71f4"><image class="user-avatar data-v-97fa71f4" src="{{userData.fileIdOfAvatar?$root.g3:'/static/default-avatar.png'}}" mode="aspectFill"></image><view class="user-details data-v-97fa71f4"><view class="user-nickname data-v-97fa71f4">{{$root.g4}}</view><view class="user-id data-v-97fa71f4">{{"ID: "+userData.id}}</view><block wx:if="{{userData.phone}}"><view class="user-phone data-v-97fa71f4">{{''+userData.phoneAreaCode+" "+userData.phone+''}}</view></block></view></view></view><view class="info-section data-v-97fa71f4"><view class="section-title data-v-97fa71f4">{{$root.g5}}</view><view class="probability-display data-v-97fa71f4">{{availableProbability+"%"}}</view></view><block wx:if="{{isEdit}}"><view class="info-section data-v-97fa71f4"><view class="section-title data-v-97fa71f4">{{$root.g6}}</view><view class="current-probability-display data-v-97fa71f4">{{currentProbability+"%"}}</view></view></block></view></block><view class="probability-section data-v-97fa71f4"><view class="section-title data-v-97fa71f4">{{$root.g7}}</view><view class="input-container data-v-97fa71f4"><input class="probability-input data-v-97fa71f4" type="number" placeholder="{{$root.g8}}" max="{{availableProbability}}" min="0" data-event-opts="{{[['input',[['__set_model',['','inputProbability','$event',[]]]]]]}}" value="{{inputProbability}}" bindinput="__e"/><text class="unit data-v-97fa71f4">%</text></view><view class="tips data-v-97fa71f4">{{$root.g9}}</view></view><view class="footer data-v-97fa71f4"><view class="buttons data-v-97fa71f4"><button class="cancel-btn data-v-97fa71f4" disabled="{{processing}}" data-event-opts="{{[['tap',[['closePop',['$event']]]]]}}" bindtap="__e">{{''+$root.g10+''}}</button><button class="confirm-btn data-v-97fa71f4" disabled="{{processing}}" data-event-opts="{{[['tap',[['handleConfirm',['$event']]]]]}}" bindtap="__e">{{''+(processing?$root.g11:$root.g12)+''}}</button></view></view></view></kt-popup></view>