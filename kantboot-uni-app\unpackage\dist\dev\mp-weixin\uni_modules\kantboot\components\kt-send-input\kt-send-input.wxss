@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.chat-footer-input-container.data-v-eddcde14 {
  padding: 20rpx;
  box-sizing: border-box;
}
.chat-footer-input.data-v-eddcde14 {
  width: 100%;
  height: 100%;
  background-color: #f8f8f8;
  padding: 10rpx;
  border-radius: 10rpx;
  max-height: 200rpx;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}
.chat-footer-input.data-v-eddcde14::-webkit-scrollbar {
  width: 0;
  height: 0;
  background: transparent;
}
.chat-footer-input.data-v-eddcde14 {
  height: 100%;
  width: 100%;
  padding: 15rpx;
}
.btn.data-v-eddcde14 {
  cursor: pointer;
}
.btn.data-v-eddcde14:active {
  opacity: 0.8;
}
.chat-footer-container.data-v-eddcde14 {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding-top: 40rpx;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 10%, white 30%, white 100%);
}
.icon.data-v-eddcde14 {
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  left: 50%;
  -webkit-transform: translate(-50%, calc(-100% - 10rpx));
          transform: translate(-50%, calc(-100% - 10rpx));
}
.icon-loading.data-v-eddcde14 {
  -webkit-animation: loading-icon-ani-in-page-chat-data-v-eddcde14 1s linear infinite;
          animation: loading-icon-ani-in-page-chat-data-v-eddcde14 1s linear infinite;
}
@-webkit-keyframes loading-icon-ani-in-page-chat-data-v-eddcde14 {
0% {
    -webkit-transform: translate(-50%, calc(-100% - 10rpx)) scale(1);
            transform: translate(-50%, calc(-100% - 10rpx)) scale(1);
}
50% {
    -webkit-transform: translate(-50%, calc(-100% - 10rpx)) scale(0.3);
            transform: translate(-50%, calc(-100% - 10rpx)) scale(0.3);
}
100% {
    -webkit-transform: translate(-50%, calc(-100% - 10rpx)) scale(1);
            transform: translate(-50%, calc(-100% - 10rpx)) scale(1);
}
}
@keyframes loading-icon-ani-in-page-chat-data-v-eddcde14 {
0% {
    -webkit-transform: translate(-50%, calc(-100% - 10rpx)) scale(1);
            transform: translate(-50%, calc(-100% - 10rpx)) scale(1);
}
50% {
    -webkit-transform: translate(-50%, calc(-100% - 10rpx)) scale(0.3);
            transform: translate(-50%, calc(-100% - 10rpx)) scale(0.3);
}
100% {
    -webkit-transform: translate(-50%, calc(-100% - 10rpx)) scale(1);
            transform: translate(-50%, calc(-100% - 10rpx)) scale(1);
}
}
.add-icon.data-v-eddcde14 {
  padding: 0 10rpx 0 10rpx;
  border-radius: 30rpx;
  width: 47rpx;
  height: 47rpx;
  border: 2px solid #333333;
}
