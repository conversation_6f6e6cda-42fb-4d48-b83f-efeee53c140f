<template>
	<view class="edit-box">
		<image class="edit-icon" :src="$kt.file.byPath('icon/edit.svg')"></image>
	</view>
</template>

<script>
export default {
	data() {
		return {
		}
	},
	onLoad(options) {
	},
	methods: {

	}
}
</script>

<style lang="scss" scoped>
.edit-box {
	position: absolute;
	width: 20px;
	height: 20px;
	right: 30px;
	z-index: 3;
	.edit-icon {
		width: 100%;
		height: 100%;
	}
}

.edit-box:active {
	transform: scale(0.9);
}
</style>
