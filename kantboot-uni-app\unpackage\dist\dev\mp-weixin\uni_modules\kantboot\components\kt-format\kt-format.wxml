<view class="{{['data-v-1897cc4c',clazz.ktFormat]}}"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index"><block wx:if="{{item.$orig.type==='text'}}"><view class="kt-format-content data-v-1897cc4c"><text class="kt-format-content-text data-v-1897cc4c">{{item.$orig.content}}</text></view></block><block wx:else><block wx:if="{{item.$orig.type==='image:id'}}"><view class="kt-format-content data-v-1897cc4c"><view class="data-v-1897cc4c"><image class="kt-format-content-image data-v-1897cc4c" mode="widthFix" src="{{item.g0}}" data-event-opts="{{[['tap',[['showImage',['['+item.$orig.content+']',0]]]]]}}" catchtap="__e"></image></view></view></block><block wx:else><block wx:if="{{item.$orig.type==='images:id'}}"><view class="kt-format-content data-v-1897cc4c"><block wx:if="{{item.g1===1}}"><view style="width:100%;" class="data-v-1897cc4c"><image class="kt-format-content-image data-v-1897cc4c" src="{{item.g2}}" mode="widthFix" data-event-opts="{{[['tap',[['showImage',['$0',0],[[['data','',index,'content']]]]]]]}}" catchtap="__e"></image></view></block><block wx:for="{{item.l0}}" wx:for-item="imageItem" wx:for-index="imageIndex"><block wx:if="{{imageItem.g3===2}}"><view style="aspect-ratio:1 / 1;width:calc(50% - 50rpx);border-radius:20rpx;margin-right:10rpx;display:inline-block;" class="data-v-1897cc4c"><image class="kt-format-content-image data-v-1897cc4c" src="{{imageItem.g4}}" mode="aspectFill" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig,imageIndex})}}" catchtap="__e"></image></view></block></block><block wx:for="{{item.l1}}" wx:for-item="imageItem" wx:for-index="imageIndex"><block wx:if="{{imageItem.g5>=3}}"><view style="aspect-ratio:1 / 1;width:calc(33.3%);padding:5rpx;box-sizing:border-box;display:inline-block;" class="data-v-1897cc4c"><image class="kt-format-content-image data-v-1897cc4c" src="{{imageItem.g6}}" mode="aspectFill" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item:item.$orig,imageIndex})}}" catchtap="__e"></image></view></block></block></view></block><block wx:else><block wx:if="{{item.$orig.type==='video:id'}}"><view class="kt-format-content data-v-1897cc4c"><view style="{{'position:'+('relative')+';'+('width:'+('100%')+';')+('aspect-ratio:'+(item.$orig.ratio||'16 / 9')+';')}}" class="data-v-1897cc4c"><video style="width:100%;height:100%;border-radius:20rpx;" src="{{item.g7}}" class="data-v-1897cc4c"></video></view></view></block><block wx:else><block wx:if="{{item.$orig.type==='postShare'}}"><view class="kt-format-content data-v-1897cc4c"><view style="{{'position:'+('relative')+';'+('width:'+('100%')+';')}}" class="data-v-1897cc4c"><block wx:if="{{item.$orig.content}}"><view data-event-opts="{{[['tap',[['postCardClick',[['o',['id',item.$orig.content]]]]]]]}}" catchtap="__e" class="data-v-1897cc4c"><kt-community-post-card vue-id="{{'fa60bb28-1-'+index}}" post-id="{{item.$orig.content}}" has-dot="{{false}}" has-bottom-operation="{{false}}" data-event-opts="{{[['^userClick',[['postUserCardClick']]]]}}" bind:userClick="__e" class="data-v-1897cc4c" bind:__l="__l"></kt-community-post-card></view></block></view></view></block></block></block></block></block></block></view>