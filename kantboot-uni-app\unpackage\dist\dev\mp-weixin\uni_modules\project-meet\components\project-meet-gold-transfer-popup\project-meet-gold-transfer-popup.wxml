<view class="data-v-3b72e4f5"><kt-popup vue-id="32ea3ab1-1" data-ref="ktPopup" class="data-v-3b72e4f5 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-3b72e4f5',clazz.box]}}"><view class="my-gold data-v-3b72e4f5">{{''+$root.g0+": "+''}}<text class="my-gold-text data-v-3b72e4f5">{{self.projectMeetGold||'0'}}</text></view><view class="title data-v-3b72e4f5"><image class="title-icon data-v-3b72e4f5" src="{{$root.g1}}"></image>{{''+$root.g2+''}}</view><view class="data-v-3b72e4f5"><view style="height:30rpx;" class="data-v-3b72e4f5"></view><view class="input-box data-v-3b72e4f5"><input class="input-input data-v-3b72e4f5 vue-ref" type="text" id="goldTransferInput" pattern="\d*" placeholder="{{$root.g3}}" data-ref="intInput" data-event-opts="{{[['input',[['__set_model',['$0','number','$event',[]],['requestParams']],['input',['$event']]]]]}}" value="{{requestParams.number}}" bindinput="__e"/></view><view style="height:30rpx;" class="data-v-3b72e4f5"></view><view class="data-v-3b72e4f5"><kt-button bind:click="__e" vue-id="{{('32ea3ab1-2')+','+('32ea3ab1-1')}}" data-ref="ktButton" data-event-opts="{{[['^click',[['toTransfer']]]]}}" class="data-v-3b72e4f5 vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g4+''}}</kt-button></view><view style="height:30rpx;" class="data-v-3b72e4f5"></view><view style="text-align:center;" class="data-v-3b72e4f5"><view data-event-opts="{{[['tap',[['toRecharge',['$event']]]]]}}" class="recharge-btn data-v-3b72e4f5" bindtap="__e">{{$root.g5}}</view></view><view style="height:30rpx;" class="data-v-3b72e4f5"></view></view></view></kt-popup><block wx:if="{{self.id}}"><project-meet-gold-popup vue-id="32ea3ab1-3" user-account-id="{{self.id}}" data-ref="projectMeetGoldPopup" data-event-opts="{{[['^paySuccess',[['getSelf']]]]}}" bind:paySuccess="__e" class="data-v-3b72e4f5 vue-ref" bind:__l="__l"></project-meet-gold-popup></block></view>