<view class="data-v-1c5c753a"><view id="headerInUserAccountInterrelation" class="data-v-1c5c753a"><kt-nav-bar vue-id="6f32b30c-1" title="{{$root.g0}}" class="data-v-1c5c753a" bind:__l="__l"></kt-nav-bar></view><view class="box data-v-1c5c753a"><kt-user-account-interrelation-panel vue-id="6f32b30c-2" user-account-id="{{userAccountId}}" init-code="{{initCode}}" height="{{'calc(100vh - '+headerHeight+'rpx - 160rpx)'}}" data-ref="userAccountInterrelationPanel" data-event-opts="{{[['^select',[['toUserInfoPage']]]]}}" bind:select="__e" class="data-v-1c5c753a vue-ref" bind:__l="__l"></kt-user-account-interrelation-panel></view></view>