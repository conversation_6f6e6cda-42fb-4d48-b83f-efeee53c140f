@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.panel.data-v-71ea47f9 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.panel .panel-header.data-v-71ea47f9 {
  padding: 20rpx;
  width: 100%;
}
.panel .panel-header .panel-header-content.data-v-71ea47f9 {
  white-space: nowrap;
  overflow-x: auto;
}
.panel .panel-header .panel-header-content .panel-header-content-item.data-v-71ea47f9 {
  display: inline-block;
  margin-right: 30rpx;
  font-size: 36rpx;
  color: #333;
}
.panel .panel-header .panel-header-content .panel-header-content-item-selected.data-v-71ea47f9 {
  font-weight: bold;
  color: #000000;
  border-bottom: 2rpx solid #000000;
}
.panel .panel-header-fixed.data-v-71ea47f9 {
  background-color: #fff;
}
.panel-header-content.data-v-71ea47f9::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}
.panel-header-content.data-v-71ea47f9::-webkit-scrollbar-thumb {
  background-color: #fff;
  border-radius: 0;
}
.box-item.data-v-71ea47f9 {
  width: 50%;
  display: inline-block;
  padding: 20rpx;
  box-sizing: border-box;
}
.box-item-split.data-v-71ea47f9 {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  margin-top: 20rpx;
}
.no-data.data-v-71ea47f9 {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 100rpx;
}
.no-data .no-data-image.data-v-71ea47f9 {
  width: 100rpx;
  height: 100rpx;
  opacity: 0.6;
}
.panel-mode-color-scheme-light panel-header-fixed.data-v-71ea47f9 {
  background-color: #fff;
}
.panel-mode-color-scheme-dark .panel-header.data-v-71ea47f9 {
  background-color: #191919;
}
.panel-mode-color-scheme-dark .panel-header .panel-header-content-item.data-v-71ea47f9 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.panel-mode-color-scheme-dark .no-data.data-v-71ea47f9 {
  color: #b9b9b9;
}
.panel-mode-color-scheme-dark .no-data-image.data-v-71ea47f9 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
