<template>
  <view :class="clazz.container">
    <view :class="clazz.bg"></view>

    <view id="pageHomeHeader" style="width: 100%;top:0;left:0;">

      <kt-status-bar-height></kt-status-bar-height>
      <view style="text-align: left">
<!--        <kt-nav-bar-->
<!--            :background-color="'rgba(255,255,255,0)'"-->
<!--        :is-has-i18n="false"-->
<!--        :title="$i18n.zhToGlobal('老乡驿站')"-->
<!--        :icon="$kt.file.byPath('tabbar/home-selected.svg')"-->
<!--        ></kt-nav-bar>-->
        <image
            style="height: 70rpx;margin-left: 20rpx"
            mode="heightFix"
        src="https://pay.kantboot.com/logo2.png"
        ></image>

      </view>
      <view style="padding: 20rpx">
        <kt-carousel
            type-code="adOfHomePage"
            :height="'300rpx'"
        ></kt-carousel>
      </view>

    </view>


    <view class="box-2">
      <view class="in-box">
        <view
            class="option-box">

          <view
              class="option-box-item-2"
              style="color: #333;font-size:34rpx;font-weight: bold;display: inline-block"
          >
            {{"保定市人"}}{{"在"}}{{"大同市"}}
          </view>

          <view
              class="option-box-item-3"
              style="margin-left: 10rpx;opacity: .7;font-size: 26rpx;"
          >
            <image
                v-if="false"
                style="width: 25rpx;height: 25rpx;margin-right: 10rpx;"
                :src="$kt.file.byPath('icon/personMove.svg')"
            ></image>

            {{"切换城市"}}
          </view>

          <view
              class="option-box-item-1"
              style="float: right;font-weight: bold;margin-top: 10rpx">
            <image
                style="width: 40rpx;height: 40rpx;margin-right: 0rpx;"
                :src="$kt.file.byPath('icon/doubleTop.svg')"
            ></image>
            <view

                style="display: inline-block;vertical-align: top;color:#046b50">
              {{$i18n.zhToGlobal("我要置顶")}}<text style="font-size: 20rpx">{{"（367人）"}}</text>
            </view>
          </view>

        </view>

        <view style="height: 40rpx"></view>

        <project-hometown-user-account-list-panel
            :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px - 190rpx)'">
          ></project-hometown-user-account-list-panel>
      </view>
    </view>


    <view
        v-if="false"
    class="list-count"
    >
      <image
          style="width: 25rpx;height: 25rpx;margin-right: 10rpx;"
      :src="$kt.file.byPath('icon/invitePersons.svg')"
      ></image>
      {{"人数"}}{{": "}}<text style="font-weight: bold">{{"100"}}</text>
    </view>


  </view>
</template>

<script>


export default {
  data() {
    return {
      navHeight: 48,
      menuButtonWidth: 0,
      pageHomeHeaderHeight: 0,
      clazz: {
        container: this.$kt.style.toggleClass("container"),
        bg: this.$kt.style.toggleClass("bg"),
      },
      selected: 'recommend',
    }
  },
  mounted() {
    // #ifdef MP-WEIXIN
    this.navHeight = uni.getSystemInfoSync().system.indexOf("ios") != -1 ? 44 : 48;
    // 获取胶囊信息
    this.menuButtonWidth = wx.getMenuButtonBoundingClientRect().width;
    console.log(this.menuButtonWidth, "获取胶囊信息");
    // #endif
    // 获取pageHomeHeader的高度
    this.$nextTick(() => {
      // uniapp的获取元素高度的方法
      uni.createSelectorQuery().in(this).select('#pageHomeHeader').boundingClientRect((rect) => {
        console.log(rect, "获取pageHomeHeader的高度")
        this.pageHomeHeaderHeight = rect.height;
      }).exec()
    });
    this.$kt.event.on('changeTabbar', () => {
      console.log("获取#pageHomeHeader的高度");
      this.$nextTick(() => {
        uni.createSelectorQuery()
            .in(this)
            .select("#pageHomeHeader")
            .boundingClientRect((res) => {
              console.log(res, "pageHomeHeader");
              this.pageHomeHeaderHeight = res.height;
            })
            .exec();
      });
    });
  },

  methods: {
    toSearch(){
      this.$kt.router.navTo("/pages/project-make-friends-pages/user-search/user-search", {
        code: this.initCode
      });
    },
    cardUserClick(user){
      this.$kt.router.navTo("/pages/project-make-friends-pages/user-info/user-info",{
        userAccountId: user.id
      });
    },
    cardDotClick(post){
      console.log(post,"post");
      if(!this.$kt.userAccount.getIsLogin()){
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.$refs.communityPostOperatePopup.open(post);
    },
    cardClick(post){
      this.$kt.router.navTo("/pages/project-make-friends-pages/post-detail/post-detail",{
        postId: post.id
      });
    },
    toPostPopup(post) {
      this.$refs.communityPostPopup.open(post);
    },
    swiperChange(e){
      this.selected = e.target.currentItemId;
    },
    isY() {
      // 转换为大写
      let deviceType = this.$kt.style.detectDeviceType().toUpperCase();
      return deviceType === 'PC' ||
          deviceType === 'TV';

    }
  }
}
</script>

<style lang="scss" scoped>
.bg {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  //background-color: #f9f9f9;
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
  //background-color: #F0F0F0;
}

.bg-mode-color-scheme-light {
  //background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}

.bg-mode-color-scheme-dark {
  background: #191919;
}

.header-box-1 {
  padding: 0 10rpx;
  box-sizing: border-box;
  // 不换行
  white-space: nowrap;

  .header-item {
    display: inline-block;
    font-size: 36rpx;
    letter-spacing: 3rpx;
    margin-right: 5rpx;
    padding: 10rpx 20rpx;
  }

  .header-item-selected {
    font-weight: bold;
  }
}

.scroll-view {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);

  // 超出滚动
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;
  // 隐藏滚动条
  &::-webkit-scrollbar {
    width: 0;
    height: 1rpx;
    display: none;
  }

  // 滚动
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

}

// 不显示滚动条
::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}

.second-box {
  width: 100%;

  .second-box-box {
    width: calc(100% - 100rpx);
    // 不换行
    white-space: nowrap;
    // 超出滚动
    overflow-x: auto;

    .second-box-item {
      display: inline-block;
      font-size: 32rpx;
      letter-spacing: 3rpx;
      margin-right: 5rpx;
      padding: 10rpx 20rpx;
      border-radius: 10rpx;
    }

    .second-box-item-selected {
      font-weight: bold;
    }
  }
}

.container-mode-device-pc {
  width: calc(100% - 240px - 400px);
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
}

.split {
  width: 100%;
  height: 3rpx;
  background-color: #eee;
}

.container-mode-color-scheme-dark {
  background-color: #191919;

  .split{
    background-color: #888888;
  }

  .header-box {
    background-color: #191919;
  }

  .scroll-view {
    background-color: #191919;
  }

  .second-icon {
    // 颜色反转
    filter: invert(1);
  }


  .header-box-1 {
    background-color: #191919;

    .header-item {
      color: #fff;
    }
  }

  .second-box {
    .second-box-box {
      .second-box-item {
        color: #fff;
      }

      .second-box-item-selected {
        color: #fff;
        font-weight: bold;
      }
    }
  }
}

.container-mode-device-pc {
  position: relative;
  width: calc(100% + 20px);
  padding: 0;
  box-sizing: border-box;

  .second-icon {
    margin-right: 10px;
  }
}

.option-box{
  width: 100%;

  .option-box-item-1{
    display: inline-block;
    font-size: 28rpx;
  }
  .option-box-item-3,.option-box-item-1{
    display: inline-block;
    font-size: 28rpx;
  }
}

.change-city-btn{
  position: fixed;
  top: 20rpx;
  right: 30rpx;
  z-index: 99999999999;
}

.re-btn{
  position: fixed;
  bottom: 150rpx;
  right: 30rpx;
  background-color: rgba(0,0,0,.9);
  color: #fff;
  border-radius: 20rpx;
  padding: 5rpx 20rpx;
}

.list-count{
  position: fixed;
  bottom: 120rpx;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 28rpx;
  color: #000000;
  background-color: #FFFFFF;
  padding: 20rpx;
}

.box-2{
  padding: 0 20rpx 20rpx 20rpx;
  box-sizing: border-box;
}

.in-box{
  background-color: rgba(255,255,255,1);
  border-radius: 30rpx;
  padding: 20rpx;
}

</style>