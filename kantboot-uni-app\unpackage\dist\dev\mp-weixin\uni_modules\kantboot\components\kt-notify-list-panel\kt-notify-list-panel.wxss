@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box-item.data-v-d504a67c {
  padding: 20rpx;
  border: 1rpx solid #F0F0F0;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
}
.box-item .box-item-date.data-v-d504a67c {
  font-size: 28rpx;
  text-align: right;
}
.no-data.data-v-d504a67c {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 30rpx;
}
.no-data .no-data-image.data-v-d504a67c {
  width: 100rpx;
  height: 100rpx;
  opacity: 0.6;
}
.loading-box.data-v-d504a67c {
  text-align: center;
  margin-top: 100rpx;
}
.loading-box .loading-box-img.data-v-d504a67c {
  width: 100rpx;
  height: 100rpx;
  opacity: 0.6;
  -webkit-filter: invert(1);
          filter: invert(1);
  -webkit-animation: loading-data-v-d504a67c 1s infinite;
          animation: loading-data-v-d504a67c 1s infinite;
}
@-webkit-keyframes loading-data-v-d504a67c {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes loading-data-v-d504a67c {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
