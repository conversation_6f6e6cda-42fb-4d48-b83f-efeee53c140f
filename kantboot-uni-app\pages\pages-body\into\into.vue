<template>
  <view>
    <into-back></into-back>
    <view class="center-box">
      <view class="img">
        <kt-image
            class="img-img"
            border-radius="20rpx"
            mode="widthFix"
            :src="$kt.file.byPath('image/logo.png')"></kt-image>
      </view>
    </view>

  </view>
</template>

<script>
import IntoBack from "./components/IntoBack.vue";
import $kt from "@/uni_modules/kantboot"
import projectConfig from "@/uni_modules/project-config";
import makeFriends from '@/uni_modules/project-make-friends/libs/load';


export default {
  components: {
    IntoBack
  },
  data() {
    return {
      isHasBack: false,
      isToTo: false
    };
  },
  onLoad(options) {
	  this.init(options);
  },
  async created() {
    // 获取该页面所有参数
    let pages = getCurrentPages();
    // 获取当前页面
    let currentPage = pages[pages.length - 1];
    let options = currentPage.options;
    setTimeout(() => {
      this.toTo(options)
    }, 20000)

    this.$kt.dataChange.checkDataChange("clientInit")
        .then(async (res) => {
          if(res.isChange){
            await $kt.i18n.loadingSupportLanguage();
            await $kt.i18n.loadingLocalized();
            await $kt.i18n.loadLanguagePackage("appFront", "en");
            await $kt.i18n.loadLanguagePackage("appFront", "zh_CN");
            await $kt.i18n.loadLanguagePackage("appFront");
            await $kt.userAccount.requestSelf();

            // 如果是交友项目
            if(projectConfig.projectCode === "makeFriends"){
              await makeFriends.loadCharacteristics();
              await makeFriends.loadInterests();
              await makeFriends.loadJobTypeAll();
            }

            this.$kt.event.emit("init:clientInit");
            return;
          }
          this.$kt.event.emit("init:clientInit");
        }).catch((err)=>{
          this.$kt.event.emit("init:clientInit");
        });

    this.$kt.event.on("init:clientInit",()=>{
      // 检查是否有待处理的邀约组ID
      this.checkPendingInvitationGroup();
      this.toTo(options)
    })


  },
  onShow() {
    uni.$emit("intoPage", true);
  },
  methods: {
	  init(options){
	    if(options.userAccountIdOfInviter&&!this.$kt.userAccount.getIsLogin()){
	      this.$kt.userAccount.setUserAccountIdOfInviter(options.userAccountIdOfInviter);
	    }
      console.log("init", options);
	    // 处理邀约组ID参数
	    if(options.invitationGroupId) {
	      if(!this.$kt.userAccount.getIsLogin()) {
	        // 未登录时缓存组ID
	        this.$kt.storage.set('pendingInvitationGroupId', options.invitationGroupId);
	      } else {
	        // 已登录时直接处理邀请逻辑
	        this.handleInvitationGroup(options.invitationGroupId);
	      }
	    }
	  },

	  // 处理邀约组邀请逻辑
	  handleInvitationGroup(invitationGroupId) {
	    // 根据组概率获取邀请人ID
	    this.$request.post('/project-make-friends-web/invitationGroup/getUserIdByGroupProbability', {
	      data: {
	        invitationGroupId: invitationGroupId
	      }
	    }).then(res => {
	      if (res.state === 2000 && res.data) {
	        const inviterUserId = res.data;
	        // 调用设置邀请人的方法
	        this.$kt.userAccount.setInviter(inviterUserId).then(() => {
	          console.log('邀请关系设置成功');
	          uni.showToast({
	            title: this.$i18n.zhToGlobal('加入成功'),
	            icon: 'success'
	          });
	        }).catch(err => {
	          console.error('设置邀请关系失败:', err);
	        });
	      }
	    }).catch(err => {
	      console.error('获取邀请人失败:', err);
	    });
	  },

	  // 检查待处理的邀约组ID
	  checkPendingInvitationGroup() {
	    const pendingGroupId = this.$kt.storage.get('pendingInvitationGroupId');
	    if (pendingGroupId && this.$kt.userAccount.getIsLogin()) {
	      // 清除缓存的组ID
	      this.$kt.storage.remove('pendingInvitationGroupId');
	      // 处理邀请逻辑
	      this.handleInvitationGroup(pendingGroupId);
	    }
	  },
    toTo(options) {
      if (this.isToTo) {
        return;
      }
      this.isToTo = true;
      // 判断是否参数是否有重定向
      if (options?.redirect) {
        // 跳转到指定页面
        uni.redirectTo({
          url: options.redirect
        })
      } else {
        // 获取所有页面
        let pages = getCurrentPages();
        let url = $kt.router.config.indexPath;
        // 判断是否有上一级页面
        if (pages.length > 1) {
          // 获取上一个页面
          url = "/"+pages[pages.length - 2].route;
        }

        // 跳转到指定页面
        uni.reLaunch({
          url
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.box {
  position: fixed;
  left: 0;
  bottom: 0;
  font-size: 30rpx;
  z-index: 999;
  color: #000000;
  width: 100%;
  text-align: center;
  padding: 20rpx 60rpx 20rpx 60rpx;
  box-sizing: border-box;

  .progress-box {
    width: 80%;
    height: 20rpx;
    //color: #FFFFFF;
    color: #000000;
    left: 0;
    background-color: #f2f2f2;
    //background-color: rgba(0, 0, 0, .8);
    border: 1px solid #FFFFFF;
    border-radius: 12rpx;
    margin-top: 10rpx;

    .progress {
      width: 0%;
      height: 100%;
      background-color: #000000;
      //background-color: rgba(255, 255, 255, .8);
      border-radius: 10rpx;
    }
  }
}

.progress-text {
  //color: #FFFFFF;
  color: #000000;
  font-weight: bold;
}

.center-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;

  .img {
    width: 400rpx;
    height: 400rpx;
    // 动画
    animation: into-rotate 2s linear infinite;
  }
}

@keyframes into-rotate {
  0% {
    opacity: 1;
  }
  25% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  75% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

.progress-text-2 {
  color: rgba(255, 255, 255, .7)
}

.img-img{
  width: 200rpx;
}

</style>
