<template>
  <view class="container">

  <view
      style="position: relative">
    <!-- #ifdef MP-WEIXIN -->
    <wechat-mp-login
        ref="wechatMpLogin"
    :agree="bodyData.agree"
    ></wechat-mp-login>
    <!-- #endif -->
    <type-select @change="changeType"></type-select>

    <view style="height: 10px"></view>
    <view class="input-box">
      <username-input
          @addUserLog="addUserLog"
          :body-data="bodyData"
          @change="changeMethod"></username-input>

      <view style="height: 30rpx"></view>

      <password-input
          @addUserLog="addUserLog"
          :body-data="bodyData"
          v-if="bodyData.typeCode==='login'"
          @change="changePassword"></password-input>


    </view>

    <view
        v-if="$i18n.getLanguageCode()==='fi_FI'||$i18n.getLanguageCode()==='bg_BG'||$i18n.getLanguageCode()==='fr_BE'"
        style="height: 30rpx"
    ></view>

    <view style="height: 30rpx"></view>
    <kt-button
        @click="toLogin()"
        ref="nextButton"
        :is-open-box-shadow="false">{{ $i18n.zhToGlobal("登录") }}{{ " " }}{{"➠"}}
    </kt-button>
    <view style="height: 20rpx"></view>
    <view class="forget-password-box">
      <view
          @click="projectMeetForgetPasswordPopupOpen()"
          class="forget-password">{{$i18n.zhToGlobal("忘记密码")}}</view>
    </view>
    <view style="height: 30rpx"></view>


    <!-- #ifdef MP-WEIXIN -->
    <view style="text-align: center">
      <view
          @click="$refs.wechatMpLogin.open()"
          class="to-back-btn">{{ $i18n.zhToGlobal("返回") }}</view>
    </view>
    <view style="height: 20rpx"></view>
    <!-- #endif -->
  </view>

    <login-agreement
        :agree="bodyData.agree"
    ref="loginAgreement"
    @change="changeAgree"
    ></login-agreement>

    <view style="height: 30rpx"></view>


  </view>
</template>

<script>
import LoginAgreement from "./LoginAgreement.vue";
import TypeSelect from "./TypeSelect.vue";
import VerificationCodeInput from "../inputComponents/VerificationCodeInput.vue";
import PasswordInput from "../inputComponents/PasswordInput.vue";
import $kt from "@/uni_modules/kantboot";
import operate from "../js/operate";
import WechatMpLogin
  from "./WechatMpLogin.vue";
import UsernameInput
  from "../inputComponents/UsernameInput.vue";

export default {
  components: {
    WechatMpLogin,
    LoginAgreement,
    TypeSelect,
    VerificationCodeInput,
    PasswordInput,
    UsernameInput
  },
  data() {
    return {
      passwordType:"password",
      bodyData: {
        phoneAreaCode: "86",
        typeCode: "verificationCode",
        methodCode: "email",
        to:"",
        email:"",
        phone: "",
        password: "",
        verificationCode: "",
        agree:true,
      },
      clazz:{
        tips: this.$kt.style.toggleClass("tips"),
      }

    }
  },
  created() {
  },
  mounted() {
    $kt.event.on("nextButtonInLogin:success",(res)=>{
      this.$refs.nextButton.success(res.msg);
    });
    $kt.event.on("nextButtonInLogin:error",(err)=>{
      this.$refs.nextButton.error(err.errMsg);
    });
    // this.$kt.event.emit("projectMeet:registerSuccess",this.bodyData);
    this.$kt.event.on("projectMeet:registerSuccess",(res)=>{
      setTimeout(()=>{
        this.bodyData.to = res.username;
        this.bodyData.password = res.password;
        this.bodyData.agree = true;

      },1000);

    })

  },
  methods: {
    addUserLog(){
      this.$request.post("/project-meet-web/userLog/add",{
        data:{
          typeCode: "login",
          // operationCode: "login",
          safeInputContent: "账号: "+this.bodyData.to +" 协议："+(this.bodyData.agree?"勾选":"未勾选"),
          inputContent: "账号: "+this.bodyData.to+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选"),
          operationCode: "loginInput",
          sceneCode: this.$kt.style.detectDeviceType(),
          operationText: "登录中",
          levelCode: "info",
          levelText: "普通信息",
        }
      });

    },
    projectMeetForgetPasswordPopupOpen(){
      this.$kt.event.emit("projectMeet:forgetPasswordPopupOpen");
      this.$request.post("/project-meet-web/userLog/add",{
        data:{
          typeCode: "forgetPassword",
          operationCode: "forgetPasswordClick",
          safeInputContent: "点击忘记密码 账号: "+this.bodyData.to +" 协议："+(this.bodyData.agree?"勾选":"未勾选"),
          inputContent: "点击忘记密码 账号: "+this.bodyData.to+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选"),
          // operationCode: "loginInput",
          sceneCode: this.$kt.style.detectDeviceType(),
          operationText: "点击忘记密码",
          levelCode: "info",
          levelText: "普通信息",
        }
      });
    },
    changeType(e) {
      this.bodyData.typeCode = e.typeCode;
    },
    changeMethod(e){
      this.bodyData.methodCode = e.methodCode;
      this.bodyData.to = e.to;
      if(e.methodCode==="email"){
        this.bodyData.email = e.email;
      }else{
        this.bodyData.phone = e.phone;
        this.bodyData.phoneAreaCode = e.phoneAreaCode;
      }
    },
    changeVerificationCode(e){
      this.bodyData.verificationCode = e.verificationCode;
    },
    changePassword(e){
      this.bodyData.password = e.password
    },
    changeAgree(e){
      this.bodyData.agree = e.agree;
    },
    toLogin(){
      if(!this.bodyData.agree){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未同意协议"));
        return;
      }
      this.$refs.nextButton.loading(null,99999);
      this.$request.post("/project-meet-web/userAccount/loginByUsernameAndPassword",{
        data:{
          username:this.bodyData.to,
          password:this.bodyData.password
        }
      }).then(res=>{
        if(res.data.userAccount.isBanned){
          this.$refs.nextButton.error(this.$i18n.zhToGlobal("此账号正在审核，审核通过后，我们会发送邮件通知您"));
          this.$request.post("/project-meet-web/userLog/add",{
            data:{
              typeCode: "login",
              operationCode: "loginFailToBanned",
              safeInputContent: "账号: "+this.bodyData.to +" 协议："+(this.bodyData.agree?"勾选":"未勾选"),
              inputContent: "账号: "+this.bodyData.to+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选"),
              sceneCode: this.$kt.style.detectDeviceType(),
              operationText: "登录失败，登录了被禁止的账号",
              levelCode: "success",
              levelText: "成功",
            }
          });
          return;

        }
        this.$refs.nextButton.success(res.msg);

        operate.loginSuccessHandle(res);

        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "login",
            operationCode: "loginSuccess",
            safeInputContent: "账号: "+this.bodyData.to +" 协议："+(this.bodyData.agree?"勾选":"未勾选"),
            inputContent: "账号: "+this.bodyData.to+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选"),
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "登录成功",
            levelCode: "success",
            levelText: "成功",
          }
        });

      }).catch(err=>{
        this.$refs.nextButton.error(err.errMsg);
        if(err.stateCode === 'usernameOrPasswordError'){
          this.$request.post("/project-meet-web/userLog/add",{
            data:{
              typeCode: "login",
              operationCode: "loginFail",
              sceneCode: this.$kt.style.detectDeviceType(),
              safeInputContent: "账号或密码错误 账号: "+this.bodyData.to +" 协议："+(this.bodyData.agree?"勾选":"未勾选"),
              inputContent: "账号或密码错误 账号: "+this.bodyData.to+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选"),
              operationText: "登录失败",
              levelCode: "error",
              levelText: "错误",
              errorReasonCode: "usernameOrPasswordError",
              errorReasonText: "用户名或密码错误",
            }
          });
        }


      });
    },
  },
}
</script>

<style lang="scss" scoped>
@import "../css/loginInput";
.container{
  position: relative;
}
.to-back-btn{
  color: #666666;
  font-size: 30rpx;
  letter-spacing: 2rpx;
}
.forget-password-box{
  text-align: center;
  .forget-password{
    display: inline-block;
    font-size: 28rpx;
    cursor: pointer;
  }
}

.forget-password:active{
  opacity: .8;
}
</style>
