<template>
  <view :class="clazz.container">
    <view :class="clazz.bg">
      <image
        v-if="$kt.style.detectDeviceType() === 'mobile'"
        class="bg-image"
        mode="aspectFill"
        :src="$kt.file.byPath('meet/bg.png')"
      ></image>
      <image
        v-if="$kt.style.detectDeviceType() === 'mobile'"
        class="bg-image-2"
        mode="aspectFill"
        :src="$kt.file.byPath('meet/bg.png')"
      ></image>
      <image
        v-if="$kt.style.detectDeviceType() === 'mobile'"
        class="bg-image-3"
        mode="aspectFill"
        :src="$kt.file.byPath('meet/bg.png')"
      ></image>

      <view class="bg-bg"></view>
    </view>
    <view
      v-if="userInfo.id"
      class="header-box"
      v-for="item in 2"
      :style="{
        position: item === 1 ? 'fixed' : 'relative',
        opacity: item === 1 ? 1 : 0,
        'z-index': item === 1 ? 999 : 0,
      }"
    >
      <kt-nav-bar
        background-color="rgba(255,255,255,.3)"
        :icon="$kt.file.byPath('tabbar/mine-selected.svg')"
        :title="$i18n.zhToGlobal('我的')"
      ></kt-nav-bar>
    </view>

    <view class="big-box">
      <view class="user-info-box"
      :class="{
        'user-info-box-vip': vipStatus === 'vip',
        'user-info-box-svip': vipStatus === 'svip',
      }"
      >
<!--        @click="$refs.userInfoPopup.open(userInfo)"-->
        <user-info-card-setting
            @click="toSetting()"
          style="position: absolute; right: 10px"
        ></user-info-card-setting>

        <view
            @click="$refs.userInfoPopup.open(userInfo)"
        >
          <kt-user-info-card
              class="user-info-card"
              :user-info="userInfo"
          ></kt-user-info-card>
        </view>
        <view style="height: 30rpx"></view>
        <project-meet-user-account-number-grid
        :user-account="userInfo"
        ></project-meet-user-account-number-grid>

        <view
        class="user-info-box-bottom-text"
        v-if="vipStatus === 'vip'"
        >{{ "VIP" }}</view>
        <view
        class="user-info-box-bottom-text"
        v-if="vipStatus === 'svip'"
        >{{ "SVIP" }}</view>
      </view>

      <view style="height: 40rpx"></view>

      <view
          v-if="$kt.style.detectDeviceType() === 'mobile'">
        <view
            @click="$refs.projectMeetVipPopup.open()"
            class="show-box">
          <image
              class="show-box-image"
              :src="$kt.file.byPath('/projectMeet/icon/guan.png')"
          ></image>
          <view
              class="show-box-in-box"
          >
            <view
                class="show-box-in-box-title"
            >{{$i18n.zhToGlobal("会员")}}
            </view>
            <view class="show-box-in-box-text">
              <text v-if="vipStatus==='none'">
                {{$i18n.zhToGlobal("会员可以享受更多的功能和服务")}}
              </text>
              <text v-if="vipStatus==='vip'">
                {{$i18n.zhToGlobal("VIP到期时间")}}{{": "}}{{$kt.date.format(userInfo.gmtVipExpire, "yyyy-MM-dd") }}
              </text>
              <text v-if="vipStatus==='svip'">
                {{$i18n.zhToGlobal("SVIP到期时间")}}{{": "}}{{$kt.date.format(userInfo.gmtSvipExpire, "yyyy-MM-dd") }}
              </text>
            </view>
          </view>
          <view class="show-box-btn show-box-btn-2">
            <view v-if="vipStatus==='none'">
              {{$i18n.zhToGlobal("立即开通")}}
            </view>
            <view v-else>
              {{$i18n.zhToGlobal("续费")}}
            </view>
          </view>

        </view>

        <view style="height: 30rpx"></view>

        <view>
          <view
              class="flex-box"
          >
            <view
                class="flex-box-item"
            >
              <view
                  @click="$refs.projectMeetGoldPopup.open()"
                  class="show-box show-gold-box">
                <image
                    class="show-box-image"
                    :src="$kt.file.byPath('/projectMeet/icon/gold.png')"
                ></image>
                <view
                    class="show-box-in-box"
                >
                  <view
                      class="show-box-in-box-title"
                  >{{$i18n.zhToGlobal("金币")}}
                  </view>
                  <view class="show-box-in-box-text">
                    <text style="font-size: 30rpx">
                      {{$i18n.zhToGlobal("我的金币")}}
                    </text>
                  </view>
                </view>

              </view>
            </view>

            <view
                class="flex-box-item"
                style="float: right"
            >
              <view
                  @click="$refs.projectMeetPointsPopup.open()"
                  class="show-box show-points-box">
                <image
                    class="show-box-image"
                    :src="$kt.file.byPath('/projectMeet/icon/points.svg')"
                ></image>
                <view
                    class="show-box-in-box"
                >
                  <view
                      class="show-box-in-box-title"
                  >{{$i18n.zhToGlobal("积分")}}
                  </view>
                  <view class="show-box-in-box-text">
                    <text style="font-size: 30rpx">
                      {{$i18n.zhToGlobal("我的积分")}}
                    </text>
                  </view>
                </view>

              </view>
            </view>

          </view>
        </view>

      </view>

      <view
      v-if="$kt.style.detectDeviceType() === 'pc'"
      >
        <view>
          <view
              class="flex-box flex-box-pc"
          >
            <view
            class="flex-box-item"
            >
              <view
                  @click="$refs.projectMeetVipPopup.open()"
                  class="show-box"
                  style="position: relative"
              >
                <image
                    class="show-box-image"
                    :src="$kt.file.byPath('/projectMeet/icon/guan.png')"
                ></image>
                <view
                    class="show-box-in-box"
                >
                  <view
                      class="show-box-in-box-title"
                  >{{$i18n.zhToGlobal("会员")}}
                  </view>
                  <view class="show-box-in-box-text">
                    <text v-if="vipStatus==='none'">
                      {{$i18n.zhToGlobal("会员可以享受更多的功能和服务")}}
                    </text>
                    <text v-if="vipStatus==='vip'">
                      {{$i18n.zhToGlobal("VIP到期时间")}}{{": "}}{{$kt.date.format(userInfo.gmtVipExpire, "yyyy-MM-dd") }}
                    </text>
                    <text v-if="vipStatus==='svip'">
                      {{$i18n.zhToGlobal("SVIP到期时间")}}{{": "}}{{$kt.date.format(userInfo.gmtSvipExpire, "yyyy-MM-dd") }}
                    </text>
                  </view>
                </view>
                <view class="show-box-btn"
                style="position: absolute;top:10rpx;right: 10rpx"
                >
                  <view v-if="vipStatus==='none'">
                    {{$i18n.zhToGlobal("立即开通")}}
                  </view>
                  <view v-else>
                    {{$i18n.zhToGlobal("续费")}}
                  </view>
                </view>

              </view>
            </view>
            <view
                class="flex-box-item"
            >
              <view
                  @click="$refs.projectMeetGoldPopup.open()"
                  class="show-box show-gold-box">
                <image
                    class="show-box-image"
                    :src="$kt.file.byPath('/projectMeet/icon/gold.png')"
                ></image>
                <view
                    class="show-box-in-box"
                >
                  <view
                      class="show-box-in-box-title"
                  >{{$i18n.zhToGlobal("金币")}}
                  </view>
                  <view class="show-box-in-box-text">
                    <text style="font-size: 30rpx">
                      {{$i18n.zhToGlobal("我的金币")}}
                    </text>
                  </view>
                </view>

              </view>
            </view>

            <view
                class="flex-box-item"
                style="float: right"
            >
              <view
                  @click="$refs.projectMeetPointsPopup.open()"
                  class="show-box show-points-box">
                <image
                    class="show-box-image"
                    :src="$kt.file.byPath('/projectMeet/icon/points.svg')"
                ></image>
                <view
                    class="show-box-in-box"
                >
                  <view
                      class="show-box-in-box-title"
                  >{{$i18n.zhToGlobal("积分")}}
                  </view>
                  <view class="show-box-in-box-text">
                    <text style="font-size: 30rpx">
                      {{$i18n.zhToGlobal("我的积分")}}
                    </text>
                  </view>
                </view>

              </view>
            </view>

          </view>
        </view>
      </view>



      <view
      v-if="false"
      >
        <view class="box-box">
          <view class="box-body">
            <view class="box-text">{{$i18n.zhToGlobal("常用功能")}}</view>
            <view class="box-list">

              <view class="list-item" @click="$refs.userInfoPopup.open(userInfo)">
                <image
                    :src="$kt.file.byPath('/projectMeet/icon/userInfo.svg')"
                    style="width: 40rpx; height: 40rpx"
                    alt=""
                >
                </image>
                <view class="list-item-text">{{
                    $i18n.zhToGlobal("我的资料")
                  }}</view>
              </view>


              <view class="list-item" @click="toAlbum()">
                <image
                  :src="$kt.file.byPath('projectMeet/icon/album.png')"
                  style="width: 40rpx; height: 40rpx"
                  alt=""
                >
                </image>
                <view class="list-item-text">{{
                  $i18n.zhToGlobal("我的相册")
                }}</view>
              </view>

              <view class="list-item" @click="toMatch()">
                <image
                    :src="$kt.file.byPath('projectMeet/icon/match.png')"
                    style="width: 40rpx; height: 40rpx"
                    alt=""
                >
                </image>
                <view class="list-item-text">{{
                    $i18n.zhToGlobal("一键搭讪")
                  }}</view>
              </view>

              <view
                  @click="toSetting()"
                  class="list-item">
                <image
                  :src="$kt.file.byPath('icon/setting.svg')"
                  style="width: 40rpx; height: 40rpx"
                  alt=""
                >
                </image>
                <view class="list-item-text">{{ $i18n.zhToGlobal('设置') }}</view>
              </view>
            </view>
          </view>


        </view>
      </view>

      <view style="height: 40rpx"></view>
      <view class="box box-2">
        <user-info-card-edit
            @click="$refs.userInfoSelfIntroduction.open(userInfo)"
        style="position: absolute;top:20rpx;right: 100rpx;"
        ></user-info-card-edit>
        <view class="box-title">{{$i18n.zhToGlobal("个人介绍")}}</view>
        <view style="height: 30rpx"></view>
        <view
        v-if="userInfo.selfIntroduction"
        >
          {{userInfo.selfIntroduction}}
        </view>
        <view
        v-else
        style="color: #999999"
        >
          {{$i18n.zhToGlobal("未填写个人介绍")}}
        </view>

      </view>

      <view style="height: 20rpx"></view>
      <view class="box box-2">
        <user-info-card-edit
            @click="$refs.interestsEditPopup.open(userInfo)"
            style="position: absolute;top:20rpx;right: 100rpx;"
        ></user-info-card-edit>
        <view class="box-title">{{$i18n.zhToGlobal("兴趣爱好")}}</view>
        <view style="height: 30rpx"></view>
        <view
            v-if="userInfo.interestsIds&&userInfo.interestsIds.length > 0"
        >
          <view>
            <view
                v-for="(item, index) in userInfo.interestsIds"
                class="tag">{{interestsMap[item]}}</view>
          </view>
        </view>
        <view
            v-else
            style="color: #999999"
        >
          {{$i18n.zhToGlobal("未设置兴趣爱好")}}
        </view>
      </view>
      <view style="height: 20rpx"></view>

      <view class="box box-2"
      >
        <user-info-card-edit
            @click="$refs.expectedRelationshipPopup.open(userInfo)"
            style="position: absolute;top:20rpx;right: 100rpx;"
        ></user-info-card-edit>
        <view class="box-title">{{$i18n.zhToGlobal("期待的关系")}}</view>
        <view style="height: 30rpx"></view>
        <view
            v-if="userInfo.expectedRelationshipIds&&userInfo.expectedRelationshipIds.length > 0"
        >
          <view>
            <view
                v-for="(item, index) in userInfo.expectedRelationshipIds"
                class="tag">{{expectedRelationshipMap[item]}}</view>
          </view>
        </view>
        <view
            v-else
            style="color: #999999"
        >
          {{$i18n.zhToGlobal("未设置兴趣爱好")}}
        </view>
      </view>

      <view style="height: 180rpx"></view>
    </view>

    <user-info-popup ref="userInfoPopup"></user-info-popup>

    <kt-no-login ref="noLogin"></kt-no-login>

    <kt-setting-popup
        :noHasOldPassword="true"
        :has-data-manage="false"
        :has-email="false"
        :has-phone="false"
        :has-app="false"
        :has-about="false"
        :has-color-mode="false"
        :privacy-agreement-url="'https://www.kantboot.com'"
        :user-agreement-url="'https://www.baidu.com'"
        ref="settingPopup"></kt-setting-popup>

    <user-info-self-introduction
    ref="userInfoSelfIntroduction"
    @confirm="userInfoSelfIntroductionConfirm"
    ></user-info-self-introduction>

    <project-meet-user-match-popup
    ref="projectMeetUserMatchPopup"
    ></project-meet-user-match-popup>


    <project-meet-album-popup
      ref="projectMeetAlbumPopup"
    ></project-meet-album-popup>
    <project-meet-user-info-popup ref="usPopup"></project-meet-user-info-popup>

    <project-meet-vip-popup
        :user-account-id="userInfo.id"
        ref="projectMeetVipPopup"
    ></project-meet-vip-popup>


    <project-meet-gold-popup
    :user-account-id="userInfo.id"
    ref="projectMeetGoldPopup"
    ></project-meet-gold-popup>

    <project-meet-points-popup
      :user-account-id="userInfo.id"
      ref="projectMeetPointsPopup">
    </project-meet-points-popup>

    <interests-edit-popup
    ref="interestsEditPopup"
    @confirm="initSelf"
    ></interests-edit-popup>

    <expected-relationship-popup
    ref="expectedRelationshipPopup"
    @confirm="initSelf"
    ></expected-relationship-popup>


  </view>
</template>

<script>
import userInfoCardEdit from "../components/user-info-card-edit.vue";
import userInfoCardSetting from "../components/user-info-card-setting.vue";
import UserInfoPopup from "../components/user-info-popup.vue";
import projectMeetUserInfoPopup from "../../../../uni_modules/project-meet/components/project-meet-user-info-popup/project-meet-user-info-popup.vue";
import userInfoSelfIntroduction from "../components/user-info-self-introduction.vue";
import interestsEditPopup from "../components/interests-edit-popup.vue";
import expectedRelationshipPopup from "../components/expected-relationship-popup.vue";

export default {
  components: { UserInfoPopup, userInfoCardEdit, projectMeetUserInfoPopup,
    userInfoCardSetting,
    userInfoSelfIntroduction,
    interestsEditPopup,
    expectedRelationshipPopup
  },
  data() {
    return {
      userInfo: {
        nickname: "",
        fileIdOfAvatar: "",
        genderCode: "male",
        email: "<EMAIL>",
      },
      isLogin: this.$kt.userAccount.getIsLogin(),
      isLogoutModal: false,
      // 国家获取地区
      stateAreaList: [],
      stateAreaMap: {},
      clazz: {
        container: this.$kt.style.toggleClass("container"),
        bg: this.$kt.style.toggleClass("bg"),
      },
      vipStatus:{},
      interestsList:[],
      interestsMap: {},
      expectedRelationshipList: [],
      expectedRelationshipMap: {},
    };
  },
  created() {
    setTimeout(()=>{
      this.$kt.userAccount.requestSelf();
    },10);


    // 监听登录成功事件
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.userInfo = this.$kt.userAccount.getSelf();
    });
    this.$kt.event.on("userAccount:getSelf", (res) => {
        this.userInfo.fileIdOfAvatar = res.fileIdOfAvatar;
        this.userInfo.introduction = res.introduction || "";
        this.userInfo.gmtBirthday = res.gmtBirthday || "";
        try{
          this.$forceUpdate();
        }catch (e) {

        }
    });
    this.$kt.event.on("language:change", () => {
      console.log("language:change====");
      this.getAllStateArea();
    });
    this.getAllStateArea();
    this.$kt.event.on("projectMeet:vipPaySuccess",async ()=>{
      this.getVipStatusSelf();
      await this.$kt.userAccount.requestSelf();
      this.userInfo = this.$kt.userAccount.getSelf();
      try{
        this.$forceUpdate();
      }catch (e) {

      }
    });
  },
  mounted() {
    this.isLogin = true;
    this.userInfo = this.$kt.userAccount.getSelf();
    this.getVipStatusSelf();
    this.getInterests();
    this.getExpectedRelationship();
  },
  methods: {

    async initSelf(){
      await this.$kt.userAccount.requestSelf();
      this.userInfo = this.$kt.userAccount.getSelf();
   },
    // /project-meet-web/expectedRelationship/getAll
    getExpectedRelationship() {
      this.$request
        .post("/project-meet-web/expectedRelationship/getAll", { data: {} })
        .then((res) => {
          this.expectedRelationshipList = res.data;
          this.expectedRelationshipMap = {};
          for (let i = 0; i < this.expectedRelationshipList.length; i++) {
            const item = this.expectedRelationshipList[i];
            this.expectedRelationshipMap[item.id] = item.name;
          }
        });
    },
    // //project-meet-web/interests/getAll
    getInterests() {
      this.$request
        .post("/project-meet-web/interests/getAll", { data: {} })
        .then((res) => {
          this.interestsList = res.data;
          this.interestsMap = {};
          for (let i = 0; i < this.interestsList.length; i++) {
            const item = this.interestsList[i];
            this.interestsMap[item.id] = item.name;
          }
        });
    },

    getVipStatusSelf() {
      this.$request
        .post("/project-meet-web/userAccount/getVipStatusSelf", { data: {} })
        .then((res) => {
          this.vipStatus = res.data;
        });
    },

    toMatch(){
      this.$refs.projectMeetUserMatchPopup.open();
    },
    toSetting() {
      if(!this.isY()){
        this.$kt.router.navTo("/pages/project-meet-pages/setting/setting");
        return;
      }
      this.$refs.settingPopup.open();
    },
    // 前往相册
    toAlbum() {
      if (!this.isY()) {
        this.$kt.router.navTo("/pages/project-meet-pages/album/album");
        return;
      }
      this.$refs.projectMeetAlbumPopup.open();
    },
    isY() {
      // 转换为大写
      let deviceType = this.$kt.style.detectDeviceType().toUpperCase();
      return deviceType === "PC";
    },
    routerEvent() {
       if (this.isY()) {
        this.$refs.usPopup.open()
        // this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info");
      } else {
        this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info");
      }
    },
    toLogout() {
      this.isLogin = false;
      this.$kt.userAccount.setIsLogin(false);
      this.$kt.storage.remove("token");

      uni.reLaunch({
        url: this.$kt.router.config.intoPath,
      });
    },
    // /tool-state-area-web/stateArea/getAll
    async getAllStateArea() {
      this.stateAreaMap = {};
      await this.$request
        .post("/tool-state-area-web/stateArea/getAll", { data: {} })
        .then((res) => {
          this.stateAreaList = res.data;
          for (let i = 0; i < this.stateAreaList.length; i++) {
            const item = this.stateAreaList[i];
            this.stateAreaMap[item.code] = item.name;
          }
        });
      await this.$request
        .post("/system-language-web/languageI18n/getList", {
          data: {
            topKey: "ToolStateArea",
            bottomKey: "name",
            languageCode: this.$i18n.getLanguageCode(),
          },
        })
        .then((res) => {
          for (let i = 0; i < res.data.length; i++) {
            const item = res.data[i];
            this.stateAreaMap[item.centerKey] = item.content;
          }
          try {
            // vue更新渲染
            // this.$forceUpdate();
          } catch (e) {
            // console.log(e);
          }
        });
    },
    async userInfoSelfIntroductionConfirm(){
      await this.$kt.userAccount.requestSelf();
      let userInfo = this.$kt.userAccount.getSelf();
      this.userInfo.selfIntroduction = userInfo.selfIntroduction;
    }
  },
};
</script>

<style lang="scss" scoped>
.show-box {
  width: 100%;
  height: 180rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  padding: 20rpx;
  border: 5rpx solid #FFFFFF;
  // 背景渐变，从左到右
  //#e4eaff #adc0ff
  background: linear-gradient(
          to right,
          rgba(228, 234, 255, 0.8) 20%,
          rgba(173, 192, 255, 0.7) 80%,
          rgba(173, 192, 255, 0.3) 100%
  );

  .show-box-image{
    width: 100rpx;
    height: 100rpx;
    border-radius: 20rpx;
    object-fit: cover;
  }

  .show-box-in-box {
    position: absolute;
    display: inline-block;
    vertical-align: top;
    margin-top: 10rpx;
    margin-left: 10rpx;
    text-shadow: 0 5rpx 10rpx rgba(0, 0, 0, 0.2);
    .show-box-in-box-title {
      color: #FFFFFF;
      font-size: 32rpx;
      font-weight: bold;
    }
    .show-box-in-box-text {
      font-size: 28rpx;
      color: #FFFFFF;
      box-sizing: border-box;
      margin-top: 10rpx;
    }

  }
  .show-box-btn{
    position: absolute;
    display: inline-block;
    font-size: 24rpx;
    color: rgba(173, 192, 255, 1);
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
    text-align: center;
    margin-top: 10rpx;
    right: 50rpx;
    cursor: pointer;
    background-color: rgba(255,255,255,.5);
  }
}

.show-gold-box{
  background: linear-gradient(
          to right,
          rgba(255, 228, 173, 0.8) 20%,
          rgba(255, 192, 173, 0.7) 80%,
          rgba(255, 192, 173, 0.3) 100%
  );

}

.show-points-box{
  //background: linear-gradient(
  //        to left,
  //        rgba(255, 228, 173, 0.8) 20%,
  //        rgba(255, 192, 173, 0.7) 80%,
  //        rgba(255, 192, 173, 0.3) 100%
  //);
  // 科技蓝
  background: linear-gradient(
          to right,
          rgba(173, 228, 255, 0.8) 20%,
          rgba(173, 192, 255, 0.7) 80%,
          rgba(173, 228, 255, 0.8) 100%
  );
}

.flex-box {
  .flex-box-item{
    display: inline-block;
    width: calc(50% - 20rpx);
  }
}

.box-body {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
}
.box-text {
  font-size: 34rpx;
  font-weight: bold;
}
.box-list {
  display: flex;
  flex-wrap: wrap;
}
.list-item {
  width: 25%;
  display: inline-block;
  text-align: center;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 40rpx;
}
.list-item-text {
  font-size: 26rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
.info-btn {
  width: 25px;
  height: 25px;
}

.info-btn:active {
  transform: scale(0.95);
}

.back {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: #f0f0f0;
  top: 0;
  left: 0;
  z-index: -1;
}

.box-box {
  box-sizing: border-box;
  margin-top: 40rpx;
}

.box {
  position: relative;
  vertical-align: top;

  width: 100%;
  display: inline-block;
  text-align: left;
  border-radius: 20rpx;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.8) 20%,
    rgba(255, 255, 255, 0.7) 80%,
    rgba(255, 255, 255, 0.3) 100%
  );
}

.title {
  font-size: 32rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-weight: bold;
}

.line {
  width: 100%;
  height: 1rpx;
  background-color: #f0f0f0;
}

.menu-box {
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx 20rpx 10rpx 20rpx;

  .menu {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    border-bottom: 1rpx solid #f0f0f0;
    background-color: #ffffff;
    margin-bottom: 20rpx;

    .menu-icon-box {
      width: 50rpx;
      height: 40rpx;
    }

    .menu-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
}

.menu:active {
  opacity: 0.6;
}

.ip-tag {
  position: absolute;
  padding: 10rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #999;
  width: 300rpx;
  top: 20rpx;
  right: 20rpx;
  z-index: 9999;
  text-align: right;
}

.header-box {
  top: 0;
  left: 0;
  width: 100%;
}

.user-info-box {
  position: relative;
  //background-color: #fff;
  padding: 50rpx 30rpx 50rpx 30rpx;
  border-radius: 20rpx;
  // 渐变，从上到下
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.8) 20%,
    rgba(255, 255, 255, 0.7) 80%,
    rgba(255, 255, 255, 0.3) 100%
  );

  box-sizing: border-box;
}
.container-mode-color-scheme-light {
  .back {
    background-color: #f0f0f0;
  }
}
.container-mode-color-scheme-dark {
  .back {
    background-color: #191919;
  }

  .user-info-box {
    background-color: #191919;

    .info-btn {
      // 颜色反转
      filter: invert(1);
    }
  }

  .menu-box {
    background-color: #1f1f1f;

    .menu {
      border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);

      background-color: rgba(0, 0, 0, 0);
      color: #aaaaaa;
      .menu-icon-box {
        filter: invert(1);
      }
    }
  }
}

.container-mode-device-pc {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;

  .show-box{
    cursor: pointer;
  }

  .user-info-box {
    .user-info-card {
      left: -450rpx;
    }
  }
  .header-box {
    width: 100%;
    left: 240px;
  }
  .list-item {
    cursor: pointer;
  }
  .list-item:hover {
    transform: scale(1.05);
  }
  .list-item:active {
    transform: scale(0.95);
  }
}

.bg {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  //background-color: #f9f9f9;
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);

  .bg-image {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    right: 0;
    z-index: -2;
  }

  .bg-image-2 {
    position: fixed;
    width: 100vw;
    height: 100vh;
    bottom: 25vh;
    right: 0;
    // 上下翻转
    transform: scaleY(-1);
    z-index: -2;
  }

  .bg-image-3 {
    position: fixed;
    width: 100vw;
    height: 100vh;
    bottom: 0;
    right: 0;
    z-index: -2;
    // 上下翻转
    transform: scaleY(-1);
  }

  .bg-bg {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    right: 0;
    z-index: -1;
    // 渐变，从上到下
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 100rpx,
      rgba(255, 255, 255, 1) 100%
    );
  }


}

.bg-mode-color-scheme-light {
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}

.bg-mode-color-scheme-dark {
  background: #191919;
}

.big-box {
  padding: 30rpx;
}

.box-2{
  position: relative;
  padding: 30rpx;
  font-size: 28rpx;
  color: #333;
}

.box-title{
  font-weight: bold;
}

.flex-box-pc{
  .flex-box-item{
    width: calc(33% - 20rpx);
    margin-right: 20rpx;
  }
}

.tag{
  display: inline-block;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #333;
}
.user-info-box{
  position: relative;
}
.user-info-box-bottom-text{
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 5rpx 4rpx rgba(0, 0, 0, 0.2);
}
.user-info-box-svip{
  border: 5rpx solid #ffcc00;

  .user-info-box-bottom-text{
    color: #ffcc00;
  }
}

.user-info-box-vip{
  border: 5rpx solid #5A7EF6;
  .user-info-box-bottom-text{
    color: #5A7EF6;
  }
}

.container-mode-device-pc {
  .user-info-box-svip{
    border: 5px solid #ffcc00;
    box-shadow: 0 0 10px rgba(255, 204, 0, 0.5);
    // 动画
    animation: box-shadow-svip 2s infinite alternate;

    .user-info-box-bottom-text{
      color: #ffcc00;
    }
  }

  .user-info-box-vip{
    border: 5px solid #5A7EF6;
    //border:none;
    box-shadow: 0 0 10px rgba(90, 126, 246, 0.5);
    // 动画
    animation: box-shadow-vip 2s infinite alternate;
    .user-info-box-bottom-text{
      color: #5A7EF6;
    }
  }

}
//
//@keyframes box-shadow-svip {
//  0% {
//    box-shadow: 0 0 1px rgba(255, 204, 0, 0.5);
//  }
//  50% {
//    box-shadow: 0 0 10px rgba(255, 204, 0, 1);
//  }
//  100% {
//    box-shadow: 0 0 1px rgba(255, 204, 0, 0.5);
//  }
//}
//
//@keyframes box-shadow-vip {
//  0% {
//    box-shadow: 0 0 1px rgba(90, 126, 246, 0.5);
//  }
//  50% {
//    box-shadow: 0 0 10px rgba(90, 126, 246, 1);
//  }
//  100% {
//    box-shadow: 0 0 1px rgba(90, 126, 246, 0.5);
//  }
//}
//

</style>