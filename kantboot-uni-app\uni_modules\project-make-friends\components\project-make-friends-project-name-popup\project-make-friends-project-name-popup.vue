<template>
  <view>
    <kt-popup :overlayClose="false" ref="ktPopup" @close="close" :zIndex="999999999">
      <view class="box">
        <view class="close-btn" @click="closePop">×</view>
        <view class="title">{{ $i18n.zhToGlobal("输入项目名称") }}</view>

        <!-- 项目名称输入区域 -->
        <view class="content-area">
          <view class="input-section">
            <view class="section-title">{{ $i18n.zhToGlobal("项目名称") }}</view>
            <view class="input-container">
              <input 
                class="project-name-input" 
                v-model="projectName" 
                :placeholder="$i18n.zhToGlobal('请输入项目名称')"
                :maxlength="50"
                @input="onInput"
              />
            </view>
            <view class="tips">
              {{ $i18n.zhToGlobal('项目名称将用于标识此共同邀约组') }}
            </view>
          </view>
        </view>

        <!-- 底部操作按钮 -->
        <view class="footer">
          <view class="buttons">
            <button class="cancel-btn" @click="closePop" :disabled="processing">
              {{ $i18n.zhToGlobal('取消') }}
            </button>
            <button class="confirm-btn" @click="handleConfirm" :disabled="processing || !projectName.trim()">
              {{ processing ? $i18n.zhToGlobal('处理中...') : $i18n.zhToGlobal('确认') }}
            </button>
          </view>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      projectName: '',
      processing: false
    };
  },
  methods: {
    open() {
      this.projectName = '';
      this.processing = false;
      this.$refs.ktPopup.open();
    },

    close() {
      this.projectName = '';
      this.processing = false;
    },

    closePop() {
      this.$refs.ktPopup.close();
    },

    onInput(e) {
      this.projectName = e.detail.value;
    },

    handleConfirm() {
      if (this.processing) return;

      const projectName = this.projectName.trim();

      if (!projectName) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('请输入项目名称'),
          icon: 'none'
        });
        return;
      }

      if (projectName.length > 50) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('项目名称不能超过50个字符'),
          icon: 'none'
        });
        return;
      }

      this.processing = true;

      // 触发确认事件
      this.$emit('confirm', {
        projectName: projectName
      });

      // 关闭弹窗
      setTimeout(() => {
        this.processing = false;
        this.closePop();
      }, 100);
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 50vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
    padding-top: 20rpx;
  }

  .content-area {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 20rpx;

    .input-section {
      margin-bottom: 30rpx;

      .section-title {
        font-size: 28rpx;
        font-weight: bold;
        margin-bottom: 15rpx;
        color: #333;
      }

      .input-container {
        background-color: #f8f8f8;
        border-radius: 12rpx;
        padding: 0 20rpx;

        .project-name-input {
          width: 100%;
          height: 80rpx;
          font-size: 32rpx;
          background: transparent;
          border: none;
        }
      }

      .tips {
        margin-top: 10rpx;
        font-size: 24rpx;
        color: #999;
        text-align: center;
      }
    }
  }

  .footer {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;

    .buttons {
      display: flex;
      gap: 20rpx;

      button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 30rpx;
        border-radius: 40rpx;
        border: none;

        &.cancel-btn {
          background-color: #f5f5f5;
          color: #666;

          &[disabled] {
            background-color: #e0e0e0;
            color: #999;
          }
        }

        &.confirm-btn {
          background-color: #000000;
          color: #fff;

          &[disabled] {
            background-color: #cccccc;
            color: #fff;
          }
        }
      }
    }
  }
}

.close-btn {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
  z-index: 10;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
</style>
