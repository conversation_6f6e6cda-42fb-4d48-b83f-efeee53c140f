<view class="data-v-2724a4ce"><block wx:if="{{$root.g0}}"><view class="no-data data-v-2724a4ce"><view class="in-no-data data-v-2724a4ce">{{''+$root.g1+''}}</view></view></block><block wx:else><view class="box data-v-2724a4ce"><view class="title data-v-2724a4ce">{{"·"+$root.g2+"·"}}</view><block wx:for="{{commentList}}" wx:for-item="item" wx:for-index="index"><view class="in-box data-v-2724a4ce"><kt-community-post-card vue-id="{{'bd6ef2e6-1-'+index}}" has-bottom-operation="{{false}}" has-dot="{{false}}" post="{{commentListNoId[index]}}" class="data-v-2724a4ce" bind:__l="__l"></kt-community-post-card><view class="line data-v-2724a4ce"></view></view></block></view></block></view>