@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.icon-img.data-v-452deadc {
  width: 40rpx;
  height: 40rpx;
}
.back.data-v-452deadc {
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  background-color: #f5f5f5;
}
@media (prefers-color-scheme: dark) {
.back.data-v-452deadc {
    background-color: #000000;
}
}
.container.data-v-452deadc {
  padding: 30rpx;
  box-sizing: border-box;
}
.box.data-v-452deadc {
  position: relative;
  background-color: #ffffff;
  border-radius: 30rpx;
  padding: 30rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}
.box .box-title.data-v-452deadc {
  font-size: 30rpx;
  font-weight: bold;
}
.box-data.data-v-452deadc {
  text-align: center;
  margin-top: 10rpx;
  padding: 0 30rpx;
}
.box-data .box-data-item.data-v-452deadc {
  display: inline-block;
  width: 33%;
  vertical-align: top;
}
.box-data .box-data-item .box-data-item-num.data-v-452deadc {
  font-size: 30rpx;
  font-weight: bold;
}
.box-data .box-data-item .box-data-item-text.data-v-452deadc {
  font-size: 24rpx;
  color: #999999;
}
.box-data-2.data-v-452deadc {
  text-align: left;
  margin-top: 10rpx;
  padding: 10rpx 0 10rpx 0rpx;
}
.box-data-2 .tag-v.data-v-452deadc {
  background-color: #fff4f0;
  display: inline-block;
  vertical-align: top;
  margin-right: 20rpx;
  height: 50rpx;
  line-height: 50rpx;
  margin-bottom: 20rpx;
  padding: 0rpx 20rpx 0rpx 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #f5ac36;
}
.tag-btn.data-v-452deadc:active {
  opacity: 0.7;
}
.box-btn-box.data-v-452deadc {
  text-align: center;
  margin-top: 10rpx;
}
.box-btn-box .box-btn.data-v-452deadc {
  display: inline-block;
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: center;
  margin-right: 10rpx;
}
.box-btn-box .box-fllow-btn.data-v-452deadc {
  background-color: #333333;
  color: #ffffff;
}
.box-btn.data-v-452deadc:active {
  opacity: 0.7;
}
