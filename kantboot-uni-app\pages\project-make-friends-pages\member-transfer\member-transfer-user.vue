<template>
  <view>
    <view class="header" id="headerInMemberTransfer">
      <kt-nav-bar :title="$i18n.zhToGlobal('会员转移')" :showBack="true"></kt-nav-bar>
    </view>
    <view>
      <view class="box">
        <!-- 搜索框 -->
        <view class="box-search">
          <view class="search-box">
            <u-input :placeholder="$i18n.zhToGlobal('输入ID或手机号搜索')" @confirm="searchUsers" v-model="searchText">
              <template slot="suffix">
                <button @tap="searchUsers" class="search-btn" :disabled="!searchText.trim()">搜索</button>
              </template>
            </u-input>
          </view>
        </view>
        
        <!-- 初始提示信息 -->
        <view class="empty-tip" v-if="userList.length === 0 && !isLoading && !hasSearched">
          {{ $kt.i18n.zhToGlobal('请输入ID或手机号搜索用户，选择将会员转移给他/她') }}
        </view>
        
        <!-- 用户列表 -->
        <view class="box-item" v-for="item in userList" :key="item.id" @click="selectUser(item)">
          <view class="box-item-card">
            <kt-user-info-card :user-info="item"></kt-user-info-card>
            <view class="box-item-info">
              <view class="box-item-id">ID: {{ item.id }}</view>
              <view class="box-item-phone" v-if="item.phone">{{ item.phoneAreaCode }} {{ item.phone }}</view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <u-loading-icon v-if="isLoading" mode="circle" size="50rpx"></u-loading-icon>
        <view v-if="isBottom && userList.length > 0" style="text-align: center; color: #999999; font-size: 24rpx;">
          {{ $kt.i18n.zhToGlobal('没有更多了') }}
        </view>
        <!-- 搜索无结果提示 -->
        <view v-if="userList.length === 0 && hasSearched && !isLoading" class="no-result">
          {{ $kt.i18n.zhToGlobal('未找到相关用户') }}
        </view>
        <view style="height: 50rpx;"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchText: '',
      userList: [],
      pageNum: 1,
      pageSize: 10,
      isLoading: false,
      isBottom: false,
      hasSearched: false // 标记是否已进行过搜索
    }
  },
  mounted() {
    // 初始化时不加载数据
  },
  methods: {
    input(e) {
      this.searchText = e.detail.value;
      this.searchUsers();
    },

    // 初始加载用户列表
    getInitList() {
      this.isLoading = true;
      this.pageNum = 1;
      this.isBottom = false;
      this.hasSearched = true; // 标记已进行搜索

      this.$request.post("/project-make-friends-web/userTransferConsent/getList", {
        data: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          searchText: this.searchText,
          excludeSelfId: true
        }
      }).then(res => {
        if (res.state === 2000) {
          this.userList = res.data || [];
          if (this.userList.length < this.pageSize) {
            this.isBottom = true;
          }
        } else {
          uni.showToast({
            title: res.msg || '获取用户列表失败',
            icon: 'none'
          });
        }
        this.isLoading = false;
      }).catch(err => {
        console.error(err);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
        this.isLoading = false;
      });
    },

    // 搜索用户
    searchUsers() {
      if (!this.searchText.trim()) {
        uni.showToast({
          title: this.$kt.i18n.zhToGlobal('请输入搜索内容'),
          icon: 'none'
        });
        return;
      }
      this.getInitList();
    },

    // 选择用户
    selectUser(user) {
      uni.navigateTo({
        url: `/pages/project-make-friends-pages/member-transfer/member-transfer-transfer?userId=${user.id}`,
        events: {
          // 为转移页面提供选中的会员数据
          selectMember: function (data) {
          }
        },
        success: function (res) {
          // 传递选中的会员数据给转移页面
          res.eventChannel.emit('selectMember', { member: user });
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;

  .box-item {
    margin-bottom: 30rpx;

    .box-item-card {
      position: relative;
      padding: 20rpx;
      border-radius: 12rpx;
      background-color: #ffffff;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      border: 1rpx solid #eaeaea;

      .box-item-info {
        font-size: 28rpx;
        color: #999999;
        margin-top: 16rpx;

        .box-item-id,
        .box-item-phone {
          margin-bottom: 8rpx;
        }
      }

      .box-item-edit {
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        padding: 10rpx;
      }
    }
  }

  .box-search {
    width: 100%;
    background-color: #F0F0F0;
    box-sizing: border-box;
    border-radius: 10rpx;
    margin-bottom: 30rpx;

    .box-search-input {
      display: flex;
      align-items: center;
      background-color: #FFFFFF;
      border-radius: 8rpx;
      input {
        flex: 1;
        height: 70rpx;
        font-size: 28rpx;
      }
    }
  }
}

.box-item:active {
  opacity: .8;
}

.box-item-card:active {
  background-color: #f9f9f9;
}

/* 搜索按钮样式 */
.search-btn {
  background-color: #000000;
  height: 30px;
  line-height: 30px;
  color: #ffffff;
  
  &[disabled] {
    background-color: #cccccc;
    color: #ffffff;
    opacity: 0.6;
  }
}

/* 提示语样式 */
.empty-tip {
  text-align: center;
  color: #999999;
  font-size: 28rpx;
  margin: 100rpx 0;
}

/* 无结果提示样式 */
.no-result {
  text-align: center;
  color: #999999;
  font-size: 28rpx;
  margin: 100rpx 0;
}
</style>