<template>
  <view
    class="slider-range"
    :style="{ paddingLeft:( blockSize+4 )/ 2 + 'px', paddingRight: (blockSize+4) / 2 + 'px' }"
  >
    <view class="slider-range-inner" :style="{ height: height + 'px' }">
      <view class="slider-bar" :style="{ height: barHeight + 'px' }">
        <view class="slider-bar-bg" :style="{ backgroundColor: backgroundColor }"></view>
        <view
          class="slider-bar-inner"
          :style="{
            width: ((values[1] - values[0]) / (max - min)) * 100 + '%',
            left: lowerHandlePosition + '%',
            backgroundColor: activeColor,
          }"
        ></view>
      </view>
      <view
        class="slider-handle-block"
        :style="{
          backgroundColor: blockColor,
          width: blockSize + 'px',
          height: blockSize + 'px',
          left: lowerHandlePosition + '%',
        }"
        @touchstart="_onTouchStart"
        @touchmove="_onBlockTouchMove"
        @touchend="_onBlockTouchEnd"
        data-tag="lowerBlock"
      ></view>
      <view
        class="slider-handle-block"
        :style="{
          backgroundColor: blockColor,
          width: blockSize + 'px',
          height: blockSize + 'px',
          left: higherHandlePosition + '%',
        }"
        @touchstart="_onTouchStart"
        @touchmove="_onBlockTouchMove"
        @touchend="_onBlockTouchEnd"
        data-tag="higherBlock"
      ></view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    value: {
      type: Array,
      default: function() {
        return [0, 100]
      },
    },
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: 100,
    },
    step: {
      type: Number,
      default: 1,
    },
    height: {
      type: Number,
      default: 50,
    },
    barHeight: {
      type: Number,
      default: 5,
    },
    backgroundColor: {
      type: String,
      default: '#e9e9e9',
    },
    activeColor: {
      type: String,
      default: '#1aad19',
    },
    blockSize: {
      type: Number,
      default: 24,
    },
    blockColor: {
      type: String,
      default: '#fff',
    },
  },
  data() {
    return {
      values: [this.min, this.max],
      startDragPos: 0,
      startVal: 0,
      isDragging: false,
    }
  },
  computed: {
    lowerHandlePosition() {
      return ((this.values[0] - this.min) / (this.max - this.min)) * 100
    },
    higherHandlePosition() {
      return ((this.values[1] - this.min) / (this.max - this.min)) * 100
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (Array.isArray(newVal) && (newVal[0] !== this.values[0] || newVal[1] !== this.values[1])) {
          this._updateValue(newVal)
        }
      },
    },
  },
  methods: {
    _updateValue(newVal, emitChange = true) {
      let newValues = [
        typeof newVal[0] === 'number' ? Math.max(this.min, Math.min(newVal[0], this.max)) : this.values[0],
        typeof newVal[1] === 'number' ? Math.max(this.min, Math.min(newVal[1], this.max)) : this.values[1],
      ];
      // 步长处理
      newValues[0] = Math.round((newValues[0] - this.min) / this.step) * this.step + this.min;
      newValues[1] = Math.round((newValues[1] - this.min) / this.step) * this.step + this.min;
      // 保证左滑块不超过右滑块，右滑块不小于左滑块
      if (newValues[0] >= newValues[1]) {
        if (this.dragTag === 'lowerBlock') {
          newValues[0] = Math.min(newValues[1] - this.step, this.max - this.step);
        } else {
          newValues[1] = Math.max(newValues[0] + this.step, this.min + this.step);
        }
      }
      // 边界保护
      newValues[0] = Math.max(this.min, Math.min(newValues[0], this.max - this.step));
      newValues[1] = Math.max(this.min + this.step, Math.min(newValues[1], this.max));
      this.values = newValues;
      this.$emit('input', this.values);
      if (emitChange) this.$emit('change', this.values);
    },
    _onTouchStart(event) {
      this.isDragging = true;
      let tag = event.target.dataset.tag;
      let e = event.changedTouches ? event.changedTouches[0] : event;
      this.startDragPos = e.pageX;
      this.startVal = tag === 'lowerBlock' ? this.values[0] : this.values[1];
      this.dragTag = tag;
    },
    _onBlockTouchMove(e) {
      if (!this.isDragging) return;
      let view = uni.createSelectorQuery().in(this).select('.slider-range-inner');
      view.boundingClientRect(data => {
        let sliderWidth = data.width;
        const tag = this.dragTag;
        let evt = e.changedTouches ? e.changedTouches[0] : e;
        let diff = ((evt.pageX - this.startDragPos) / sliderWidth) * (this.max - this.min);
        let nextVal = Math.round(this.startVal + diff);
        if (tag === 'lowerBlock') {
          this._updateValue([nextVal, this.values[1]], false);
        } else {
          this._updateValue([this.values[0], nextVal], false);
        }
      }).exec();
    },
    _onBlockTouchEnd(e) {
      this.isDragging = false;
      let view = uni.createSelectorQuery().in(this).select('.slider-range-inner');
      view.boundingClientRect(data => {
        let sliderWidth = data.width;
        const tag = this.dragTag;
        let evt = e.changedTouches ? e.changedTouches[0] : e;
        let diff = ((evt.pageX - this.startDragPos) / sliderWidth) * (this.max - this.min);
        let nextVal = Math.round(this.startVal + diff);
        if (tag === 'lowerBlock') {
          this._updateValue([nextVal, this.values[1]], true);
        } else {
          this._updateValue([this.values[0], nextVal], true);
        }
      }).exec();
    },
  },
}
</script>
<style scoped>
.slider-range {
  position: relative;
  padding-top: 40rpx;
}
.slider-range-inner {
  position: relative;
  width: 100%;
}
.slider-bar {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
}
.slider-bar-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 10000px;
  z-index: 10;
}
.slider-bar-inner {
  position: absolute;
  height: 100%;
  border-radius: 10000px;
  z-index: 11;
}
.slider-handle-block {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  box-shadow: 0 0 3px 2px rgba(227, 229, 241, 0.5);
  z-index: 12;
}
</style> 