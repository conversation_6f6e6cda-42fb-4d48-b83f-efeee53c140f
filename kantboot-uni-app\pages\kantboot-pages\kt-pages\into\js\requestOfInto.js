import $kt from "@/uni_modules/kantboot";
import into from "@/uni_modules/kantboot/libs/into"

let request = () => {
	return new Promise(async (resolve, reject) => {
		let maxLoadingNum = 100;
		setTimeout(() => {
			resolve()
		}, 10000);

		into.checkLoad().then(() => {
			resolve();
		});

		// #ifndef H5
		uni.getStorageInfo({
			success: (res) => {
				let keys = res.keys;
				for (let i = 0; i < keys.length; i++) {
					if (keys[i].indexOf("requestNumberBy") === 0) {
						localStorage.removeItem(keys[i]);
					}
				}
			}
		});
		// #endif

		// #ifdef H5
		// 清除所有requestNumberBy开头的缓存
		let keys = Object.keys(localStorage);
		for (let i = 0; i < keys.length; i++) {
		    if (keys[i].indexOf("requestNumberBy") === 0) {
		        localStorage.removeItem(keys[i]);
		    }
		}
		// #endif

		let loadingNum = $kt.storage.get("loadingNum");
		if (loadingNum === null) {
			loadingNum = 1;
		} else {
			loadingNum = parseInt(loadingNum);
		}
		loadingNum = loadingNum + 1;
		if (loadingNum > maxLoadingNum) {
			loadingNum = 1;
		}

		$kt.storage.set("loadingNum", loadingNum);

		await $kt.i18n.loadingSupportLanguage();
		await $kt.i18n.loadingLocalized();
		await $kt.i18n.loadLanguagePackage("appFront", "en");
		await $kt.i18n.loadLanguagePackage("appFront", "zh_CN");
		await $kt.i18n.loadLanguagePackage("appFront");
		await $kt.userAccount.requestSelf();
		into.toSet();
		resolve();
	});

}

export default {
	request
}