<view class="data-v-10bdaeb5"><kt-popup vue-id="06f8a8f1-1" data-ref="ktPopup" class="data-v-10bdaeb5 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-10bdaeb5',clazz.box]}}"><view class="box-title data-v-10bdaeb5">{{''+$root.g0}}</view><view style="height:20rpx;" class="data-v-10bdaeb5"></view><view class="pop_class data-v-10bdaeb5"><block wx:for="{{heightArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['heightSelect',['$0'],[[['heightArr','',index]]]]]]]}}" class="{{['class_item','data-v-10bdaeb5',(value===item.value)?'class_item-active':'']}}" bindtap="__e">{{item.title}}</view></block></view></view></kt-popup></view>