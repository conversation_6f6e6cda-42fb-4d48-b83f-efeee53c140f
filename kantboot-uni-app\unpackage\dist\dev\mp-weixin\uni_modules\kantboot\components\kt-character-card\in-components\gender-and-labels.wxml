<view class="common-info data-v-063e052c"><view class="{{['gender-age','data-v-063e052c',(character.gender==='male')?'gender-male':'',(character.gender==='female')?'gender-female':'']}}">{{''+(character.gender==="male"?"♂":"♀")+"\n    "+(character.age?character.age:'')+''}}</view><text style="margin-left:5px;" class="data-v-063e052c"><block wx:for="{{character.labels}}" wx:for-item="label" wx:for-index="index" wx:key="index"><text class="data-v-063e052c">{{'/ '+" "+label.text}}</text></block></text></view>