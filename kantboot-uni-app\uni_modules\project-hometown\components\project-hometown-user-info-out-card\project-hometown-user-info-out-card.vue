<template>
    <view :class="clazz.container">
        <view class="user-info-card">
            
            <view style="vertical-align: top;
            display: inline-block;
            width: calc(100% - 140rpx);">
                <view>

                  <view class="user-info-card-gender" style="margin-left: 0;margin-right: 10rpx">
                    <image
                        v-if="userInfo.genderCode === 'male'"
                        class="user-info-card-gender-icon"
                        :src="$kt.file.byPath('icon/male.svg')"></image>
                    <image
                        v-else
                        class="user-info-card-gender-icon"
                        :src="$kt.file.byPath('icon/female.svg')"></image>
                  </view>
                <view class="user-info-card-nickname">{{ userInfo.nickname || $i18n.zhToGlobal("无昵称") }}</view>
                
                <view
                    v-if="userInfo.gmtBirthday&&!userInfo.isAgeSecret&&false" class="user-info-card-age">
                   <view v-if="$i18n.getLanguageCode()==='zh_CN'||$i18n.getLanguageCode()==='zh_HK'||$i18n.getLanguageCode()==='zh_MO'||$i18n.getLanguageCode()==='zh_TW'">
                        {{ $kt.date.getAge(userInfo.gmtBirthday) }}{{ $i18n.zhToGlobal("岁") }}
                   </view>
                    <view v-else>
                       {{$i18n.zhToGlobal("年龄")}}{{":"}}{{ $kt.date.getAge(userInfo.gmtBirthday) }}
                   </view>
                </view>
                


                </view>

                <view style="margin-top: 10rpx;">
                     <view class="user-info-card-intro">
                        <text>{{"行业"}}{{": "}}</text>{{ "厨师" }}
                    </view>
                </view>


            </view>

            <view style="position: absolute;
            top:50%;
            right: 20rpx;
            transform: translateY(-50%);
            font-size: 28rpx;
            color: #333333;
">
              <view>{{"查看名片"}}{{ " ➠" }}</view>
            </view>
        </view>

    </view>
</template>

<script>

export default {
    props:{
        userInfo:{
            type:Object,
            default:()=>{
                return {}
            }
        }
    },
    data(){
        return {
            clazz:{
                container: this.$kt.style.toggleClass("container"),
            }
        }
    },
    methods:{
        getCharacteristics(userInfo){
            // 获取前3个特征
            let characteristics = userInfo.characteristics || [];
            let characteristicsList = [];
            for(let i=0;i<characteristics.length;i++){
                if(i>2){
                    break;
                }
                characteristicsList.push(characteristics[i]);
            }
            return characteristicsList;
        }
    }
}
</script>
<style lang="scss" scoped>
.user-info-card{
  position: relative;
}
.user-info-card-nickname{
    display: inline-block;
    font-weight: bold;
    font-size: 32rpx;
    letter-spacing: 1px;
    vertical-align: top;
}
.user-info-card-age{
    display: inline-block;
    font-size: 28rpx;
    vertical-align: top;
    // 绿色
    color: #07c160;
    // 透明绿
    background-color: rgba(7, 193, 96, 0.1);
    padding: 5rpx 10rpx;
    border-radius: 10rpx;
    margin-left: 15rpx;
}
.user-info-card-gender{
    display: inline-block;
    margin-left: 25rpx;
    vertical-align: top;
    margin-top: 5rpx;
    width: 30rpx;
    height: 30rpx;
    .user-info-card-gender-icon{
        width: 100%;
        height: 100%;
    }
}
.user-info-card-intro{
    font-size: 28rpx;
    color: #333333;
    letter-spacing: 1px;
}


.tag-v {
		background-color: rgba(255, 244, 240, 1);
		display: inline-block;
		vertical-align: top;
		margin-right: 20rpx;
		height: 50rpx;
		line-height: 50rpx;
		margin-bottom: 20rpx;
		padding: 0 20rpx 0 20rpx;
		border-radius: 10rpx;
		font-size: 28rpx;
		color: rgba(245, 172, 54, 1);

}
.intro-in{
    // 超出隐藏
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.personal-introduction{
    // 超出隐藏，2行
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.container-mode-color-scheme-dark{
  .user-info-card{
    .user-info-card-intro-1{
      color: #cccccc;
    }
    .user-info-card-nickname{
      color: #cccccc;
    }
    .user-info-card-age{
      color: #07c160;
      background-color: rgba(7, 193, 96, 0.1);
    }
    .user-info-card-intro{
      color: #999;
      .user-info-card-intro-icon{
        filter: invert(1);
      }
    }
  }
}


</style>