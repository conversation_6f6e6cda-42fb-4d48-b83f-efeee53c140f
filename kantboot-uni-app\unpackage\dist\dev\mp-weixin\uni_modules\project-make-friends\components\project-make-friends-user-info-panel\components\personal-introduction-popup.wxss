@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.avatar.data-v-b85774a2:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.popup.data-v-b85774a2 {
  padding: 20rpx 40rpx 20rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
}
.popup-title.data-v-b85774a2 {
  padding: 20rpx;
  font-size: 34rpx;
  font-weight: bold;
  text-align: left;
  letter-spacing: 2rpx;
}
.picker.data-v-b85774a2 {
  text-align: center;
  padding: 10rpx;
  box-sizing: border-box;
}
.bl-box.data-v-b85774a2 {
  text-align: left;
  width: 100%;
}
.bl-box .bl-box-item.data-v-b85774a2 {
  display: inline-block;
  margin: 10rpx;
  padding: 28rpx;
  border-radius: 20rpx;
  color: #333;
  box-sizing: border-box;
  background-color: #f5f5f5;
}
.bl-box .bl-box-item-selected.data-v-b85774a2 {
  background-color: #333;
  color: #fff;
}
.input.data-v-b85774a2 {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  font-size: 28rpx;
  text-align: center;
  letter-spacing: 2rpx;
}
.textarea.data-v-b85774a2 {
  width: 100%;
  min-height: 300rpx;
  padding: 20rpx;
  box-sizing: border-box;
  text-align: left;
}
.gender-box .gender-item.data-v-b85774a2 {
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 30rpx;
  display: inline-block;
  width: calc(50% - 40rpx);
  color: #333;
  margin-left: 20rpx;
  background-color: #f5f5f5;
}
.gender-box .gender-item-selected.data-v-b85774a2 {
  color: #fff;
  background-color: #333;
}
