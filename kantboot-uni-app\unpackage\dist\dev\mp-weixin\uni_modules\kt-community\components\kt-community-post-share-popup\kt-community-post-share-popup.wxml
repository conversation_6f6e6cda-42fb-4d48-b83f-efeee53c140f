<view class="data-v-4474847f"><kt-popup catch:click="__e" vue-id="4a1c8c67-1" data-ref="ktPopup" data-event-opts="{{[['^click',[['none']]]]}}" class="data-v-4474847f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{bindParams.postId}}"><view class="box data-v-4474847f"><view class="title data-v-4474847f">{{''+$root.g0+''}}</view><view class="input-box data-v-4474847f"><textarea class="input data-v-4474847f vue-ref" placeholder="{{$root.g1}}" data-ref="input" data-event-opts="{{[['tap',[['none']]],['input',[['__set_model',['$0','text','$event',[]],['bindParams']],['e0',['$event']]]]]}}" value="{{bindParams.text}}" catchtap="__e" bindinput="__e"></textarea></view><view style="height:20rpx;" class="data-v-4474847f"></view><view class="data-v-4474847f"><kt-community-post-card vue-id="{{('4a1c8c67-2')+','+('4a1c8c67-1')}}" post-id="{{bindParams.postId}}" has-bottom-operation="{{false}}" has-dot="{{false}}" class="data-v-4474847f" bind:__l="__l"></kt-community-post-card></view><view style="height:20rpx;" class="data-v-4474847f"></view><view class="data-v-4474847f"><kt-button bind:click="__e" vue-id="{{('4a1c8c67-3')+','+('4a1c8c67-1')}}" data-ref="confirmButton" data-event-opts="{{[['^click',[['push']]]]}}" class="data-v-4474847f vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g2}}</kt-button></view><view style="height:30rpx;" class="data-v-4474847f"></view></view></block></kt-popup></view>