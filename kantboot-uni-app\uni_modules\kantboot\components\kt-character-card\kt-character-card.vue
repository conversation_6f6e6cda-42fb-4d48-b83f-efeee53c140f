<template>
  <view class="box" style="color:#000;">
    <view>
      <kt-avatar
          :fileId="character.fileIdOfAvatar"
          size="120rpx"></kt-avatar>

      <view class="right-top">
        <slot name="rightTop"></slot>
      </view>

      <view class="right-info">
        <view class="nickname">{{ character.nickname || $i18n.zhToGlobal('无昵称') }}</view>
        <view class="small-split"></view>
        <view class="split"></view>
        <gender-and-labels :character="character"></gender-and-labels>
        <view class="small-split"></view>
        <view class="split"></view>
        <view class="introduction">{{ character.introduction || $i18n.zhToGlobal('无自我介绍') }}</view>
      </view>

      <view class="box">
        <slot name="bottom"></slot>
      </view>

    </view>
  </view>
</template>

<script>


import storage from "@/uni_modules/kantboot/libs/storage";
import GenderAndLabels
  from "@/uni_modules/kantboot/components/kt-character-card/in-components/gender-and-labels.vue";

export default {
  name: "ovo-user-info",
  components: {GenderAndLabels},
  computed: {
    storage() {
      return storage
    }
  },
  props: {
    isShowSupportLanguage: {
      type: Boolean,
      default: true
    },
    character: {
      type: Object,
      default: {
        fileIdOfAvatar: '647513348431877',
        nickname: '柏拉图',
        age:1990,
        labels: [{text:'标签'},{text:'标签'},{text:'标签'}],
        introduction:"我是柏拉图，依啊依啊哟~"
      }
    }
  },
  data() {
    return {};
  },
  created() {
  },
  methods: {},
  watch: {},
}
</script>


<style lang="scss" scoped>
.box {
  position: relative;
  width: 100%;
}

.location-in-ovo-user-card {
  position: absolute;
  top: 15rpx;
  right: 20rpx;
}

.right-info {
  float: right;
  vertical-align: top;
  width: calc(100% - 130rpx);
  padding: 0 20rpx;
  // #ifndef MP-WX
  margin-top: -5rpx;
  // #endif
  // #ifdef MP-WEIXIN
  margin-top: -7rpx;
  // #endif
  box-sizing: border-box;

  .nickname {
    font-size: 28rpx;
    letter-spacing: 5rpx;
  }

  .introduction {
    font-size: 22rpx;
    color: #555555;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

}

.small-split {
  height: 5rpx;
}

.split {
  height: 10rpx;
}

.box{
  padding: 0;
  box-sizing: border-box;
}

.right-top{
  position: absolute;
  top: 10rpx;
  right: 0;
}
</style>