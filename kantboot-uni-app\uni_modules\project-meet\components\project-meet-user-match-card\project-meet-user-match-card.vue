<template>
  <view :class="clazz.card">
    <image
        :src="$kt.file.byPath('meet/bg.png')"
        class="bg-img"></image>
    <image
        :src="$kt.file.byPath('meet/bg.png')"
        class="bg-img-2"></image>

    <view class="card-header">
      <view
          style="display: inline-block"
          v-if="!isLogin && $kt.style.detectDeviceType()==='pc'">
        {{$i18n.zhToGlobal("登录后只推荐异性")}}
      </view>
      <view
          v-else-if="languageCode.startsWith('zh')||languageCode.startsWith('sv')"
          style="display: inline-block">
        {{ $i18n.zhToGlobal("今日的心跳推荐") }}
      </view>
      <view
          style="display: inline-block"
          v-else>
        {{ $i18n.zhToGlobal("今日推荐") }}
      </view>


      <view
          @click="getInitList()"
          class="card-header-change">
        <view class="change-icon"
          :class="{
            'change-icon-loading': isLoading,
          }"
        >
          <image
              :src="$kt.file.byPath('icon/loading.svg')"
              style="width: 25rpx; height: 25rpx;">
          </image>
        </view>{{" "}}
        <view
            style="display: inline-block;">
          {{$i18n.zhToGlobal("换一批")}}
        </view>
      </view>
    </view>
    <view style="height: 20rpx"></view>

    <view class="card-content">
      <view v-if="isLoading&&userAccountList.length===0">
        <u-loading-icon
            mode="circle"
            :size="80"
            >
        </u-loading-icon>
      </view>
      <view v-for="item in userAccountList"
            class="card-content-item"
          :class="{
            'card-content-item-loading':isLoading,
            'card-content-item-loading-finish':!isLoading,
          }"
      >
        <project-meet-user-info-in-card
            v-if="$kt.style.detectDeviceType()==='mobile'"
            width="190rpx"
            height="240rpx"
            :user-info="item"
        ></project-meet-user-info-in-card>

        <project-meet-user-info-in-card
            v-else-if="$kt.style.detectDeviceType()==='pc'"
            width="300rpx"
            height="340rpx"
            :user-info="item"
        ></project-meet-user-info-in-card>

      </view>
    </view>
    <view style="height: 20rpx"></view>
    <view class="card-btn-box">
      <view
          v-if="successByTo"
          @click="to()"
          class="card-btn">{{"✔"}}{{" "}}{{ $i18n.zhToGlobal("搭讪成功") }}</view>
      <view
          v-else-if="!loading"
          @click="to()"
          class="card-btn">{{ $i18n.zhToGlobal("一键搭讪") }}</view>
      <view
          v-else-if="loading"
          @click="to()"
          class="card-btn">
        <image
            :src="$kt.file.byPath('icon/loading.svg')"
           class="loading-icon"></image>
      </view>
    </view>

    <view style="height: 20rpx"></view>
    <view
        v-if="!isLogin && $kt.style.detectDeviceType()==='mobile'"
        class="card-tips">
      {{$i18n.zhToGlobal("登录后只推荐异性")}}
    </view>

  </view>
</template>

<script>
export default {
  data() {
    return {
      // 是否初始化
      isInit: false,
      // 是否加载中
      isLoading: false,
      languageCode:  this.$kt.i18n.getLanguageCode(),
      clazz:{
        card: this.$kt.style.toggleClass("card"),
      },
      userAccountList:[],
      isLogin:false,
      loading: false,
      successByTo: false,
    };
  },
  created() {
    this.isLogin = this.$kt.userAccount.getIsLogin();
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.getInitList();
    });
    // 从缓存中获取
    let userAccountList = this.$kt.storage.get("meet-user-match-card-userAccountList");
    if (userAccountList) {
      this.userAccountList = userAccountList;
    }
    this.getInitList();
  },
  methods: {
   async chatOne(userAccountId) {

      let dialogId = 0;

      await this.$request.post("/functional-chat-web/dialog/getOneToOneByUserAccountId", {
        data: {userAccountId}
      }).then(res => {
        dialogId = res.data.id;
      }).catch(err => {
      });


      // /functional-chat-web/dialogMessage/sendMessageBySelf
      await this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf", {
        data: {
          items: [{
            content: "Let's see what sparks we can create!",
            type: "text",
          }],
          dialogId,
        }
      }).catch(err => {
        uni.showToast({
          title:err.errMsg,
          icon: 'none'
        });
      });

    },

    async to(){
      if(!this.$kt.userAccount.getIsLogin()){
        this.toLogin();
        return;
      }

      this.loading = true;

      await this.$kt.userAccount.requestSelf();
      this.userAccount = this.$kt.userAccount.getSelf();

      let currentTime = new Date().getTime();

      let isSvip = false;
      try{
        isSvip = this.userAccount.gmtSvipExpire && this.userAccount.gmtSvipExpire > currentTime;
      }catch (e) {
        isSvip = false;
      }
      let isVip = false;
      try{
        isVip = this.userAccount.gmtVipExpire && this.userAccount.gmtVipExpire > currentTime;
      }catch (e) {
        isVip = false;
      }

      if(!isVip&&!isSvip){
        uni.showToast({
          title: this.$i18n.zhToGlobal("开启VIP，即可一键搭讪"),
          icon: 'none'
        });
        this.loading=false;
        return;
      }

      setTimeout(async ()=>{
        for(let i=0;i<this.userAccountList.length;i++){
          let userAccountId = this.userAccountList[i].id;
          await this.chatOne(userAccountId);
        }
      },10)
      this.successByTo = true;
      setTimeout(()=>{
        this.successByTo = false;
        this.loading = false;
        this.getInitList();
      },1000);
    },
    toLogin(){
      this.$emit("toLogin");
      // this.$refs.ktLoginPopup.open();
    },
    getInitList() {
      if(this.isLoading){
        return;
      }
      this.isLoading = true;
      this.$request.post('/project-meet-web/userAccount/match').then(res => {
        setTimeout(()=> {

          this.userAccountList = res.data;
          // 提取前4个
          this.userAccountList = this.userAccountList.slice(0, 4);
          if(this.$kt.style.detectDeviceType()==='mobile'){
            // 如果是手机就提取前3个
            this.userAccountList = this.userAccountList.slice(0, 3);
          }
        },100);
          // 添加缓存
          this.$kt.storage.set("meet-user-match-card-userAccountList", this.userAccountList);
        setTimeout(()=>{

          this.isLoading = false;
          this.isInit = true;

        },300);
      }).catch(err => {
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.card {
  //background-color: rgba(255,255,255,.6);
  padding: 30rpx 30rpx 30rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, .05);
  // 渐变，从上到下
  background: linear-gradient(to bottom, rgba(255,255,255,1), rgba(255,255,255,.2) 100%);
  .bg-img {
    display: none;
  }
  .bg-img-2 {
    display: none;
  }

}

.card-header {
  color: #333333;
  position: relative;
  z-index: 1;
  font-size: 27rpx;
  letter-spacing: 2rpx;
  font-weight: bold;
  //color: #ff8699;
  .card-header-change{
    float: right;
    display: inline-block;
    font-size: 25rpx;
    font-weight: lighter;
    color: #000000;
    .change-icon{
      display: inline-block;
    }
    .change-icon-loading{
      animation: change-icon-loading-in-meet-user-match-card 1.5s infinite;
    }
  }
}

@keyframes change-icon-loading-in-meet-user-match-card {
  0% {
    opacity: 1;
    transform: rotate(0deg);
  }
  100% {
    opacity: 1;
    transform: rotate(750deg);
  }
}

.card-content {
  position: relative;
  z-index:2;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}


.card-content-item {
  display: inline-block;
}

.card-content-item-loading{
  transform: rotateY(90deg);
  border-radius: 20rpx;
  filter: blur(0px);

  // 一开始旋转
  animation: card-content-item-loading-in-meet-user-match-card 1s;
}

@keyframes card-content-item-loading-in-meet-user-match-card {
  0% {
    opacity: 1;


    background-color: rgba(0,0,0,0);
    transform: rotateY(0deg);
  }
  50%{
    opacity: .3;

    background-color: rgba(0,0,0,.3);
    transform: rotateY(50deg);
  }
  100%{
    opacity: .7;

    background-color: rgba(0,0,0,.1);
    transform: rotateY(85deg);
  }
}

.card-content-item-loading-finish{
  transform: rotateY(0deg);
  border-radius: 20rpx;
  animation: card-content-item-loading-finish-in-meet-user-match-card 1s;
}

@keyframes card-content-item-loading-finish-in-meet-user-match-card {
  0% {

    background-color: rgba(0,0,0,.5);
    transform: rotateY(85deg);
  }
  100% {

    background-color: rgba(0,0,0,0);
    transform: rotateY(-360deg);
  }
}


.card-btn-box {
  text-align: center;
}

.card-btn {
  position: relative;
  z-index:2;

  display: inline-block;
  border-radius: 40rpx;
  color: #FFFFFF;
  text-align: center;
  padding: 10rpx 50rpx 10rpx 50rpx;
  //background-color: rgba(0,0,0,1);
  background-color: #5A7EF6;
}

.card-btn:active {
  transform: scale(.99);
}

.card-mode-device-pc{
  position: relative;
  margin-top: -20rpx;
  // 渐变
  background: linear-gradient(to bottom, rgba(255,255,255,1), rgba(255,255,255,.9) 100%);
  .card-btn{
    cursor: pointer;
  }

  .card-header {
    color: #FFFFFF;
    text-shadow: 0 3rpx 5rpx rgba(0,0,0,.5);
    .card-header-change{
      cursor: pointer;
      color: #666666;
      text-shadow: none;
    }
  }
  .bg-img {
    opacity: .8;
    position: absolute;
    display: block;
    z-index:0;
    width: 100%;
    left: 0;
    top:0;
    border-radius: 20rpx;
  }
  .bg-img-2{
    opacity: .5;
    position: absolute;
    display: block;
    z-index:0;
    width: 100%;
    height: 150%;
    left: 0;
    bottom:0;
    border-radius: 20rpx;
    // 翻转
    transform: rotate(180deg);
  }

  .card-btn{
    width: 40%;
    padding: 20rpx;
    box-sizing: border-box;
  }
}

.card-tips{
  color: #333333;
  text-align: center;
  font-size: 25rpx;
}

.loading-icon {
  width: 45rpx;
  height: 45rpx;
  // 颜色反转
  filter: invert(1);
  // 动画
  animation: loading-icon-in-meet-user-match-card 1s infinite;
}

@keyframes loading-icon-in-meet-user-match-card {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
