<template>
  <view>
    <kt-popup
        @close="close"
        :show="show"
        bg-color="rgba(0,0,0,0)"
        ref="ktPopup">
      <view :class="clazz.box">
        <view class="box-title">
          {{ $i18n.zhToGlobal("可见范围") }}
        </view>
        <scroll-view
            class="scroll-view"
            style="max-height: 800rpx"
            :scroll-y="true">
          <view class="menu-box">
            <view
                v-for="item in enums.getVisibleScopeList()"
                class="menu-item"
                @click="select(item)"
                :class="{
              'menu-item-selected': selected === item.code
            }"
            >{{ item.name }}
            </view>
          </view>
          <view style="height: 10rpx"></view>

          <view
              v-if="selected === 'part'">

            <view
                style="position: relative"
                class="sub-title-box">
              <view class="sub-title">
                {{ $i18n.zhToGlobal('部分可见') }}
              </view>
              <view class="sub-title"
                    style="position: absolute;right: 30rpx;top:-3rpx;"
              >
                {{ userAccountIdsOfPartVisible.length }}
              </view>
            </view>

            <view style="height: 10rpx"></view>
            <view class="no-data"
                  @click="openPartVisible"
                  v-if="userAccountIdsOfPartVisible.length === 0">
              {{ "+" }}
            </view>
            <swiper
                v-if="userAccountIdsOfPartVisible.length > 0"
                :style="{
                height: 'calc('+userAccountIdsOfPartVisibleHeight + 'px + 60rpx)',
              }">
              <swiper-item
                  v-for="(item,index) in userAccountIdsOfPartVisible">
                <view class="user-info-card-box-box">
                  <view class="user-info-card-box">
                    <view
                        @click="userAccountIdsOfPartVisible.splice(index, 1)"
                        class="user-info-card-box-tb">
                      <image
                          class="user-info-card-box-remove"
                          :src="$kt.file.byPath('icon/remove.svg')"
                      ></image>
                    </view>
                    <view
                        style="height: 10rpx"
                    ></view>
                    <view :id="'userAccountOfPartVisible_'+uuid+'_'+index">
                      <view v-if="!customUserInfoCard">
                        <kt-user-info-card :user-account-id="item"></kt-user-info-card>
                      </view>
                      <view v-else>
                        <slot name="userInfoCardOfPartVisible"
                              :user-account-id="item"></slot>
                      </view>

                    </view>
                    <view
                        style="height: 10rpx"
                    ></view>

                  </view>
                </view>
              </swiper-item>
            </swiper>
            <view
                v-if="userAccountIdsOfPartVisible.length > 0"
                @click="openPartVisible"
                class="add-btn-box">
              <view
                  class="add-btn"
              >{{ $i18n.zhToGlobal('添加') }}
              </view>
            </view>

          </view>


          <view
              v-if="selected === 'not'">

            <view
                style="position: relative"
                class="sub-title-box">
              <view class="sub-title">
                {{ $i18n.zhToGlobal('部分的人不可见') }}
              </view>
              <view class="sub-title"
                    style="position: absolute;right: 30rpx;top:-3rpx;">
                {{ userAccountIdsOfNotVisible.length }}
              </view>
            </view>

            <view style="height: 10rpx"></view>
            <view class="no-data"
                  @click="openNotVisible"
                  v-if="userAccountIdsOfNotVisible.length === 0">
              {{ "+" }}
            </view>
            <swiper
                v-if="userAccountIdsOfNotVisible.length > 0"
                :style="{
                height: 'calc('+userAccountIdsOfNotVisibleHeight + 'px + 60rpx)',
              }"
            >
              <swiper-item
                  v-for="(item,index) in userAccountIdsOfNotVisible">
                <view class="user-info-card-box-box">
                  <view class="user-info-card-box">
                    <view
                        @click="userAccountIdsOfNotVisible.splice(index, 1)"
                        class="user-info-card-box-tb">
                      <image
                          class="user-info-card-box-remove"
                          :src="$kt.file.byPath('icon/remove.svg')"
                      ></image>
                    </view>
                    <view
                        style="height: 10rpx"
                    ></view>

                    <view :id="'userAccountOfNotVisible_'+uuid+'_'+index">
                      <view v-if="!customUserInfoCard">
                        <kt-user-info-card :user-account-id="item">
                        </kt-user-info-card>
                      </view>
                      <view v-else>
                        <slot name="userInfoCardOfNotVisible"
                              :user-account-id="item"></slot>
                      </view>
                    </view>

                    <view style="height: 10rpx"></view>

                  </view>
                </view>
              </swiper-item>
            </swiper>
            <view
                v-if="userAccountIdsOfNotVisible.length > 0"
                @click="openNotVisible"
                class="add-btn-box">
              <view
                  class="add-btn"
              >{{ $i18n.zhToGlobal('添加') }}
              </view>
            </view>

          </view>

        </scroll-view>

        <view style="padding: 30rpx;box-sizing: border-box">
          <kt-button
              ref="confirmButton"
              @click="confirm()"
          >{{ $i18n.zhToGlobal("确定") }}
          </kt-button>
          <view style="height: 30rpx"></view>
        </view>


      </view>
    </kt-popup>

    <view>
      <kt-user-account-interrelation-popup
      ref="userAccountInterrelationPopupOfPart"
      @selectMany="selectPartIds"
      ></kt-user-account-interrelation-popup>
      <kt-user-account-interrelation-popup
          ref="userAccountInterrelationPopupOfNotVisible"
          @selectMany="selectNotIds"
      ></kt-user-account-interrelation-popup>
    </view>
  </view>

</template>

<script>
import enums from "@/uni_modules/kt-community/libs/enums";

export default {
  props:{
    /**
     * 是否自定义用户弹出层
     */
    customUserAccountsSelectPopup: {
      type: Boolean,
      default: false
    },
    /**
     * 是否自定义用户信息卡片
     */
    customUserInfoCard: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      enums,
      uuid: "",
      show: false,
      selected: 'all',
      // 部分可见的用户账号ID
      userAccountIdsOfPartVisible: [],
      // 不可见的用户账号ID
      userAccountIdsOfNotVisible: [],
      userAccountIdsOfNotVisibleHeight: 0,
      userAccountIdsOfPartVisibleHeight: 0,
      clazz: {
        box: this.$kt.style.toggleClass("box")
      }
    };
  },
  watch: {
    userAccountIdsOfNotVisible: {
      handler(newVal) {
        setTimeout(() => {
          if (newVal.length > 0) {
            uni.createSelectorQuery().select('#userAccountOfNotVisible_' + this.uuid + '_0')
                .boundingClientRect((rect) => {
                  this.userAccountIdsOfNotVisibleHeight = rect.height;
                }).exec();
          }
        }, 100);
      },
      deep: true
    },
    userAccountIdsOfPartVisible: {
      handler(newVal) {
        setTimeout(() => {
          if (newVal.length > 0) {
            uni.createSelectorQuery().select('#userAccountOfPartVisible_' + this.uuid + '_0')
                .boundingClientRect((rect) => {
                  this.userAccountIdsOfPartVisibleHeight = rect.height;
                }).exec();
          }
        }, 100);
      },
      deep: true
    }
  },
  mounted() {
    this.uuid = this.$kt.util.generateUUID();
  },
  methods: {
    open(selected) {
      this.selected = selected + "";
      this.show = true;
      this.$refs.ktPopup.open();
    },
    close() {
      this.show = false;
      this.$refs.ktPopup.close();
    },
    selectPartIds(idSelectedList) {
      this.userAccountIdsOfPartVisible = idSelectedList;
    },
    selectNotIds(idSelectedList) {
      this.userAccountIdsOfNotVisible = idSelectedList;
    },
    select(item) {
      this.selected = item.code;
    },
    confirm() {
      this.$refs.confirmButton.loading();
      setTimeout(() => {
        this.close();
        this.$emit('select', this.selected);
        this.$emit("userAccountIdsOfPartVisibleSelect", this.userAccountIdsOfPartVisible);
        this.$emit("userAccountIdsOfNotVisibleSelect", this.userAccountIdsOfNotVisible);
        this.$refs.confirmButton.toNone();
      }, 100);
    },
    openPartVisible() {
      this.$emit("openPartVisible", this.userAccountIdsOfPartVisible);
      if(!this.customUserAccountsSelectPopup){
        this.$refs.userAccountInterrelationPopupOfPart.setIdSelectedList(this.userAccountIdsOfPartVisible);
        this.$refs.userAccountInterrelationPopupOfPart.open();
      }
    },
    openNotVisible() {
      this.$emit("openNotVisible", this.userAccountIdsOfNotVisible);
      if(!this.customUserAccountsSelectPopup){
        this.$refs.userAccountInterrelationPopupOfNotVisible.setIdSelectedList(this.userAccountIdsOfNotVisible);
        this.$refs.userAccountInterrelationPopupOfNotVisible.open();
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.box {
  bottom: 0;
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;

  .box-title {
    padding: 20rpx;
    font-size: 28rpx;
    color: #333333;
    text-align: center;
    font-weight: bold;
  }
}

.menu-box {
  padding: 20rpx;
  box-sizing: border-box;

  .menu-item {
    padding: 20rpx;
    border: 3px solid #F0F0F0;
    text-align: center;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    color: #666666;
  }

  .menu-item-selected {
    border: 3px solid #333333;
    background-color: #333333;
    color: #FFFFFF;
  }
}

.scroll-view {
  padding: 30rpx;
  box-sizing: border-box;
}

.user-info-card-box-box {
  padding: 0 20rpx 0 20rpx;
  box-sizing: border-box;
}

.user-info-card-box {
  border: 3px solid #F0F0F0;
  padding: 10rpx;
  border-radius: 20rpx;
}

.add-btn-box {
  text-align: center;

  .add-btn {
    color: #999999;
    font-size: 28rpx;
  }
}

.sub-title {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}

.no-data {
  text-align: center;
  font-size: 90rpx;
  font-weight: lighter;
  color: #999999;
  height: 200rpx;
  line-height: 200rpx;
  background-color: #F0F0F0;
  border-radius: 20rpx;
}

.no-data:active {
  transform: scale(0.97);
}

.user-info-card-box-tb {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  //background-color: rgba(0,0,0,.5);
  border-radius: 55%;
  top: 20rpx;
  right: 40rpx;
}

.user-info-card-box-remove {
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  opacity: .8;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  // 颜色反转
  //filter: invert(1);
  // 黑色转成红色
  filter: invert(16%) /* 反转黑色(0,0,0)为白色(255,255,255) */
  sepia(100%) /* 添加深红色调 */
  saturate(1000%) /* 增强饱和度 */
  hue-rotate(300deg); /* 调整色相到红色 */
}

.box-mode-device-pc {
  position: fixed;
  border-radius: 20rpx;
  // 超出不显示
  overflow: hidden;
  width: 400px;
  left: 50%;
  bottom: 50%;
  transform: translate(-50%, 50%);
}
</style>
