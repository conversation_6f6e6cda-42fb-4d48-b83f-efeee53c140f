<view class="data-v-2bc9e664"><u-popup vue-id="59470df8-1" show="{{show}}" mode="bottom" bgColor="rgba(0,0,0,0)" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-2bc9e664" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup data-v-2bc9e664"><view class="popup-title data-v-2bc9e664">{{$root.g0}}</view><scroll-view class="picker data-v-2bc9e664" style="max-height:500px;" scroll-y="{{true}}"><view class="bl-box data-v-2bc9e664"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__"><block wx:if="{{item.$orig.name}}"><view data-event-opts="{{[['tap',[['select',['$0'],[[['list','',__i0__]]]]]]]}}" class="{{['bl-box-item','data-v-2bc9e664',(item.m0)?'bl-box-item-selected':'']}}" bindtap="__e">{{item.g1}}</view></block></block></view></scroll-view><view style="height:10px;" class="data-v-2bc9e664"></view><kt-button bind:click="__e" vue-id="{{('59470df8-2')+','+('59470df8-1')}}" data-ref="confirmBtn" data-event-opts="{{[['^click',[['submit']]]]}}" class="data-v-2bc9e664 vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g2}}</kt-button><view style="height:10px;" class="data-v-2bc9e664"></view></view></u-popup></view>