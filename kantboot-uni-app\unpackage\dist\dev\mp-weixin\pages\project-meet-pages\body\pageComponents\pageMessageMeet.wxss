@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-11f32d7e {
  padding: 20rpx;
  box-sizing: border-box;
}
.bg.data-v-11f32d7e {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.bg-mode-color-scheme-light.data-v-11f32d7e {
  background-color: #FFFFFF;
}
.bg-mode-color-scheme-dark.data-v-11f32d7e {
  background-color: #191919;
}
.in-box.data-v-11f32d7e {
  position: relative;
  height: 170rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.in-box .in-box-user-info-card.data-v-11f32d7e {
  position: absolute;
  width: 100%;
  top: 15rpx;
  left: 0;
}
.container-mode-color-scheme-dark .in-box.data-v-11f32d7e {
  border-bottom: 1rpx solid #404a56;
}
.container-mode-device-pc.data-v-11f32d7e {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
}
.container-mode-device-pc .box .in-box.data-v-11f32d7e {
  position: relative;
  width: 100%;
  height: 160rpx;
}
.container-mode-device-pc .box .in-box .in-box-user-info-card.data-v-11f32d7e {
  position: absolute;
  width: 100%;
  top: 15rpx;
  left: -450rpx;
}
.container-mode-device-pc .header-box.data-v-11f32d7e {
  width: 100%;
}
.container-mode-device-pc .kt-chat-list-panel-box.data-v-11f32d7e {
  position: relative;
  display: inline-block;
  width: 600rpx;
  background-color: #F3F3F3;
}
.container-mode-device-pc .kt-chat-dialog-panel-box.data-v-11f32d7e {
  position: relative;
  display: inline-block;
  width: calc(100% - 600rpx);
  color: #000000;
  vertical-align: top;
  background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
}
.container-mode-device-pc .kt-chat-dialog-panel-box .charge-button.data-v-11f32d7e {
  position: absolute;
  top: 20rpx;
  right: 0;
  z-index: 9999;
  color: #FFFFFF;
  font-size: 28rpx;
  background-color: #F6A496;
  padding: 10rpx 10rpx 10rpx 30rpx;
  border-radius: 30rpx 0 0 30rpx;
  cursor: pointer;
}
.container-mode-device-pc .kt-chat-dialog-panel-box .remark-button.data-v-11f32d7e {
  position: absolute;
  top: 90rpx;
  right: 0;
  z-index: 10000;
  color: #FFFFFF;
  font-size: 28rpx;
  background-color: rgba(0, 0, 0, 0.8);
  padding: 10rpx 10rpx 10rpx 30rpx;
  border-radius: 30rpx 0 0 30rpx;
}
.footer.data-v-11f32d7e {
  background-color: #FFFFFF;
}
.bg.data-v-11f32d7e {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
  opacity: 0.5;
}
.bg .bg-image.data-v-11f32d7e {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  right: 0;
  z-index: -2;
}
.bg .bg-image-2.data-v-11f32d7e {
  position: fixed;
  width: 100vw;
  height: 100vh;
  bottom: 25vh;
  right: 0;
  -webkit-transform: scaleY(-1);
          transform: scaleY(-1);
  z-index: -2;
}
.bg .bg-image-3.data-v-11f32d7e {
  position: fixed;
  width: 100vw;
  height: 100vh;
  bottom: 0;
  right: 0;
  z-index: -2;
  -webkit-transform: scaleY(-1);
          transform: scaleY(-1);
}
.bg .bg-bg.data-v-11f32d7e {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  right: 0;
  z-index: -1;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 100rpx, white 100%);
}
.bg-mode-color-scheme-light.data-v-11f32d7e {
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}
.bg-mode-color-scheme-dark.data-v-11f32d7e {
  background: #191919;
}
.input-icon.data-v-11f32d7e {
  width: 40rpx;
  height: 40rpx;
  margin-left: 30rpx;
  vertical-align: top;
  margin-top: -7rpx;
  cursor: pointer;
}
.kt-checkbox.data-v-11f32d7e {
  position: absolute;
  display: inline-block;
  text-align: center;
  border-radius: 25rpx;
  cursor: pointer;
  right: 70rpx;
  bottom: 40rpx;
  font-size: 28rpx;
  border: 1rpx solid #f0f0f0;
  padding: 0 20rpx;
}
.kt-checkbox .input-language-icon.data-v-11f32d7e {
  width: 30rpx;
  height: 30rpx;
  vertical-align: middle;
  margin-right: 10rpx;
  margin-top: -10rpx;
}
.top-left-btn.data-v-11f32d7e {
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #cccccc;
  cursor: pointer;
}
.top-left-btn.data-v-11f32d7e:active {
  background-color: #f0f0f0;
}
