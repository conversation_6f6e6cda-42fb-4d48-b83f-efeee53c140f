<template>
  <view>
    <view class="box">
      <view
          v-for="item in subAccounts"
          class="box-item"
          :class="{'box-item-selected': item.id === selected}"
          @click="selectAccount(item)"
      >
        {{ item.username }}
        <view
            v-if="item.messageUnreadCount"
            class="unread-count">{{ item.messageUnreadCount }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>

export default {
  data() {
    return {
      // 子账号列表
      subAccounts: [],
      selected: '',
      // 子账号对应的token
      tokenMap: {},
      isPc: true

    };
  },
  mounted() {
    this.getSubAccounts();
    this.$kt.event.on("FunctionalProjectMeet:message", () => {
      this.getSubAccounts();
    });
    this.getIsPc();
  },
  methods: {
    getIsPc() {
      // 转换为大写
      let deviceType = this.$kt.style.detectDeviceType().toUpperCase();
      return deviceType === 'PC';
    },
    selectAccount(item) {
      this.selected = item.id;
      let token = this.tokenMap[item.id + ""];
      if (!token) {
        // /project-meet-web/userAccount/loginByUserAccountId
        this.$request.post("/project-meet-web/userAccount/loginByUserAccountId", {
          data: {
            userAccountId: item.id
          }
        }).then(res => {
          // 保存token
          this.tokenMap[item.id + ""] = res.data.token;
          // 触发事件，传递选中的子账号
          this.$emit("select", res.data.token);
          setTimeout(() => {
            this.$emit("select", res.data.token);
          }, 500);
          setTimeout(() => {
            this.$emit("select", res.data.token);
          }, 2000);
        }).catch(err => {
        });
      } else {
        // 触发事件，传递选中的子账号
        this.$emit("select", token);
      }
    },
    // 获取子账号
    getSubAccounts() {
      // /project-meet-web/admin/userAccount/getSubUserAccounts
      this.$request.post("/project-meet-web/admin/userAccount/getSubUserAccounts", {}).then(res => {
        this.subAccounts = res.data || [];
        if (!this.selected && this.subAccounts.length > 0) {
          this.selectAccount(res.data[0]);
        }
      }).catch(err => {
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.box-item {
  display: inline-block;
  padding: 10rpx;
  padding-right: 30rpx;
  border: 1px solid #ccc;
  margin: 10rpx;
  font-size: 10px;
  border-radius: 30rpx;
  cursor: pointer;

  .unread-count {
    display: inline-block;
    color: #FFFFFF;
    background-color: #FF0000;
    margin-left: 5rpx;
    font-size: 10px;
    padding: 0 5rpx;
    border-radius: 10rpx;
  }
}

.box-item-selected {
  background-color: #666666;
  color: #FFFFFF;
}
</style>
