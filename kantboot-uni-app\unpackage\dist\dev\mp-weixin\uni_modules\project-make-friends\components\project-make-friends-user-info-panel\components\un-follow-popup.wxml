<view class="data-v-dfb416e6"><u-popup vue-id="74991e19-1" show="{{show}}" mode="bottom" bgColor="rgba(0,0,0,0)" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-dfb416e6" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup data-v-dfb416e6"><view style="height:30rpx;" class="data-v-dfb416e6"></view><kt-user-info-card vue-id="{{('74991e19-2')+','+('74991e19-1')}}" userInfo="{{userAccount}}" class="data-v-dfb416e6" bind:__l="__l"></kt-user-info-card><view style="height:70rpx;" class="data-v-dfb416e6"></view><kt-button vue-id="{{('74991e19-3')+','+('74991e19-1')}}" boxShadow="{{false}}" data-ref="unFollowBtn" data-event-opts="{{[['^click',[['unFollow']]]]}}" bind:click="__e" class="data-v-dfb416e6 vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g0}}</kt-button><view style="height:100rpx;" class="data-v-dfb416e6"></view></view></u-popup></view>