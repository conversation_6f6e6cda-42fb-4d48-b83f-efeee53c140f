<template>
<view :class="clazz.panel">
  <view class="panel-body">
    <view class="panel-body-content">
      <view
          v-if="loading"
          style="height: 50rpx"></view>
      <u-loading-icon
          v-if="loading"
          mode="circle"
          :size="50"
      ></u-loading-icon>
      <view class="box">

        <view
            v-for="item in aiCenterList"
            class="box-item">
          <kt-course-card
              @click="cardClick(item)"
              :info="{...item}"
          ></kt-course-card>
        </view>


        <view style="height: 30rpx"></view>

      </view>
    </view>
  </view>
</view>
</template>

<script>

export default {
  props:{
    headerFixed: {
      type: Boolean,
      default: false
    },
    headerTop:{
      type: String,
      default: "0rpx"
    }
  },
  data(){
    return {
      clazz:{
        panel: this.$kt.style.toggleClass("panel"),
      },
      typeList: [
        {
          name: '课程',
          id: 'course'
        },
        {
          name: '课程包',
          id: 'coursePackage'
        }
      ],
      list: [],
      resList:[],
      // 免费区
      freeList:[],
      // 会员免费区域
      vipFreeList:[],
      // 收费区
      noFreeList:[],
      // AI中心区
      aiCenterList: [],
      // 选中的课程类型
      selectedType: 'course',
      loading: true,
    };
  },
  async created(){
    this.loading = true;
    await this.getTypeList();
    await this.getShelves();
    this.loading = false;
  },
  methods:{
    /**
     * 课程卡片点击事件
     * @param item
     */
    cardClick(item){
      this.$emit('cardClick',item);
    },
    // /fp-course-web/courseType/getAll
    getTypeList(){
      return this.$kt.request.post('/fp-course-web/courseType/getAll')
          .then(res => {
            this.typeList = res.data;
            // 默认选中第一个
            this.selectedType = this.typeList[0].id;
          }).catch(err => {
        console.log(err);
      })
    },
    // list分区
    listSplit(){
      this.freeList = [];
      this.vipFreeList = [];
      this.noFreeList = [];
      for (let i = 0; i < this.resList.length; i++) {
        const item = this.resList[i];
        if (item.permissionTagCode === "free") {
          this.freeList.push(item);
        }else if (item.permissionTagCode === "vipFree") {
          this.vipFreeList.push(item);
        } else if (item.permissionTagCode === "noFree") {
          this.noFreeList.push(item);
        } else if (item.permissionTagCode === "aiCenter") {
          this.aiCenterList.push(item);
        }
      }
    },
    // 过滤对应type
    filterType(){
      this.resList = [];
      for (let i = 0; i < this.list.length; i++) {
        const item = this.list[i];
      //   if (item.typeId === this.selectedType) {
          this.resList.push(item);
      //   }
      }
      this.listSplit();
    },
    // /fp-course-web/admin/course/getShelves
    getShelves(){
      return this.$kt.request.post('/fp-course-web/course/getShelves')
          .then(res => {
            this.list = res.data;
            // this.listSplit();
            this.filterType();
          }).catch(err => {
        console.log(err);
      })
    },

  }

}
</script>

<style lang="scss" scoped>
.panel {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;

  .panel-header {
    padding: 20rpx;
    width: 100%;


    .panel-header-content {
      // 超出不换行
      white-space: nowrap;
      // 左右滚动
      overflow-x: auto;

      .panel-header-content-item {
        display: inline-block;
        margin-right: 30rpx;
        font-size: 36rpx;
        color: #333;
      }

      .panel-header-content-item-selected {
        font-weight: bold;
        color: #000000;
        border-bottom: 2rpx solid #000000;
      }
    }
  }

  .panel-header-fixed {
    background-color: #fff;
  }

}

// 不显示滚动条
.panel-header-content::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}

  // 滚动
.panel-header-content::-webkit-scrollbar-thumb {
    background-color: #fff;
    border-radius: 0;
}

.box-item{
  width: 50%;
  display: inline-block;
  padding: 20rpx;
  box-sizing: border-box;
}

.box-item-split{
  font-size: 28rpx;
  color: #999;
  text-align: center;
  margin-top: 20rpx;
}

.no-data{
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 100rpx;
  .no-data-image{
    width: 100rpx;
    height: 100rpx;
    opacity: .6;
  }
}

.panel-mode-color-scheme-light{
  panel-header-fixed{
    background-color: #fff;
  }
}

.panel-mode-color-scheme-dark{
  .panel-header {
    background-color: #191919;
    .panel-header-content-item {
      // 颜色反转
      filter: invert(1);
    }
  }
  .no-data{
    color: #b9b9b9;
  }
  .no-data-image{
    filter: invert(1);
  }
}
</style>