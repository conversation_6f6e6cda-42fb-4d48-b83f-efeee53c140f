<template>
  <view>

    <view
        @click="show = !show"
        v-if="$kt.style.detectDeviceType() === 'mobile'"
        class="fixed-box-mobile">{{$i18n.zhToGlobal("开关")}}</view>
    <view
        :class="clazz.fixedBox"
    :style="{
      display:$kt.style.detectDeviceType() === 'pc' || show ? 'block' : 'none',
    }"
    >
      <accountList
      @select="select"
      ></accountList>
    </view>

    <view>
      <iframe
          v-if="ifa"
        :src="'/#/pages/project-meet-pages/body/body?token='+token"
        style="width: 100%; height: 100vh; border: none;"
        frameborder="0"
        allowfullscreen
      ></iframe>
    </view>
  </view>
</template>

<script>
import accountList from "@/pages/project-meet-pages/body/components/account-list.vue";
export default {
  components: {
    accountList
  },
  data() {
    return {
      token: "",
      show:false,
      ifa:false,
      clazz:{
        fixedBox: this.$kt.style.toggleClass("fixed-box"),
      }
    };
  },
  methods: {
    select(token) {
      this.token = token;
      this.ifa = false;
      setTimeout(()=>{
        this.ifa = true;
      },10);
    },
  },
}
</script>

<style lang="scss" scoped>
.fixed-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255,255,255,.9);
  z-index: 9999; /* 确保在最上层 */
}

.fixed-box-mobile {
  position: fixed;
  bottom: 170rpx;
  right: 0;
  display: inline-block;
  border-radius: 20rpx 0 0 20rpx;
  box-shadow: 0 0 10rpx rgba(0,0,0,.8);
  font-size: 28rpx;
  padding: 10rpx;
  z-index: 9999; /* 确保在最上层 */
}

.fixed-box-mode-device-mobile {
  bottom: 0;
  left: 0;
  width: 200px;
  border-radius: 0;
}
</style>
