<template>
  <view>
    <kt-nav-bar
    :title="title"
    :is-has-i18n="false"
    background-color="rgba(0,0,0,0)"
    ></kt-nav-bar>
    <web-view :src="src"></web-view>
  </view>
</template>

<script>
export default {
  onLoad(option) {
    this.src = option.src;
    this.title = option.title;
    if(option.title){
      uni.setNavigationBarTitle({
        title: option.title
      });
    }else{
      uni.setNavigationBarTitle({
        title: "第三方页面"
      });
    }
  },
  data() {
    return {
      src:"",
      title:""
    };
  },
}
</script>

<style lang="scss">

</style>
