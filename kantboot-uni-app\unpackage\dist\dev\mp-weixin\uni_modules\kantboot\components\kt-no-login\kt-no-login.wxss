@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-7b905926 {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.icon.data-v-7b905926 {
  width: 200rpx;
  height: 200rpx;
  -webkit-animation: inNoLoginShake-data-v-7b905926 2s infinite;
          animation: inNoLoginShake-data-v-7b905926 2s infinite;
}
@-webkit-keyframes inNoLoginShake-data-v-7b905926 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
25% {
    -webkit-transform: rotate(10deg);
            transform: rotate(10deg);
}
50% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
75% {
    -webkit-transform: rotate(-10deg);
            transform: rotate(-10deg);
}
100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
}
@keyframes inNoLoginShake-data-v-7b905926 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
25% {
    -webkit-transform: rotate(10deg);
            transform: rotate(10deg);
}
50% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
75% {
    -webkit-transform: rotate(-10deg);
            transform: rotate(-10deg);
}
100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
}
.in-box.data-v-7b905926 {
  position: fixed;
  top: calc(50% - 75rpx);
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  text-align: center;
  font-size: 30rpx;
  color: #000000;
  width: 100%;
  padding: 20rpx 60rpx 20rpx 60rpx;
  box-sizing: border-box;
  z-index: 701;
}
.box-mode-color-scheme-dark .icon.data-v-7b905926 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.box-mode-device-pc .in-box.data-v-7b905926 {
  width: 400px;
}
.back.data-v-7b905926 {
  z-index: 600;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
}
.back-mode-color-scheme-light.data-v-7b905926 {
  background-color: #FFFFFF;
}
.back-mode-color-scheme-dark.data-v-7b905926 {
  background-color: #191919;
}
.setting-icon.data-v-7b905926 {
  width: 50rpx;
  height: 50rpx;
  opacity: 0.7;
}
.setting-icon.data-v-7b905926:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.box-mode-color-scheme-dark .setting-icon.data-v-7b905926 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
