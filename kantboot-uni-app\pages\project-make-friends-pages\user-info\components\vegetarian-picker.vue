<template>
	<view class="edit-box">
        <u-popup :show="show" 
        mode="bottom"
        :bgColor="'rgba(0,0,0,0)'"
        @close="close" @confirm="confirm">
            <view class="popup">
                <view class="popup-title">{{$i18n.zhToGlobal("年份")}}</view>

                <scroll-view
				style="height: 300px;"
                :scroll-y="true"
                class="year-picker">
                        <view
                        @click="select(item-(-1))"
                        class="year-picker-item"
                        v-for="item in 160">{{ item-(-1) }}</view>
                </scroll-view>
				
                <view style="height: 10px;"></view>
				
            </view>
        </u-popup>
	</view>

</template>

<script>
export default {
	data() {
		return {
            show: false,
            selectedYearIndex: 0,
		}
	},
	onLoad(options) {
	},
	methods: {
        select(year){
            console.log(year);
            this.close();
            this.$emit('select', year);
        },
        open(){
            this.show = true;
        },
        close(){
            this.show = false;
        },
        confirm() {
            this.close();
            this.$emit('confirm', this.selectedYear);
        },
        bindYearChange(e) {
            this.selectedYearIndex = e.detail.value;
            this.selectedYear = this.years[this.selectedYearIndex];
        }
	}
}
</script>

<style lang="scss" scoped>
.popup{
    padding: 20rpx 40rpx 20rpx 40rpx;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    box-sizing: border-box;
}
.popup-title{
    padding: 20rpx;
    font-size: 34rpx;
    font-weight: bold;
    text-align: left;
    letter-spacing: 2rpx;
}
.year-picker {
    text-align: center;
    padding: 10rpx;
    box-sizing: border-box;
}

.year-picker-item{
    padding: 30rpx;
    border: 1rpx solid #eee;
    // background-color: #f0f0f0;
    border-radius: 10rpx;
    margin-bottom: 30rpx;
    color: #666666;
}

.year-picker-item:active{
    background-color: #eee;
}

</style>

