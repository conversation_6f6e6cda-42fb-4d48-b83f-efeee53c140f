<template>
  <view>

    <view
        class="header"
        id="projectMeetPagesHeader">
      <kt-nav-bar
          :title="title"></kt-nav-bar>
    </view>
    <view class="bg"></view>
    <view class="panel-box">
      <kt-chat-dialog-panel
          v-if="ifa"
          :has-read="self.isSubAccount"
          :copy="self.isSubAccount"
          @userCardClick="userCardClick"
        :height="'calc(100vh - '+headerHeight+'px - '+footerHeight+'px)'"
        :dialog-id="dialogId"
        @load="dialogLoad"
      >
        <template v-slot:messageItem="{item,message}">
          <view v-if="item.type==='projectMeetGift'&&item.content">
            <project-meet-gift-card
            :gift-id="item.content"
            ></project-meet-gift-card>
          </view>
          <view v-if="item.type==='projectMeetGoldTransfer'&&item.content">
            <project-meet-gold-transfer-card
                :number="item.content"
            ></project-meet-gold-transfer-card>
          </view>
        </template>
      </kt-chat-dialog-panel>

      <view
          v-if="dialog.id&&dialog.userAccountId&&!isCustomerService">
        <view
            class="charge-button"
            v-if="userAccount.genderCode==='female'"
            @click="$refs.projectMeetVgpPopup.open()"
        >{{$i18n.zhToGlobal('为她充值')}}
        </view>
        <view class="charge-button"
              v-if="userAccount.genderCode==='male'"
              @click="$refs.projectMeetVgpPopup.open()">
          {{$i18n.zhToGlobal('为他充值')}}
        </view>
      </view>

        <view
            v-if="dialog.id&&dialog.userAccountId&&self.isSubAccount&&!isCustomerService"
            @click="$refs.setRemarkPopup.open(dialog.userAccountId)"
            class="remark-button">{{$i18n.zhToGlobal('修改备注')}}</view>
    </view>
    <view id="projectMeetPageFooter"
    class="footer">
      <kt-send-input
          :has-user-account="false"
          :has-voice="false"
          :has-gift="true&&!isCustomerService"
          @change="change"
          @send="send"
          @chooseGift="chooseGift()"
          mode="bar"
          ref="sendInput"
      >
        <template v-slot:operateExtra>
          <view style="display: inline-block"
          v-if="!isCustomerService"
          >
            <image
                v-if="!self.isSubAccount"
                @click="openGoldPopup()"
                class="input-icon"
            :src="$kt.file.byPath('projectMeet/icon/gold.png')"
            ></image>
            <view style="display: inline-block;margin-left: 30rpx;vertical-align: top;"
                  >
              <view
                  class="kt-checkbox"
                  @click="$refs.ktLanguageSelectPopup.open()"
              >
                <image
                    :src="$kt.file.byPath('kantboot/icon/language.svg')"
                    class="input-language-icon"
                ></image>
                <text
                    v-if="!targetLanguage.code"
                >{{$i18n.zhToGlobal("不翻译")}}</text>
                <text
                    v-else
                >
                  {{targetLanguage.i18nName}}
                </text>
              </view>
            </view>
          </view>
        </template>
      </kt-send-input>

    </view>

    <project-meet-vgp-popup
        v-if="dialog.id&&dialog.userAccountId"
        :userAccountId="dialog.userAccountId"
        ref="projectMeetVgpPopup"
    ></project-meet-vgp-popup>

    <project-meet-gift-popup
      ref="projectMeetGiftPopup"
    ></project-meet-gift-popup>

    <project-meet-gold-transfer-popup
    ref="projectMeetGoldTransferPopup"
    ></project-meet-gold-transfer-popup>


    <project-meet-user-info-popup
        ref="usPopup"
    ></project-meet-user-info-popup>

    <project-meet-points-popup
        :hasSelfNumber="false"
        ref="projectMeetPointsPopup"
        :user-account-id="self.id"
    ></project-meet-points-popup>

    <project-meet-vip-popup
    ref="projectMeetVipPopup"
    ></project-meet-vip-popup>

    <project-meet-gold-popup
    ref="projectMeetGoldPopup"
    ></project-meet-gold-popup>


    <kt-set-remark-popup
    ref="setRemarkPopup"
    ></kt-set-remark-popup>

    <kt-language-select-popup
        ref="ktLanguageSelectPopup"
        :reset="false"
        @select="selectLanguage"
    >
      <template
          v-slot:topLeft
      >
        <view
            @click="toUnTranslate()"
            class="top-left-btn">{{$i18n.enToGlobal("Cancel translation")}}</view>
      </template>
    </kt-language-select-popup>
    </view>

</template>

<script>
import ProjectMeetGiftCard from "@/pages/project-make-friends-pages/project-meet-gift-card/project-meet-gift-card.vue";

export default {
  components: {ProjectMeetGiftCard},
  data() {
    return {
      title: this.$i18n.zhToGlobal("对话"),
      self:{
        isSubAccount:false
      },
      isCustomerService:false,
      dialogId:"",
      headerHeight:"",
      footerHeight:"",
      dialog:{},
      userAccount:{},
      targetLanguage: {
        code: "",
      },
      token:"",
      ifa:true,
      unreadCount: 0,

    };
  },
  async onLoad(options) {
    if(this.getUrlParameter("token")){
      await this.$kt.userAccount.requestSelf();
      this.self = this.$kt.userAccount.getSelf();
      this.ifa = false;
      setTimeout(()=>{
        this.ifa = true;
      },100);

    }
    this.dialogId = options.dialogId;
    this.userAccountId = options.userAccountId;
    if (this.userAccountId) {
      await this.getChatByUserAccountId();
    }
    this.getById();



    // this.$refs.projectMeetGiftPopup.open();
  },
  onUnload() {
    this.ifa = false;
    setTimeout(()=>{
      this.ifa = true;
    },10);
  },
  async mounted() {
    this.$kt.event.on("FunctionalChatDialogMessage.handleRelationship",(res)=>{
      // alert(JSON.stringify(res));
      this.unreadCount=res.userAccountRelationship.unreadCount;


      setTimeout(()=>{
        if(!this.unreadCount){
          this.title = this.$i18n.zhToGlobal("对话");

          // // 获取现在的标题-uniapp
          uni.setNavigationBarTitle({
            title: this.$i18n.zhToGlobal("对话")
          });
        }else{
          this.title = this.$i18n.zhToGlobal("新消息") + "（" + this.unreadCount + "）";

          // // 获取现在的标题-uniapp
          uni.setNavigationBarTitle({
            title: this.$i18n.zhToGlobal("新消息") + "（" + this.unreadCount + "）"
          });
        }

      },1000);


      this.$kt.event.on("translate:balanceNotEnough",()=>{
        this.$refs.projectMeetPointsPopup.open(this.self.id);
      });



    });
    await this.$kt.userAccount.requestSelf();
    this.self = this.$kt.userAccount.getSelf();

    // 获取navBarPostDetail的高度
    this.$nextTick(() => {

      uni.createSelectorQuery()
          .select('#projectMeetPagesHeader')
          .boundingClientRect((res) => {
            this.headerHeight = res.height
          }).exec();

      uni.createSelectorQuery()
          .select('#projectMeetPageFooter')
          .boundingClientRect((res) => {
            this.footerHeight = res.height
          }).exec();

    });


  },
  methods: {
    getUrlParameter(name) {
      name = name.replace(/[\[\]]/g, "\\$&");
      let regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
          results = regex.exec(window.location.href);
      if (!results) return null;
      if (!results[2]) return '';
      return decodeURIComponent(results[2].replace(/\+/g, " "));
    },
    selectLanguage(res){
      this.targetLanguage = res;
      this.$refs.ktLanguageSelectPopup.close();
    },
    toUnTranslate(){
      this.targetLanguage = {
        code:""
      };
      this.$refs.ktLanguageSelectPopup.close();
    },
    getIsCustomerService(){
      this.$request.post("/functional-chat-web/dialog/isCustomerService",{
        data:{
          userAccountId:this.dialog.userAccountId
        }
      }).then((res)=>{
        this.isCustomerService = res.data;
      }).catch((res)=>{
        this.isCustomerService = false;
      })
    },
    userCardClick(userAccount) {
      if (this.$kt.style.detectDeviceType()==='pc') {
        this.$refs.usPopup.open(userAccount.id);
        // this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info");
      } else {
        this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info?userAccountId=" + userAccount.id);
      }
    },
    openGoldPopup(){
      this.$refs.projectMeetGoldTransferPopup.open(this.userAccount.id);
    },
    chooseGift() {
      this.$refs.projectMeetGiftPopup.open(this.userAccount.id);
    },
    change(){
      // 获取navBarPostDetail的高度
      this.$nextTick(() => {

        uni.createSelectorQuery()
            .select('#projectMeetPagesHeader')
            .boundingClientRect((res) => {
              this.headerHeight = res.height
        }).exec();

        uni.createSelectorQuery()
            .select('#projectMeetPageFooter')
            .boundingClientRect((res) => {
              this.footerHeight = res.height
        }).exec();

      });

      setTimeout(()=>{
        uni.createSelectorQuery()
            .select('#projectMeetPagesHeader')
            .boundingClientRect((res) => {
              this.headerHeight = res.height
            }).exec();

        uni.createSelectorQuery()
            .select('#projectMeetPageFooter')
            .boundingClientRect((res) => {
              this.footerHeight = res.height
            }).exec();
      },100);

      setTimeout(()=>{
        uni.createSelectorQuery()
            .select('#projectMeetPagesHeader')
            .boundingClientRect((res) => {
              this.headerHeight = res.height
            }).exec();

        uni.createSelectorQuery()
            .select('#projectMeetPageFooter')
            .boundingClientRect((res) => {
              this.footerHeight = res.height
            }).exec();
      },1000);

    },
    // /functional-chat-web/chatDialog/getByUserAccountId
    async getChatByUserAccountId() {
      await this.$request.post("/functional-chat-web/dialog/getOneToOneByUserAccountId", {
        data: {userAccountId: this.userAccountId}
      }).then(res => {
        this.dialog = res.data;
        this.dialogId = res.data.id;
      }).catch(err => {
        this.$kt.toast.error(err);
      });
    },
    send(param){
      if(this.targetLanguage.code){
        param.identifyCode = "auto";
        param.targetLanguageCode = this.targetLanguage.code;
      }
      this.$refs.sendInput.toLoading();
      // /fp-community-web/post/push
      this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf",{
        data:{
          items:[param],
          dialogId:this.dialogId
        }
      }).then((res)=>{
        this.$refs.sendInput.clear();
        this.$refs.sendInput.toNone();
        this.$kt.userAccount.requestSelf();
      }).catch((res)=>{
        if(res.stateCode === 'balanceNotEnough'){
          setTimeout(()=>{
            this.$refs.projectMeetPointsPopup.open(this.self.id);
          },1000);

        }
        if(res.stateCode === 'onlyVipCanSendImage'||
          res.stateCode === 'onlyVipCanSendVideo'
        ){
          setTimeout(()=>{
            this.$refs.projectMeetVipPopup.open(this.self.id);
          },1000);
        }
        if(res.stateCode === 'goldNotEnough'){
          setTimeout(()=>{
            this.$refs.projectMeetGoldPopup.open(this.self.id);
          },1000);
        }
        uni.showToast({
          title: res.errMsg,
          icon:"none"
        });
        this.$refs.sendInput.toNone();
      })
    },
    dialogLoad(dialog) {
      this.dialog = dialog;
      this.$kt.userAccount.getById(dialog.userAccountId).then((userAccount) => {
        this.userAccount = userAccount;
      });
      this.getIsCustomerService();
    },
  },
}
</script>

<style lang="scss" scoped>
.bg{
  position: fixed;
  top:0;
  left:0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-color: #f0f0f0;
}

.footer{
  position: fixed;
  bottom: 0;
  left:0;
  width: 100%;
  z-index:9999;
  background-color: #FFFFFF;
}

.header{
  background-color: #FFFFFF;
}

.panel-box{
  position: relative;
  .charge-button{
    position: absolute;
    top:20rpx;
    right:0;
    z-index: 10000;
    color: #FFFFFF;
    font-size: 28rpx;
    background-color: #F6A496;
    padding: 10rpx 10rpx 10rpx 30rpx;
    border-radius: 30rpx 0 0 30rpx;
  }

  .remark-button{
    position: absolute;
    top:90rpx;
    right:0;
    z-index: 10000;
    color: #FFFFFF;
    font-size: 28rpx;
    background-color: rgba(0,0,0,.8);
    padding: 10rpx 10rpx 10rpx 30rpx;
    border-radius: 30rpx 0 0 30rpx;
  }
}
.charge-button:active{
  opacity: 0.8;
}

.input-icon{
  width: 40rpx;
  height: 40rpx;
  margin-left: 30rpx;
  vertical-align: top;
  margin-top: -7rpx;
}


.kt-checkbox{
  position: absolute;
  display: inline-block;
  text-align: center;
  border-radius: 25rpx;
  cursor: pointer;
  right:20rpx;
  bottom: 35rpx;
  font-size: 28rpx;
  border: 1rpx solid #f0f0f0;
  padding: 0 20rpx;
  max-width: 160rpx;
  // 超出省略号
  overflow: hidden;
  text-overflow: ellipsis;
  .input-language-icon{
    width: 30rpx;
    height: 30rpx;
    vertical-align: middle;
    margin-right: 10rpx;
    margin-top: -10rpx;
  }
}

.top-left-btn{
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #cccccc;
  cursor: pointer;
}

.top-left-btn:active{
  background-color: #f0f0f0;
}
</style>

