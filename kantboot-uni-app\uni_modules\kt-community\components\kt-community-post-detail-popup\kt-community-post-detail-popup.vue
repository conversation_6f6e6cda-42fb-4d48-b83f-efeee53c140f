<template>
  <view>
    <kt-popup
        z-index="999999999"
    ref="ktPopup"
    >
      <view :class="clazz.box">
        <view class="title">{{$i18n.zhToGlobal("帖子详情")}}</view>
        <kt-community-post-detail-panel
            v-if="postId"
            :height="height"
            :post-id="postId"
        ></kt-community-post-detail-panel>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  props: {
    height: {
      type: String,
      default: "100vh",
    }
  },
  data() {
    return {
      postId: null,
      clazz: {
        box: this.$kt.style.toggleClass("box"),
      },
    };
  },
  methods: {
    open(postId) {
      this.postId = postId;
      this.$refs.ktPopup.open();
    },
    close() {
      this.$refs.ktPopup.close();
    }
  },

}
</script>

<style lang="scss" scoped>
.box{
  position: relative;
  height: 100%;
  width: 100%;
  background-color: #fff;
  padding: 20rpx 20rpx 10rpx 10rpx;
  border-radius: 20rpx 20rpx 0 0;
}

.title{
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.box-mode-device-pc{
  border-radius: 20rpx;
}

</style>
