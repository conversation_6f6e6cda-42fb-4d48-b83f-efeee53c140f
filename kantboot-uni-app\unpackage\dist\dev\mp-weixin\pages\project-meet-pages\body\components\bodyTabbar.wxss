@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box-box.data-v-a7f4aac0 {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  z-index: 999;
  text-align: center;
}
.box-box-mode-device-mobile.data-v-a7f4aac0, .box-box-mode-device-pad.data-v-a7f4aac0 {
  text-align: center;
  white-space: nowrap;
  overflow-x: auto;
  scrollbar-width: thin;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9) 0%, white 100%);
  box-shadow: 0 10rpx 20rpx rgba(118, 118, 118, 0.2);
}
.box-box-mode-device-mobile .box.data-v-a7f4aac0, .box-box-mode-device-pad .box.data-v-a7f4aac0 {
  width: 25%;
  text-align: center;
}
.box-box-mode-device-mobile .box .icon.data-v-a7f4aac0, .box-box-mode-device-pad .box .icon.data-v-a7f4aac0 {
  width: 40rpx;
  height: 40rpx;
}
.box-box-mode-device-pc.data-v-a7f4aac0 {
  scrollbar-color: #000000 #FFFFFF;
  width: 240px;
  height: 100%;
  padding: 30rpx 30rpx 30rpx 60rpx;
  box-sizing: border-box;
}
.box-box-mode-device-pc .box.data-v-a7f4aac0 {
  display: block;
  cursor: pointer;
  width: 100%;
  text-align: left;
}
.box-box-mode-device-pc .box.data-v-a7f4aac0:hover {
  opacity: 0.8;
}
.box-box-mode-device-pc .icon-box.data-v-a7f4aac0 {
  vertical-align: top;
  display: inline-block;
  margin-right: 10px;
}
.box-box-mode-device-pc .icon.data-v-a7f4aac0 {
  width: 25px;
  height: 25px;
}
.box-box-mode-device-pc .icon-selected.data-v-a7f4aac0 {
  width: 25px;
  height: 25px;
}
.box-box-mode-device-pc .text.data-v-a7f4aac0 {
  margin-top: 2rpx;
  vertical-align: top;
  display: inline-block;
}
.box-box-mode-color-scheme-dark.data-v-a7f4aac0 {
  background-color: #191919;
}
.box-box-mode-color-scheme-dark .box .icon-selected.data-v-a7f4aac0 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.box-box-mode-color-scheme-dark .box .text-selected.data-v-a7f4aac0 {
  color: #FFFFFF;
}
.data-v-a7f4aac0::-webkit-scrollbar {
  display: none;
}
.box.data-v-a7f4aac0 {
  position: relative;
  text-align: center;
  display: inline-block;
  width: 150rpx;
}
.unread-tag.data-v-a7f4aac0 {
  position: absolute;
  right: 20rpx;
  border-radius: 20rpx;
  background-color: #FF0000;
  color: #FFFFFF;
  font-size: 20rpx;
  text-align: center;
  padding: 0 10rpx 0 10rpx;
}
.icon.data-v-a7f4aac0 {
  width: 40rpx;
  height: 40rpx;
}
.text.data-v-a7f4aac0 {
  font-size: 20rpx;
  color: #AAAAAA;
}
.icon-selected.data-v-a7f4aac0 {
  width: 40rpx;
  height: 40rpx;
}
.text-selected.data-v-a7f4aac0 {
  font-size: 20rpx;
  color: #000000;
}
.push-btn.data-v-a7f4aac0 {
  width: 100%;
  background-color: #292929;
  padding: 20rpx;
  box-sizing: border-box;
  color: #FFFFFF;
  text-align: center;
  cursor: pointer;
  border-radius: 30rpx;
  letter-spacing: 2rpx;
}
