@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.pay-result.data-v-9d0cf2fc {
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}
.pay-result .pay-result-amount.data-v-9d0cf2fc {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
}
.pay-result .pay-result-amount .pay-result-amount-symbol.data-v-9d0cf2fc {
  font-size: 30rpx;
  font-weight: bold;
  margin-right: 10rpx;
}
.pay-result .pay-result-amount .pay-result-amount-value.data-v-9d0cf2fc {
  font-size: 50rpx;
  font-weight: bold;
}
.pay-result .pay-result-currency.data-v-9d0cf2fc {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 24rpx;
  color: #999999;
  background-color: #F0F0F0;
  padding: 2rpx 10rpx 2rpx 10rpx;
  border-radius: 20rpx;
}
.box.data-v-9d0cf2fc {
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}
.box .btn.data-v-9d0cf2fc {
  position: relative;
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  text-align: center;
  line-height: 80rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.box .btn .btn-icon.data-v-9d0cf2fc {
  position: absolute;
  left: 20rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
}
.box .btn .btn-icon .btn-icon-image.data-v-9d0cf2fc {
  width: 100%;
  height: 100%;
  -webkit-filter: invert(1);
          filter: invert(1);
}
.box .btn .btn-text.data-v-9d0cf2fc {
  display: inline-block;
  margin-left: 20rpx;
  color: #FFFFFF;
}
.box .wechat-pay-btn.data-v-9d0cf2fc {
  background-color: #1AAD19;
}
.box .alipay-pay-btn.data-v-9d0cf2fc {
  background-color: #1678FF;
}
.box .paypal-pay-btn.data-v-9d0cf2fc {
  background-color: #000;
}
.box .paypal-pay-btn .btn-icon-image.data-v-9d0cf2fc {
  width: 100%;
  height: 100%;
}
.box-mode-device-pc.data-v-9d0cf2fc {
  cursor: pointer;
}
.loading-box.data-v-9d0cf2fc {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F0F0F0;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.loading-box .loading-image.data-v-9d0cf2fc {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  opacity: 0.6;
  -webkit-filter: invert(1);
          filter: invert(1);
  -webkit-animation: loading-image-animation-data-v-9d0cf2fc 1s linear infinite;
          animation: loading-image-animation-data-v-9d0cf2fc 1s linear infinite;
}
@-webkit-keyframes loading-image-animation-data-v-9d0cf2fc {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes loading-image-animation-data-v-9d0cf2fc {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.loading-box .loading-text.data-v-9d0cf2fc {
  font-size: 28rpx;
  color: #999999;
}
.success-box.data-v-9d0cf2fc {
  text-align: center;
}
.success-logo.data-v-9d0cf2fc {
  width: 200rpx;
  height: 200rpx;
  -webkit-filter: invert(1);
          filter: invert(1);
  -webkit-animation: success-logo-animation-data-v-9d0cf2fc 1s linear;
          animation: success-logo-animation-data-v-9d0cf2fc 1s linear;
}
.success-text.data-v-9d0cf2fc {
  margin-top: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
}
@-webkit-keyframes success-logo-animation-data-v-9d0cf2fc {
from {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg);
}
to {
    -webkit-transform: rotateY(360deg);
            transform: rotateY(360deg);
}
}
@keyframes success-logo-animation-data-v-9d0cf2fc {
from {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg);
}
to {
    -webkit-transform: rotateY(360deg);
            transform: rotateY(360deg);
}
}
