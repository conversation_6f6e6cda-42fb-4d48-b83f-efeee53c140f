<view class="data-v-27439abf"><kt-popup bind:close="__e" bind:confirm="__e" vue-id="7d9f83b2-1" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" class="data-v-27439abf vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-27439abf',clazz.popup]}}"><view class="popup-title data-v-27439abf">{{$root.g0}}</view><block wx:if="{{false}}"><view class="picker data-v-27439abf"><view class="bl-box data-v-27439abf"><view style="text-align:center;" class="data-v-27439abf"><view style="display:inline-block;position:relative;" class="data-v-27439abf"><kt-avatar class="avatar data-v-27439abf" vue-id="{{('7d9f83b2-2')+','+('7d9f83b2-1')}}" size="220rpx" src="{{viewSrc?viewSrc:$root.g1}}" bind:__l="__l"></kt-avatar><button style="width:calc(100% + 10rpx);height:100%;position:absolute;top:0;left:0;opacity:0;" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['chooseAvatarInMpWeixin',['$event']]]]]}}" bindchooseavatar="__e" class="data-v-27439abf">微信头像</button></view></view><view style="height:30rpx;" class="data-v-27439abf"></view><block wx:if="{{false}}"><view style="padding:0 50rpx 0 50rpx;box-sizing:border-box;" class="data-v-27439abf"><input class="input nickname-input data-v-27439abf" disabled="{{true}}" type="text" adjust-position="{{false}}" placeholder="{{$root.g2}}" data-event-opts="{{[['blur',[['nicknameBlur',['$event']]]],['focus',[['nicknameFocus',['$event']]]],['input',[['__set_model',['$0','nickname','$event',[]],['userAccount']]]]]}}" value="{{userAccount.nickname}}" bindblur="__e" bindfocus="__e" bindinput="__e"/></view></block></view><block wx:if="{{false}}"><view style="height:30px;" class="data-v-27439abf"></view></block><block wx:if="{{false}}"><view class="data-v-27439abf"><view class="gender-box data-v-27439abf"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['gender-item','data-v-27439abf',(userAccount.genderCode=='male')?'gender-item-selected':'']}}" style="{{(userAccount.genderCode=='male'?'background-color: #409EFF;':'')}}" bindtap="__e">{{$root.g3+''}}</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['gender-item','data-v-27439abf',(userAccount.genderCode=='female')?'gender-item-selected':'']}}" style="{{(userAccount.genderCode=='female'?'background-color: rgba(255,0,0,.6);':'')}}" bindtap="__e">{{$root.g4+''}}</view></view></view></block><block wx:if="{{!isNicknameFocus}}"><view style="height:30px;" class="data-v-27439abf"></view></block><block wx:if="{{!isNicknameFocus}}"><view style="padding:0 50rpx 0 50rpx;" class="data-v-27439abf"><view class="input-box data-v-27439abf" style="position:relative;"><block wx:if="{{false}}"><textarea class="input textarea data-v-27439abf" maxlength="100" adjust-position="{{false}}" type="nickname" placeholder="{{$root.g5}}" data-event-opts="{{[['input',[['__set_model',['$0','introduction','$event',[]],['userAccount']]]]]}}" value="{{userAccount.introduction}}" bindinput="__e"></textarea></block></view></view></block></view></block><view class="picker data-v-27439abf" style="text-align:left;"><scroll-view style="height:calc(100vh - 600rpx);" scroll-y="{{true}}" class="data-v-27439abf"><project-meet-set-user-info-panel vue-id="{{('7d9f83b2-3')+','+('7d9f83b2-1')}}" has-submit-button="{{false}}" data-ref="setUserInfoPanel" data-event-opts="{{[['^change',[['changeUserInfo']]]]}}" bind:change="__e" class="data-v-27439abf vue-ref" bind:__l="__l"></project-meet-set-user-info-panel></scroll-view></view><view style="height:10px;" class="data-v-27439abf"></view><block wx:if="{{!isNicknameFocus}}"><kt-button bind:click="__e" vue-id="{{('7d9f83b2-4')+','+('7d9f83b2-1')}}" data-ref="confirmBtn" data-event-opts="{{[['^click',[['submit']]]]}}" class="data-v-27439abf vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g6+''}}</kt-button></block><view style="height:10px;" class="data-v-27439abf"></view><kt-keyboard-size vue-id="{{('7d9f83b2-5')+','+('7d9f83b2-1')}}" data-ref="keyboardSize" class="data-v-27439abf vue-ref" bind:__l="__l"></kt-keyboard-size></view></kt-popup></view>