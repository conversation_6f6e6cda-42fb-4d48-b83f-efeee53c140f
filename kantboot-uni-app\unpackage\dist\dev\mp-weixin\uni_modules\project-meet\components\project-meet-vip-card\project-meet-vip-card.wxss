@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-140184fb {
  display: inline-block;
  width: 200rpx;
  height: 230rpx;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3) 0%, white 100%);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 10rpx;
  box-sizing: border-box;
  border: 2rpx solid #F0F0F0;
  border-radius: 20rpx;
}
.box .name.data-v-140184fb {
  font-size: 24rpx;
  color: #000000;
  text-align: center;
  margin-top: 20rpx;
}
.box .price.data-v-140184fb {
  font-size: 42rpx;
  color: #000000;
  text-align: center;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  font-weight: bold;
}
.box .tip.data-v-140184fb {
  color: #999999;
  background-color: #f0f0f0;
  border-radius: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
  text-align: center;
}
.box-selected.data-v-140184fb {
  border: 2rpx solid #FF9900;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, rgba(255, 153, 0, 0.1) 0%, rgba(255, 153, 0, 0.5) 100%);
}
.box-selected .name.data-v-140184fb {
  color: #ff5b5b;
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);
}
.box-selected .price.data-v-140184fb {
  color: #ff5b5b;
  text-shadow: 0 0 10rpx rgba(255, 0, 0, 0.2);
}
.box-selected .tip.data-v-140184fb {
  color: #FFFFFF;
  background-color: rgba(255, 255, 255, 0.1);
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);
}
.box-mode-device-pc.data-v-140184fb {
  width: 300rpx;
}
