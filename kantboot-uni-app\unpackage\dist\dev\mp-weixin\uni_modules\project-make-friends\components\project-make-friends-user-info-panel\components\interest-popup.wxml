<view class="data-v-ecae4ae0"><u-popup vue-id="a91a0f14-1" show="{{show}}" mode="bottom" bgColor="rgba(0,0,0,0)" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-ecae4ae0" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup data-v-ecae4ae0"><view class="popup-title data-v-ecae4ae0">{{$root.g0}}</view><scroll-view class="picker data-v-ecae4ae0" style="max-height:500px;" scroll-y="{{true}}"><view class="bl-box data-v-ecae4ae0"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__"><block wx:if="{{item.$orig.name}}"><view data-event-opts="{{[['tap',[['select',['$0'],[[['list','',__i0__]]]]]]]}}" class="{{['bl-box-item','data-v-ecae4ae0',(item.m0)?'bl-box-item-selected':'']}}" bindtap="__e">{{item.g1}}</view></block></block></view></scroll-view><view style="height:10px;" class="data-v-ecae4ae0"></view><kt-button bind:click="__e" vue-id="{{('a91a0f14-2')+','+('a91a0f14-1')}}" data-ref="confirmBtn" data-event-opts="{{[['^click',[['submit']]]]}}" class="data-v-ecae4ae0 vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g2}}</kt-button><view style="height:10px;" class="data-v-ecae4ae0"></view></view></u-popup></view>