<template>
  <view>
    <kt-popup ref="ktPopup"
    :overlayClose="false"
    >
      <view class="set-user-box">
        <view class="set-user-box-header">
          <view class="set-user-box-icon">
            <image
                class="set-user-box-icon-img"
              :src="$kt.file.byPath('projectMeet/icon/userInfo.svg')"></image>
          </view>
          <view class="set-user-box-text">
            {{$i18n.zhToGlobal("信息初始化")}}
          </view>
        </view>
        <projectMeetSetUserInfoPanel
            @submitSuccess="submitSuccess"
        :height="'calc(100vh - 200rpx)'"
        ></projectMeetSetUserInfoPanel>
      </view>
    </kt-popup>
  </view>
</template>
<script>
import projectMeetSetUserInfoPanel from "../project-meet-set-user-info-panel/project-meet-set-user-info-panel.vue";
export default {
  components: { projectMeetSetUserInfoPanel },
  data() {
    return {};
  },
  async mounted() {
    await this.$kt.userAccount.requestSelf();
    try{
      if(this.$kt.userAccount.getIsLogin() &&
          (this.$kt.userAccount.getSelf().genderCode==null ||
              this.$kt.userAccount.getSelf().genderCode==='')
      ) {
        this.open();
      }
    }catch (e) {

    }

    this.$kt.event.on("login:success", () => {
      if(this.$kt.userAccount.getIsLogin() &&
          (this.$kt.userAccount.getSelf().genderCode==null ||
              this.$kt.userAccount.getSelf().genderCode==='')

      ) {
        this.open();
      }
    });
  },
  methods: {
    open() {
      setTimeout(()=>{
        this.$refs.ktPopup.open();
      },10);
    },
    close() {
      setTimeout(()=>{
        this.$refs.ktPopup.close();
      },10);
    },
    submitSuccess() {
      this.$emit("submitSuccess");
    },
  },
};
</script>

<style lang="scss" scoped>
.set-user-box {
  width: 400px;
  min-height: 500px;
  background-color: white;
  border-radius: 40rpx;

  .set-user-box-header{
    padding: 20rpx;
    .set-user-box-icon{
      display: inline-block;
      .set-user-box-icon-img{
        width: 50rpx;
        height: 50rpx;
        display: inline-block;
      }
    }
    vertical-align: top;
  }
}

.set-user-box::-webkit-scrollbar {
  display: none;
}

.set-user-box-text{
  display: inline-block;
  vertical-align: top;
  margin-left: 10rpx;
}

</style>
