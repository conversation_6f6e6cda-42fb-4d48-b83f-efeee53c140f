@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-info-card.data-v-a8483a56 {
  position: relative;
}
.user-info-card-nickname.data-v-a8483a56 {
  display: inline-block;
  font-weight: bold;
  font-size: 32rpx;
  letter-spacing: 1px;
  vertical-align: top;
}
.user-info-card-age.data-v-a8483a56 {
  display: inline-block;
  font-size: 28rpx;
  vertical-align: top;
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 15rpx;
}
.user-info-card-gender.data-v-a8483a56 {
  display: inline-block;
  margin-left: 25rpx;
  vertical-align: top;
  margin-top: 5rpx;
  width: 30rpx;
  height: 30rpx;
}
.user-info-card-gender .user-info-card-gender-icon.data-v-a8483a56 {
  width: 100%;
  height: 100%;
}
.user-info-card-intro.data-v-a8483a56 {
  font-size: 28rpx;
  color: #333333;
  letter-spacing: 1px;
}
.tag-v.data-v-a8483a56 {
  background-color: #fff4f0;
  display: inline-block;
  vertical-align: top;
  margin-right: 20rpx;
  height: 50rpx;
  line-height: 50rpx;
  margin-bottom: 20rpx;
  padding: 0 20rpx 0 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #f5ac36;
}
.intro-in.data-v-a8483a56 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.personal-introduction.data-v-a8483a56 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.container-mode-color-scheme-dark .user-info-card .user-info-card-intro-1.data-v-a8483a56 {
  color: #cccccc;
}
.container-mode-color-scheme-dark .user-info-card .user-info-card-nickname.data-v-a8483a56 {
  color: #cccccc;
}
.container-mode-color-scheme-dark .user-info-card .user-info-card-age.data-v-a8483a56 {
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
}
.container-mode-color-scheme-dark .user-info-card .user-info-card-intro.data-v-a8483a56 {
  color: #999;
}
.container-mode-color-scheme-dark .user-info-card .user-info-card-intro .user-info-card-intro-icon.data-v-a8483a56 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
