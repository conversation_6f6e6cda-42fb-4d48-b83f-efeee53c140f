<template>
  <view>
    <kt-popup
    ref="ktPopup">
      <view
      :class="clazz.box">
        <view class="title">{{$i18n.zhToGlobal("礼物赠送")}}</view>
        <view class="my-gold">
          <view class="my-gold-icon">
            <image
                class="my-gold-icon-image"
            :src="$kt.file.byPath('projectMeet/icon/gold.png')"
            ></image>
          </view>
          <view class="my-gold-text">
            {{$i18n.zhToGlobal("我的金币")}}{{": "}}{{self.projectMeetGold||0}}
          </view>
        </view>
        <view class="grid">
          <swiper style="height: 500rpx">
            <swiper-item>
              <view
                  v-for="item in list.slice(0, 8)"
                  @click="select(item)"
                  class="grid-item"
                  :class="{'grid-item-selected': selected.id === item.id}"
              >
                <view class="grid-item-image">
                  <image
                      class="grid-item-image-image"
                      mode="aspectFill"
                      :src="$kt.file.visit(item.fileIdOfCover)"
                  ></image>
                </view>
                <view class="grid-item-name">
                  <kt-one-line-text
                      :text="item.name"
                      :font-size="'25rpx'"
                  ></kt-one-line-text>
                </view>
                <view class="grid-item-price">
                  {{item.goldNumber}}{{" "}}{{$i18n.zhToGlobal("金币")}}
                </view>
              </view>
            </swiper-item>
            <swiper-item>
              <view
                  v-for="item in list.slice(8, 16)"
                  @click="select(item)"
                  class="grid-item"
                  :class="{'grid-item-selected': selected.id === item.id}"
              >
                <view class="grid-item-image">
                  <image
                      class="grid-item-image-image"
                      mode="aspectFill"
                      :src="$kt.file.visit(item.fileIdOfCover)"
                  ></image>
                </view>
                <view class="grid-item-name">
                  <kt-one-line-text
                      :text="$kt.util.firstLetterUpper(item.name)"
                      width="100rpx"
                      :font-size="'25rpx'"
                  ></kt-one-line-text>
                </view>
                <view class="grid-item-price">
                  {{item.goldNumber}}{{" "}}{{$i18n.zhToGlobal("金币")}}
                </view>
              </view>
            </swiper-item>

          </swiper>
        </view>
        <view style="height: 30rpx"></view>
        <kt-button
        ref="ktButton"
        @click="give()"
        >{{$i18n.zhToGlobal("赠送")}}</kt-button>
        <view style="height: 30rpx"></view>
        <view
            @click="toRecharge"
            class="recharge-btn">{{$i18n.zhToGlobal("充值金币")}}</view>
        <view style="height: 30rpx"></view>
      </view>
    </kt-popup>

    <project-meet-gift-show
        ref="projectMeetGiftShow"
    ></project-meet-gift-show>

    <project-meet-gold-popup
        v-if="self.id"
        @paySuccess="getSelf"
        :user-account-id="self.id"
      ref="projectMeetGoldPopup"
    ></project-meet-gold-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      },
      list:[],
      self:{
        id:"",
        projectMeetGold: 0,
      },
      selected: {
        goodsNumber: 1,
        fileIdOfEffect: "",
      },
      userAccountIdOfGetter: "",
      projectMeetOrderId: "",
    };
  },
  mounted() {
    // this.open();
  },
  methods: {
    select(item) {
      this.selected = item;
      try {
        this.$forceUpdate();
      } catch (e) {
      }
    },
    toRecharge() {
      this.$refs.projectMeetGoldPopup.open();
    },
    getSelf(){
      this.$kt.userAccount.requestSelf().then((res) => {
        this.self.id = res.data.id;
        this.self.projectMeetGold = res.data.projectMeetGold;
        try {
          this.$forceUpdate();
        } catch (e) {
        }
      });
    },
    /**
     * 打开礼物赠送弹窗
     */
    open(userAccountIdOfGetter) {
      this.userAccountIdOfGetter = userAccountIdOfGetter || "";
      this.selected = {
        id: "",
        goodsNumber: "",
        fileIdOfEffect: "",
      };
      this.$refs.ktPopup.open();
      this.createOrder();
      this.getSelf();
      this.getAll();
    },
    close() {
      this.$refs.ktPopup.close();
    },
    async createOrder(){
      setTimeout(()=>{
        this.$refs.ktButton.loading(null,9999999);
      },1);
      await this.$kt.userAccount.requestSelf();
      this.self = this.$kt.userAccount.getSelf();
      if(this.self.isSubAccount){
        this.$refs.ktButton.toNone();
        return;
      }
      // /project-meet-web/gift/createOrder
      this.$kt.request.post("/project-meet-web/gift/createOrder", {
        data: {
          userAccountIdOfGetter: this.userAccountIdOfGetter,
        },
      }).then((res) => {
        this.projectMeetOrderId = res.data;
        this.$refs.ktButton.toNone();
      }).catch((err) => {
        this.$refs.ktButton.error(err.errMsg);
      });
    },
    give() {
      if(this.self.isSubAccount){
        if (!this.selected.id) {
          this.$refs.ktButton.error(this.$i18n.zhToGlobal("未选择礼物"));
          return;
        }

        this.$kt.request.post("/project-meet-web/gift/subGive", {
          data: {
            id: this.selected.id,
            userAccountIdOfGetter: this.userAccountIdOfGetter,
            // projectMeetOrderId: this.projectMeetOrderId,
          },
        }).then((res) => {
          this.$refs.ktButton.success(this.$i18n.zhToGlobal("赠送成功"));
          setTimeout(()=>{
            this.close();
          },1000);
          setTimeout(()=>{
            this.$refs.projectMeetGiftShow.open(this.selected.fileIdOfEffect);
          },1);
          // 重新获取金币
          this.getSelf();
        }).catch((err) => {
          this.$refs.ktButton.error(err.errMsg);
        });
        return;
      }

      if(!this.projectMeetOrderId){
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("订单未创建完成"));
        return;
      }
      if (!this.selected.id) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未选择礼物"));
        return;
      }
      // 余额是否足够
      if (this.self.projectMeetGold < this.selected.goldNumber) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("金币不足"));
        return;
      }

      this.$refs.ktButton.loading(null,9999999);
      // this.$refs.projectMeetGiftShow.open(this.selected.fileIdOfEffect);
      // /project-meet-web/gift/give
      this.$kt.request.post("/project-meet-web/gift/give", {
        data: {
          giftId: this.selected.id,
          // userAccountIdOfGetter: this.userAccountIdOfGetter,
          projectMeetOrderId: this.projectMeetOrderId,
        },
      }).then((res) => {
        this.$refs.ktButton.success(this.$i18n.zhToGlobal("赠送成功"));
        setTimeout(()=>{
          this.close();
        },1000);
        setTimeout(()=>{
          this.$refs.projectMeetGiftShow.open(this.selected.fileIdOfEffect);
        },1);
        // 重新获取金币
        this.getSelf();
      }).catch((err) => {
        this.$refs.ktButton.error(err.errMsg);
      });
    },
    getAll() {
      this.$kt.request.post("/project-meet-web/gift/getAll").then((res) => {
        this.list = res.data;
        try {
         this.$forceUpdate();
        } catch (e) {
        }
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.box{
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  font-size: 20px;
  color: #000000;
  padding: 20rpx;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
}

.title{
  width: 100%;
  text-align: left;
  font-size: 30rpx;
  color: #000000;
  font-weight: bold;
  letter-spacing: 2rpx;
}

.grid {
}

.grid-item {
  display: inline-block;
  width: calc(25% - 20rpx);
  height: 150rpx;
  margin: 40rpx 10rpx 40rpx 10rpx;
  font-size: 25rpx;
  color: #666666;
  text-align: center;
  cursor: pointer;
}

.grid-item:active{
  .grid-item-image{
    transform: scale(.95);
  }
}

.grid-item-selected {
  .grid-item-image {
    box-sizing: border-box;
    background-color: rgba(118,118,118,.07);
    padding: 10rpx;
    border-radius: 20rpx;
  }
}

.grid-item-image {
  display: inline-block;
  width: 100rpx;
  height: 100rpx;
  .grid-item-image-image{
    width: 100%;
    height: 100%;
  }
}

.box-mode-device-pc {
  border-radius: 20rpx;
}

.my-gold {
  position: absolute;
  font-size: 25rpx;
  color: #000000;
  top: 20rpx;
  right: 20rpx;
  .my-gold-icon {
    display: inline-block;
    width: 50rpx;
    height: 50rpx;
    margin-right: 10rpx;
    .my-gold-icon-image {
      width: 100%;
      height: 100%;
    }
  }
  .my-gold-text {
    display: inline-block;
    vertical-align: top;
    margin-top: 10rpx;
    font-weight: bold;
  }
}
.recharge-btn{
  width: 100%;
  height: 60rpx;
  color: #000000;
  text-align: center;
  line-height: 60rpx;
  font-size: 25rpx;
  border-radius: 10rpx;
  cursor: pointer;
}

.recharge-btn:active{
  opacity: .5;
  transform: scale(.95);
}
</style>
