<template>
  <view class="content">
    <view :class="clazz.bg"></view>
    <view v-if="isLogin" :class="clazz.header">
      <view style="display: inline-block;width: calc(100% - 100px);position: relative;">
        <kt-nav-bar background-color="rgba(255,255,255,0)" :title="$i18n.zhToGlobal('为你推荐合适的朋友')"
          :icon="$kt.file.byPath('projectMeet/icon/logo.png')"></kt-nav-bar>
      </view>
    </view>
    <view v-if="isLogin" :class="clazz.viewBox" :style="{
      marginTop: $kt.style.detectDeviceType() === 'mobile' ? '0px' : '50px',
    }">
      <view class="view-left">
        <body-tabbar @openPush="openPush" ref="bodyTabbar" :selected="tabbarSelected"
          @changeTabbar="changeTabbar"></body-tabbar>
      </view>
      <view class="view-center">
        <page-home-meet v-show="tabbarSelected === 'homeMeet'"></page-home-meet>
        <page-community-meet v-show="tabbarSelected === 'communityMeet'"></page-community-meet>
        <page-message-meet v-show="tabbarSelected === 'messageMeet'"></page-message-meet>
        <page-mine-meet v-show="tabbarSelected === 'mineMeet'"></page-mine-meet>
      </view>
      <view :class="clazz.viewRight"></view>

      <view v-if="self.isBanned" class="bg-self-banned">
        <view class="box1">
          <view>
            <image class="logo" :src="$kt.file.byPath('projectMeet/icon/logo.png')"></image>
          </view>
          <view class="text">{{ $i18n.zhToGlobal("此账号正在审核，审核通过后，我们会发送邮件通知您") }}</view>
        </view>
      </view>

    </view>

    <project-meet-no-login></project-meet-no-login>

    <kt-community-post-push-popup @pushSuccess="pushSuccess" :is-show-permission-setting="false"
      ref="projectMeetPostPushPopup"></kt-community-post-push-popup>

    <project-meet-gift-popup></project-meet-gift-popup>

    <project-meet-forget-password-popup ref="projectMeetForgetPasswordPopup"></project-meet-forget-password-popup>

  </view>
</template>

<script>
import bodyTabbar from './components/bodyTabbar.vue'
import pageHomeMeet from './pageComponents/pageHomeMeet.vue'
import pageCommunityMeet from './pageComponents/pageCommunityMeet.vue'
import pageMessageMeet from './pageComponents/pageMessageMeet.vue'
import pageMineMeet from "./pageComponents/pageMineMeet.vue";
import setUserInfo from "@/pages/project-meet-pages/set-user-info/set-user-info.vue";
import alert from "@/uni_modules/uview-ui/libs/config/props/alert";
export default {
  computed: {
    setUserInfo() {
      return setUserInfo
    }
  },
  components: {
    bodyTabbar,
    pageHomeMeet,
    pageMessageMeet,
    pageCommunityMeet,
    pageMineMeet
  },
  created() {

  },
  data() {
    return {
      mineInfo: {},
      tabbarSelected: 'home',
      clazz: {
        header: this.$kt.style.toggleClass("header"),
        bg: this.$kt.style.toggleClass("bg"),
        viewBox: this.$kt.style.toggleClass("view-box"),
        viewRight: this.$kt.style.toggleClass("view-right"),
      },
      self: {
        id: '',
        account: '',
        name: '',
        avatar: '',
        isInternalAccount: false,
        isBanned: false,
        isSetUserInfo: false,
        isSetUserInfoPopupOpen: false,
        isSetUserInfoPopupShow: false,
      },
      isLogin: false
    }
  },
  // 分享给好友
  onShareAppMessage() {
    return {
      path: '/pages/pages-body/into/into?userAccountIdOfInviter=' + this.userAccount.id,
      title: '',
    }
  },
  async onLoad(query) {
    //页面传参tabbarSelected=all
    if (query && query.tabbarSelected) {
      this.tabbarSelected = query.tabbarSelected;
      this.changeTabbar(query.tabbarSelected);
    } else {
      this.changeTabbar("homeMeet");
    }
    this.isLogin = this.$kt.userAccount.getIsLogin();
    if (this.isLogin) {
      this.self = this.$kt.userAccount.getSelf();
      this.into();
    }
    this.$kt.event.on('login:success', () => {
      this.isLogin = this.$kt.userAccount.getIsLogin();
      this.self = this.$kt.userAccount.getSelf();
      this.into();
    });
    this.$kt.event.on("projectMeetChatDialogMessageSend", (dialogId) => {
      this.changeTabbar('messageMeet');
    });
    this.$kt.event.on("projectMeet:forgetPasswordPopupOpen", () => {
      this.$refs.projectMeetForgetPasswordPopup.open();
    });


  },
  async mounted() {
    // this.$refs.ktPopup.open();
    // this.$refs.projectMeetGoldPopup.open();
    let token = this.getUrlParameter("token");
    if (token) {
      await this.$kt.userAccount.requestSelf();
      this.isLogin = this.$kt.userAccount.getIsLogin();
      this.self = this.$kt.userAccount.getSelf();
      this.changeTabbar("messageMeet");
      this.into();
    }




  },
  methods: {
    getUrlParameter(name) {
      name = name.replace(/[\[\]]/g, "\\$&");
      let regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
        results = regex.exec(window.location.href);
      if (!results) return null;
      if (!results[2]) return '';
      return decodeURIComponent(results[2].replace(/\+/g, " "));
    },
    into() {
      if (this.self.isBanned) {
        setTimeout(() => {
          this.$kt.userAccount.setIsLogin(false);
          this.$request.setToken("");
          uni.navigateTo({
            url: '/pages/pages-body/into/into'
          });
        }, 2000);
        return;
      }
      if (this.self.isInternalAccount) {
        uni.navigateTo({
          url: '/pages/project-meet-pages/body/body-emp'
        });
      }
    },
    openPush() {
      this.$refs.projectMeetPostPushPopup.open();
    },
    pushSuccess() {
      this.$refs.projectMeetPostPushPopup.close();
      this.$refs.bodyTabbar.changeTabbar('communityMeet');
    },
    setUserInfoSubmitSuccess() {
      this.$refs.projectMeetSetUserInfoPopup.close();
    },
    changeTabbar(code) {
      this.tabbarSelected = code;
      this.clazz.viewBox = this.$kt.style.toggleClass("view-box-meet");

      this.$forceUpdate();
      this.$kt.event.emit("changeTabbar", code);
      this.$kt.event.emit("changeTabbar:" + code, code);
    }
  },

}
</script>

<style lang="scss" scoped>
.bg {
  position: fixed;
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
  z-index: -1;
}

.bg-mode-color-scheme-light {
  background-color: #ffffff;
}

.bg-mode-color-scheme-dark {
  background-color: #191919;
}



.show {
  opacity: 1;
  z-index: 10;
}

.hide {
  opacity: 0;
  z-index: -1;
}

.content {}

.header {
  display: none;
}

.header-mode-device-pc {
  display: block;
  border-bottom: 1px solid #e5e5e5;
  height: 50px;
  //background-color: #FFFFFF;
  background-color: rgba(255, 255, 255, .8);
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  text-align: center;
  z-index: 999999;

}




.view-box-mode-device-pc {
  position: fixed;
  //background-color: #FF0000;
  width: 1200px;
  height: 100vh;
  top: 0;
  left: 50%;
  transform: translateX(-50%);

  .view-center {
    width: 600px;
  }

  .view-right {
    position: absolute;
    display: inline-block;
    width: 360px;
    height: 100%;
    right: 0;
    top: 0;
    z-index: 10;
  }

  .view-right-mode-color-scheme-light {
    background-color: #ffffff;
  }

  .view-right-mode-color-scheme-dark {
    background-color: #191919;
  }

}

.view-box-meet-mode-device-pc {
  position: fixed;
  //background-color: #FF0000;
  width: 1200px;
  height: 100vh;
  top: 0;
  left: 50%;
  transform: translateX(-50%);

  .view-center {
    width: 960px;
  }

  .view-right {
    position: absolute;
    display: inline-block;
    width: 5px;
    height: 100%;
    right: 0;
    top: 0;
    z-index: 99;
  }

  .view-right-mode-color-scheme-light {
    background-color: #ffffff;
  }

  .view-right-mode-color-scheme-dark {
    background-color: #191919;
  }
}

.bg-self-banned {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  z-index: 9999999;

  .box1 {
    position: absolute;
    padding: 60rpx;
    box-sizing: border-box;
    left: 0;
    width: 100%;
    top: 100rpx;

    .logo {
      width: 200px;
      height: 200px;
      display: block;
      margin: 0 auto;
    }

    .text {
      text-align: center;
      font-size: 32rpx;
      color: #666666;
      margin-top: 20rpx;
    }
  }

}
</style>
