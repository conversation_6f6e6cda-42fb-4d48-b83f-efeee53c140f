@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-75b0b7ec {
  width: 100vw;
  height: 50vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;
}
.box .title.data-v-75b0b7ec {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
  padding-top: 20rpx;
}
.box .content-area.data-v-75b0b7ec {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20rpx;
}
.box .content-area .input-section.data-v-75b0b7ec {
  margin-bottom: 30rpx;
}
.box .content-area .input-section .section-title.data-v-75b0b7ec {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  color: #333;
}
.box .content-area .input-section .input-container.data-v-75b0b7ec {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 0 20rpx;
}
.box .content-area .input-section .input-container .project-name-input.data-v-75b0b7ec {
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  background: transparent;
  border: none;
}
.box .content-area .input-section .tips.data-v-75b0b7ec {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
.box .footer.data-v-75b0b7ec {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}
.box .footer .buttons.data-v-75b0b7ec {
  display: flex;
  gap: 20rpx;
}
.box .footer .buttons button.data-v-75b0b7ec {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  border: none;
}
.box .footer .buttons button.cancel-btn.data-v-75b0b7ec {
  background-color: #f5f5f5;
  color: #666;
}
.box .footer .buttons button.cancel-btn[disabled].data-v-75b0b7ec {
  background-color: #e0e0e0;
  color: #999;
}
.box .footer .buttons button.confirm-btn.data-v-75b0b7ec {
  background-color: #000000;
  color: #fff;
}
.box .footer .buttons button.confirm-btn[disabled].data-v-75b0b7ec {
  background-color: #cccccc;
  color: #fff;
}
.close-btn.data-v-75b0b7ec {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
  z-index: 10;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
