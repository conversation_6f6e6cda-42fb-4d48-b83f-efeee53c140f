<view class="data-v-bb037ae2"><block wx:if="{{loading}}"><u-loading-icon vue-id="a56adf52-1" mode="circle" size="{{50}}" class="data-v-bb037ae2" bind:__l="__l"></u-loading-icon></block><block wx:if="{{$root.g0}}"><view class="loading-box data-v-bb037ae2"><image class="loading-image data-v-bb037ae2" src="{{$root.g1}}"></image><view class="loading-text data-v-bb037ae2">{{$root.g2}}</view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['select',['$0'],[[['list','',__i0__,'userAccountOfFollowed']]]]]]]}}" class="{{['box','data-v-bb037ae2',(item.m0)?'box-selected':'']}}" bindtap="__e"><block wx:if="{{item.m1}}"><image class="box-selected-icon data-v-bb037ae2" src="{{item.g3}}"></image></block><block wx:if="{{!customUserInfoCard}}"><kt-user-info-card vue-id="{{'a56adf52-2-'+__i0__}}" user-info="{{item.$orig.userAccountOfFollowed}}" class="data-v-bb037ae2" bind:__l="__l"></kt-user-info-card></block><block wx:else><slot name="userInfoCard"></slot><scoped-slots-userInfoCard userInfo="{{item.$orig.userAccountOfFollowed}}" class="scoped-ref" bind:__l="__l"></scoped-slots-userInfoCard></block></view></block></view>