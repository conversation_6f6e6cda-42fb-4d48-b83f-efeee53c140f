.login-input-box {
  position: relative;
  height: 55px;
  border: 5rpx solid #F0F0F0;
  border-radius: 20rpx;
  font-size: 32rpx;
  padding: 30rpx 20rpx 30rpx 20rpx;
  box-sizing: border-box;

  .icon-box {
    display: inline-block;
    width: 90rpx;
    height: 100%;
    vertical-align: top;
    text-align: center;
    color: #000000;
    font-weight: bold;
    letter-spacing: 1px;
    font-size: 30rpx;

    .icon {
      position: relative;
      top: 50%;
      transform: translateY(-50%);

      .icon-img {
        width: 40rpx;
      }
    }
  }

  .input-box {
    position: relative;
    display: inline-block;
    width: calc(100% - 90rpx);
    height: 100%;


    .input {
      position: relative;
      width: 100%;
      font-size: 30rpx;
      top: 50%;
      transform: translateY(-50%);
    }

  }

}

.icon-box-btn:active {
  opacity: .4;
}

.icon-img-loading {
  // 动画
  animation: loading 1s infinite;
  // 颜色反转
  //filter: invert(1);
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.login-input-box-mode-color-scheme-light {
  border: 5rpx solid #F0F0F0;
  color: #000000;

  .icon-box {
    // 颜色反转
    filter: invert(0);
  }

}

.login-input-box-mode-color-scheme-dark {
  color: #FFFFFF;
  border: 3rpx solid #404a56;

  .icon-box {
    // 颜色反转
    filter: invert(1);
  }

}


.tips {
  font-size: 24rpx;
  color: #666666;
  margin-top: 10rpx;
  text-align: left;
}

.tips-mode-color-scheme-light {
  color: #666666;
}

.tips-mode-color-scheme-dark {
  color: #b9b9b9;
}