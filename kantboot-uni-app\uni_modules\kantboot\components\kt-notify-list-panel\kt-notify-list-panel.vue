<template>
  <view>
    <view class="box">
      <view v-if="!loading">


      <view class="box-item"
      v-for="item in list">
        <view class="box-item-date">
          {{$kt.date.format(item.gmtCreate, 'yyyy-MM-dd hh:mm:ss')}}
        </view>
        <kt-format
        :data="item.items"
        ></kt-format>
      </view>
      </view>

      <view v-if="list.length === 0&&!loading"
            class="no-data">
        <view>
          <image
              class="no-data-image"
              :src="$kt.file.byPath('icon/book.svg')"
          ></image>
        </view>
        <view
            class="no-data-text"
        >{{ $i18n.zhToGlobal("暂无通知") }}
        </view>
      </view>

      <view
          v-if="loading"
          class="loading-box">
        <view>
          <image
              class="loading-box-img"
          :src="$kt.file.byPath('kantboot/icon/loading.svg')"
          ></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      loading:true
    };
  },
  mounted() {
    this.getList();
  },
  methods:{
    // /functional-notify-web/notify/getListBySelf
    getList() {
      this.loading = true;
      this.$request.post("/functional-notify-web/notify/getListBySelf").then(res => {
        this.list = res.data;
        // if(this.list&&this.list.length>0){
        // //  只保留第一个
        //   this.list = this.list.slice(0, 1);
        // }
        // 根据gmtCreate（时间戳），保留一个小时的，
        this.list = this.list.filter(item => {
          return new Date().getTime() - new Date(item.gmtCreate).getTime() < 3600000; // 3600000毫秒 = 1小时
        });
        this.loading = false;
        this.readBySelf();
      });
    },
    readBySelf() {
      this.$request.post("/functional-notify-web/notify/readBySelf").then(res => {
      });
    }
  }
}
</script>

<style lang="scss" scoped>

.box-item {
  padding: 20rpx;
  border: 1rpx solid #F0F0F0;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  .box-item-date{
    font-size: 28rpx;
    text-align: right;
  }
}


.no-data {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 30rpx;

  .no-data-image {
    width: 100rpx;
    height: 100rpx;
    opacity: .6;
  }
}

.loading-box{
  text-align: center;
  margin-top: 100rpx;
  .loading-box-img {
    width: 100rpx;
    height: 100rpx;
    opacity: .6;
    // 颜色反转
    filter: invert(1);
    // 动画
    animation: loading 1s infinite;
  }
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

</style>
