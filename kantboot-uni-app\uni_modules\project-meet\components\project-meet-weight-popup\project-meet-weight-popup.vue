<template>
  <view>
    <kt-popup ref="weightPopup">
      <view :class="clazz.box">
        <view class="box-title">
          {{ $i18n.zhToGlobal("选择体重") }}</view>
        <view class="pop_class">
          <view
              class="class_item"
              v-for="(item, index) in weightArr"
              :key="index"
              :class="{
                'class_item-active': value === item.value
              }"
              @click="weightSelect(item)"
          >{{ item.value }}</view>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
   props: {
    value: {
      type: String,
      default: '',
    }
   },
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      },
      weightArr: [
      ],
    };
  },
  created() {
    this.generateWeightArr();
  },
  methods: {
	  // 生成weightArr
    generateWeightArr() {
      for (let i = 80; i < 300; i++) {
        let weight = `${i + 1} LBS (${parseInt((i + 1) * 0.45359237)} KG)`;
        this.weightArr.push({
          value: weight,
        });
      }
    },
	
    open() {
      this.$refs.weightPopup.open();
    },
    weightSelect(item) {
      this.value = item.value;
      this.$refs.weightPopup.close();
      this.$emit('input', this.value);

    }
  },
};
</script>

<style lang="scss" scoped>
.box{
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  padding: 20rpx;
}
.box-title{
  font-size: 32rpx;
  color: #333333;
}
.pop_class {
  height: 600rpx;
  overflow: auto;
}
.class_item {
  padding: 30rpx 0 30rpx 30rpx;
  color: #999;
  font-size: 30rpx;
}
.class_item:active {
  background-color: #e5e5e5;
  color: white;
}

.class_item-active {
  background-color: #000000;
  color: #FFFFFF;
  font-weight: bold;
  border-radius: 10rpx;
}

.box-mode-device-pc{
  border-radius: 20rpx;
}
</style>
