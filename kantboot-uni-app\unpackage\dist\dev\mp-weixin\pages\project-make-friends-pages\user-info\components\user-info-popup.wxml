<view class="data-v-6d49505a"><u-popup vue-id="3be13bec-1" show="{{show}}" mode="bottom" bgColor="rgba(0,0,0,0)" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-6d49505a" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup data-v-6d49505a"><view class="popup-title data-v-6d49505a">{{$root.g0}}</view><view class="picker data-v-6d49505a"><view class="bl-box data-v-6d49505a"><view style="text-align:center;" class="data-v-6d49505a"><view data-event-opts="{{[['tap',[['selectAvatar']]]]}}" style="display:inline-block;" bindtap="__e" class="data-v-6d49505a"><kt-avatar class="avatar data-v-6d49505a" vue-id="{{('3be13bec-2')+','+('3be13bec-1')}}" size="220rpx" src="{{viewSrc?viewSrc:$root.g1}}" bind:__l="__l"></kt-avatar></view></view><view style="height:30rpx;" class="data-v-6d49505a"></view><view style="padding:0 50rpx 0 50rpx;box-sizing:border-box;" class="data-v-6d49505a"><input class="input data-v-6d49505a" type="text" adjust-position="{{false}}" placeholder="{{$root.g2}}" data-event-opts="{{[['blur',[['nicknameBlur',['$event']]]],['focus',[['nicknameFocus',['$event']]]],['input',[['__set_model',['$0','nickname','$event',[]],['userAccount']]]]]}}" value="{{userAccount.nickname}}" bindblur="__e" bindfocus="__e" bindinput="__e"/></view></view><block wx:if="{{!isNickenameFocus}}"><view style="height:30px;" class="data-v-6d49505a"></view></block><block wx:if="{{!isNickenameFocus}}"><view class="data-v-6d49505a"><view class="gender-box data-v-6d49505a"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['gender-item','data-v-6d49505a',(userAccount.genderCode=='male')?'gender-item-selected':'']}}" style="{{(userAccount.genderCode=='male'?'background-color: #409EFF;':'')}}" bindtap="__e">{{$root.g3+''}}</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['gender-item','data-v-6d49505a',(userAccount.genderCode=='female')?'gender-item-selected':'']}}" style="{{(userAccount.genderCode=='female'?'background-color: rgba(255,0,0,.6);':'')}}" bindtap="__e">{{$root.g4+''}}</view></view></view></block><block wx:if="{{!isNickenameFocus}}"><view style="height:30px;" class="data-v-6d49505a"></view></block><block wx:if="{{!isNickenameFocus}}"><view style="padding:0 50rpx 0 50rpx;" class="data-v-6d49505a"><view class="input-box data-v-6d49505a"><textarea class="input textarea data-v-6d49505a" maxlength="100" adjust-position="{{false}}" type="textarea" placeholder="{{$root.g5}}" data-event-opts="{{[['input',[['__set_model',['$0','introduction','$event',[]],['userAccount']]]]]}}" value="{{userAccount.introduction}}" bindinput="__e"></textarea></view></view></block></view><view style="height:10px;" class="data-v-6d49505a"></view><block wx:if="{{!isNickenameFocus}}"><kt-button bind:click="__e" vue-id="{{('3be13bec-3')+','+('3be13bec-1')}}" data-ref="confirmBtn" data-event-opts="{{[['^click',[['submit']]]]}}" class="data-v-6d49505a vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g6+''}}</kt-button></block><view style="height:10px;" class="data-v-6d49505a"></view><kt-keyboard-size vue-id="{{('3be13bec-4')+','+('3be13bec-1')}}" data-ref="keyboardSize" class="data-v-6d49505a vue-ref" bind:__l="__l"></kt-keyboard-size></view></u-popup></view>