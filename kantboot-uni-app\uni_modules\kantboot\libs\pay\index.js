import router from "@/uni_modules/kantboot/libs/router";
import payConfig from "@/uni_modules/kantboot/libs/pay/pay.config";
import i18n from "@/uni_modules/kantboot/libs/i18n";

let result = {};

result.config = payConfig;

/**
 * 前往paypal支付
 */
result.toPaypal = (params) => {
    // 前往paypal支付
    router.toWebview(payConfig.paypalPath+"?orderNumber=" + params.orderNumber,{
        title: i18n.zhToGlobal("Paypal支付"),
    });
}

export default result;