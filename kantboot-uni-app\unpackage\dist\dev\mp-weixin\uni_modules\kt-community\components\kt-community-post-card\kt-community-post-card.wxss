@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.card.data-v-0d73e0ab {
  position: relative;
}
.card .card-three-dot.data-v-0d73e0ab {
  display: inline-block;
  width: 30rpx;
  height: 30rpx;
  margin-right: -30rpx;
  float: right;
}
.card .card-three-dot.data-v-0d73e0ab:active {
  opacity: 0.5;
}
.in-user-card.data-v-0d73e0ab {
  position: relative;
  text-align: left;
  margin: 10rpx;
}
.in-user-card .in-user-card-avatar-box.data-v-0d73e0ab {
  display: inline-block;
  width: 75rpx;
  height: 75rpx;
  border-radius: 50%;
}
.in-user-card .in-user-card-avatar-box .in-user-card-avatar.data-v-0d73e0ab {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.in-user-card .in-user-card-info-box.data-v-0d73e0ab {
  position: relative;
  display: inline-block;
  margin-left: 20rpx;
  width: calc(100% - 95rpx);
  vertical-align: top;
}
.in-user-card .in-user-card-info-box .in-user-card-info-box-nickname.data-v-0d73e0ab {
  font-size: 28rpx;
}
.in-user-card .in-user-card-info-box .in-user-card-info-box-time.data-v-0d73e0ab {
  font-size: 24rpx;
  color: #999;
}
.card.data-v-0d73e0ab {
  padding: 30rpx;
  border-radius: 30rpx;
}
.in-card.data-v-0d73e0ab {
  width: 100%;
  text-align: left;
}
.in-card-content-box-text.data-v-0d73e0ab {
  font-size: 32rpx;
  color: #333;
  margin: 10rpx 0;
  letter-spacing: 2rpx;
}
.in-footer-card.data-v-0d73e0ab {
  text-align: left;
  margin-top: 20rpx;
}
.in-footer-card .in-footer-card-item.data-v-0d73e0ab {
  display: inline-block;
  margin-right: 20rpx;
}
.in-footer-card .in-footer-card-item .in-footer-card-item-icon.data-v-0d73e0ab {
  display: inline-block;
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}
.in-footer-card .in-footer-card-item .in-footer-card-item-number.data-v-0d73e0ab {
  display: inline-block;
  font-size: 28rpx;
  color: #333;
  vertical-align: top;
  margin-right: 10rpx;
}
.in-footer-footer-card.data-v-0d73e0ab {
  margin-top: 20rpx;
  padding: 30rpx;
  border: 1px solid #eee;
  border-radius: 20rpx;
}
.in-footer-footer-card .in-footer-footer-card-title.data-v-0d73e0ab {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.tag.data-v-0d73e0ab {
  display: inline-block;
  background-color: #eee;
  color: #333;
  font-size: 24rpx;
  padding: 2rpx 15rpx 2rpx 15rpx;
  border-radius: 20rpx;
  margin-left: 10rpx;
}
.card-mode-color-scheme-dark.data-v-0d73e0ab {
  background-color: #212932;
}
.card-mode-color-scheme-dark .card-three-dot.data-v-0d73e0ab {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.card-mode-color-scheme-dark .in-card.data-v-0d73e0ab {
  color: #e9e9e9;
}
.card-mode-color-scheme-dark .in-card-content-box-text.data-v-0d73e0ab {
  color: #e9e9e9;
}
.card-mode-color-scheme-dark .in-user-card .in-user-card-info-box .in-user-card-info-box-nickname.data-v-0d73e0ab {
  color: #e9e9e9;
}
.card-mode-color-scheme-dark .in-footer-card .in-footer-card-item .in-footer-card-item-icon.data-v-0d73e0ab {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.card-mode-color-scheme-dark .in-footer-card .in-footer-card-item .in-footer-card-item-number.data-v-0d73e0ab {
  color: #F8F8F8;
}
.dot.data-v-0d73e0ab {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  margin-top: 15rpx;
}
.dot-audit-auditing.data-v-0d73e0ab {
  background-color: #FF9800;
}
.dot-audit-pass.data-v-0d73e0ab {
  background-color: #4CAF50;
}
.dot-audit-fail.data-v-0d73e0ab {
  background-color: #F44336;
}
.dot-text.data-v-0d73e0ab {
  position: relative;
  display: inline-block;
  vertical-align: top;
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}
.dot-text .dot-text-auditing.data-v-0d73e0ab {
  color: #FF9800;
}
.dot-text .dot-text-pass.data-v-0d73e0ab {
  color: #4CAF50;
}
.dot-text .dot-text-fail.data-v-0d73e0ab {
  color: #F44336;
}
.dot-fail-reason.data-v-0d73e0ab {
  position: relative;
  display: inline-block;
  vertical-align: top;
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
  float: right;
  margin-top: -5rpx;
}
.f-btn.data-v-0d73e0ab {
  position: absolute;
  border: 2px solid #333;
  color: #333;
  border-radius: 30rpx;
  padding: 5rpx 20rpx;
  font-size: 24rpx;
  top: -10rpx;
  right: 10rpx;
}
.f-btn.data-v-0d73e0ab:active {
  opacity: 0.5;
}
.card-mode-device-pc .f-btn.data-v-0d73e0ab {
  cursor: pointer;
}
