<template>
  <view class="second-box-box">
    <view
      v-for="(tab, index) in internalTabs"
      :key="tab.key"
      class="second-box-item"
      :class="{
        'second-box-item-selected': selected === tab.key,
        'dragging': draggingIndex === index
      }"
      :style="tabStyles[index]"
      @click="handleTabClick(tab, index)"
      @touchstart="handleTouchStart($event, index)"
      @touchmove="handleTouchMove($event, index)"
      @touchend="handleTouchEnd($event, index)"
      @mousedown="handleMouseDown($event, index)"
    >
      {{ tab.label }}
    </view>
  </view>
</template>

<script>
export default {
  name: 'DraggableTabs',
  props: {
    tabs: {
      type: Array,
      required: true,
      default: () => []
    },
    selected: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      internalTabs: [],
      dragging: false,
      draggingIndex: -1,
      placeholderIndex: -1,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
      dragOffset: { x: 0, y: 0 },
      tabPositions: [],
      isMobile: false,
      draggedTab: null,
      longPressTimer: null,
      showLongPressHint: false,
      longPressThreshold: 500 // 长按阈值（毫秒）
    }
  },
  computed: {
    tabStyles() {
      // 为每个tab计算样式
      return this.internalTabs.map((tab, index) => {
        const style = {};
        if (this.draggingIndex === index && this.dragging) {
          style.transform = `translate(${this.dragOffset.x}px, ${this.dragOffset.y}px)`;
          style.zIndex = '1000';
          style.opacity = '0.7';
          style.position = 'relative';
        }
        return style;
      });
    }
  },
  watch: {
    tabs: {
      handler(newTabs) {
        this.internalTabs = [...newTabs];
        this.updateTabPositions();
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.detectDevice();
    this.updateTabPositions();

    // 添加全局事件监听器 (仅在H5环境下)
    // #ifdef H5
    if (!this.isMobile && typeof document !== 'undefined') {
      document.addEventListener('mousemove', this.handleGlobalMouseMove);
      document.addEventListener('mouseup', this.handleGlobalMouseUp);
    }
    // #endif
  },
  beforeDestroy() {
    // #ifdef H5
    if (!this.isMobile && typeof document !== 'undefined') {
      document.removeEventListener('mousemove', this.handleGlobalMouseMove);
      document.removeEventListener('mouseup', this.handleGlobalMouseUp);
    }
    // #endif
  },
  methods: {
    detectDevice() {
      // 检测设备类型
      const systemInfo = uni.getSystemInfoSync();
      this.isMobile = systemInfo.platform === 'android' ||
                     systemInfo.platform === 'ios' ||
                     systemInfo.platform === 'devtools';

      // 在H5环境下进一步检测
      // #ifdef H5
      if (typeof navigator !== 'undefined') {
        this.isMobile = this.isMobile || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      }
      // #endif
    },
    
    updateTabPositions() {
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        this.internalTabs.forEach((tab, index) => {
          query.select(`.draggable-tab-item:nth-child(${index + 1})`).boundingClientRect();
        });
        query.exec((res) => {
          this.tabPositions = res.filter(item => item);
        });
      });
    },
    

    
    handleTabClick(tab, index) {
      if (!this.dragging) {
        this.$emit('tab-click', tab.key);
      }
    },
    
    // 触摸事件处理 (移动端)
    handleTouchStart(event, index) {
      const touch = event.touches[0];
      this.startDrag(touch.clientX, touch.clientY, index);
    },

    handleTouchMove(event, index) {
      if (!this.dragging || this.draggingIndex !== index) return;

      const touch = event.touches[0];
      this.updateDrag(touch.clientX, touch.clientY);
      event.preventDefault();
    },

    handleTouchEnd(event, index) {
      if (this.draggingIndex !== index) return;

      this.endDrag();
    },

    // 鼠标事件处理 (PC端)
    handleMouseDown(event, index) {
      // #ifdef H5
      if (this.isMobile) return;

      this.startDrag(event.clientX, event.clientY, index);
      // #endif
    },

    handleGlobalMouseMove(event) {
      if (!this.dragging) return;

      this.updateDrag(event.clientX, event.clientY);
    },

    handleGlobalMouseUp(event) {
      if (!this.dragging) return;

      this.endDrag();
    },
    
    // 拖拽逻辑
    startDrag(clientX, clientY, index) {
      this.dragging = true;
      this.draggingIndex = index;
      this.startX = clientX;
      this.startY = clientY;
      this.currentX = clientX;
      this.currentY = clientY;
      this.dragOffset = { x: 0, y: 0 };
      this.draggedTab = this.internalTabs[index];
      
      this.updateTabPositions();
    },
    
    updateDrag(clientX, clientY) {
      this.currentX = clientX;
      this.currentY = clientY;
      this.dragOffset = {
        x: clientX - this.startX,
        y: clientY - this.startY
      };
      
      // 计算应该插入的位置
      this.calculateDropPosition(clientX);
    },
    
    calculateDropPosition(clientX) {
      let newIndex = this.draggingIndex;
      const tabWidth = 120; // 估算的选项卡宽度

      // 简化的位置计算
      for (let i = 0; i < this.internalTabs.length; i++) {
        if (i === this.draggingIndex) continue;

        const tabLeft = i * tabWidth;
        const tabRight = (i + 1) * tabWidth;

        if (clientX > tabLeft && clientX < tabRight) {
          newIndex = i;
          break;
        }
      }

      this.placeholderIndex = newIndex;
    },
    
    endDrag() {
      if (!this.dragging) return;

      const oldIndex = this.draggingIndex;
      let newIndex = this.placeholderIndex !== -1 ? this.placeholderIndex : oldIndex;

      // 确保新索引在有效范围内
      newIndex = Math.max(0, Math.min(newIndex, this.internalTabs.length - 1));

      if (oldIndex !== newIndex && oldIndex >= 0 && newIndex >= 0) {
        // 重新排序
        const newTabs = [...this.internalTabs];
        const [draggedTab] = newTabs.splice(oldIndex, 1);
        newTabs.splice(newIndex, 0, draggedTab);

        this.internalTabs = newTabs;

        // 立即触发事件，确保父组件能收到更新
        this.$emit('tab-order-change', newTabs);

        console.log('拖拽完成，新顺序:', newTabs.map(tab => tab.key));
      }

      // 重置状态
      this.dragging = false;
      this.draggingIndex = -1;
      this.placeholderIndex = -1;
      this.dragOffset = { x: 0, y: 0 };
      this.draggedTab = null;
    },

  }
}
</script>

<style lang="scss" scoped>
.second-box-box {
  width: calc(100% - 100rpx);
  // 不换行
  white-space: nowrap;
  // 超出滚动
  overflow-x: auto;
}

.second-box-item {
  display: inline-block;
  font-size: 32rpx;
  letter-spacing: 3rpx;
  margin-right: 5rpx;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  user-select: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &.second-box-item-selected {
    font-weight: bold;
  }

  &.dragging {
    opacity: 0.7;
    z-index: 1000;
  }
}
</style>
