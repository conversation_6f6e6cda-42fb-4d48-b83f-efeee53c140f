{"pages": ["pages/pages-body/into/into"], "subPackages": [{"root": "pages/kantboot-pages/kt-pages/", "pages": ["demo/demo", "language-select/language-select", "image-cropper/image-cropper", "web-view/web-view"]}, {"root": "pages/kantboot-pages/kt-link-pages", "pages": ["demo/demo"]}, {"root": "pages/kantboot-pages/kt-community-pages", "pages": ["demo/demo"]}, {"root": "pages/project-acm-pages/", "pages": ["body/body", "user-info/user-info", "invite/invite", "setting/setting", "post-self/post-self", "course-detail/course-detail", "post-detail/post-detail", "post-push/post-push"]}, {"root": "pages/project-meet-pages/", "pages": ["body/body", "demo/demo", "invite/invite", "album/album", "user-info/user-info", "chat-dialog/chat-dialog", "set-user-info/set-user-info", "setting/setting", "post-detail/post-detail", "post-push/post-push", "test/test", "user-account-interrelation/user-account-interrelation", "post-self/post-self", "like-list/like-list", "user-visit/user-visit", "user-post/user-post", "body/body-emp"]}, {"root": "pages/project-make-friends-pages/", "pages": ["body/body", "post-to/post-to", "invite/invite", "user-info/user-info", "setting/setting", "post-push/post-push", "post-detail/post-detail", "chat-dialog/chat-dialog", "user-account-interrelation/user-account-interrelation", "post-self/post-self", "post-collect/post-collect", "user-qrcode/user-qrcode", "my-invite-persons/my-invite-persons", "user-search/user-search", "user-post/user-post", "member-transfer/member-transfer", "member-transfer/member-transfer-user", "member-transfer/member-transfer-transfer", "member-transfer/who-helped", "member-transfer/transfer-detail-list", "member-partner/member-partner", "member-partner/member-partner-content", "member-partner/user-select", "member-partner/collaborator-select", "member-partner/miniprogram-code"]}, {"root": "pages/project-hometown-pages/", "pages": ["body/body"]}], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "<PERSON><PERSON>", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8", "navigationStyle": "custom"}, "usingComponents": {}}