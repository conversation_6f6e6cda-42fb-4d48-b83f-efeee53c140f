<view class="data-v-7b905926"><view hidden="{{!(!isLogin)}}" class="{{['data-v-7b905926',clazz.box]}}" style="{{'z-index:'+(zIndex)+';'}}"><view class="{{['data-v-7b905926',clazz.back]}}"></view><view class="in-box data-v-7b905926"><view class="icon-box data-v-7b905926"><image class="icon data-v-7b905926" src="{{$root.g0}}"></image></view><view style="height:30rpx;" class="data-v-7b905926"></view><kt-button bind:click="__e" vue-id="130966c0-1" data-event-opts="{{[['^click',[['toLogin']]]]}}" class="data-v-7b905926" bind:__l="__l" vue-slots="{{['default']}}">{{''+$root.g1+" "+"➠"+''}}</kt-button><view style="height:30rpx;" class="data-v-7b905926"></view></view><view style="position:fixed;z-index:701;width:100%;left:0;text-align:center;bottom:150rpx;" class="data-v-7b905926"><image class="setting-icon data-v-7b905926" src="{{$root.g2}}" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e"></image></view></view><kt-login-popup vue-id="130966c0-2" wechat-login-method="{{wechatLoginMethod}}" data-ref="ktLoginPopup" class="data-v-7b905926 vue-ref" bind:__l="__l"></kt-login-popup></view>