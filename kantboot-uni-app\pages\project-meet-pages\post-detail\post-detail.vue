<template>
  <view>
    <kt-nav-bar
        :title="$i18n.zhToGlobal('帖子详情')"
    ></kt-nav-bar>
    <view class="box" v-if="postId">
      <kt-community-post-card
          :post-id="postId"
          :is-forbid-forward="false"
          :is-forbid-collect="false"
          @dotClick="cardDotClick"
          @userClick="cardUserClick"
          ref="projectAcmCard"
      >
      </kt-community-post-card>
      <kt-community-post-operate-popup
          @removeSuccess="removeSuccess"
          ref="communityPostOperatePopup">
      </kt-community-post-operate-popup>
    </view>

    <view
    v-if="isSvip||isVip"
    >

      <view>
        <view :class="clazz.box">
          <view class="box-title">{{ $i18n.zhToGlobal("评论") }}</view>
          <view v-show="loading">
            <view style="height: 100rpx"></view>
            <u-loading-icon
                v-if="loading"
                mode="circle"
                :size="50"
            ></u-loading-icon>
          </view>
          <view v-if="list.length === 0&&!loading"
                class="no-data"
          >
            <view>
              <image
                  class="no-data-image"
                  :src="$kt.file.byPath('icon/book.svg')"
              ></image>
            </view>
            <view
                class="no-data-text"
            >{{ $i18n.zhToGlobal("暂无评论") }}
            </view>
          </view>
          <view v-for="item in list"
                class="box-item">
            <kt-community-post-card
                :has-bottom-operation="false"
                :has-dot="false"
                :post="item"
            ></kt-community-post-card>
          </view>
        </view>
      </view>

      <view style="height: 100rpx"></view>

      <view
          class="bottom">
        <kt-image-select
            v-if="bindParams.imageIds.length > 0"
            file-group-code="fp"
            v-model="bindParams.imageIds"
        ></kt-image-select>
        <kt-send-input
            :placeholder="placeholder"
            mode="panel"
            ref="sendInput"
            :has-video="false"
            :has-voice="false"
            :has-user-account="false"
            @send="send"
        ></kt-send-input>
      </view>

    </view>

    <view
    v-else
    style="position: fixed;bottom: 0;left: 0;right: 0;padding: 50rpx;z-index: 100;box-sizing: border-box;"
    >
      <kt-button
      @click="() => $refs.projectMeetVipPopup.open()"
      >
        {{ $i18n.zhToGlobal("开通VIP，享受评论功能") }}
      </kt-button>
    </view>

    <project-meet-vip-popup
    ref="projectMeetVipPopup"
    ></project-meet-vip-popup>

  </view>
</template>

<script>
export default {
  data() {
    return {
      placeholder: this.$i18n.zhToGlobal("发送"),
      postId: null,
      post: {},
      bindParams:{
        imageIds: [],
      },
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      },
      loading:true,
      isSelf: false,
      isLogin: false,
      self: {},
      currentPath: "/pages/project-meet-pages/post-detail/post-detail",
      isVip: false,
      isSvip: false,
    };
  },
  onLoad(options) {
    this.postId = options.postId;
    this.getByPostId();
    this.isLogin = this.$kt.userAccount.getIsLogin();
    if(this.isLogin){
      this.self = this.$kt.userAccount.getSelf();
      this.getIsVip();
    }
    this.getInitList();

  },
  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.$kt.userAccount.getSelf().nickname,
      path: this.currentPath+'?userAccountIdOfDirect='+this.userAccount.id+'&postId='+this.postId,
    }
  },
  onShareAppMessage() {
    return {
      title: this.$kt.userAccount.getSelf().nickname,
      path: this.currentPath+'?userAccountIdOfDirect='+this.userAccount.id+'&postId='+this.postId,
    }
  },
  methods: {
    getIsVip(){
      let currentTime = new Date().getTime();

      this.isSvip = this.self.gmtSvipExpire && this.self.gmtSvipExpire > currentTime;
      this.isVip = this.self.gmtVipExpire && this.self.gmtVipExpire > currentTime;
    },
    cardUserClick(userAccount){
      this.$kt.router.navTo('/pages/project-meet-pages/user-info/user-info', {'userAccountId': userAccount.id})
    },
    getMaxAndMinId() {
      let maxId = 0;
      let minId = 0;
      for (let i = 0; i < this.list.length; i++) {
        let item = this.list[i];
        if (item.id > maxId) {
          maxId = item.id;
        }
        if (item.id < minId || minId === 0) {
          minId = item.id;
        }
      }
      this.requestParam.maxId = minId;
      this.requestParam.minId = maxId;
    },
    getInitList() {
      if (!this.postId) {
        return;
      }
      this.loading = true;
      this.$kt.request.post("/fp-community-web/postComment/getList", {
        data: {
          postId: this.postId
        }
      }).then(async (res) => {
        this.list = res.data;
        this.loading = false;
        this.getMaxAndMinId();
      }).catch(err => {
        this.loading = false;
        this.$kt.message.error(err.errMsg);
      });
    },
    removeSuccess(){
      uni.navigateBack();
    },
    getByPostId(){
      this.$request.post("/fp-community-web/post/getById", {
        data: {
          id: this.postId,
        }
      }).then((res) => {
        this.post = res.data;
        this.isSelf = this.post.userAccountIdOfUploader === this.self.id;
        // if(this.post.userAccountIdOfUploader === this.self.id) {
        //   this.placeholder = this.$i18n.zhToGlobal("追问");
        // }
        // if(this.post.userAccountIdOfUploader !== this.self.id) {
        //   this.placeholder = this.$i18n.zhToGlobal("回答");
        // }
        try{
          this.$forceUpdate();
        }catch (e) {

        }
      }).catch((err) => {
        uni.showToast({
          title: err.errMsg,
          icon: "none",
        });
      });
    },
    send(ktFormat){
      if(ktFormat.type==="image:id"){
        this.bindParams.imageIds.push(ktFormat.content);
      }
      if(ktFormat.type==="text"){
        // TODO
        this.$refs.sendInput.loading = true;
        this.$request.post("/fp-community-web/postComment/sendPostComment", {
          data:{
            postId: this.postId,
            items:[{
              type: "text",
              content: ktFormat.content,
            },{
              type: "images:id",
              content: JSON.stringify(this.bindParams.imageIds),
            }]
          }
        }).then((res) => {
          this.$refs.sendInput.loading = false;
          this.$refs.sendInput.clear();
          this.bindParams.imageIds = [];
          this.getInitList();
          this.$emit("sendSuccess", res.data);
          // this.$kt.event.emit("projectAcm:sendPostComment", res.data);
        }).catch((err) => {
          uni.showToast({
            title: err.errMsg,
            icon: "none",
          });
        });
      }
    },
    cardDotClick(post){
      if(!this.$kt.userAccount.getIsLogin()){
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.$refs.communityPostOperatePopup.open(post);
    },
  },
}
</script>

<style lang="scss" scoped>
.box{
  box-sizing: border-box;
}
.bottom{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  z-index: 100;
  box-sizing: border-box;
}


.no-data {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 30rpx;

  .no-data-image {
    width: 100rpx;
    height: 100rpx;
    opacity: .6;
  }
}

.box {
  padding: 20rpx;
  box-sizing: border-box;

  .box-title {
    font-size: 32rpx;
    color: #333;
  }

  .box-item {
    width: 100%;
    display: inline-block;
    box-sizing: border-box;
  }
}

.box-mode-color-scheme-dark {
  .box-title {
    color: #fff;
  }

  .no-data {
    color: #b9b9b9;
  }

  .no-data-image {
    filter: invert(1);
  }
}
</style>
