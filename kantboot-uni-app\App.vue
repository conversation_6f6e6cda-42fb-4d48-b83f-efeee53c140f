<script>
	export default {
		onLaunch: function() {
			// console.log('App Launch')
		  this.$kt.websocket.start();
      this.$kt.storage.clearByEx();

      this.$kt.event.on("ProjectMeetUserAccount:setIsBanned",(res)=>{
        // alert(res.isBanned);
        if(res.isBanned){
          uni.showToast({
            title: this.$i18n.zhToGlobal("此账号正在审核，审核通过后，我们会发送邮件通知您"),
            icon: 'none',
            duration: 2000
          });
          this.$kt.userAccount.setIsLogin(false);
          this.$request.setToken("");
          setTimeout(()=>{
            uni.navigateTo({
              url: '/pages/pages-body/into/into'
            });
          },1000);
        }

      })
    },
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */
</style>
