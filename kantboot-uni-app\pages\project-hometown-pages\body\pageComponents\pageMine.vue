<template>
  <view :class="clazz.container">
    <view class="back"
    ></view>
    <view v-if="userInfo.id">

      <view
          class="header-box"
          v-for="item in 2"
          :style="{
      'position': item === 1 ? 'fixed' : 'relative',
      'opacity': item === 1 ? 1 : 0,
      'z-index': item === 1 ? 999 : 0,
    }">

        <kt-nav-bar
            :icon="$kt.file.byPath('tabbar/mine-selected.svg')"
            :title="$i18n.zhToGlobal('我的')"></kt-nav-bar>

      </view>


      <view
          @click="$kt.router.navTo('/pages/project-make-friends-pages/user-info/user-info?userAccountId='+userInfo.id)"
          class="user-info-box"
      >
        <kt-user-info-card
            class="user-info-card"
            :user-info="userInfo"
        ></kt-user-info-card>

        <view style="position: relative;width: 100%;bottom: 10rpx;left:0;">
          <view style="text-align: right;">
            <image
                style="margin-right: 20rpx;"
                @click.stop="$kt.router.navTo('/pages/project-make-friends-pages/user-qrcode/user-qrcode')"
                class="info-btn"
                :src="$kt.file.byPath('icon/qrCode.svg')"></image>
          </view>
        </view>

      </view>

      <view>


        <view
            class="box-box">

          <!-- <user-info-order-card></user-info-order-card> -->

          <view style="height: 30rpx"></view>
          <view>

            <!-- 换成菜单 -->
            <view class="menu-box">

              <view
                  v-if="userInfo.showPaiPan"
                  class="menu" @click="$kt.router.navTo('/pages/address/address')">
                <view class="menu-text">{{ $i18n.zhToGlobal('排盘') }}</view>
              </view>

              <view class="menu" @click="$kt.router.navTo('/pages/project-make-friends-pages/user-info/user-info?userAccountId='+userInfo.id)">
                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('icon/userInfo.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('个人资料') }}</view>
              </view>

              <view class="menu" @click="$kt.router.navTo('/pages/project-make-friends-pages/user-qrcode/user-qrcode')">
                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('icon/qrCode.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('个人二维码') }}</view>
              </view>

              <view class="menu" @click="toScan()">
                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('icon/scan.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('扫一扫') }}</view>
              </view>

              <view class="menu" @click="$kt.router.navTo('/pages/project-make-friends-pages/invite/invite')">
                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('icon/invite.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('邀请') }}</view>
              </view>

              <view class="menu" @click="$kt.router.navTo('/pages/project-make-friends-pages/my-invite-persons/my-invite-persons')">
                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('icon/invitePersons.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('我的会员') }}</view>
              </view>


              <view
                  v-if="false"
                  class="menu" @click="$kt.router.navTo('/pages/address/address')">
                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('image/address.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('收货地址') }}</view>
              </view>
              <view
                  @click="$kt.router.navTo('/pages/project-make-friends-pages/post-collect/post-collect')"
                  class="menu">

                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('icon/collection.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('收藏') }}</view>
              </view>

              <view class="menu" @click="$kt.router.navTo('/pages/address/address')">
                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('icon/personMove.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('会员转移') }}</view>
              </view>

              <view class="menu" @click="$kt.router.navTo('/pages/address/address')">
                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('icon/probabilityInvite.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('概率邀约') }}</view>
              </view>

              <view
                  @click="$kt.router.toLanguageSelect()"
                  class="menu">

                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('kantboot/icon/language.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('语言切换') }}</view>
              </view>

              <view
                  @click="$kt.router.navTo('/pages/project-make-friends-pages/setting/setting')"
                  class="menu">

                <view class="menu-icon-box">
                  <image
                      mode="aspectFill"
                      class="menu-icon"
                      :src="$kt.file.byPath('icon/setting.svg')"></image>
                </view>
                <view class="menu-text">{{ $i18n.zhToGlobal('设置') }}</view>
              </view>

              <!--              <view-->
              <!--                  @click="$refs.ktModal.open()"-->
              <!--                  class="menu">-->

              <!--                <view class="menu-icon-box">-->
              <!--                  <image-->
              <!--                      mode="aspectFill"-->
              <!--                      class="menu-icon"-->
              <!--                      :src="$kt.file.byPath('kantboot/icon/logout.svg')"></image>-->
              <!--                </view>-->
              <!--                <view-->
              <!--                    class="menu-text">{{ $i18n.zhToGlobal('退出登录') }}-->
              <!--                </view>-->
              <!--              </view>-->
            </view>

          </view>

        </view>

      </view>

      <view style="height: 180rpx"></view>
    </view>


    <kt-no-login
        wechat-login-method="loginByCode"
        ref="noLogin"
    ></kt-no-login>

    <!--    <u-modal :show="isLogoutModal"-->
    <!--             :showCancelButton="true"-->
    <!--             :title="$i18n.zhToGlobal('提示')"-->
    <!--             :content="$i18n.zhToGlobal('是否确定退出登录')"-->
    <!--             :confirm-text="$i18n.zhToGlobal('确定')"-->
    <!--             :cancel-text="$i18n.zhToGlobal('取消')"-->
    <!--             confirm-color="#000000"-->
    <!--             @cancel="isLogoutModal = false"-->
    <!--             :closeOnClickOverlay="true"-->
    <!--             @confirm="toLogout"-->
    <!--    ></u-modal>-->


  </view>
</template>

<script>
// import UserCommonInfoCard from "@/components/user-info/user-common-info-card.vue";
// import UserInfoOrderCard from "@/components/user-info/user-info-order-card.vue";

export default {
  // components: {UserInfoOrderCard, UserCommonInfoCard},
  data() {
    return {
      userInfo: {
        nickname: "",
        fileIdOfAvatar: "",
        genderCode: "male",
        email: "<EMAIL>",
      },
      isLogin: this.$kt.userAccount.getIsLogin(),
      isLogoutModal: false,
      // 国家获取地区
      stateAreaList: [],
      stateAreaMap: {},
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      }
    }
  },
  created() {
    // 监听登录成功事件
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.userInfo = this.$kt.userAccount.getSelf();
    });
    // 监听用户信息改变事件
    // userAccount:selfInfo:change
    this.$kt.event.on("userAccount:selfInfo:change", (userInfo) => {
      // this.userInfo = this.$kt.userAccount.requestSelf();
      this.userInfo.nickname = userInfo.nickname || "";
      this.userInfo.email = userInfo.email || "";
      this.userInfo.phoneAreaCode = userInfo.phoneAreaCode || "";
      this.userInfo.phone = userInfo.phone || "";
      this.userInfo.genderCode = userInfo.genderCode || "male";
      this.userInfo.fileIdOfAvatar = userInfo.fileIdOfAvatar || "";
      this.userInfo.gmtCreate = userInfo.gmtCreate || "";

      this.userInfo.showPaiPan = userInfo.showPaiPan || false;

    });
    // userAccount:getSelf
    this.$kt.event.on("userAccount:getSelf", () => {
      this.userInfo = this.$kt.userAccount.getSelf();
    });
    this.$kt.event.on("language:change", () => {
      console.log("language:change====");
      this.getAllStateArea();
    });
    this.getAllStateArea();
  },
  mounted() {
    this.isLogin = true;
    this.userInfo = this.$kt.userAccount.getSelf();
  },
  methods: {
    toScan() {
      console.log("开始扫码");
      // 打开微信扫一扫
      uni.scanCode({
        onlyFromCamera: true,
        scanType: ['qrCode'],
        success: (res) => {
          // 处理扫码结果
          console.log("扫码结果：", res);
          if (res.result) {
            // 解析扫码结果
            let result = JSON.parse(res.result);
            if (result.operateCode === 'user-qrcode') {
              this.$kt.router.navTo('/pages/project-make-friends-pages/user-info/user-info?userAccountId=' + result.userAccountId);
            } else if (result.operateCode === 'member-partner') {
              // 处理协作组邀请二维码
              this.joinGroupByQrCode(result.invitationGroupId);
            }

          }
        },
        fail: (err) => {
          console.error("扫码失败：", err);
        }
      });
    },
    isY() {
      // 转换为大写
      let deviceType = this.$kt.style.detectDeviceType().toUpperCase();
      return deviceType === 'PC';
    },

    // 通过二维码加入协作组
    joinGroupByQrCode(invitationGroupId) {
      console.log("加入协作组：", invitationGroupId);

      // 显示加载提示
      uni.showLoading({
        title: this.$i18n.zhToGlobal('正在加入协作组...')
      });

      // 调用后端接口加入协作组
      this.$request.post('/project-make-friends-web/invitationRelation/joinGroupByQrCode', {
        data: {
          invitationGroupId: invitationGroupId
        }
      }).then(res => {
        uni.hideLoading();
        if (res.state === 2000) {
          uni.showToast({
            title: this.$i18n.zhToGlobal('成功加入协作组'),
            icon: 'success'
          });

          // 可选：跳转到协作组详情页面
          setTimeout(() => {
            this.$kt.router.navTo('/pages/project-make-friends-pages/member-partner/member-partner-content?invitationGroupId=' + invitationGroupId);
          }, 1500);
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('加入协作组失败'),
            icon: 'none'
          });
        }
      }).catch(err => {
        uni.hideLoading();
        console.error('加入协作组失败:', err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
      });
    },

    toLogout() {
      this.isLogin = false;
      this.$kt.userAccount.setIsLogin(false);
      this.$kt.storage.remove("token");

      uni.reLaunch({
        url: this.$kt.router.config.intoPath
      });
    },
    // /tool-state-area-web/stateArea/getAll
    async getAllStateArea() {
      this.stateAreaMap = {};
      await this.$request.post("/tool-state-area-web/stateArea/getAll", {data: {}})
          .then(res => {
            this.stateAreaList = res.data;
            for (let i = 0; i < this.stateAreaList.length; i++) {
              const item = this.stateAreaList[i];
              this.stateAreaMap[item.code] = item.name;
            }
          });
      await this.$request.post("/system-language-web/languageI18n/getList", {
        data: {
          topKey: "ToolStateArea",
          bottomKey: "name",
          languageCode: this.$i18n.getLanguageCode()
        }

      }).then(res => {
        for (let i = 0; i < res.data.length; i++) {
          const item = res.data[i];
          this.stateAreaMap[item.centerKey] = item.content;
        }
        try {
          // vue更新渲染
          // this.$forceUpdate();
        } catch (e) {
          // console.log(e);
        }
      });
    },
  }

}
</script>

<style lang="scss" scoped>
.info-btn {
  width: 25px;
  height: 25px;
}


.info-btn:active {
  transform: scale(.95);
}

.back {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: #F0F0F0;
  top: 0;
  left: 0;
  z-index: -1;
}

.box-box {
  padding: 20rpx;
  box-sizing: border-box;
}

.box {
  position: relative;
  vertical-align: top;

  width: 100%;
  display: inline-block;
  text-align: left;
  background-color: #FFFFFF;
  border-radius: 20rpx;
}

.title {
  font-size: 32rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-weight: bold;
}

.line {
  width: 100%;
  height: 1rpx;
  background-color: #F0F0F0;
}

.menu-box {
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 20rpx 20rpx 10rpx 20rpx;


  .menu {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    border-bottom: 1rpx solid #F0F0F0;
    background-color: #FFFFFF;
    margin-bottom: 20rpx;


    .menu-icon-box {
      width: 50rpx;
      height: 40rpx;
    }

    .menu-icon {
      width: 40rpx;
      height: 40rpx;
    }


  }

}

.menu:active {
  opacity: .6;
}

.ip-tag {
  position: absolute;
  padding: 10rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #999;
  width: 300rpx;
  top: 20rpx;
  right: 20rpx;
  z-index: 9999;
  text-align: right;
}

.header-box {
  top: 0;
  left: 0;
  width: 100%;
}


.user-info-box {
  position: relative;
  background-color: #fff;
  padding: 20rpx;
  box-sizing: border-box;
}
.container-mode-color-scheme-light {
  .back {
    background-color: #f0f0f0;
  }
}
.container-mode-color-scheme-dark {
  .back {
    background-color: #191919;
  }

  .user-info-box {
    background-color: #191919;

    .info-btn{
      // 颜色反转
      filter: invert(1);
    }
  }

  .menu-box{
    background-color: #1f1f1f;

    .menu{
      border-bottom: 1rpx solid rgba(255,255,255,.1);

      background-color: rgba(0,0,0,0);
      color: #AAAAAA;
      .menu-icon-box{
        filter: invert(1);
      }

    }
  }

}



.container-mode-device-pc {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;

  .user-info-box{
    .user-info-card{
      left: -450rpx;
    }
  }
  .header-box {
    width: 100%;
    left: 240px;
  }
}
</style>