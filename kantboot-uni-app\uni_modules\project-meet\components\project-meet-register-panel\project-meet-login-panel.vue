<template>
  <view :class="clazz.box"
        :style="{
    borderRadius: borderRadius,
  }"
  >

    <view>
      <view class="title">
        <image
            class="logo"
            mode="aspectFit"
            :src="$kt.file.byPath('projectMeet/icon/logo.png')"></image>
        <view class="text">{{"Meetov"}}</view>
      </view>

      <view>
        <login-operate-box></login-operate-box>
      </view>
    </view>

    <view style="height: 30rpx"></view>

    <kt-keyboard-size
        ref="keyboardSize"
        @show="focus"
        @hide="blur"
    ></kt-keyboard-size>
  </view>
</template>

<script>
import KtKeyboardSize from "@/uni_modules/kantboot/components/kt-keyboard-size/kt-keyboard-size.vue";
import LoginAgreement from "./components/LoginAgreement.vue";
import LoginOperateBox from "./components/LoginOperateBox.vue";
export default {
  props:{
    borderRadius:{
      type: String,
      default: "20rpx 20rpx 0 0"
    },
  },
  components: {
    LoginAgreement,
    KtKeyboardSize,
    LoginOperateBox,
  },
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      }
    };
  },
  mounted() {
    this.$kt.event.on("login:success",(res)=>{
      if(this.$kt.userAccount.getUserAccountIdOfInviter()){
        this.$kt.userAccount.setInviter(this.$kt.userAccount.getUserAccountIdOfInviter());
        this.$kt.userAccount.removeUserAccountIdOfInviter();
      }
    });
  },
}
</script>

<style lang="scss" scoped>
.box{
  width: 100%;
  padding: 10rpx 20rpx 0 20rpx ;
  box-sizing: border-box;
  //background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  //box-shadow: 0 0 20rpx 0 rgba(118,118,118,0.1);
  text-align: left;
  // 渐变，从上到下
  background: linear-gradient(180deg, rgba(255, 255, 255, 1) 300px, rgba(255, 255, 255, 0.5) 100%);

  .title{
    font-size: 30rpx;
    text-align: center;
    color: #000;
    line-height: 40rpx;
    font-weight: bold;
    margin: 10rpx;
  }

}

.box-mode-device-pc{
  padding: 10px 40px 0 40px;
}

.box-mode-color-scheme-light{
  //background-color: #FFFFFF;
  color: #333333;
}

.box-mode-color-scheme-dark{
  background-color: #292929;
  color: #f5f5f5;
  .title{
    color: #f5f5f5;
    .logo{
      width: 80rpx;
      height: 80rpx;
    }
    .text{
      color: #f5f5f5;
    }

  }
}

.other-box{
  margin-top: 20rpx;
  width: 100%;
  text-align: center;
  .other-title{
    font-size: 24rpx;
    color: #666666;
  }
  .other-icon-box{
    display: inline-block;
    width: 100%;
    text-align: center;
    margin-top: 20rpx;
    .icon{
      width: 80rpx;
      height: 80rpx;
    }
  }
}

.logo-box{
  text-align: center;
  width: 100%;
}

.logo{
  display: inline-block;
  width: 200rpx;
  height: 100rpx;
  margin: 0 auto;
}

</style>
