<view class="data-v-19cc5828"><view class="data-v-19cc5828"><block wx:if="{{!customUserInfoCard}}"><view class="data-v-19cc5828"><kt-user-info-card vue-id="70212a9a-1" user-info="{{userAccount}}" class="data-v-19cc5828" bind:__l="__l"></kt-user-info-card></view></block><block wx:else><view class="data-v-19cc5828"><slot name="userInfoCard"></slot><scoped-slots-userInfoCard userAccount="{{userAccount}}" class="scoped-ref" bind:__l="__l"></scoped-slots-userInfoCard></view></block><view style="text-align:center;" class="data-v-19cc5828"><block wx:if="{{!$root.m0}}"><kt-qrcode vue-id="70212a9a-2" size="400rpx" content="https://www.kantboot.com" image-src="{{$root.g0}}" class="data-v-19cc5828" bind:__l="__l"></kt-qrcode></block><block wx:else><kt-qrcode vue-id="70212a9a-3" content="https://www.kantboot.com" image-src="{{$root.g1}}" class="data-v-19cc5828" bind:__l="__l"></kt-qrcode></block></view><block wx:if="{{hasDirectCode&&userAccount.directCode}}"><view style="text-align:center;font-size:26rpx;color:#333333;" class="data-v-19cc5828"><text style="vertical-align:top;" class="data-v-19cc5828">{{''+$root.g2+": "+userAccount.directCode+''}}</text><image class="copy-icon data-v-19cc5828" src="{{$root.g3}}" data-event-opts="{{[['tap',[['toCopy',['$0'],['userAccount.directCode']]]]]}}" bindtap="__e"></image></view></block><view style="height:40rpx;" class="data-v-19cc5828"></view><view class="data-v-19cc5828"><block wx:if="{{userAccount.id}}"><kt-button vue-id="70212a9a-4" open-type="share" class="data-v-19cc5828" bind:__l="__l" vue-slots="{{['default']}}">{{''+$root.g4+''}}</kt-button></block></view></view></view>