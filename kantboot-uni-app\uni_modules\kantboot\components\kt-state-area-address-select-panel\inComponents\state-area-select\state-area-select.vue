<template>
  <view>
    <view :class="clazz.box">
      <view class="header">
        <view class="search-input-box">
          <input
              class="search-input"
              v-model="keywords"
              @input="input"
              :placeholder="$i18n.zhToGlobal('关键字')"
          ></input>
        </view>
        <view style="height: 20rpx"></view>
      </view>
      <view
          class="area-select-box"
          :style="{
             height: height,
            }"
      >
        <kt-menu-box>
          <kt-menu
              v-for="item in stateAreaListOfFilter"
              :title="item.name"
              @click="select(item)"
              :is-right="item.adLevel>1"
          ></kt-menu>
        </kt-menu-box>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props:{
    height: {
      type: String,
      default: "calc(100vh - 700rpx)"
    }
  },
  data() {
    return {
      selected:{
        code:''
      },
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      },
      keywords:"",
      stateAreaList: [],
      stateAreaListOfFilter: [],

    };
  },
  created() {
    this.getAll();
  },
  methods: {
    select(item) {
      this.selected = item;
      this.$emit("select", item);
    },
    input(e) {
      this.keywords = e.detail.value;
      this.filter();
    },
    filter() {
      this.stateAreaListOfFilter = this.stateAreaList.filter(item => {
        return item.name.indexOf(this.keywords) > -1;
      });
    },
    getAll() {
      this.$request.post("/tool-state-area-web/stateArea/getAll").then(res => {
        this.stateAreaList = res.data;
        this.filter();
      });
    },

  },
}
</script>

<style lang="scss" scoped>

.search-input-box{
  width: 100%;
  border: 1px solid #E5E5E5;
  border-radius: 10rpx;
  padding: 20rpx;
  box-sizing: border-box;
  .search-input{
    height: 60rpx;
  }
}

.area-select-box{
  // 超出滚动条
  overflow-y: scroll;
  padding: 20rpx 40rpx;

  .area-select-box-tag{
    display: inline-block;
    padding: 10rpx 20rpx;
    border-radius: 10rpx;
    color:#333333;
    background-color: #F5F5F5;
    margin-right: 30rpx;
    margin-bottom: 30rpx;
  }

}
// 滚动条样式
.area-select-box::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}


.box-mode-device-pc{
  .area-select-box{
    .area-select-box-tag{
      cursor: pointer;
    }
  }
}
</style>
