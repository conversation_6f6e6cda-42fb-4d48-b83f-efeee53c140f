<template>
  <view>
    <kt-popup
    ref="ktPopup">
      <view :class="clazz.box">
        <kt-pay-panel
			@close="close()"
            @calling="calling()"
            @paySuccess="paySuccess()"
            :pay-methods="payMethods"
            ref="ktPayPanel">
        </kt-pay-panel>
      </view>
    </kt-popup>

  </view>
</template>

<script>
export default {
  props : {
    payMethods: {
      type: String,
      default: ['paypal']
    }
  },
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      }
    };
  },
  methods: {
	close(){
		this.$refs.ktPopup.close();
	},
    calling(){
		// this.$refs.ktPopup.close();
		this.$emit("calling");
    },
    open(orderId){
      setTimeout(()=>{
        this.$refs.ktPayPanel.getOrder(orderId);
      },100);
      console.log("open");
      this.$refs.ktPopup.open();
    },
    paySuccess(){
      setTimeout(()=>{
        this.$refs.ktPopup.close();
      },2500);
      this.$emit("paySuccess");
    }
  },
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  padding: 20rpx;
  box-sizing: border-box;
}

.box-mode-device-pc {
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 20rpx;
}
</style>
