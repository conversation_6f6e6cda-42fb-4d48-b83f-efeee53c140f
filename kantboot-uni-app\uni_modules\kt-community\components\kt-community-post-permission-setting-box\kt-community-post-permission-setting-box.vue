<template>
  <view>
    <view :class="clazz.content">

      <view
          @click="$refs.communityPostVisibleTypePopup.open(value.visibleType)"
          class="menu-box">
        <view class="menu-title">
          <view class="menu-icon">
            <image
                class="menu-icon-image"
                :src="$kt.file.byPath('fpCommunity/icon/fiveDot.svg')"></image>
          </view>
          {{$i18n.zhToGlobal("可见类型")}}
        </view>
        <view class="menu-fixed">
          <view class="menu-fixed-tips">
          </view>
          <view class="menu-fixed-content">
            {{ enums.getVisibleType(value.visibleType) }}
          </view>
        </view>
      </view>

      <view
          @click="$refs.communityPostVisibleScopePopup.open(value.visibleScope)"
          class="menu-box">
        <view class="menu-title">
          <view class="menu-icon">
            <image
                class="menu-icon-image"
                :src="$kt.file.byPath('fpCommunity/icon/fiveDot.svg')"></image>
          </view>
          {{$i18n.zhToGlobal("可见范围")}}
        </view>
        <view class="menu-fixed">
          <view class="menu-fixed-tips">
          </view>
          <view class="menu-fixed-content">
            {{ enums.getVisibleScope(value.visibleScope) }}
          </view>
        </view>
      </view>

      <view>

        <view
            @click="isMoreSetting=!isMoreSetting"
            class="menu-box">
          <view class="menu-title">
            <view class="menu-icon">
              <image
                  class="menu-icon-image"
                  :src="$kt.file.byPath('fpCommunity/icon/setting.svg')"></image>
            </view>
            {{$i18n.zhToGlobal("更多设置")}}
          </view>
          <view class="menu-fixed">

            <view class="menu-fixed-content">
              <image
                  class="menu-fixed-content-icon"
                  :class="{
                      'menu-fixed-content-icon-bottom': !isMoreSetting,
                      'menu-fixed-content-icon-top': isMoreSetting
                    }"
                  :src="$kt.file.byPath('fpCommunity/icon/arrowRight.svg')"></image>
            </view>

          </view>
        </view>

        <view
            v-show="isMoreSetting"
            class="menu-box-box">
          <view class="menu-box">
            <view class="menu-title">
              <view class="menu-icon">
                <image
                    class="menu-icon-image"
                    :src="$kt.file.byPath('fpCommunity/icon/noAllowComment.svg')"></image>
              </view>
              {{$i18n.zhToGlobal("禁止评论")}}
            </view>
            <view class="menu-fixed">
              <view class="menu-fixed-tips">
                {{ value.isForbidComment ? $i18n.zhToGlobal('是') : $i18n.zhToGlobal('否') }}
              </view>
              <view class="menu-fixed-content">
                <u-switch
                    active-color="rgba(0,0,0)"
                    v-model="value.isForbidComment"
                    size="30"></u-switch>
              </view>

            </view>
          </view>

          <view class="menu-box">
            <view class="menu-title">
              <view class="menu-icon">
                <image
                    class="menu-icon-image"
                    :src="$kt.file.byPath('fpCommunity/icon/noAllowForward.svg')"></image>
              </view>
              {{$i18n.zhToGlobal("禁止转发")}}
            </view>
            <view class="menu-fixed">
              <view class="menu-fixed-tips">
                {{ value.isForbidForward ? $i18n.zhToGlobal('是') : $i18n.zhToGlobal('否') }}
              </view>
              <view class="menu-fixed-content">
                <u-switch
                    active-color="rgba(0,0,0)"
                    v-model="value.isForbidForward"
                    size="30"></u-switch>
              </view>
            </view>
          </view>

          <view class="menu-box">
            <view class="menu-title">
              <view class="menu-icon">
                <image
                    class="menu-icon-image"
                    :src="$kt.file.byPath('fpCommunity/icon/noAllowLike.svg')"></image>
              </view>
              {{$i18n.zhToGlobal("禁止点赞")}}
            </view>
            <view class="menu-fixed">
              <view class="menu-fixed-tips">
                {{ value.isForbidLike ? $i18n.zhToGlobal('是') : $i18n.zhToGlobal('否') }}
              </view>
              <view class="menu-fixed-content">
                <u-switch
                    active-color="rgba(0,0,0)"
                    v-model="value.isForbidLike"
                    size="30"></u-switch>
              </view>
            </view>
          </view>

          <view class="menu-box">
            <view class="menu-title">
              <view class="menu-icon">
                <image
                    class="menu-icon-image"
                    :src="$kt.file.byPath('fpCommunity/icon/noAllowCollect.svg')"></image>
              </view>
              {{$i18n.zhToGlobal("禁止收藏")}}
            </view>
            <view class="menu-fixed">
              <view class="menu-fixed-tips">
                {{ value.isForbidCollect ? $i18n.zhToGlobal('是') : $i18n.zhToGlobal('否') }}
              </view>
              <view class="menu-fixed-content">
                <u-switch
                    active-color="rgba(0,0,0)"
                    v-model="value.isForbidCollect"
                    size="30"></u-switch>
              </view>
            </view>
          </view>

        </view>

      </view>


    </view>

    <visible-type-popup
        ref="communityPostVisibleTypePopup"
        @select="value.visibleType = $event">
    </visible-type-popup>

    <visible-scope-popup
        :customUserAccountsSelectPopup="customUserAccountsSelectPopup"
        :customUserInfoCard="customUserInfoCard"
        @select="value.visibleScope = $event"
        @openPartVisible="openPartVisible"
        @openNotVisible="openNotVisible"
        @userAccountIdsOfPartVisibleSelect="selectPartIds"
        @userAccountIdsOfNotVisibleSelect="selectNotIdSelect"
        ref="communityPostVisibleScopePopup">
      <template v-slot:userInfoCardOfPartVisible="{userAccountId}">
        <slot name="userInfoCardOfPartVisible"
              :user-account-id="userAccountId"></slot>
      </template>
      <template v-slot:userInfoCardOfNotVisible="{userAccountId}">
        <slot name="userInfoCardOfNotVisible"
              :user-account-id="userAccountId"></slot>
      </template>
    </visible-scope-popup>
  </view>
</template>

<script>
import enums from "@/uni_modules/kt-community/libs/enums";
import api from "@/uni_modules/kt-community/libs/api";
import visibleTypePopup from "./components/visible-type-popup.vue";
import visibleScopePopup from "./components/visible-scope-popup.vue";

export default {
  components: {
    visibleTypePopup,
    visibleScopePopup
  },
  props:{
    /**
     * 是否自定义用户弹出层
     */
    customUserAccountsSelectPopup:{
      type: Boolean,
      default: false
    },
    customUserInfoCard:{
      type: Boolean,
      default: false
    },
    value:{
      type:Object,
      default(){
        return {
          // 可见范围
          visibleScope: "all",
          visibleType: "public",
          // 禁止评论
          isForbidComment: false,
          // 禁止转发
          isForbidForward: false,
          // 禁止点赞
          isForbidLike: false,
          // 禁止收藏
          isForbidCollect: false,
          // 可见范围
          userAccountIdsOfPartVisible: [],
          userAccountIdsOfNotVisible: [],
        }
      }
    },
  },
  data() {
    return {
      enums,
      api,
      clazz:{
        content: this.$kt.style.toggleClass("content")
      },
      isMoreSetting: false,
      loading:false
    };
  },
  methods: {
    async updatePostPermission(){
      this.loading = true;
      await this.api.updatePostPermission(this.value)
          .then((res)=>{
            this.loading = false;
            this.$emit("updatePostPermissionSuccess",res);
          })
          .catch((err)=>{
            this.loading = false;
            this.$emit("updatePostPermissionFail",err);
          });
    },
    selectPartIds(idSelect){
      this.value.userAccountIdsOfPartVisible = idSelect;
      this.$refs.selectPartIds.selectPartIds(idSelect);
    },
    selectNotIdSelect(idSelect){
      this.value.userAccountIdsOfNotVisible = idSelect;
      this.$refs.selectNotIdSelect.selectNotIdSelect(idSelect);
    },
    /**
     * 打开部分可见的用户选择
     * @param event
     */
    openPartVisible(event){
      this.$emit("openPartVisible",event)
    },
    /**
     * 打开不可见的用户选择
     * @param event
     */
    openNotVisible(event){
      this.$emit("openNotVisible",event)
    },
  },
}
</script>

<style lang="scss" scoped>


.menu-box{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  .menu-title{
    font-size: 28rpx;
    color: #333;
    vertical-align: top;
    .menu-icon{
      width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
      display: inline-block;
      vertical-align: top;
      .menu-icon-image{
        width: 100%;
        height: 100%;
        opacity: .8;
      }
    }
  }
  .menu-fixed{
    position: relative;
    .menu-fixed-tips{
      position: absolute;
      font-size: 28rpx;
      color: #333;
      text-align: right;
      width: 300rpx;
      right: 70rpx;
      vertical-align: top;
      margin-right: 5rpx;
    }
    .menu-fixed-content{
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      color: #666;
      .menu-fixed-content-icon{
        width: 20rpx;
        height: 20rpx;
      }
      .menu-fixed-content-icon-bottom{
        transform: rotate(90deg);
      }
      .menu-fixed-content-icon-top{
        transform: rotate(-90deg);
      }
    }
  }
}

.menu-box-box{
  padding: 0 20rpx 20rpx 20rpx;
}

.content-mode-device-pc{
  .menu-box{
    cursor: pointer;
  }
}
</style>
