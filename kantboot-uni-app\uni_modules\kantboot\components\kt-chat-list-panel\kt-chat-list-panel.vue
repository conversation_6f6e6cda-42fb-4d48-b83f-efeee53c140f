<template>
  <view>
    <scroll-view
        :style="'height: calc(100vh - '+navBarHeight+'px - 20rpx - 50rpx)'"
        scroll-y
        @scroll="scroll"
    >
      <view class="box">
        <view style="height: 20rpx"></view>
        <view
            @click="$refs.notifyListPopup.open()"
            class="in-box"
            :class="{
              'in-box-selected': '-1' === selected+'',
            }"
        >
          <kt-user-info-card
              class="in-box-user-info-card"
              :user-info="{
                'id': '-1',
                'srcOfAvatar': $kt.file.byPath('kantboot/icon/customerService.svg'),
                'nickname':$i18n.zhToGlobal('系统通知'),
                'isGenderSecret':true,
                'introduction': $i18n.zhToGlobal('系统通知'),
              }"
          >
          </kt-user-info-card>
          <view
              class="unread-count"
              v-if="notifyUnreadCount>0"
          >{{notifyUnreadCount}}</view>

        </view>

        <view
            @click="cardClick(dialogOfCustomerService)"
            class="in-box"
            :class="{
              'in-box-selected': dialogOfCustomerService.id === selected,
            }"
        >
          <kt-user-info-card
              @click="cardClick(dialogOfCustomerService)"
              class="in-box-user-info-card"
              :user-info="{
                'id': '0',
                'srcOfAvatar': $kt.file.byPath('kantboot/icon/customerService.png'),
                'nickname':$i18n.zhToGlobal('客服'),
                'isGenderSecret':true,
                'introduction': $i18n.zhToGlobal('客服')
              }"
          >
          </kt-user-info-card>
          <view
              class="unread-count"
              v-if="getUnreadCount(dialogOfCustomerService.id)>0"
          >{{getUnreadCount(dialogOfCustomerService.id)}}</view>

        </view>

        <view v-for="(item, index) in list" :key="index"
              class="in-box"
              :class="{
                'in-box-selected':item.id === selected,
              }"
              v-if="item.id !== dialogOfCustomerService.id"
              @click="cardClick(item)">

          <kt-user-info-card
              :showAge="false"
              class="in-box-user-info-card"
              :user-info="item.userAccount">
          </kt-user-info-card>

          <view
              class="unread-count"
              v-if="getUnreadCount(item.id)>0"
          >{{getUnreadCount(item.id)}}</view>

        </view>

        <view style="height: 100rpx"></view>
      </view>
    </scroll-view>

    <kt-notify-list-popup
        ref="notifyListPopup"
    ></kt-notify-list-popup>
  </view>
</template>

<script>
export default {
  props: {
    height: {
      type: String,
      default: "100vh"
    },
    selected: {
      type: String | Number,
      default: ""
    }
  },
  watch: {
    remarkMap: {
      handler(val) {
        this.toRemark();
      },
      immediate: true,
    },
    list: {
      handler(val) {
        this.toRemark();
      },
      immediate: true,
      deep:true
    },
  },
  data() {
    return {
      // 组件高度
      navBarHeight: 0,
      // 是否加载完成
      isLoad: false,
      scrollTop: 0,
      list: [],
      dialogOfCustomerService: {
        id: "0",
      },
      unreadMap: {

      },
      notifyUnreadCount: 0,
      // 备注map
      remarkMap: {},
    };
  },
  mounted() {

  },
  created() {
    // 获取列表数据
    this.getInitList();
    // 监听登录成功事件
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.getInitList();
    });

    // 接收到消息，将lastMessage更新到列表中
    this.$kt.event.on('FunctionalChatDialogMessage:sendMessage', (lastMessage) => {
      console.log(lastMessage, "pageMessage收到新消息");
      if (lastMessage && lastMessage.dialogId) {
        this.updateMessageInList(lastMessage);
        this.toRemark();
      }
    });

    // "FunctionalNotify:relationship"
    this.$kt.event.on("FunctionalNotify:handleRelationship",(res)=>{
      if(res.unreadCount){
        this.notifyUnreadCount = res.unreadCount;
      }else{
        this.notifyUnreadCount = 0;
      }
    })

    // FunctionalChatDialogMessage.setDeletedStatus
    this.$kt.event.on("FunctionalChatDialogMessage.setDeletedStatus",(res)=>{
      // 重新刷新
      this.getInitList();
    })

    this.$kt.event.on("FunctionalChatDialogMessage.handleRelationship",(res)=>{

      let dialogRelationship = res.dialogRelationship;
      if (dialogRelationship && dialogRelationship.dialogId) {
        this.unreadMap[dialogRelationship.dialogId + ""] = dialogRelationship.unreadCount;
      }
      try{
        this.$forceUpdate();
      }catch (e) {

      }


    });

    this.$kt.event.on("remarkChange",()=>{
      this.getRemarkSelf();
    });

    this.getByCustomerService();
    this.getRelationshipBySelf();

  },
  methods: {

    getRelationshipBySelf() {
      this.$kt.request.post("/functional-notify-web/notify/getRelationshipBySelf", {
        data: {}
      }).then(res => {
        console.log(res, "getRelationshipBySelf");
        this.notifyUnreadCount = res.data.unreadCount;
      }).catch(err => {
        console.error("获取通知关系失败", err);
      });
    },

// /user-interrelation-web/interrelationRemark/getSelf
    async getRemarkSelf() {
      return this.$kt.request.post("/user-interrelation-web/interrelationRemark/getBySelf", {
        data: {}
      }).then(res => {
        // this.remarkMap = res.data;
        this.remarkMap = {};
        for (let i = 0; i < res.data.length; i++) {
          let item = res.data[i];
          this.remarkMap[item.userAccountIdOfRemark+""] = item.remark;
        }

        this.toRemark();


      }).catch(err => {
        console.error("获取备注失败", err);
      });
    },
    toRemark() {
      for (let i = 0; i < this.list.length; i++) {
        let item = this.list[i];
        if (this.remarkMap[item.userAccount.id]&&item.userAccount) {
          item.userAccount.remark = this.remarkMap[item.userAccount.id+""];
          console.log(item.userAccount, "item.userAccount");
        } else {
        }
      }
      try{
        this.$forceUpdate();
      }catch (e) {

      }
    },
    listToIds(list) {
      let ids = [];
      for (let i = 0; i < list.length; i++) {
        ids.push(list[i].id);
      }
      return ids;
    },
    getUnreadCountSelfByDialogIds(dialogIds){
      this.$kt.request.post("/functional-chat-web/relationship/getUnreadCountSelfByDialogIds", {
        data: {
          dialogIds: dialogIds
        }
      }).then(async (res) => {
        console.log(res, "getUnreadCountSelfByDialogIds");
        this.unreadMap = {};
        if(res.data==null){
          return;
        }
        for(let i=0;i<res.data.length;i++){
          let item = res.data[i];
          this.unreadMap[item.dialogId] = item.unreadCount;
        }
      }).catch(err => {
        console.error("获取未读消息数量失败", err);
      });
    },
    getUnreadCount(dialogId) {
      let count = this.unreadMap[dialogId+""];
      if (!count) {
        return 0;
      }
      return count;
    },

    getByCustomerService() {
      // /functional-chat-web/dialog/getByCustomerService
      this.$kt.request.post("/functional-chat-web/dialog/getByCustomerService", {
        data: {}
      }).then(async (res) => {
        this.dialogOfCustomerService = res.data;
      }).catch(err => {
      });
    },
    cardClick(item) {
      // this.$kt.router.navTo('/pages/chat/chat?dialogId='+item.id);
      this.$emit("cardClick", item);
    },
    scroll(res) {
      this.scrollTop = this.$kt.util.pxToRpx(res.target.scrollTop);
    },
    /**
     * 更新消息列表中的消息或添加新对话
     * @param messageData 新消息数据
     */
    async updateMessageInList(messageData) {
      try {
        const dialogId = messageData.dialogId;
        const lastMessage = messageData || {};

        // 查找是否已存在此对话
        const existingIndex = this.list.findIndex(item => item.id === dialogId);

        if (existingIndex >= 0) {
          // 对话已存在，更新最后一条消息
          const updatedItem = {
            ...this.list[existingIndex],
            lastMessage: lastMessage,
            lastMessageJsonStr: JSON.stringify(lastMessage)
          };

          // 更新对话信息
          const updatedDialog = await this.updateItem(updatedItem);

          // 删除旧位置
          this.list.splice(existingIndex, 1);
          // 添加到列表顶部
          this.list.unshift(updatedDialog);
        } else {
          // 对话不存在，获取对话信息并添加到列表
          this.$kt.request.post("/functional-chat-web/dialog/getById", {
            data: {id: dialogId}
          }).then(async (res) => {
            if (res.data) {
              const newDialog = {
                ...res.data,
                lastMessage: lastMessage,
                lastMessageJsonStr: JSON.stringify(lastMessage)
              };

              // 完善对话信息
              const updatedDialog = await this.updateItem(newDialog);
              // 添加到列表顶部
              this.list.unshift(updatedDialog);
              this.toRemark()
            }
          });
        }
      } catch (err) {
        console.error("更新消息列表失败", err);
      }
    },
    /**
     * 修改item信息
     */
    updateItem(item) {
      return new Promise((resolve, reject) => {
        let oneToOneIdentifierSplit = item.oneToOneIdentifier.split("&");

        let userAccountId = oneToOneIdentifierSplit[0].split(":")[1];
        if (userAccountId + "" === this.$kt.userAccount.getSelf().id + "") {
          userAccountId = oneToOneIdentifierSplit[1].split(":")[1];
        }
        this.$kt.userAccount.getById(userAccountId).then((res) => {
          console.log(res, "userAccount");
          let lastMessage = {};
          if (item.lastMessage) {
            lastMessage = item.lastMessage;
          } else {
            // 最后一条消息
            lastMessage = JSON.parse(item.lastMessageJsonStr || "{}");
            console.log(lastMessage, "lastMessage");
          }
          res.remark = this.remarkMap[userAccountId + ""] || "";
          res.introduction = "";
          try {
            let type = lastMessage.items[0].type;
            let content = lastMessage.items[0].content;
            if (type === "text") {
              res.introduction = content;
            } else if (type.startsWith("image")) {
              res.introduction = "[" + this.$i18n.zhToGlobal("图片") + "]";
            } else if (type.startsWith("userAccount")) {
              res.introduction = "[" + this.$i18n.zhToGlobal("用户名片") + "]";
            } else if (type.startsWith("video")) {
              res.introduction = "[" + this.$i18n.zhToGlobal("视频") + "]";
            } else if (type.startsWith("audio")) {
              res.introduction = "[" + this.$i18n.zhToGlobal("音频") + "]";
            } else if(type.startsWith("projectMeetGift")){
              res.introduction = "["+ this.$i18n.zhToGlobal("礼物") +"]"
            }
            else if(type.startsWith("projectMeetGold")){
              res.introduction = "["+ this.$i18n.zhToGlobal("金币") +"]"
            } else{
              res.introduction = "["+this.$kt.date.toReadable(lastMessage.gmtCreate)+"]"
            }
          } catch (err) {
            reject(err);
          }
          item = {
            ...item,
            userAccount: res,
            lastMessageJsonStr: lastMessage,
          };
          resolve(item);
        });
      }).catch(err => {
        reject(item);
      })

    },
    getInitList() {
      // /functional-chat-web/dialog/getBySelf
      this.$kt.request.post("/functional-chat-web/dialog/getBySelf", {
        data: {}
      }).then(async (res) => {
        this.list = [];
        let list = res.data;
        console.log(list, "userAccount");
        for (let i = 0; i < list.length; i++) {
          let item = list[i];
          if (!item.oneToOneIdentifier) {
            continue;
          }
          await this.updateItem(item).then((res) => {
            this.list.push(res);
          });

          this.getUnreadCountSelfByDialogIds(this.listToIds(this.list));
          this.getRemarkSelf();

        }
      }).catch(err => {
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  padding: 20rpx;
  box-sizing: border-box;

}

.bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  // 从白到黑的渐变，从上到下
  //background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}

.bg-mode-color-scheme-light {
  //background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
  background-color: #FFFFFF;
}

.bg-mode-color-scheme-dark {
  background-color: #191919;
}

.in-box {
  position: relative;
  height: 170rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .in-box-user-info-card {
    position: absolute;
    width: 100%;
    top: 15rpx;
    left: 0
  }

  .unread-count{
    position: absolute;
    right: 20rpx;
    top: 20rpx;
    background-color: rgba(255,0,0,.7);
    color: #FFFFFF;
    font-size: 25rpx;
    padding: 0 10rpx 0 10rpx;
    border-radius: 10rpx;
  }
}

.container-mode-color-scheme-dark {
  .in-box {
    border-bottom: 1rpx solid #404a56;
  }
}

.container-mode-device-pc {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;

  .box {
    .in-box {
      position: relative;
      width: 100%;
      height: 160rpx;
      cursor: pointer;

      .in-box-user-info-card {
        position: absolute;
        width: 100%;
        top: 15rpx;
        left: -450rpx
      }
    }
  }

  .header-box {
    width: 100%;
  }
}

.in-box-selected {
  //background-color: #f0f0f0;
  background-color: #cccccc;
}
</style>
