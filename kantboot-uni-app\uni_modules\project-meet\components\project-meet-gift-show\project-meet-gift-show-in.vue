<template>
  　　<view>
        <view class="bg"></view>
        <div id="svgaBox"></div>
  　　</view>
</template>
<script>
import SVGA from 'svgaplayerweb'
export default {
  props:{
    fileId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      player:{},
      parser:{},
      uuid: ""
    }
  },
  created() {
  },
  watch: {
    uuid(newUuid) {
    }
  },
  mounted() {
    this.uuid = this.$kt.util.generateUUID();
    // 动态引入类库
    const script = document.createElement('script')
    script.src = SVGA
    document.head.appendChild(script)
    script.onload = this.ready();

  },
  methods: {
    ready(){
      this.player = new SVGA.Player(document.getElementById('svgaBox'));
      this.parser = new SVGA.Parser(document.getElementById('svgaBox'));
      //注意，这里必须是服务器地址，否则报错
      this.parser.load(this.$kt.file.visit(this.fileId),
          (videoItem)=> {
            this.player.loops = 1; // 设置循环播放次数是1
            this.player.setVideoItem(videoItem);
            this.player.startAnimation();
            // 动画播放至某帧后回调
            this.player.onFrame((i)=>{
              // console.log("播放到第"+i+"帧")
            })
            // 动画播放至某进度后回调
            this.player.onPercentage((i)=>{
              // console.log("播放到"+(i*100)+"%")
            })
            // 动画播放结束回调
            this.player.onFinished(res=>{
              console.log("动画结束");
              // 清空画布
              this.player.clear()
              this.$emit("close");
            })

          }
      );
    }
  }
};
</script>
<style>
.bg{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999999999;
  background-color: rgba(0, 0, 0, 0.2);
}

#svgaBox{
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 199999999999;
}
</style>