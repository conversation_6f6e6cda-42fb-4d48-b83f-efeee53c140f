<template>
  <view>
    <kt-popup
        ref="ktPopup">
      <view class="box">
        <view class="box-title">{{$i18n.zhToGlobal("职业选择")}}</view>
        <view style="height: 30rpx"></view>
        <view>
          <scroll-view
              :scroll-y="true"
              style="height: calc(100vh - 300rpx);width: 300rpx;display: inline-block">
            <view>
              <view
                  class="bl-box"
                  v-for="item in list"
                  :key="item.id"
                  :class="{
              'bl-box-item-selected': parentSelected.id === item.id
            }"
                  @click="parentSelect(item)"
              >
                <view class="bl-box-item">{{ item.name }}</view>
              </view>
            </view>
          </scroll-view>

          <scroll-view
              :scroll-y="true"
              style="height: calc(100vh - 300rpx);width: calc(100% - 300rpx);display: inline-block">
            <view>
              <view
                  class="bl-box"
                  v-for="item in parentSelected.children"
                  :key="item.id"
                  :class="{
              'bl-box-item-selected': isSelected(item)
            }"
                  @click="toggleSelect(item)"
              >
                <view class="bl-box-item">{{ item.name }}</view>
              </view>
            </view>
          </scroll-view>
        </view>
        <view style="margin-top: 30rpx; text-align: center;">
          <kt-button type="primary" @click="confirmSelect">{{ $i18n.zhToGlobal('确定') }}</kt-button>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      parentSelected: {
        id: null
      },
      selectedList: [] // 多选
    };
  },
  mounted() {
    // this.open();
  },
  methods: {
    getList() {
      // /project-make-friends-web/admin/jobType/getAllParentIsNull
      this.$request.get('/project-make-friends-web/admin/jobType/getAllParentIsNull').then(res => {
        this.list = res.data;
        if (this.list.length > 0) {
          this.parentSelected = this.list[0];
        } else {
          this.parentSelected = {id: null};
          this.selectedList = [];
        }
      }).catch(err => {
      })
    },
    parentSelect(item) {
      this.parentSelected = item;
    },
    isSelected(item) {
      return this.selectedList.some(sel => sel.id === item.id);
    },
    toggleSelect(item) {
      const idx = this.selectedList.findIndex(sel => sel.id === item.id);
      if (idx > -1) {
        this.selectedList.splice(idx, 1);
      } else {
        this.selectedList.push(item);
      }
    },
    confirmSelect() {
      this.$emit('select', this.selectedList);
      this.close();
    },
    open(selectedIds = []) {
      this.parentSelected = {
        id: null
      };
      // 如果传入的是id数组，需转换为对象数组
      if (Array.isArray(selectedIds) && selectedIds.length > 0 && typeof selectedIds[0] !== 'object') {
        // 需要等getList后再转换
        this.selectedList = [];
        this.getListWithSelectedIds(selectedIds);
      } else {
        this.selectedList = Array.isArray(selectedIds) ? selectedIds : [];
        this.getList();
      }
      this.$refs.ktPopup.open();
    },
    // 辅助：getList后根据id数组恢复对象数组
    getListWithSelectedIds(selectedIds) {
      this.$request.get('/project-make-friends-web/admin/jobType/getAllParentIsNull').then(res => {
        this.list = res.data;
        if (this.list.length > 0) {
          this.parentSelected = this.list[0];
        } else {
          this.parentSelected = {id: null};
        }
        // 扁平化所有子职业
        const allChildren = this.list.flatMap(p => p.children || []);
        this.selectedList = selectedIds.map(id => allChildren.find(c => c.id === id)).filter(Boolean);
      }).catch(err => {});
    },
    close() {
      this.$refs.ktPopup.close();
    },

  },
}
</script>

<style lang="scss" scoped>
.box {
  padding: 20rpx 40rpx 20rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;

  .box-title {
    font-weight: bold;
  }
}

.bl-box {
  text-align: left;
  width: 100%;

  .bl-box-item {
    padding: 20rpx;
    font-size: 28rpx;
    color: #333;
    border-bottom: 1px solid #eee;
    // 超出省略
    overflow: hidden;
  }
}

.bl-box-item-selected {
  background-color: rgba(0, 0, 0, .2);
}
</style>
