<template>
  <view class="container">
    <view id="findPageHeader">
      <project-acm-nav-bar
          :is-has-i18n="false"
          :icon="$kt.file.byPath('tabbar/practice-selected.svg')"
          :title="$i18n.zhToGlobal('问答')"></project-acm-nav-bar>
    </view>
    <view :class="clazz.bg"></view>

        <kt-community-post-panel
            @cardClick="cardClick"
            @cardDotClick="cardDotClick"
            :has-card-bottom-operation="false"
            ref="communityPostPanel"
            :height="'calc(100vh - '+headerHeight+'px - 170rpx - 30rpx - 70rpx)'" >
          <template v-slot:cardBottom="{postId}">
            <view>
              <project-acm-card-bottom :post-id="postId"></project-acm-card-bottom>
            </view>
          </template>
        </kt-community-post-panel>


    <kt-community-post-operate-popup
        :remove-text="$kt.i18n.zhToGlobal('删除')"
        :report-text="$kt.i18n.zhToGlobal('举报')"
        :has-permission-setting="false"
        ref="communityPostOperatePopup">
    </kt-community-post-operate-popup>

    <view class="bottom-btn-box">
      <kt-button
          @click="$kt.router.navTo('/pages/project-acm-pages/post-push/post-push')"
      :is-open-box-shadow="false"
      >{{$i18n.zhToGlobal("我要提问")}}</kt-button>
    </view>
    </view>
</template>

<script>


export default {
  data() {
    return {
      clazz:{
        bg: this.$kt.style.toggleClass("bg"),
      },
      headerHeight: 0,
    }
  },
  mounted() {
    uni.createSelectorQuery().in(this).select('#findPageHeader').boundingClientRect((rect) => {
      console.log(rect,"获取headerHeight的高度")
      this.headerHeight = rect.height;
    }).exec();

    this.$kt.event.on("projectAcm:sendPostComment",()=>{
      this.$refs.communityPostPanel.refresh();
    });
  },
  methods: {
    cardDotClick(post){
      if(!this.$kt.userAccount.getIsLogin()){
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.$refs.communityPostOperatePopup.open(post);
    },
    cardClick(post){
      this.$kt.router.navTo("/pages/project-acm-pages/post-detail/post-detail",{
        postId: post.id
      });
    },
  },

}
</script>

<style lang="scss" scoped>
.bg{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  // 渐变，从300rpx开始，从上到下
  background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}

.bg-mode-color-scheme-light{
  // 渐变，从300rpx开始，从上到下
  background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}

.bg-mode-color-scheme-dark{
  background: #191919;
}

.bottom-btn-box{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  z-index: 99;
  padding: 30rpx 30rpx 240rpx 30rpx;
  box-sizing: border-box;
}

</style>