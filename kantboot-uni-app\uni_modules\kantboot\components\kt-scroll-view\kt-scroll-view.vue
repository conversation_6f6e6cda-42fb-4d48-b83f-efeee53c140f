<template>
    <scroll-view
        scroll-y
        class="scroll-view"
        :style="{height,}"
        @scroll="onScroll"
        ref="scrollView"
        @scrolltolower="onScrolltolower"
        @scrolltoupper="onScrolltoupper"
        :scroll-into-view="scrollIntoViewId"
    >
      <view>
        <slot></slot>
        <view style="height: 20rpx"></view>
        <view :id="scrollIntoViewIdFinal"></view>

      </view>
    </scroll-view>
</template>

<script>

export default {
  props: {
    height: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      scrollIntoViewIdFinal: '',
      scrollIntoViewId: 'scrollIntoViewId',
      uuid: null,
      isMoveToEnd: true,
    };
  },
  created() {
    this.initScrollViewId();
  },
  watch: {
  },
  methods: {
    setScrollIntoViewId(id) {
      this.scrollIntoViewId = ""; // 清空之前的值
      this.$nextTick(() => {
        this.scrollIntoViewId = id; // 确保 DOM 渲染完成后再设置
      });
    },
    onScroll(res) {
      this.isMoveToEnd = false;
      this.$emit('scroll', res);
    },
    onScrolltolower(res) {
      this.$emit('scrolltolower', res);
      setTimeout(() => {
        this.isMoveToEnd = true;
        console.log(this.isMoveToEnd);
      }, 100);
    },
    onScrolltoupper(res) {
      this.$emit('scrolltoupper', res);
    },
    // 移动到最后
    moveToEnd() {
      if (!this.isMoveToEnd) {
        return;
      }
      if (this.loading) {
        return;
      }
      this.scrollIntoViewId = "";
      setTimeout(()=>{
        this.scrollIntoViewId = ""+this.scrollIntoViewIdFinal;
      },1);
      setTimeout(() => {
        this.scrollIntoViewId = ""+this.scrollIntoViewIdFinal;
        this.loadFinish = true;
      }, 100);
    },
    /**
     * 强制移到最底部
     */
    moveToEndForce() {
      this.isMoveToEnd = true;
      this.moveToEnd();
    },
    initScrollViewId() {
      this.uuid = this.$kt.util.generateUUID();
      this.scrollIntoViewIdFinal = 'scrollIntoView_' + this.uuid;
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
