@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.show-box.data-v-22e03eda {
  width: 100%;
  height: 180rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  padding: 20rpx;
  border: 5rpx solid #FFFFFF;
  background: linear-gradient(to right, rgba(228, 234, 255, 0.8) 20%, rgba(173, 192, 255, 0.7) 80%, rgba(173, 192, 255, 0.3) 100%);
}
.show-box .show-box-image.data-v-22e03eda {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  object-fit: cover;
}
.show-box .show-box-in-box.data-v-22e03eda {
  position: absolute;
  display: inline-block;
  vertical-align: top;
  margin-top: 10rpx;
  margin-left: 10rpx;
  text-shadow: 0 5rpx 10rpx rgba(0, 0, 0, 0.2);
}
.show-box .show-box-in-box .show-box-in-box-title.data-v-22e03eda {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: bold;
}
.show-box .show-box-in-box .show-box-in-box-text.data-v-22e03eda {
  font-size: 28rpx;
  color: #FFFFFF;
  box-sizing: border-box;
  margin-top: 10rpx;
}
.show-box .show-box-btn.data-v-22e03eda {
  position: absolute;
  display: inline-block;
  font-size: 24rpx;
  color: #adc0ff;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
  margin-top: 10rpx;
  right: 50rpx;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.5);
}
.show-gold-box.data-v-22e03eda {
  background: linear-gradient(to right, rgba(255, 228, 173, 0.8) 20%, rgba(255, 192, 173, 0.7) 80%, rgba(255, 192, 173, 0.3) 100%);
}
.show-points-box.data-v-22e03eda {
  background: linear-gradient(to right, rgba(173, 228, 255, 0.8) 20%, rgba(173, 192, 255, 0.7) 80%, rgba(173, 228, 255, 0.8) 100%);
}
.flex-box .flex-box-item.data-v-22e03eda {
  display: inline-block;
  width: calc(50% - 20rpx);
}
.box-body.data-v-22e03eda {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
}
.box-text.data-v-22e03eda {
  font-size: 34rpx;
  font-weight: bold;
}
.box-list.data-v-22e03eda {
  display: flex;
  flex-wrap: wrap;
}
.list-item.data-v-22e03eda {
  width: 25%;
  display: inline-block;
  text-align: center;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 40rpx;
}
.list-item-text.data-v-22e03eda {
  font-size: 26rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
.info-btn.data-v-22e03eda {
  width: 25px;
  height: 25px;
}
.info-btn.data-v-22e03eda:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.back.data-v-22e03eda {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: #f0f0f0;
  top: 0;
  left: 0;
  z-index: -1;
}
.box-box.data-v-22e03eda {
  box-sizing: border-box;
  margin-top: 40rpx;
}
.box.data-v-22e03eda {
  position: relative;
  vertical-align: top;
  width: 100%;
  display: inline-block;
  text-align: left;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8) 20%, rgba(255, 255, 255, 0.7) 80%, rgba(255, 255, 255, 0.3) 100%);
}
.title.data-v-22e03eda {
  font-size: 32rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-weight: bold;
}
.line.data-v-22e03eda {
  width: 100%;
  height: 1rpx;
  background-color: #f0f0f0;
}
.menu-box.data-v-22e03eda {
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx 20rpx 10rpx 20rpx;
}
.menu-box .menu.data-v-22e03eda {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}
.menu-box .menu .menu-icon-box.data-v-22e03eda {
  width: 50rpx;
  height: 40rpx;
}
.menu-box .menu .menu-icon.data-v-22e03eda {
  width: 40rpx;
  height: 40rpx;
}
.menu.data-v-22e03eda:active {
  opacity: 0.6;
}
.ip-tag.data-v-22e03eda {
  position: absolute;
  padding: 10rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #999;
  width: 300rpx;
  top: 20rpx;
  right: 20rpx;
  z-index: 9999;
  text-align: right;
}
.header-box.data-v-22e03eda {
  top: 0;
  left: 0;
  width: 100%;
}
.user-info-box.data-v-22e03eda {
  position: relative;
  padding: 50rpx 30rpx 50rpx 30rpx;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8) 20%, rgba(255, 255, 255, 0.7) 80%, rgba(255, 255, 255, 0.3) 100%);
  box-sizing: border-box;
}
.container-mode-color-scheme-light .back.data-v-22e03eda {
  background-color: #f0f0f0;
}
.container-mode-color-scheme-dark .back.data-v-22e03eda {
  background-color: #191919;
}
.container-mode-color-scheme-dark .user-info-box.data-v-22e03eda {
  background-color: #191919;
}
.container-mode-color-scheme-dark .user-info-box .info-btn.data-v-22e03eda {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.container-mode-color-scheme-dark .menu-box.data-v-22e03eda {
  background-color: #1f1f1f;
}
.container-mode-color-scheme-dark .menu-box .menu.data-v-22e03eda {
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  background-color: rgba(0, 0, 0, 0);
  color: #aaaaaa;
}
.container-mode-color-scheme-dark .menu-box .menu .menu-icon-box.data-v-22e03eda {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.container-mode-device-pc.data-v-22e03eda {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
}
.container-mode-device-pc .show-box.data-v-22e03eda {
  cursor: pointer;
}
.container-mode-device-pc .user-info-box .user-info-card.data-v-22e03eda {
  left: -450rpx;
}
.container-mode-device-pc .header-box.data-v-22e03eda {
  width: 100%;
  left: 240px;
}
.container-mode-device-pc .list-item.data-v-22e03eda {
  cursor: pointer;
}
.container-mode-device-pc .list-item.data-v-22e03eda:hover {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.container-mode-device-pc .list-item.data-v-22e03eda:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.bg.data-v-22e03eda {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}
.bg .bg-image.data-v-22e03eda {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  right: 0;
  z-index: -2;
}
.bg .bg-image-2.data-v-22e03eda {
  position: fixed;
  width: 100vw;
  height: 100vh;
  bottom: 25vh;
  right: 0;
  -webkit-transform: scaleY(-1);
          transform: scaleY(-1);
  z-index: -2;
}
.bg .bg-image-3.data-v-22e03eda {
  position: fixed;
  width: 100vw;
  height: 100vh;
  bottom: 0;
  right: 0;
  z-index: -2;
  -webkit-transform: scaleY(-1);
          transform: scaleY(-1);
}
.bg .bg-bg.data-v-22e03eda {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  right: 0;
  z-index: -1;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 100rpx, white 100%);
}
.bg-mode-color-scheme-light.data-v-22e03eda {
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}
.bg-mode-color-scheme-dark.data-v-22e03eda {
  background: #191919;
}
.big-box.data-v-22e03eda {
  padding: 30rpx;
}
.box-2.data-v-22e03eda {
  position: relative;
  padding: 30rpx;
  font-size: 28rpx;
  color: #333;
}
.box-title.data-v-22e03eda {
  font-weight: bold;
}
.flex-box-pc .flex-box-item.data-v-22e03eda {
  width: calc(33% - 20rpx);
  margin-right: 20rpx;
}
.tag.data-v-22e03eda {
  display: inline-block;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #333;
}
.user-info-box.data-v-22e03eda {
  position: relative;
}
.user-info-box-bottom-text.data-v-22e03eda {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 5rpx 4rpx rgba(0, 0, 0, 0.2);
}
.user-info-box-svip.data-v-22e03eda {
  border: 5rpx solid #ffcc00;
}
.user-info-box-svip .user-info-box-bottom-text.data-v-22e03eda {
  color: #ffcc00;
}
.user-info-box-vip.data-v-22e03eda {
  border: 5rpx solid #5A7EF6;
}
.user-info-box-vip .user-info-box-bottom-text.data-v-22e03eda {
  color: #5A7EF6;
}
.container-mode-device-pc .user-info-box-svip.data-v-22e03eda {
  border: 5px solid #ffcc00;
  box-shadow: 0 0 10px rgba(255, 204, 0, 0.5);
  -webkit-animation: box-shadow-svip 2s infinite alternate;
          animation: box-shadow-svip 2s infinite alternate;
}
.container-mode-device-pc .user-info-box-svip .user-info-box-bottom-text.data-v-22e03eda {
  color: #ffcc00;
}
.container-mode-device-pc .user-info-box-vip.data-v-22e03eda {
  border: 5px solid #5A7EF6;
  box-shadow: 0 0 10px rgba(90, 126, 246, 0.5);
  -webkit-animation: box-shadow-vip 2s infinite alternate;
          animation: box-shadow-vip 2s infinite alternate;
}
.container-mode-device-pc .user-info-box-vip .user-info-box-bottom-text.data-v-22e03eda {
  color: #5A7EF6;
}
