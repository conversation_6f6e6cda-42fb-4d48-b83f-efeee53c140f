<template>
  <view>
    <view class="header" id="headerInMiniprogramCode">
      <kt-nav-bar :title="groupName || $i18n.zhToGlobal('邀请码')" :showBack="true"></kt-nav-bar>
    </view>

    <!-- 内容区域 -->
    <view class="content-box">
      <!-- 组名展示区域 -->
      <view class="group-name-section" v-if="groupName">
        <view class="group-name-container">
          <text class="group-name-text">{{ groupName }}</text>
        </view>
      </view>

      <!-- 二维码区域 -->
      <view class="qrcode-section">
        <view class="qrcode-container">
          <kt-qrcode v-if="miniprogramCodeUrl" :content="miniprogramCodeUrl" size="400rpx">
          </kt-qrcode>
          <view v-else class="qrcode-loading">
            <text>{{ $i18n.zhToGlobal('二维码生成中...') }}</text>
          </view>
        </view>
        <view class="qrcode-tip">
          {{ $i18n.zhToGlobal('长按保存二维码，分享给好友扫码加入') }}
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="description-section">
        <view class="description-title">{{ $i18n.zhToGlobal('使用说明') }}</view>
        <view class="description-list">
          <view class="description-item">
            <text class="item-number">1.</text>
            <text class="item-text">{{ $i18n.zhToGlobal('长按上方二维码保存到相册') }}</text>
          </view>
          <view class="description-item">
            <text class="item-number">2.</text>
            <text class="item-text">{{ $i18n.zhToGlobal('将二维码分享给好友') }}</text>
          </view>
          <view class="description-item">
            <text class="item-number">3.</text>
            <text class="item-text">{{ $i18n.zhToGlobal('好友扫码即可加入共同邀约') }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>


<script>
import projectConfig from "@/uni_modules/project-config/index.js";

export default {
  data() {
    return {
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      },
      invitationGroupId: null, // 邀约组ID
      miniprogramCodeUrl: '', // 二维码URL
      isLoading: false, // 是否加载中
      groupName: '' // 组名
    }
  },
  onLoad(options) {
    // 获取传入的邀约组ID
    if (options.invitationGroupId) {
      this.invitationGroupId = options.invitationGroupId;
      // 从配置中获取 wxUrl
      const wxUrl = projectConfig.wxUrl.makeFriends;
      this.miniprogramCodeUrl = wxUrl + this.invitationGroupId;
      this.getGroupInfo();
    } else {
      uni.showToast({
        title: this.$i18n.zhToGlobal('参数错误'),
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 500);
    }
  },
  methods: {
    // 获取组信息
    getGroupInfo() {
      this.$request.post('/project-make-friends-web/invitationGroup/getById', {
        data: {
          id: this.invitationGroupId
        }
      }).then(res => {
        if (res.state === 2000) {
          this.groupName = res.data.groupName || '';
        }
      }).catch(err => {
        console.error('获取组信息失败:', err);
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #FFFFFF;
}

.content-box {
  padding-top: 100rpx;
  padding-bottom: 40rpx;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #F5F5F5;
}

/* 组名展示区域 */
.group-name-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx 20rpx;
  text-align: center;
  margin: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
  width: calc(100% - 40rpx);
  max-width: 600rpx;

  .group-name-container {
    background-color: rgba(255, 255, 255, 0.95);
    padding: 25rpx 30rpx;
    border-radius: 12rpx;
    backdrop-filter: blur(10rpx);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  }

  .group-name-text {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
    line-height: 1.4;
    word-break: break-all;
  }
}

/* 二维码区域 */
.qrcode-section {
  background-color: #FFFFFF;
  padding: 40rpx;
  text-align: center;
  margin: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  width: calc(100% - 40rpx);
  max-width: 600rpx;

  .qrcode-container {
    background-color: #FFFFFF;
    padding: 30rpx;
    border-radius: 16rpx;
    display: inline-block;
    margin-bottom: 30rpx;
    border: 2rpx solid #F0F0F0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 440rpx;
  }

  .qrcode-loading {
    color: #999999;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400rpx;
  }

  .qrcode-tip {
    color: #666666;
    font-size: 28rpx;
    line-height: 1.5;
    padding: 0 20rpx;
  }
}



/* 说明区域 */
.description-section {
  background-color: #FFFFFF;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: calc(100% - 40rpx);
  max-width: 600rpx;

  .description-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 30rpx;
    text-align: center;
  }

  .description-list {
    .description-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20rpx;

      .item-number {
        color: #007AFF;
        font-weight: bold;
        font-size: 28rpx;
        margin-right: 15rpx;
        min-width: 30rpx;
      }

      .item-text {
        color: #666666;
        font-size: 28rpx;
        line-height: 1.5;
        flex: 1;
      }
    }
  }
}

/* 操作按钮区域 */
.action-section {
  padding: 40rpx 20rpx;
  display: flex;
  gap: 20rpx;

  .action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: bold;
    transition: all 0.3s ease;

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }

    &.save-btn {
      background-color: #007AFF;
      color: #FFFFFF;
    }

    &.share-btn {
      background-color: #07C160;
      color: #FFFFFF;
    }
  }
}

/* 暗色模式适配 */
.container-mode-theme-dark {
  .header {
    background-color: #1F1F1F;
  }

  .content-box {
    background-color: #191919;
  }

  .group-name-section {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);

    .group-name-container {
      background-color: rgba(45, 55, 72, 0.95);
    }

    .group-name-text {
      color: #E2E8F0;
    }
  }

  .qrcode-section {
    background-color: #1F1F1F;

    .qrcode-container {
      background-color: #1F1F1F;
      border-color: #333333;
    }

    .qrcode-tip {
      color: #AAAAAA;
    }
  }

  .description-section {
    background-color: #1F1F1F;

    .description-title {
      color: #FFFFFF;
    }

    .description-item {
      .item-number {
        color: #0A84FF;
      }

      .item-text {
        color: #AAAAAA;
      }
    }
  }
}
</style>
