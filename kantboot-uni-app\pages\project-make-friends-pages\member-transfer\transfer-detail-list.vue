<template>
  <view>
    <view
        class="header"
        id="headerInTransferDetailList">
      <kt-nav-bar :title="pageTitle" :showBack="true"></kt-nav-bar>
    </view>

    <view class="content-box">
      <!-- 搜索框 -->
      <view class="box-search">
        <view class="search-box">
          <u-input :placeholder="$i18n.zhToGlobal('输入ID或手机号搜索')" @confirm="searchUsers" v-model="searchText">
            <template slot="suffix">
              <button @tap="searchUsers" class="search-btn" :disabled="!searchText.trim()">搜索</button>
            </template>
          </u-input>
        </view>
      </view>

      <!-- 转移记录详细列表 -->
      <view class="box">
        <view v-if="userList.length === 0 && !isLoading" class="empty-tip">
          {{ $i18n.zhToGlobal('暂无转移记录') }}
        </view>

        <!-- 转移记录卡片列表 -->
        <view class="box-item" v-for="item in userList" :key="item.id" @click="selectUser(item)">
          <view class="box-item-card">
            <kt-user-info-card :user-info="item"></kt-user-info-card>
            <view class="box-item-info">
              <view class="box-item-id">ID: {{ item.userAccountId }}</view>
              <view class="box-item-phone" v-if="item.phone">{{ item.phoneAreaCode }} {{ item.phone }}</view>
              <view class="box-item-transfer-time" v-if="item.transferTime">
                {{ $i18n.zhToGlobal('转移时间') }}: {{ formatDate(item.transferTime) }}
              </view>
            </view>
            <!-- 状态显示 -->
            <view :class="[getStatusClass(item.status), 'box-item-status']">
              {{ getStatusText(item.status) }}
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <u-loading-icon v-if="isLoading" mode="circle" size="50rpx"></u-loading-icon>
        <view v-if="isBottom && userList.length > 0" style="text-align: center; color: #999999; font-size: 24rpx;">
          {{ $kt.i18n.zhToGlobal('没有更多了') }}
        </view>
        <view style="height: 50rpx;"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      },
      searchText: '',
      userList: [],
      pageNum: 1,
      pageSize: 10,
      isLoading: false,
      isBottom: false,
      // 页面参数
      newInviterUserAccountId: null,
      originalInviterUserAccountId: null,
      nickname: '',
      pageTitle: ''
    }
  },
  onLoad(options) {
    // 获取页面参数
    this.newInviterUserAccountId = options.newInviterUserAccountId ? parseInt(options.newInviterUserAccountId) : null;
    this.originalInviterUserAccountId = options.originalInviterUserAccountId ? parseInt(options.originalInviterUserAccountId) : null;
    this.nickname = options.nickname ? decodeURIComponent(options.nickname) : '';
    
    // 设置页面标题
    if (this.nickname) {
      this.pageTitle = `${this.nickname}${this.$i18n.zhToGlobal('的转移记录')}`;
    } else {
      this.pageTitle = this.$i18n.zhToGlobal('转移记录详情');
    }
    
    this.getInitList();
  },
  onReachBottom() {
    this.loadMore();
  },
  methods: {
    // 初始加载用户列表
    getInitList() {
      this.isLoading = true;
      this.pageNum = 1;
      this.isBottom = false;

      let apiUrl = '';
      let requestData = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        searchText: this.searchText
      };

      if (this.newInviterUserAccountId) {
        // 来自member-transfer.vue页面，查询特定newInviterUserAccountId下的记录
        apiUrl = "/project-make-friends-web/userTransferRecord/getTransferListByNewInviter";
        requestData.newInviterUserAccountId = this.newInviterUserAccountId;
      } else if (this.originalInviterUserAccountId) {
        // 来自who-helped.vue页面，查询特定originalInviterUserAccountId下的记录
        apiUrl = "/project-make-friends-web/userTransferRecord/getTransferListByOriginalInviter";
        requestData.originalInviterUserAccountId = this.originalInviterUserAccountId;
      }

      this.$request.post(apiUrl, {
        data: requestData
      }).then(res => {
        if (res.state === 2000) {
          this.userList = res.data || [];
          if (this.userList.length < this.pageSize) {
            this.isBottom = true;
          }
        } else {
          uni.showToast({
            title: res.msg || '获取用户列表失败',
            icon: 'none'
          });
        }
        this.isLoading = false;
      }).catch(err => {
        console.error(err);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
        this.isLoading = false;
      });
    },

    // 搜索用户
    searchUsers() {
      this.getInitList();
    },

    // 选择用户
    selectUser(user) {
      uni.navigateTo({
        url: `/pages/project-make-friends-pages/member-transfer/transfer-detail?userId=${user.id}`
      });
    },

    // 加载更多
    loadMore() {
      if (this.isLoading || this.isBottom) return;

      this.isLoading = true;
      this.pageNum++;

      let apiUrl = '';
      let requestData = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        searchText: this.searchText
      };

      if (this.newInviterUserAccountId) {
        apiUrl = "/project-make-friends-web/userTransferRecord/getTransferListByNewInviter";
        requestData.newInviterUserAccountId = this.newInviterUserAccountId;
      } else if (this.originalInviterUserAccountId) {
        apiUrl = "/project-make-friends-web/userTransferRecord/getTransferListByOriginalInviter";
        requestData.originalInviterUserAccountId = this.originalInviterUserAccountId;
      }

      this.$request.post(apiUrl, requestData).then(res => {
        if (res.state === 2000) {
          const newData = res.data.content || [];
          this.userList = [...this.userList, ...newData];

          if (newData.length < this.pageSize) {
            this.isBottom = true;
          }
        } else {
          uni.showToast({
            title: res.msg || '获取用户列表失败',
            icon: 'none'
          });
        }
        this.isLoading = false;
      }).catch(err => {
        console.error(err);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
        this.isLoading = false;
      });
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '转移中',
        1: '转移成功',
        2: '转移失败',
        3: '已退回',
        4: '已弃掉'
      };
      return this.$i18n.zhToGlobal(statusMap[status] || '未知状态');
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        0: 'status-transferring',
        1: 'status-success',
        2: 'status-failed',
        3: 'status-rollback',
        4: 'status-abandoned'
      };
      return classMap[status] || 'status-unknown';
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.back {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: #F0F0F0;
  top: 0;
  left: 0;
  z-index: -1;
  overflow: hidden;
}

.header-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}

.content-box {
  padding-bottom: 120rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 搜索框样式 */
.box-search {
  width: calc(100% - 40rpx);
  background-color: #F0F0F0;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin: 20rpx auto;
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  padding: 10rpx;
}

/* 搜索按钮样式 */
.search-btn {
  background-color: #000000;
  height: 30px;
  line-height: 30px;
  color: #ffffff;

  &[disabled] {
    background-color: #cccccc;
    color: #ffffff;
    opacity: 0.6;
  }
}

/* 主容器样式 */
.box {
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;

  .box-item {
    margin-bottom: 30rpx;

    .box-item-card {
      position: relative;
      padding: 20rpx;
      border-radius: 12rpx;
      background-color: #ffffff;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      border: 1rpx solid #eaeaea;

      .box-item-info {
        font-size: 28rpx;
        color: #999999;
        margin-top: 16rpx;

        .box-item-id,
        .box-item-phone,
        .box-item-transfer-time {
          margin-bottom: 8rpx;
        }
      }

      .box-item-status {
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: bold;

        &.status-transferring {
          background-color: #FFF3CD;
          color: #856404;
        }

        &.status-success {
          background-color: #D4EDDA;
          color: #155724;
        }

        &.status-failed {
          background-color: #F8D7DA;
          color: #721C24;
        }

        &.status-rollback {
          background-color: #D1ECF1;
          color: #0C5460;
        }

        &.status-abandoned {
          background-color: #E2E3E5;
          color: #383D41;
        }

        &.status-unknown {
          background-color: #F8F9FA;
          color: #6C757D;
        }
      }
    }
  }
}

.box-item:active {
  opacity: .8;
}

.box-item-card:active {
  background-color: #f9f9f9;
}

/* 提示语样式 */
.empty-tip {
  text-align: center;
  color: #999999;
  font-size: 28rpx;
  margin: 100rpx 0;
}

// 暗黑模式适配
.container-mode-color-scheme-dark {
  .back {
    background-color: #191919;
  }

  .search-box {
    background-color: #1F1F1F;
  }

  .box {
    .box-item {
      .box-item-card {
        background-color: #1F1F1F;
        border: 1rpx solid #333333;

        &:active {
          background-color: #2A2A2A;
        }
      }
    }
  }
}

// PC端适配
.container-mode-device-pc {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;

  .header-box {
    width: calc(100% - 240px);
    left: 240px;
  }

  .content-box {
    margin-left: 240px;
    width: calc(100% - 240px);

    .box {
      max-width: 800rpx;
      margin: 0 auto;
    }
  }
}
</style>
