<view class="kt-community-post-detail-panel data-v-7356a296" style="{{'height:'+(height)+';'}}"><scroll-view class="scroll-view data-v-7356a296" style="{{'height:'+(height)+';'}}" scroll-y="{{true}}"><block wx:if="{{postId}}"><view class="box data-v-7356a296"><kt-community-post-card vue-id="0441a8f1-1" post-id="{{postId}}" is-forbid-forward="{{false}}" is-forbid-collect="{{false}}" data-ref="projectAcmCard" data-event-opts="{{[['^dotClick',[['cardDotClick']]]]}}" bind:dotClick="__e" class="data-v-7356a296 vue-ref" bind:__l="__l"></kt-community-post-card><kt-community-post-operate-popup bind:removeSuccess="__e" vue-id="0441a8f1-2" data-ref="communityPostOperatePopup" data-event-opts="{{[['^removeSuccess',[['removeSuccess']]]]}}" class="data-v-7356a296 vue-ref" bind:__l="__l"></kt-community-post-operate-popup></view></block><block wx:if="{{isSvip||isVip}}"><view class="data-v-7356a296"><view class="data-v-7356a296"><view class="{{['data-v-7356a296',clazz.box]}}"><view class="box-title data-v-7356a296">{{$root.g0}}</view><view hidden="{{!(loading)}}" class="data-v-7356a296"><view style="height:100rpx;" class="data-v-7356a296"></view><block wx:if="{{loading}}"><u-loading-icon vue-id="0441a8f1-3" mode="circle" size="{{50}}" class="data-v-7356a296" bind:__l="__l"></u-loading-icon></block></view><block wx:if="{{$root.g1}}"><view class="no-data data-v-7356a296"><view class="data-v-7356a296"><image class="no-data-image data-v-7356a296" src="{{$root.g2}}"></image></view><view class="no-data-text data-v-7356a296">{{$root.g3+''}}</view></view></block><block wx:for="{{list}}" wx:for-item="item" wx:for-index="__i0__"><view class="box-item data-v-7356a296"><kt-community-post-card vue-id="{{'0441a8f1-4-'+__i0__}}" is-transparent="{{true}}" border-radius="0" has-dot="{{false}}" has-bottom-operation="{{false}}" post="{{item}}" class="data-v-7356a296" bind:__l="__l"></kt-community-post-card></view></block></view></view><view style="height:400rpx;" class="data-v-7356a296"></view></view></block></scroll-view><block wx:if="{{isSvip||isVip}}"><view class="bottom data-v-7356a296"><block wx:if="{{$root.g4>0}}"><kt-image-select bind:input="__e" vue-id="0441a8f1-5" file-group-code="fp" value="{{bindParams.imageIds}}" data-event-opts="{{[['^input',[['__set_model',['$0','imageIds','$event',[]],['bindParams']]]]]}}" class="data-v-7356a296" bind:__l="__l"></kt-image-select></block><kt-send-input vue-id="0441a8f1-6" placeholder="{{placeholder}}" mode="panel" has-video="{{false}}" has-voice="{{false}}" has-user-account="{{false}}" data-ref="sendInput" data-event-opts="{{[['^send',[['send']]]]}}" bind:send="__e" class="data-v-7356a296 vue-ref" bind:__l="__l"></kt-send-input></view></block><block wx:else><view class="bottom data-v-7356a296"><kt-button bind:click="__e" vue-id="0441a8f1-7" data-event-opts="{{[['^click',[['e0']]]]}}" class="data-v-7356a296" bind:__l="__l" vue-slots="{{['default']}}">{{''+$root.g5+''}}</kt-button></view></block><project-meet-vip-popup vue-id="0441a8f1-8" data-ref="projectMeetVipPopup" class="data-v-7356a296 vue-ref" bind:__l="__l"></project-meet-vip-popup></view>