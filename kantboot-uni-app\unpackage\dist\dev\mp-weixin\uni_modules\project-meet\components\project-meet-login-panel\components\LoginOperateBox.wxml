<view class="container data-v-1a52395e"><view style="position:relative;" class="data-v-1a52395e"><wechat-mp-login vue-id="2ff57368-1" agree="{{bodyData.agree}}" data-ref="wechatMpLogin" class="data-v-1a52395e vue-ref" bind:__l="__l"></wechat-mp-login><type-select bind:change="__e" vue-id="2ff57368-2" data-event-opts="{{[['^change',[['changeType']]]]}}" class="data-v-1a52395e" bind:__l="__l"></type-select><view style="height:10px;" class="data-v-1a52395e"></view><view class="input-box data-v-1a52395e"><username-input vue-id="2ff57368-3" body-data="{{bodyData}}" data-event-opts="{{[['^addUserLog',[['addUserLog']]],['^change',[['changeMethod']]]]}}" bind:addUserLog="__e" bind:change="__e" class="data-v-1a52395e" bind:__l="__l"></username-input><view style="height:30rpx;" class="data-v-1a52395e"></view><block wx:if="{{bodyData.typeCode==='login'}}"><password-input vue-id="2ff57368-4" body-data="{{bodyData}}" data-event-opts="{{[['^addUserLog',[['addUserLog']]],['^change',[['changePassword']]]]}}" bind:addUserLog="__e" bind:change="__e" class="data-v-1a52395e" bind:__l="__l"></password-input></block></view><block wx:if="{{$root.g0}}"><view style="height:30rpx;" class="data-v-1a52395e"></view></block><view style="height:30rpx;" class="data-v-1a52395e"></view><kt-button vue-id="2ff57368-5" is-open-box-shadow="{{false}}" data-ref="nextButton" data-event-opts="{{[['^click',[['toLogin']]]]}}" bind:click="__e" class="data-v-1a52395e vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g1+" "+"➠"+''}}</kt-button><view style="height:20rpx;" class="data-v-1a52395e"></view><view class="forget-password-box data-v-1a52395e"><view data-event-opts="{{[['tap',[['projectMeetForgetPasswordPopupOpen']]]]}}" class="forget-password data-v-1a52395e" bindtap="__e">{{$root.g2}}</view></view><view style="height:30rpx;" class="data-v-1a52395e"></view><view style="text-align:center;" class="data-v-1a52395e"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="to-back-btn data-v-1a52395e" bindtap="__e">{{$root.g3}}</view></view><view style="height:20rpx;" class="data-v-1a52395e"></view></view><login-agreement vue-id="2ff57368-6" agree="{{bodyData.agree}}" data-ref="loginAgreement" data-event-opts="{{[['^change',[['changeAgree']]]]}}" bind:change="__e" class="data-v-1a52395e vue-ref" bind:__l="__l"></login-agreement><view style="height:30rpx;" class="data-v-1a52395e"></view></view>