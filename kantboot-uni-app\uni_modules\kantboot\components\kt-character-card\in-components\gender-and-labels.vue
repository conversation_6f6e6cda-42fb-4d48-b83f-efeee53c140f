<template>
  <view class="common-info">
    <view class="gender-age"
          :class="{
                    'gender-male': character.gender==='male',
                    'gender-female': character.gender==='female'
                }"
    >
      {{ character.gender === "male" ? "♂" : "♀" }}
      {{ character.age ? character.age : '' }}
    </view>
    <text style="margin-left: 5px;">
      <text v-for="(label,index) in character.labels" :key="index">{{ " / " }} {{ label.text }}</text>
    </text>
  </view>
</template>

<script>
export default {
  props:{
    character: {
      type: Object,
      default:{
        gender:"male",
        age:"100"
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.common-info{
  width: 100%;
  font-size: 22rpx;
  color: #666;
  letter-spacing: 2rpx;
  //不换行
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.gender-age{
  display: inline-block;
  background-color: #3c9cff;
  color: #fff;
  padding: 2rpx 10rpx;
  font-size: 20rpx;
  border-radius: 15rpx;
}

.gender-male{
  background-color: #3c9cff;
}

.gender-female{
  background-color: #ff5e5e;
}
</style>
