<view class="data-v-a7154fc6"><block wx:if="{{loading}}"><u-loading-icon vue-id="3b93a6ee-1" mode="circle" size="{{50}}" class="data-v-a7154fc6" bind:__l="__l"></u-loading-icon></block><block wx:if="{{$root.g0}}"><view class="loading-box data-v-a7154fc6"><image class="loading-image data-v-a7154fc6" src="{{$root.g1}}"></image><view class="loading-text data-v-a7154fc6">{{$root.g2}}</view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['select',['$0'],[[['list','',__i0__,'userAccountOfFollower']]]]]]]}}" class="{{['box','data-v-a7154fc6',(item.m0)?'box-selected':'']}}" bindtap="__e"><block wx:if="{{item.m1}}"><image class="box-selected-icon data-v-a7154fc6" src="{{item.g3}}"></image></block><block wx:if="{{!customUserInfoCard}}"><kt-user-info-card vue-id="{{'3b93a6ee-2-'+__i0__}}" user-info="{{item.$orig.userAccountOfFollower}}" class="data-v-a7154fc6" bind:__l="__l"></kt-user-info-card></block><block wx:else><slot name="userInfoCard"></slot><scoped-slots-userInfoCard userAccount="{{item.$orig.userAccountOfFollower}}" class="scoped-ref" bind:__l="__l"></scoped-slots-userInfoCard></block></view></block></view>