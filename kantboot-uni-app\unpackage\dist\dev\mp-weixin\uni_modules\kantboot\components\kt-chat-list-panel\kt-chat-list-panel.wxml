<view class="data-v-7d622d22"><scroll-view style="{{('height: calc(100vh - '+navBarHeight+'px - 20rpx - 50rpx)')}}" scroll-y="{{true}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e" class="data-v-7d622d22"><view class="box data-v-7d622d22"><view style="height:20rpx;" class="data-v-7d622d22"></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['in-box','data-v-7d622d22',('-1'===selected+'')?'in-box-selected':'']}}" bindtap="__e"><kt-user-info-card class="in-box-user-info-card data-v-7d622d22" vue-id="2ee3f824-1" user-info="{{$root.a0}}" bind:__l="__l"></kt-user-info-card><block wx:if="{{notifyUnreadCount>0}}"><view class="unread-count data-v-7d622d22">{{notifyUnreadCount}}</view></block></view><view data-event-opts="{{[['tap',[['cardClick',['$0'],['dialogOfCustomerService']]]]]}}" class="{{['in-box','data-v-7d622d22',(dialogOfCustomerService.id===selected)?'in-box-selected':'']}}" bindtap="__e"><kt-user-info-card class="in-box-user-info-card data-v-7d622d22" vue-id="2ee3f824-2" user-info="{{$root.a1}}" data-event-opts="{{[['^click',[['cardClick',['$0'],['dialogOfCustomerService']]]]]}}" bind:click="__e" bind:__l="__l"></kt-user-info-card><block wx:if="{{$root.m0>0}}"><view class="unread-count data-v-7d622d22">{{$root.m1}}</view></block></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.$orig.id!==dialogOfCustomerService.id}}"><view data-event-opts="{{[['tap',[['cardClick',['$0'],[[['list','',index]]]]]]]}}" class="{{['in-box','data-v-7d622d22',(item.$orig.id===selected)?'in-box-selected':'']}}" bindtap="__e"><kt-user-info-card class="in-box-user-info-card data-v-7d622d22" vue-id="{{'2ee3f824-3-'+index}}" showAge="{{false}}" user-info="{{item.$orig.userAccount}}" bind:__l="__l"></kt-user-info-card><block wx:if="{{item.m2>0}}"><view class="unread-count data-v-7d622d22">{{item.m3}}</view></block></view></block></block><view style="height:100rpx;" class="data-v-7d622d22"></view></view></scroll-view><kt-notify-list-popup vue-id="2ee3f824-4" data-ref="notifyListPopup" class="data-v-7d622d22 vue-ref" bind:__l="__l"></kt-notify-list-popup></view>