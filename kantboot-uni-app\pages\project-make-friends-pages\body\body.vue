<template>
  <view class="content">
    <view :class="clazz.bg"></view>

    <!-- 电脑端顶部背景条 -->
    <view :class="clazz.topBar">
      <view class="top-nav-container">
        <view class="top-nav-content">
          <view class="nav-left">
            <view class="company-name" @click="handleNavClick('company')">成都市长长久久科技有限公司</view>
            <view class="nav-menu">
              <view class="nav-item" @click="handleNavClick('company')">公司首页</view>
              <view class="nav-item" @click="handleNavClick('social')">社交平台</view>
              <view class="nav-item" @click="handleNavClick('shopping')">电商平台（多商家入驻版）</view>
              <view class="nav-item" @click="handleNavClick('security')">网络安全监管措施</view>
              <view class="nav-item" @click="handleNavClick('certificate')">公司证件</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view :class="clazz.viewBox">
      <view class="view-left">
        <body-tabbar ref="bodyTabbar" :selected="tabbarSelected" @changeTabbar="changeTabbar"></body-tabbar>
      </view>
      <view class="view-center">
        <page-home v-show="tabbarSelected === 'home'"></page-home>
        <page-find v-show="tabbarSelected === 'find'"></page-find>
        <page-message v-show="tabbarSelected === 'message'"></page-message>
        <page-mine v-show="tabbarSelected === 'mine'"></page-mine>
      </view>
      <!--      <view :class="clazz.viewRight"></view>-->
    </view>


    <project-make-friends-bine-phone-popup
      ref="projectMakeFriendsBinePhonePopup"></project-make-friends-bine-phone-popup>

    <project-make-friends-set-user-info-popup
      ref="projectMakeFriendsSetUserInfoPopup"></project-make-friends-set-user-info-popup>


  </view>
</template>

<script>
import bodyTabbar from './components/bodyTabbar.vue'
import pageHome from './pageComponents/pageHome.vue'
import pageMessage from './pageComponents/pageMessage.vue'
import PageMine from "./pageComponents/pageMine.vue";
import PageFind from "./pageComponents/pageFind.vue";

export default {
  components: {
    PageMine,
    bodyTabbar,
    pageHome,
    pageMessage,
    PageFind
  },
  data() {
    return {
      mineInfo: {},
      tabbarSelected: 'home',
      clazz: {
        bg: this.$kt.style.toggleClass("bg"),
        topBar: this.$kt.style.toggleClass("top-bar"),
        viewBox: this.$kt.style.toggleClass("view-box"),
        viewRight: this.$kt.style.toggleClass("view-right"),
      },
      self: {}
    }
  },
  // 分享给好友
  onShareAppMessage() {
    return {
      path: '/pages/pages-body/into/into',
      title: '',
    }
  },
  onLoad(query) {
    //页面传参tabbarSelected=all
    // if (query && query.tabbarSelected) {
    //   this.tabbarSelected = query.tabbarSelected;
    //   this.changeTabbar(query.tabbarSelected);
    // }
    this.changeTabbar("home");
  },
  mounted() {
    // let colorScheme = this.$kt.style.getMode().colorScheme;
    // if(colorScheme === 'dark'){
    //   // 将状态栏调成白色
    //   uni.setNavigationBarColor({
    //     frontColor: '#ffffff',
    //     backgroundColor: '#ffffff',
    //     animation: {
    //       duration: 0,
    //       timingFunc: 'easeIn'
    //     }
    //   });
    // } else{
    //   // 将状态栏调成黑色
    //   uni.setNavigationBarColor({
    //     frontColor: '#000000',
    //     backgroundColor: '#000000',
    //     animation: {
    //       duration: 0,
    //       timingFunc: 'easeIn'
    //     }
    //   });
    // }
  },
  methods: {

    changeTabbar(code) {
      this.tabbarSelected = code;
      this.$forceUpdate();
      console.log(code)
    },

    handleNavClick(type) {
      // 处理导航菜单点击事件
      switch(type) {
        case 'company':
          window.open("http://www.changchangjiujiu.top/index.html", '_blank');
          // 可以在这里添加跳转逻辑
          break;
        case 'social':
          window.open("http://www.changchangjiujiu.top/index.html", '_blank');
          break;
        case 'shopping':
          window.open("https://shopping.changchangjiujiu.top/pc/", '_blank');
          break;
        case 'security':
          window.open("http://www.changchangjiujiu.top/aq.html", '_blank');
          break;
        case 'certificate':
          window.open("http://www.changchangjiujiu.top/gszj.html", '_blank');
          break;
        default:
          break;
      }
    }
  },

}
</script>

<style lang="scss" scoped>
.bg {
  position: fixed;
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
  z-index: -1;
}

.bg-mode-color-scheme-light {
  background-color: #ffffff;
}

.bg-mode-color-scheme-dark {
  background-color: #191919;
}

// 顶部背景条样式 - 完全对应HTML的TailwindCSS样式
.top-bar {
  display: none; // 默认隐藏，只在PC端显示
}

.top-bar-mode-device-pc {
  display: block;
  // 对应 bg-gray-800 text-white w-full fixed top-0 z-20
  background-color: #1f2937; // bg-gray-800
  color: white; // text-white
  width: 100%; // w-full
  position: fixed; // fixed
  top: 0; // top-0
  left: 0;
  z-index: 20; // z-20

  .top-nav-container {
    // 对应 w-[1440px] mx-auto h-14 flex items-center justify-between px-4
    width: 1440px; // w-[1440px]
    margin: 0 auto; // mx-auto
    height: 56px; // h-14 (3.5rem = 56px)
    display: flex; // flex
    align-items: center; // items-center
    justify-content: space-between; // justify-between
    padding: 0 16px; // px-4 (1rem = 16px)

    .top-nav-content {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .nav-left {
        // 对应 flex items-center
        display: flex; // flex
        align-items: center; // items-center

        .company-name {
          // 对应 text-xl font-bold mr-32
          font-size: 20px; // text-xl (1.25rem = 20px)
          line-height: 28px; // text-xl line-height
          font-weight: 700; // font-bold
          margin-right: 128px; // mr-32 (8rem = 128px)
          color: white;
          cursor: pointer;
          text-decoration: none;
          transition: color 0.15s ease-in-out;

          &:hover {
            // 对应 hover:text-primary，这里使用蓝色作为primary色
            color: #3b82f6;
          }
        }

        .nav-menu {
          // 对应 flex items-center space-x-12
          display: flex; // flex
          align-items: center; // items-center
          gap: 48px; // space-x-12 (3rem = 48px)

          .nav-item {
            // 对应 hover:text-primary
            color: white;
            cursor: pointer;
            text-decoration: none;
            transition: color 0.15s ease-in-out;
            white-space: nowrap;

            &:hover {
              // 对应 hover:text-primary
              color: #3b82f6;
            }
          }
        }
      }
    }
  }
}

// 亮色和暗色模式都保持与HTML一致的样式
.top-bar-mode-device-pc.top-bar-mode-color-scheme-light,
.top-bar-mode-device-pc.top-bar-mode-color-scheme-dark {
  // 保持与HTML完全一致的bg-gray-800样式
  background-color: #1f2937 !important; // bg-gray-800
  color: white !important; // text-white

  .top-nav-container {
    .top-nav-content {
      .nav-left {
        .company-name {
          color: #ffffff !important; // text-white

          &:hover {
            color: #3b82f6 !important; // hover:text-primary
          }
        }

        .nav-menu {
          .nav-item {
            color: #ffffff !important; // text-white

            &:hover {
              color: #3b82f6 !important; // hover:text-primary
            }
          }
        }
      }
    }
  }
}

.show {
  opacity: 1;
  z-index: 10;
}

.hide {
  opacity: 0;
  z-index: -1;
}

.content {}

//.view-box{
//  position: fixed;
//  left:0;
//  top:0;
//  width: 1200px;
//  height: 100vh;
//  background-color: #ff9900;
//  vertical-align: top;
//}
//
//.view-center{
//  width: 600px;
//  display: inline-block;
//}

.view-box-mode-device-pc {
  position: fixed;
  //background-color: #FF0000;
  width: 100vw;
  height: calc(100vh - 56px); // 对应HTML中h-14的高度
  top: 56px; // 对应HTML中h-14的高度 (3.5rem = 56px)
  left: 50%;
  transform: translateX(-50%);

  .view-center {
    width: 900px;
  }

  .view-right {
    position: absolute;
    display: inline-block;
    width: 360px;
    height: 100%;
    right: 0;
    top: 0;
    z-index: 10;
  }

  .view-right-mode-color-scheme-light {
    background-color: #ffffff;
  }

  .view-right-mode-color-scheme-dark {
    background-color: #191919;
  }

}

.view-box-meet-mode-device-pc {
  position: fixed;
  //background-color: #FF0000;
  width: 1200px;
  height: 100vh;
  top: 0;
  left: 50%;
  transform: translateX(-50%);

  .view-center {
    width: 960px;
  }

  .view-right {
    position: absolute;
    display: inline-block;
    width: 5px;
    height: 100%;
    right: 0;
    top: 0;
    z-index: 99;
  }

  .view-right-mode-color-scheme-light {
    background-color: #ffffff;
  }

  .view-right-mode-color-scheme-dark {
    background-color: #191919;
  }
}
</style>
