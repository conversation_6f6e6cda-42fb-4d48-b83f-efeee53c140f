@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-b72e5afc {
  padding: 50rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
}
.box .title.data-v-b72e5afc {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.box .in-box.data-v-b72e5afc {
  padding: 20rpx;
}
.box .in-box .in-box-input.data-v-b72e5afc {
  position: relative;
  background-color: #f0f0f0;
  padding: 30rpx;
  border-radius: 20rpx;
}
.box .in-box .in-box-input .in-box-input-icon.data-v-b72e5afc {
  width: 40rpx;
  height: 40rpx;
  display: inline-block;
  vertical-align: middle;
}
.box .in-box .in-box-input .in-box-input-input.data-v-b72e5afc {
  position: relative;
  vertical-align: top;
  display: inline-block;
  letter-spacing: 2rpx;
  margin-left: 10rpx;
  width: calc(100% - 100rpx);
  border: none;
  font-size: 28rpx;
  color: #333333;
  z-index: 2;
}
.box .in-box .in-box-input .in-box-input-show-change.data-v-b72e5afc {
  position: absolute;
  right: 0;
  top: 0;
  width: 100rpx;
  height: 100%;
  opacity: 0.1;
  z-index: 99999;
}
.box-mode-device-pc.data-v-b72e5afc {
  border-radius: 20rpx;
}
