<template>
  <view
      :class="clazz.box"
      style="width: 100%;">
    <view style="position: relative">
      <swiper style="height: 520rpx;">
        <swiper-item>
          <image
              mode="aspectFill"
              style="width: 100%; height: 550rpx"
              v-if="userAccount.fileIdOfAvatar"
              :src="$kt.file.visit(userAccount.fileIdOfAvatar)"
          ></image>
          <image
              mode="aspectFill"
              style="width: 100%; height: 550rpx"
              v-else
              :src="$kt.file.byPath('image/logo.png')">
          </image>
        </swiper-item>

        <swiper-item v-for="item in userAccount.fileIdsOfPhotos">
          <image
              mode="aspectFill"
              style="width: 100%; height: 750rpx"
              :src="$kt.file.visit(item)"
          ></image>
        </swiper-item>
      </swiper>
      <view
      class="online-box"
      v-if="isOnline(userAccount)&&self.isSubAccount"
      >{{$i18n.zhToGlobal("在线")}}{{" "}}{{getOnlineTime(userAccount)}}{{$i18n.zhToGlobal("分钟")}}
      </view>
      <view
          class="online-box offline-box"
          v-if="!isOnline(userAccount)&&self.isSubAccount"
      >{{$i18n.zhToGlobal("离线")}}{{" "}}{{getOfflineTime(userAccount)}}{{$i18n.zhToGlobal("分钟")}}</view>


    </view>
    <view class="body">
      <view class="body_title">
        <view class="body_title_left">
          <view class="body_title_name">{{ userAccount.nickname }}
          <view
              class="tag-v tag-v-svip"
              v-if="isSvip">{{"SVIP"}}</view>
          <view
              class="tag-v tag-v-vip"
              v-if="ktPopup&&!isSvip">{{"VIP"}}</view>
          </view>
          <view class="body_title_age body_title_age_male"
                v-if="userAccount.genderCode === 'male'"
          >
            <view style="margin-left: 5rpx;">♂</view>
            <view style="margin-right: 5rpx">{{ $kt.date.getAge(userAccount.gmtBirthday) }}</view>
          </view>
          <view class="body_title_age  body_title_age_female"
                v-if="userAccount.genderCode === 'femele'">
            <view style="margin-left: 5rpx;">♀</view>
            <view style="margin-right: 5rpx">{{ $kt.date.getAge(userAccount.gmtBirthday) }}</view>
          </view>
        </view>
        <view
            v-if="userAccount.genderCode === 'male'"
            @click="openSetRemarkPopup"
            class="body_title_right">
          <view class="">{{ $i18n.zhToGlobal('备注') }}</view>
          <view>{{":"}}</view>
          <view
              v-if="!remark"
              style="margin-left: 5rpx">{{ $i18n.zhToGlobal("设置备注") }}</view>
          <view
              v-if="remark"
              style="margin-left: 5rpx">{{ remark }}</view>
        </view>
      </view>
      <view class="body_info">
        <view style="padding-right: 20rpx; padding-left: 7rpx">
          <view
          v-if="userAccount.countryCn"
          >{{$i18n.zhToGlobal(userAccount.countryCn)}}</view>
        </view>
      </view>
      <view class="body_adjective">
        <view class="body_adjective_title">
          <view class="body_adjective_icn"></view>
          <view class="body_adjective_text">{{ $i18n.zhToGlobal("兴趣爱好") }}
          </view>
        </view>
        <view class="body_adjective_body">
          <view
              v-if="!userAccount.interestsIds||userAccount.interestsIds.length===0"
              class="no-data-text"
          >{{ $i18n.zhToGlobal('无') }}
          </view>
          <view
              v-if="userAccount.interestsIds&&userAccount.interestsIds.length>0"
              v-for="interest in userAccount.interestsIds"
              class="adjective_body_icon tag-v-v">
            {{ interestMap[interest] ? interestMap[interest].name : "" }}
          </view>
        </view>
      </view>
      <view class="body_adjective">
        <view class="body_adjective_title">
          <view class="body_adjective_icn"></view>
          <view class="body_adjective_text">{{ $i18n.zhToGlobal("期待的关系") }}</view>
        </view>
        <view class="body_adjective_body">
          <!--          <view class="adjective_body_icon">约会</view>-->
          <view
              v-if="!userAccount.expectedRelationshipIds||userAccount.expectedRelationshipIds.length===0"
              class="no-data-text"
          >{{ $i18n.zhToGlobal('无') }}
          </view>
          <view
              v-if="userAccount.expectedRelationshipIds&&userAccount.expectedRelationshipIds.length>0"
              v-for="expectedRelationship in userAccount.expectedRelationshipIds"
              class="adjective_body_icon">
            {{ expectedRelationshipMap[expectedRelationship+""] ? expectedRelationshipMap[expectedRelationship+""].name : $i18n.zhToGlobal("未知") }}
          </view>

        </view>
      </view>
      <view class="body_adjective">
        <view class="body_adjective_title">
          <view class="body_adjective_icn"></view>
          <view
              class="body_adjective_text">{{ $i18n.zhToGlobal("自我介绍") }}
          </view>
        </view>
        <view class="body_adjective_body">
          <view
              v-if="userAccount.introduction"
          >
            {{ userAccount.introduction }}
          </view>
          <view
              v-else
              class="no-data-text">
            {{ $i18n.zhToGlobal("无") }}
          </view>
        </view>
      </view>
      <view class="body_adjective"
      @click="toUserPost(userAccountId)"
      >
        <view class="body_adjective_title">
          <view class="body_adjective_icn"></view>
          <view
              class="body_adjective_text">{{ $i18n.zhToGlobal('动态') }}
          </view>

          <view
              style="
              flex: 1;
              display: flex;
              justify-content: flex-end;
              padding-right: 20rpx;
            "
          >
            <image :src="$kt.file.byPath('icon/arrowRight.svg')"
                   mode="widthFix"
                   style="width: 25rpx;" alt="">
            </image>
          </view
          >
        </view>
      </view>

      <view
          style="height: 200rpx"></view>



    </view>
    <view class="foot_box"
    :class="{'foot_box_fixed': bottomFixed}"
    >
      <view
          style="
          flex: 0.5;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <view
            @click="$refs.projectMeetVgpPopup.open()"
            class="foot_btn">
          <text
              v-if="userAccount.genderCode==='male'"
          >{{ $i18n.zhToGlobal('为他升级') }}
          </text>
          <text
              v-if="userAccount.genderCode==='female'"
          >{{ $i18n.zhToGlobal('为她升级') }}
          </text>
        </view>
      </view>
      <view
          style="
          flex: 0.5;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <view
            @click="toPrivateChat"
            class="foot_btn">
          {{ $i18n.zhToGlobal("私聊") }}
        </view>
      </view>
    </view>

    <project-meet-vgp-popup
        :user-account-id="userAccountId"
        ref="projectMeetVgpPopup"
    ></project-meet-vgp-popup>

    <kt-set-remark-popup
    ref="ktSetRemarkPopup"
    @confirm="getRemark"
    ></kt-set-remark-popup>


    <kt-user-post-popup
    ref="ktUserPostPopup"
    ></kt-user-post-popup>

  </view>
</template>

<script>
import userAccount from "@/uni_modules/kantboot/libs/userAccount";

export default {
  computed: {
    userAccount() {
      return userAccount
    }
  },
  props: {
    bottomFixed: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      clazz: {
        box: this.$kt.style.toggleClass("box"),
      },
      userAccountId: "",
      userAccount: {
        // 头像
        fileIdOfAvatar: "",
        interestsIds: [],
        fileIdsOfPhotos: [],
        expectedRelationshipIds: [],
        // 时间戳
        gmtVipExpire: null,
        gmtSvipExpire: null,
      },
      interests: [],
      interestMap: {},
      expectedRelationships: [],
      expectedRelationshipMap: {},
      isSvip: false,
      isVip: false,
      remark:"",
      self:{
        isSubAccount:false
      }
    };
  },
  created() {
    this.getInterests();
    this.getExpectedRelationships();
  },
  mounted() {
    this.self = this.$kt.userAccount.getSelf();
  },
  methods: {
    isOnline(userInfo){
      try {
        return userInfo.onlineShow.isOnlineOfShow;

      } catch (e) {
        return false
      }
    },
    // 计算在线时间，当前时间的时间戳-userInfo.onlineShow.gmtLastOnlineOfShow
    getOnlineTime(userInfo){
      try {
        let dateNow = new Date().getTime();
        let dateLong = dateNow - userInfo.onlineShow.gmtLastOnlineOfShow;
        // 计算多少分钟
        let minutes = Math.floor(dateLong / (1000 * 60));
        return minutes;
      } catch (e) {
        return 0;
      }
    },
    // 计算离线时间
    getOfflineTime(userInfo){
      // 当前时间的时间戳-userInfo.onlineShow.gmtLastOfflineOfShow

      try {
        let dateNow = new Date().getTime();
        let dateLong = dateNow - userInfo.onlineShow.gmtLastOfflineOfShow;
        // 计算多少分钟
        let minutes = Math.floor(dateLong / (1000 * 60));
        return minutes;
      } catch (e) {
        return 0;
      }

    },

    toUserPost(){
      if(this.$kt.style.detectDeviceType() === 'pc'){
        this.$refs.ktUserPostPopup.open(this.userAccountId);
        return;
      }
      this.$kt.router.navTo("/pages/project-meet-pages/user-post/user-post", {
        userAccountId: this.userAccountId
      });
    },
    async getRemark() {
      this.$request.post("/user-interrelation-web/interrelationRemark/getRemark", {
        data: {
          userAccountIdOfRemark: this.userAccountId
        }
      }).then(res => {
        this.remark = res.data.remark;
      }).catch(err => {
      });
    },
    async openSetRemarkPopup() {
      if (!this.userAccountId) {
        uni.showToast({
          title: this.$i18n.zhToGlobal("用户加载未完成"),
          icon: "none",
        });
        return;
      }
      await this.$refs.ktSetRemarkPopup.open(this.userAccountId);
    },
    async toPrivateChat() {
      if (!this.userAccountId) {
        uni.showToast({
          title: this.$i18n.zhToGlobal("用户加载未完成"),
          icon: "none",
        });
        return;
      }
      if (this.$kt.style.detectDeviceType() === 'pc') {
        // this.$kt.router.navTo("/pages/project-meet-pages/body/body", {
        //   tabbarSelected:"messageMeet"
        // });
        uni.showLoading({
          // title: this.$i18n.zhToGlobal("正在加载"),
          title:""
        });

        let dialogId = 0;

        await this.$request.post("/functional-chat-web/dialog/getOneToOneByUserAccountId", {
          data: {userAccountId: this.userAccountId}
        }).then(res => {
          dialogId = res.data.id;
        }).catch(err => {
          this.$kt.toast.error(err);
        });


        // /functional-chat-web/dialogMessage/sendMessageBySelf
        // await this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf", {
        //   data: {
        //     items: [{
        //       content: "Let's see what sparks we can create!",
        //       type: "text",
        //     }],
        //     dialogId,
        //   }
        // }).catch(err => {
        //   this.$kt.toast.error(err);
        // });

        this.$kt.event.emit("projectMeetChatDialogMessageSend", dialogId
        );

        uni.hideLoading();

        return;
      }
      this.$kt.router.navTo("/pages/project-meet-pages/chat-dialog/chat-dialog", {
        userAccountId: this.userAccountId,
      });

    },
    visit(userAccountId){
      // /fp-community-web/userAccountVisit/visit
      this.$kt.request.post("/fp-community-web/userAccountVisit/visit", {
        data: {
          userAccountId: userAccountId
        }
      }).then(res => {
        // 访问成功
      }).catch(err => {
      });

    },
    async setUserAccountId(userAccountId) {
      this.visit(userAccountId);
      this.userAccountId = userAccountId;
      await this.$kt.userAccount.requestById(this.userAccountId)
          .then((res) => {
            this.userAccount = res;
            // 计算是否是svip
            // 获取当前时间
            const currentTime = new Date().getTime();
            this.isSvip = this.userAccount.gmtSvipExpire && this.userAccount.gmtSvipExpire > currentTime;
            this.ktPopup = this.userAccount.gmtVipExpire && this.userAccount.gmtVipExpire > currentTime;
          });
      if (!this.userAccount.fileIdsOfPhotos) {
        this.userAccount.fileIdsOfPhotos = [];
      }

      this.getRemark();
    },
// /project-meet-web/interests/getAll
    getInterests() {
      this.$kt.request.get("/project-meet-web/interests/getAll")
          .then((res) => {
            this.interests = res.data;
            for (let i = 0; i < this.interests.length; i++) {
              const interest = this.interests[i];
              this.interestMap[interest.id] = interest;
            }
          })
          .catch((err) => {
            console.error("获取兴趣失败", err);
          });
    },
    // /project-meet-web/expectedRelationship/getAll
    getExpectedRelationships() {
      this.$kt.request.get("/project-meet-web/expectedRelationship/getAll")
          .then((res) => {
            this.expectedRelationships = res.data;
            for (let i = 0; i < this.expectedRelationships.length; i++) {
              const expectedRelationship = this.expectedRelationships[i];
              this.expectedRelationshipMap[expectedRelationship.id+""] = expectedRelationship;
            }
          })
          .catch((err) => {
            console.error("获取期待的关系失败", err);
          });
    },
  },
};
</script>

<style>
.body {
  position: relative;
  top: -20rpx;
  background-color: white;
  border-radius: 10px 10px 0 0;
}

.body_title {
  /* background-color: aqua; */
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.body_title_left {
  margin-top: 20rpx;
  margin-left: 20rpx;
  display: flex;
  align-items: flex-end;
}

.body_title_right {
  margin-top: 20rpx;
  margin-right: 20rpx;
  display: flex;
  color: rgb(133, 133, 133);
  font-size: 24rpx;
}

.body_title_name {
  font-size: 32rpx;
  font-weight: bold;
}

.body_title_age {
  margin-left: 20rpx;
  background: rgb(238, 238, 238);
  border-radius: 14rpx;
  font-size: 20rpx;
  min-width: 60rpx;
  padding: 10rpx 0 10rpx 0;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

.body_title_age_female {
  background: #ff5f79;
}

.body_title_age_male {
  background: #5a7ef6;
}

.body_info {
  display: flex;
  font-size: 22rpx;
  margin-top: 20rpx;
  margin-left: 20rpx;
}

.body_adjective {
  margin-top: 40rpx;
  padding-left: 20rpx;
  margin-bottom: 30rpx;
}

.body_adjective_title {
  display: flex;
  align-items: center;
}

.body_adjective_icn {
  height: 32rpx;
  width: 8rpx;
  background: #ff5f79;
  border-radius: 4rpx;
}

.body_adjective_text {
  margin-left: 15rpx;
  color: rgba(0, 0, 0, 0.9);
  font-weight: 700;
  font-size: 32rpx;
}

.body_adjective_body {
  margin-top: 30rpx;
}

.adjective_body_icon {
  display: inline-block;
  margin: 10rpx;
  padding: 16rpx 28rpx;
  background-color: #eef2fe;
  font-size: 28rpx;
  color: #5a7ef6;
  border-radius: 30rpx;
}

.foot_box {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 200rpx;
  border-radius: 10rpx;
  background: white;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 16px 0px;
  display: flex;
}

.foot_btn {
  width: 224rpx;
  height: 86rpx;
  background: #5a7ef6;
  border-radius: 42rpx;
  text-align: center;
  line-height: 86rpx;
  font-size: 30rpx;
  color: #fff;
  font-weight: 700;
}

.no-data-text {
  font-size: 28rpx;
  color: #999;
}

.foot_btn {
  cursor: pointer;
}

.foot_box_fixed{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.tag-v{
  display: inline-block;
  padding: 0 10rpx;
  font-size: 24rpx;
  margin-left: 10rpx;
  border-radius: 20rpx;
}

.tag-v-vip{
  background-color: #ff5f79;
  color: #fff;
}
.tag-v-svip{
  background-color: #d4af37;
  color: #fff;
}

.online-box{
  position: absolute;
  bottom: 30rpx;
  z-index: 10000;
  // 绿色
  background-color: rgba(0, 118, 0, 0.7);
  color: #FFFFFF;
  border: 2rpx solid rgba(0, 118, 0, 1);
  border-radius: 20rpx;
  left: 20rpx;
  padding: 0 20rpx 0 20rpx;
  font-size: 24rpx;
}

.offline-box{
  background-color: rgba(255, 0, 0, 0.7);
  color: #FFFFFF;
  border: 2rpx solid rgba(255, 0, 0, 1);
}

</style>
