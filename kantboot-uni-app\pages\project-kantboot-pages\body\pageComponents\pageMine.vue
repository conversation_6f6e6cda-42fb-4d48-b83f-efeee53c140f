<template>
  <view :class="clazz.container">
    <view class="back"></view>
    <view v-if="userInfo.id">

      <view class="header-box" v-for="item in 2" :style="{
        'position': item === 1 ? 'fixed' : 'relative',
        'opacity': item === 1 ? 1 : 0,
        'z-index': item === 1 ? 999 : 0,
      }">

        <project-acm-nav-bar :is-has-i18n="false" :icon="$kt.file.byPath('tabbar/mine-selected.svg')"
          :title="$i18n.zhToGlobal('我的')"></project-acm-nav-bar>

        <view class="user-info-box">

          <user-info-card-edit @click="$refs.userInfoPopup.open(userInfo)"
            style="position: absolute;right: 10px;"></user-info-card-edit>

          <kt-user-info-card class="user-info-card" :user-info="userInfo"></kt-user-info-card>
          <project-acm-user-account-number-grid></project-acm-user-account-number-grid>
        </view>

      </view>



      <view>


        <view class="box-box">

          <setting-menu-box>

            <setting-menu @click="$kt.router.navTo('/pages/project-acm-pages/invite/invite')"
              :icon="$kt.file.byPath('icon/invite.svg')" :title="$i18n.zhToGlobal('邀请')"
              :is-right="true"></setting-menu>
            <setting-menu @click="$kt.router.navTo('/pages/project-acm-pages/user-team/user-team')"
              :icon="$kt.file.byPath('icon/personMove.svg')" :title="$i18n.zhToGlobal('我的团队')"
              :is-right="true"></setting-menu>

            <!-- 已购的课程 -->
            <setting-menu :icon="$kt.file.byPath('icon/course.svg')" :title="$i18n.zhToGlobal('已购课程')" :is-right="true">
            </setting-menu>
            <!-- 我的提问 -->
            <setting-menu :icon="$kt.file.byPath('icon/practice.svg')" :title="$i18n.zhToGlobal('我的提问')"
              @click="$kt.router.navTo('/pages/project-acm-pages/post-self/post-self')" :is-right="true">
            </setting-menu>
          </setting-menu-box>

          <setting-menu-box>
            <setting-menu @click="$kt.router.navTo('/pages/project-acm-pages/setting/setting')"
              :icon="$kt.file.byPath('icon/setting.svg')" :title="$i18n.zhToGlobal('设置')"
              :is-right="true"></setting-menu>
          </setting-menu-box>

        </view>

      </view>

      <view style="height: 180rpx"></view>
    </view>

    <user-info-popup ref="userInfoPopup"></user-info-popup>

    <kt-no-login ref="noLogin"></kt-no-login>

    <!--    <u-modal :show="isLogoutModal"-->
    <!--             :showCancelButton="true"-->
    <!--             :title="$i18n.zhToGlobal('提示')"-->
    <!--             :content="$i18n.zhToGlobal('是否确定退出登录')"-->
    <!--             :confirm-text="$i18n.zhToGlobal('确定')"-->
    <!--             :cancel-text="$i18n.zhToGlobal('取消')"-->
    <!--             confirm-color="#000000"-->
    <!--             @cancel="isLogoutModal = false"-->
    <!--             :closeOnClickOverlay="true"-->
    <!--             @confirm="toLogout"-->
    <!--    ></u-modal>-->


  </view>
</template>

<script>

import SettingMenuBox from "../components/setting-menu-box.vue";
import SettingMenu from "../components/setting-menu.vue";
import userInfoCardEdit from "@/pages/project-acm-pages/user-info/components/user-info-card-edit.vue";
import UserInfoPopup from "@/pages/project-acm-pages/user-info/components/user-info-popup.vue";

export default {
  components: { UserInfoPopup, userInfoCardEdit, SettingMenu, SettingMenuBox },
  data() {
    return {
      userInfo: {
        nickname: "",
        fileIdOfAvatar: "",
        genderCode: "male",
        email: "<EMAIL>",
      },
      isLogin: this.$kt.userAccount.getIsLogin(),
      isLogoutModal: false,
      // 国家获取地区
      stateAreaList: [],
      stateAreaMap: {},
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      }
    }
  },
  created() {
    // 监听登录成功事件
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.userInfo = this.$kt.userAccount.getSelf();
    });
    // 监听用户信息改变事件
    // userAccount:selfInfo:change
    this.$kt.event.on("userAccount:selfInfo:change", (userInfo) => {
      // this.userInfo = this.$kt.userAccount.requestSelf();
      this.userInfo.nickname = userInfo.nickname || "";
      this.userInfo.email = userInfo.email || "";
      this.userInfo.phoneAreaCode = userInfo.phoneAreaCode || "";
      this.userInfo.phone = userInfo.phone || "";
      this.userInfo.genderCode = userInfo.genderCode || "male";
      this.userInfo.fileIdOfAvatar = userInfo.fileIdOfAvatar || "";
      this.userInfo.gmtCreate = userInfo.gmtCreate || "";

      this.userInfo.showPaiPan = userInfo.showPaiPan || false;

    });
    // userAccount:getSelf
    this.$kt.event.on("userAccount:getSelf", () => {
      this.userInfo = this.$kt.userAccount.getSelf();
    });
    this.$kt.event.on("language:change", () => {
      console.log("language:change====");
      this.getAllStateArea();
    });
    this.getAllStateArea();
  },
  mounted() {
    this.isLogin = true;
    this.userInfo = this.$kt.userAccount.getSelf();
  },
  methods: {
    isY() {
      // 转换为大写
      let deviceType = this.$kt.style.detectDeviceType().toUpperCase();
      return deviceType === 'PC';
    },
    toLogout() {
      this.isLogin = false;
      this.$kt.userAccount.setIsLogin(false);
      this.$kt.storage.remove("token");

      uni.reLaunch({
        url: this.$kt.router.config.intoPath
      });
    },
    // /tool-state-area-web/stateArea/getAll
    async getAllStateArea() {
      this.stateAreaMap = {};
      await this.$request.post("/tool-state-area-web/stateArea/getAll", { data: {} })
        .then(res => {
          this.stateAreaList = res.data;
          for (let i = 0; i < this.stateAreaList.length; i++) {
            const item = this.stateAreaList[i];
            this.stateAreaMap[item.code] = item.name;
          }
        });
      await this.$request.post("/system-language-web/languageI18n/getList", {
        data: {
          topKey: "ToolStateArea",
          bottomKey: "name",
          languageCode: this.$i18n.getLanguageCode()
        }

      }).then(res => {
        for (let i = 0; i < res.data.length; i++) {
          const item = res.data[i];
          this.stateAreaMap[item.centerKey] = item.content;
        }
        try {
          // vue更新渲染
          // this.$forceUpdate();
        } catch (e) {
          // console.log(e);
        }
      });
    },
  }

}
</script>

<style lang="scss" scoped>
.info-btn {
  width: 25px;
  height: 25px;
}


.info-btn:active {
  transform: scale(.95);
}

.back {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: #F0F0F0;
  top: 0;
  left: 0;
  z-index: -1;
}

.box-box {
  padding: 20rpx;
  box-sizing: border-box;
}

.box {
  position: relative;
  vertical-align: top;

  width: 100%;
  display: inline-block;
  text-align: left;
  background-color: #FFFFFF;
  border-radius: 20rpx;
}

.title {
  font-size: 32rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-weight: bold;
}

.line {
  width: 100%;
  height: 1rpx;
  background-color: #F0F0F0;
}

.menu-box {
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 20rpx 20rpx 10rpx 20rpx;


  .menu {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    border-bottom: 1rpx solid #F0F0F0;
    background-color: #FFFFFF;
    margin-bottom: 20rpx;


    .menu-icon-box {
      width: 50rpx;
      height: 40rpx;
    }

    .menu-icon {
      width: 40rpx;
      height: 40rpx;
    }


  }

}

.menu:active {
  opacity: .6;
}

.ip-tag {
  position: absolute;
  padding: 10rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #999;
  width: 300rpx;
  top: 20rpx;
  right: 20rpx;
  z-index: 9999;
  text-align: right;
}

.header-box {
  top: 0;
  left: 0;
  width: 100%;
}


.user-info-box {
  position: relative;
  background-color: #fff;
  padding: 20rpx;
  box-sizing: border-box;
}

.container-mode-color-scheme-light {
  .back {
    background-color: #f0f0f0;
  }
}

.container-mode-color-scheme-dark {
  .back {
    background-color: #191919;
  }

  .user-info-box {
    background-color: #191919;

    .info-btn {
      // 颜色反转
      filter: invert(1);
    }
  }

  .menu-box {
    background-color: #1f1f1f;

    .menu {
      border-bottom: 1rpx solid rgba(255, 255, 255, .1);

      background-color: rgba(0, 0, 0, 0);
      color: #AAAAAA;

      .menu-icon-box {
        filter: invert(1);
      }

    }
  }

}



.container-mode-device-pc {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;

  .user-info-box {
    .user-info-card {
      left: -450rpx;
    }
  }

  .header-box {
    width: 100%;
    left: 240px;
  }
}
</style>