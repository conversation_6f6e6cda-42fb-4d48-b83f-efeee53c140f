@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.kt-popup-back.data-v-e975ebbc {
  position: fixed;
  top: -50vh;
  left: -50vw;
  width: 200vw;
  height: 200vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999999;
  -webkit-animation: ktPopupBackAni-data-v-e975ebbc 0.3s ease-in-out;
          animation: ktPopupBackAni-data-v-e975ebbc 0.3s ease-in-out;
}
@-webkit-keyframes ktPopupBackAni-data-v-e975ebbc {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@keyframes ktPopupBackAni-data-v-e975ebbc {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
.kt-popup-slot.data-v-e975ebbc {
  -webkit-animation: ktPopupSlotAni-data-v-e975ebbc 0.3s ease-in-out;
          animation: ktPopupSlotAni-data-v-e975ebbc 0.3s ease-in-out;
}
.kt-popup-slot-mode-device-mobile.data-v-e975ebbc {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
}
.kt-popup-slot-mode-device-pc.data-v-e975ebbc {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 400px;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
@-webkit-keyframes ktPopupSlotAni-data-v-e975ebbc {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@keyframes ktPopupSlotAni-data-v-e975ebbc {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
.kt-popup-back-closing.data-v-e975ebbc {
  -webkit-animation: ktPopupBackAniClosing-data-v-e975ebbc 0.3s ease-in-out;
          animation: ktPopupBackAniClosing-data-v-e975ebbc 0.3s ease-in-out;
}
@-webkit-keyframes ktPopupBackAniClosing-data-v-e975ebbc {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@keyframes ktPopupBackAniClosing-data-v-e975ebbc {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
.kt-popup-slot-closing.data-v-e975ebbc {
  -webkit-animation: ktPopupSlotAniClosing-data-v-e975ebbc 0.3s ease-in-out;
          animation: ktPopupSlotAniClosing-data-v-e975ebbc 0.3s ease-in-out;
}
@-webkit-keyframes ktPopupSlotAniClosing-data-v-e975ebbc {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@keyframes ktPopupSlotAniClosing-data-v-e975ebbc {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
