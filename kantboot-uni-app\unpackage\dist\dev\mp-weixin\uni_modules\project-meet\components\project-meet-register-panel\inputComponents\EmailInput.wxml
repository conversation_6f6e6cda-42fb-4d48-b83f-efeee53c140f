<view class="{{['data-v-097602ea',clazz.loginInputBox]}}"><view class="icon-box data-v-097602ea"><view data-event-opts="{{[['tap',[['toChangPhoneAreaCode',['$event']]]]]}}" class="icon data-v-097602ea" bindtap="__e"><block wx:if="{{$root.m0}}"><view class="data-v-097602ea">{{''+"+"+param.phoneAreaCode+''}}</view></block><image hidden="{{!(!$root.m1)}}" class="icon-img data-v-097602ea" src="{{$root.g0}}" mode="widthFix"></image></view></view><view class="input-box data-v-097602ea"><input class="input data-v-097602ea" autofocus="{{true}}" adjust-position="{{false}}" placeholder="{{$root.g1}}" type="text" data-event-opts="{{[['blur',[['addUserLog',['$event']]]],['input',[['__set_model',['$0','email','$event',[]],['param']],['inputInput',['$event']]]]]}}" value="{{param.email}}" bindblur="__e" bindinput="__e"/></view></view>