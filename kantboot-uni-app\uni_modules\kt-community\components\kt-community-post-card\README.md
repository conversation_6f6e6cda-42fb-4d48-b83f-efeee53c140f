# kt-community-post-card

`kt-community-post-card` 是一个用于展示社区帖子卡片的组件，支持显示用户信息、帖子内容以及点赞、评论、收藏等操作。

## 功能

- 显示用户头像、昵称、发帖时间。
- 支持帖子内容的格式化展示。
- 支持点赞、评论、收藏等操作。
- 可自定义卡片的圆角和背景透明度。
- 支持加载状态和关系状态的动态更新。

## 属性 (Props)

| 属性名               | 类型      | 默认值       | 描述                       |
|----------------------|-----------|--------------|--------------------------|
| `postId`            | `String`  | `''`         | 帖子 ID，用于根据 ID 获取帖子数据。    |
| `borderRadius`      | `String`  | `'20rpx'`    | 卡片的圆角大小。                 |
| `isTransparent`     | `Boolean` | `false`      | 是否设置卡片背景为透明。             |
| `post`              | `Object`  | `{}`         | 帖子数据对象，包含用户信息、帖子内容等。     |
| `hasBottomOperation`| `Boolean` | `true`       | 是否显示底部操作按钮（点赞、评论、收藏、转发）。 |

## 使用方法

### 引入组件

在页面或父组件中引入 `kt-community-post-card` 组件：

```vue

<template>
  <kt-community-post-card
      :postId="'123456'"
      :borderRadius="'30rpx'"
      @dtoClick="handleThreeDotClick"
  />
</template>

<script>
export default {
  methods: {
    handleThreeDotClick(post) {
      console.log('右上角的三个点被点击了', post);
    }
  },
}
</script>
```

### 属性说明（Props）

#### `postId`

传入帖子 ID，组件会自动根据 ID 获取帖子数据。

#### `post`

直接传入帖子数据对象，包含以下字段：

- `userAccountIdOfUploader`：发帖用户的账号 ID。
- `id`：帖子 ID。
- `gmtCreate`：帖子创建时间。
- `items`：帖子内容，支持 `kt-format` 格式。

#### `borderRadius`

设置卡片的圆角大小，例如 `'30rpx'`。

#### `isTransparent`

设置卡片背景是否透明，默认为 `false`。

#### `hasBottomOperation`

控制是否显示底部操作按钮（点赞、评论、收藏等）。

## 事件 (Events)

| 事件名       | 描述                                   | 参数                     |
|--------------|----------------------------------------|--------------------------|
| `dtoClick`   | 点击三点按钮时触发                    | `post` (当前帖子对象)    |

## 插槽（Slots）
| 插槽名         | 描述                                   |
| bottom        | 底部操作按钮的插槽，支持自定义内容。        |

## 样式

组件支持深色模式，自动适配 `card-mode-color-scheme-dark` 样式。

## 注意事项

1. 组件依赖 `kt-format` 进行内容格式化展示，请确保项目中已引入相关依赖。
2. 组件使用了 `$kt` 工具类，请确保项目中已配置 `$kt`。