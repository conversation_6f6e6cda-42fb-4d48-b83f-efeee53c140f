<template>
  <view class="container">

    <view style="position: relative">
      <!-- #ifdef MP-WEIXIN -->
      <wechat-mp-login :wechat-login-method="wechatLoginMethod" ref="wechatMpLogin"
        :agree="bodyData.agree"></wechat-mp-login>
      <!-- #endif -->
      <type-select @change="changeType"></type-select>

      <view style="height: 10px"></view>
      <view class="input-box">

        <email-and-phone-input :body-data="bodyData" @change="changeMethod"></email-and-phone-input>

        <view style="height: 30rpx"></view>

        <verificationCode-input :body-data="bodyData" v-if="bodyData.typeCode === 'verificationCode'"
          @change="changeVerificationCode"></verificationCode-input>

        <password-input :body-data="bodyData" v-if="bodyData.typeCode === 'password'"
          @change="changePassword"></password-input>
        <view v-if="type === 1" class="login-input-box">
          <view class="icon-box">

            <view class="icon">
              <image class="icon-img" :src="$kt.file.byPath('icon/key.svg')" mode="widthFix"></image>
            </view>

          </view>

          <view style="width: calc(100% - 120rpx)" class="input-box">
            <input @input="param.password = $event.detail.value" v-model="param.password" class="input"
              :adjust-position="false" :placeholder="$i18n.zhToGlobal('密码')" :type="passwordType"></input>

          </view>

          <view v-if="passwordType === 'password'" @click="passwordType = 'text'" class="icon-box">

            <view class="icon">
              <view style="font-size: 20rpx">
                <image :src="$kt.file.byPath('icon/eye.svg')" mode="widthFix" class="icon-img"></image>
              </view>
            </view>

          </view>

          <view v-if="passwordType === 'text'" @click="passwordType = 'password'" class="icon-box">

            <view class="icon">
              <view style="font-size: 20rpx">
                <image :src="$kt.file.byPath('icon/eyeOff.svg')" mode="widthFix" class="icon-img"></image>
              </view>
            </view>

          </view>

        </view>
        <!-- 使用“邮箱+验证码”自动注册" -->
        <!-- <view v-if="bodyData.typeCode === 'password'" :class="clazz.tips">{{ $i18n.zhToGlobal("使用“验证码登录”，会自动注册") }}</view> -->

        <!-- 登录后自动注册 -->
        <!-- <view v-if="bodyData.typeCode === 'verificationCode'" :class="clazz.tips">{{ $i18n.zhToGlobal("登录后自动注册") }}</view> -->
      </view>

      <view
        v-if="$i18n.getLanguageCode() === 'fi_FI' || $i18n.getLanguageCode() === 'bg_BG' || $i18n.getLanguageCode() === 'fr_BE'"
        style="height: 30rpx"></view>

      <view style="height: 30rpx"></view>
      <kt-button @click="toLogin()" ref="nextButton" :is-open-box-shadow="false">{{ $i18n.zhToGlobal("登录") }}{{ " "
        }}{{ "➠" }}
      </kt-button>
      <view style="height: 20rpx"></view>

      <!-- #ifdef MP-WEIXIN -->
      <view style="text-align: center">
        <view @click="$refs.wechatMpLogin.open()" class="to-back-btn">{{ $i18n.zhToGlobal("返回") }}</view>
      </view>
      <view style="height: 20rpx"></view>
      <!-- #endif -->
    </view>

    <login-agreement @change="changeAgree"></login-agreement>


  </view>
</template>

<script>
import LoginAgreement from "./LoginAgreement.vue";
import TypeSelect from "./TypeSelect.vue";
import EmailAndPhoneInput from "../inputComponents/EmailAndPhoneInput.vue";
import VerificationCodeInput from "../inputComponents/VerificationCodeInput.vue";
import PasswordInput from "../inputComponents/PasswordInput.vue";
import $kt from "@/uni_modules/kantboot";
import operate from "../js/operate";
import WechatMpLogin
  from "./WechatMpLogin.vue";

export default {
  props: {
    wechatLoginMethod: {
      type: String,
      default: "loginByPhone"
    },
  },
  components: {
    WechatMpLogin,
    LoginAgreement,
    EmailAndPhoneInput,
    TypeSelect,
    VerificationCodeInput,
    PasswordInput
  },
  data() {
    return {
      passwordType: "password",
      bodyData: {
        phoneAreaCode: "86",
        typeCode: "verificationCode",
        methodCode: "email",
        to: "",
        email: "",
        phone: "",
        password: "",
        verificationCode: "",
        agree: false,
      },
      clazz: {
        tips: this.$kt.style.toggleClass("tips"),
      }

    }
  },
  created() {
  },
  mounted() {
    $kt.event.on("nextButtonInLogin:success", (res) => {
      this.$refs.nextButton.success(res.msg);
    });
    $kt.event.on("nextButtonInLogin:error", (err) => {
      this.$refs.nextButton.error(err.errMsg);
    });

  },
  methods: {
    changeType(e) {
      this.bodyData.typeCode = e.typeCode;
    },
    changeMethod(e) {
      this.bodyData.methodCode = e.methodCode;
      this.bodyData.to = e.to;
      if (e.methodCode === "email") {
        this.bodyData.email = e.email;
      } else {
        this.bodyData.phone = e.phone;
        this.bodyData.phoneAreaCode = e.phoneAreaCode;
      }
    },
    changeVerificationCode(e) {
      this.bodyData.verificationCode = e.verificationCode;
    },
    changePassword(e) {
      this.bodyData.password = e.password
    },
    changeAgree(e) {
      this.bodyData.agree = e.agree;
    },
    toLogin() {
      operate.login(this.bodyData);
    },
  },
}
</script>

<style lang="scss" scoped>
@import "../css/loginInput";

.container {
  position: relative;
}

.to-back-btn {
  color: #666666;
  font-size: 30rpx;
  letter-spacing: 2rpx;
}
</style>
