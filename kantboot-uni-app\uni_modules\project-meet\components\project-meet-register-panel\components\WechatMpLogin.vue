<template>
  <view
      v-show="show"
      :class="clazz.box">
    <view class="box-logo">
      <image
        :src="$kt.file.byPath('image/logo.svg')"
        mode="aspectFit"
        class="logo"></image>
    </view>
    <kt-button
        v-if="false"
        ref="loginByCodeButton"
        @click="loginByCode"
        class="button">
      <image
          class="button-icon"
      :src="$kt.file.byPath('icon/wechat.svg')"
      ></image>
      {{$i18n.zhToGlobal("微信登录")}}
      <view class="button-into-tip">
        {{"➠"}}
      </view>
    </kt-button>
    <kt-button
        v-show="!agree"
        ref="loginByPhoneButtonTips"
        @click="tipAgree"
        class="button">
      {{$i18n.zhToGlobal("一键手机号登录")}}
      <view class="button-into-tip">
        {{"➠"}}
      </view>
    </kt-button>

    <kt-button
        v-show="agree"
        style="position: relative"
        ref="loginByPhoneButton"
        class="button">
      <view>

      </view>
      {{$i18n.zhToGlobal("一键手机号登录")}}
      <view class="button-into-tip">
        {{"➠"}}
      </view>
      <button
          style="position: absolute;z-index:999;width: 100%;height: 100%;top:0;left:0;opacity: 0"
          open-type="getPhoneNumber" @getphonenumber="loginByPhone"
      >123</button>
    </kt-button>



    <view style="height: 20rpx"></view>
    <view style="text-align: center">
      <view
          @click="show = false"
          class="little-btn">{{$i18n.zhToGlobal("使用其它方式登录")}}</view>
    </view>
  </view>
</template>

<script>
import operate from "../js/operate";
import $kt from "@/uni_modules/kantboot";
export default {
  props: {
    agree:{
      type:Boolean,
      default:false
    },
  },
  data() {
    return {
      show:true,
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      },
    };
  },
  methods: {
    open(){
      this.show = true;
    },
    close(){
      this.show = false;
    },
    loginByPhone(e){
      let requestParam = {
          encryptedData:e.detail.encryptedData,
          iv:e.detail.iv,
          code:e.detail.code,
      }
      this.$refs.loginByPhoneButton.loading(null,9999);
      this.$kt.request.post('/api-wechat-applet-web/apiWechatAppletLogin/loginByPhone',{
        data:requestParam,
      }).then((res)=>{
        operate.loginSuccessHandle(res);
        this.$refs.loginByPhoneButton.success(res.msg);
      }).catch((err)=>{
        console.log(err,"err");
        this.$refs.loginByPhoneButton.error(err.errMsg);
      });
    },

    tipAgree(){
      if(!this.agree){
        this.$refs.loginByPhoneButtonTips.error($kt.i18n.zhToGlobal("未同意协议"));
      }
    },
    loginByCode() {
      if(!this.agree){
        this.$refs.loginByCodeButton.error($kt.i18n.zhToGlobal("未同意协议"));
        return;
      }
      uni.login({
        provider: 'weixin',
        success: (loginRes) => {
          if (loginRes.code) {
            this.$refs.loginByCodeButton.loading(null,9999);
            // /api-wechat-applet-web/apiWechatAppletLogin/loginByCode
            this.$kt.request.post('/api-wechat-applet-web/apiWechatAppletLogin/loginByCode',{
              data:{code:loginRes.code},
            }).then((res)=>{
              console.log(res,"--=-=-");
              operate.loginSuccessHandle(res);
              // this.$refs.loginByCodeButton.success(res.msg);
            }).catch((err)=>{
              console.log(err,"err");
              this.$refs.loginByCodeButton.error(err.errMsg);
            });
          }
        },
        fail: (err) => {
        }
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.box{
  position: absolute;
  background-color: #FFFFFF;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1000;
  padding: 30rpx;
  box-sizing: border-box;

  .box-logo{
    text-align: center;
    .logo{
      width: 300rpx;
      height: 300rpx;
    }
  }
}

.little-btn{
  display: inline-block;
  font-size: 28rpx;
  color: #333333;
}

.button{
  position: relative;
  .button-icon{
    position: absolute;
    left: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 60rpx;
    height: 60rpx;
    filter: invert(1);
  }
  .button-into-tip{
    position: absolute;
    right: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    font-size: 40rpx;
  }
}

.box-mode-color-scheme-dark{
  background-color: #292929;
  .little-btn{
    color: #bbbbbb;
  }
}
</style>
