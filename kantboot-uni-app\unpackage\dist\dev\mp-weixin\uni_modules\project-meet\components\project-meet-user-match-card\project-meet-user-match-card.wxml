<view class="{{['data-v-0a67bad5',clazz.card]}}"><image class="bg-img data-v-0a67bad5" src="{{$root.g0}}"></image><image class="bg-img-2 data-v-0a67bad5" src="{{$root.g1}}"></image><view class="card-header data-v-0a67bad5"><block wx:if="{{$root.g2}}"><view style="display:inline-block;" class="data-v-0a67bad5">{{''+$root.g3+''}}</view></block><block wx:else><block wx:if="{{$root.g4}}"><view style="display:inline-block;" class="data-v-0a67bad5">{{''+$root.g5+''}}</view></block><block wx:else><view style="display:inline-block;" class="data-v-0a67bad5">{{''+$root.g6+''}}</view></block></block><view data-event-opts="{{[['tap',[['getInitList']]]]}}" class="card-header-change data-v-0a67bad5" bindtap="__e"><view class="{{['change-icon','data-v-0a67bad5',(isLoading)?'change-icon-loading':'']}}"><image style="width:25rpx;height:25rpx;" src="{{$root.g7}}" class="data-v-0a67bad5"></image></view>{{''+''}}<view style="display:inline-block;" class="data-v-0a67bad5">{{''+$root.g8+''}}</view></view></view><view style="height:20rpx;" class="data-v-0a67bad5"></view><view class="card-content data-v-0a67bad5"><block wx:if="{{$root.g9}}"><view class="data-v-0a67bad5"><u-loading-icon vue-id="429020d1-1" mode="circle" size="{{80}}" class="data-v-0a67bad5" bind:__l="__l"></u-loading-icon></view></block><block wx:for="{{userAccountList}}" wx:for-item="item" wx:for-index="__i0__"><view class="{{['card-content-item','data-v-0a67bad5',(isLoading)?'card-content-item-loading':'',(!isLoading)?'card-content-item-loading-finish':'']}}"><block wx:if="{{$root.g10==='mobile'}}"><project-meet-user-info-in-card vue-id="{{'429020d1-2-'+__i0__}}" width="190rpx" height="240rpx" user-info="{{item}}" class="data-v-0a67bad5" bind:__l="__l"></project-meet-user-info-in-card></block><block wx:else><block wx:if="{{$root.g11==='pc'}}"><project-meet-user-info-in-card vue-id="{{'429020d1-3-'+__i0__}}" width="300rpx" height="340rpx" user-info="{{item}}" class="data-v-0a67bad5" bind:__l="__l"></project-meet-user-info-in-card></block></block></view></block></view><view style="height:20rpx;" class="data-v-0a67bad5"></view><view class="card-btn-box data-v-0a67bad5"><block wx:if="{{successByTo}}"><view data-event-opts="{{[['tap',[['to']]]]}}" class="card-btn data-v-0a67bad5" bindtap="__e">{{"✔"+" "+$root.g12}}</view></block><block wx:else><block wx:if="{{!loading}}"><view data-event-opts="{{[['tap',[['to']]]]}}" class="card-btn data-v-0a67bad5" bindtap="__e">{{$root.g13}}</view></block><block wx:else><block wx:if="{{loading}}"><view data-event-opts="{{[['tap',[['to']]]]}}" class="card-btn data-v-0a67bad5" bindtap="__e"><image class="loading-icon data-v-0a67bad5" src="{{$root.g14}}"></image></view></block></block></block></view><view style="height:20rpx;" class="data-v-0a67bad5"></view><block wx:if="{{$root.g15}}"><view class="card-tips data-v-0a67bad5">{{''+$root.g16+''}}</view></block></view>