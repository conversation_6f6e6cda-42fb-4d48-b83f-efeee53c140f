<template>
  <kt-popup ref="ktPopup">
    <view class="fixed-box">
      <view :class="clazz.modal">
        <view class="modal-header">
          <view class="modal-title">{{title}}</view>
        </view>
        <view class="modal-content">
          <view class="modal-content-text">{{content}}</view>
        </view>
        <view class="modal-footer"  @click="confirm">
          <view class="modal-split"></view>
          <view>
            <view class="modal-button">
              {{confirmText}}
            </view>
          </view>
        </view>
      </view>
    </view>
  </kt-popup>
</template>

<script>

import $kt from "../../index.js";

export default {
  props:{
    title: {
      type: String,
      default: $kt.i18n.zhToGlobal("标题"),
    },
    content: {
      type: String,
      // default: "是否确定退出登录",
      default: $kt.i18n.zhToGlobal("是否确定退出登录"),
    },
    confirmText: {
      type: String,
      default: $kt.i18n.zhToGlobal("确定"),
    },
  },
  data() {
    return {
      uuid:"",
      clazz:{
        modal: this.$kt.style.toggleClass("modal"),
      }
    };
  },
  mounted() {
  },
  methods: {
    open() {
      this.$refs.ktPopup.open();
    },
    confirm() {
      this.close();
      this.$emit("confirm");
    },
    close() {
      this.$refs.ktPopup.close();
      this.$emit("close");
    },
  },
}
</script>

<style lang="scss" scoped>

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
  height: auto;
  background-color: #fff;
  border-radius: 10px;

  .modal-header {
    padding: 20rpx;
    text-align: center;
  }

  .modal-title {
    font-size: 34rpx;
    font-weight: bold;
  }

  .modal-content {
    padding: 20rpx 20rpx 0 20rpx;
    text-align: center;
    letter-spacing: 1rpx;

    .modal-content-text {
      font-size: 30rpx;
      color: #333;
    }
  }

  .modal-split{
    height: 1px;
    background-color: #e0e0e0;
    margin: 20rpx 0;
  }

  .modal-footer {
    padding: 20rpx;
    text-align: center;

    .modal-button {
      display: inline-block;
      padding: 10rpx 20rpx;
      margin: 0 10rpx;
      border-radius: 5px;
      cursor: pointer;
      font-size: 28rpx;
      letter-spacing: 2rpx;

    }
  }
}

.modal-footer:active{
  opacity: .5;
}

.modal-mode-color-scheme-dark {
  // 颜色反转
  background-color: #393939;

  .modal-header {
    color: #eeeeee;
  }
  .modal-content {
    .modal-content-text {
      color: #bbb;
    }
  }

  .modal-split {
    background-color: #444;
  }

  .modal-footer {
    .modal-button {
      color: #fff;
    }
  }
}
</style>
