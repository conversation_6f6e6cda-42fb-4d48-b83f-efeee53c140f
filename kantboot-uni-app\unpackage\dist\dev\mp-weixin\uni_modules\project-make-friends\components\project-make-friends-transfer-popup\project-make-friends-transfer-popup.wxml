<view class="data-v-68270962"><kt-popup vue-id="c68ab838-1" overlayClose="{{true}}" zIndex="{{999999999}}" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-68270962 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-68270962"><view data-event-opts="{{[['tap',[['closePop',['$event']]]]]}}" class="close-btn data-v-68270962" bindtap="__e">×</view><view class="title data-v-68270962">{{$root.g0}}</view><view class="box-search data-v-68270962"><view class="search-box data-v-68270962"><u-input vue-id="{{('c68ab838-2')+','+('c68ab838-1')}}" placeholder="{{$root.g1}}" value="{{searchText}}" data-event-opts="{{[['^confirm',[['onSearchInput']]],['^input',[['__set_model',['','searchText','$event',[]]]]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-68270962" bind:__l="__l" vue-slots="{{['suffix']}}"><view slot="suffix" class="data-v-68270962"><button class="search-btn data-v-68270962" disabled="{{!$root.g2}}" data-event-opts="{{[['tap',[['onSearchInput',['$event']]]]]}}" bindtap="__e">{{$root.g3}}</button></view></u-input></view></view><scroll-view class="member-list data-v-68270962" scroll-y="{{true}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]]]}}" bindscrolltolower="__e"><block wx:if="{{loading}}"><view class="loading-box data-v-68270962"><u-loading vue-id="{{('c68ab838-3')+','+('c68ab838-1')}}" mode="circle" size="28" class="data-v-68270962" bind:__l="__l"></u-loading></view></block><block wx:else><block wx:if="{{$root.g4===0}}"><view class="empty-tip data-v-68270962">{{''+$root.g5+''}}</view></block></block><checkbox-group data-event-opts="{{[['change',[['onCheckboxChange',['$event']]]]]}}" bindchange="__e" class="data-v-68270962"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="{{['member-item','data-v-68270962',(item.g6)?'member-item-selected':'']}}"><view class="member-info data-v-68270962"><checkbox value="{{item.g7}}" checked="{{item.g8}}" class="data-v-68270962"></checkbox><image class="member-avatar data-v-68270962" src="{{item.$orig.fileIdOfAvatar?item.g9:'/static/default-avatar.png'}}" mode="aspectFill"></image><view class="member-details data-v-68270962"><view class="member-nickname data-v-68270962">{{item.g10}}</view><view class="member-id data-v-68270962">{{"ID: "+item.$orig.id}}</view><block wx:if="{{item.$orig.phone}}"><view class="member-phone data-v-68270962">{{item.$orig.phoneAreaCode+" "+item.$orig.phone}}</view></block></view></view></view></block></checkbox-group><block wx:if="{{hasMore&&!loading}}"><view data-event-opts="{{[['tap',[['loadMore',['$event']]]]]}}" class="load-more data-v-68270962" bindtap="__e">{{''+$root.g11+''}}</view></block><block wx:if="{{$root.g12}}"><view class="no-more data-v-68270962">{{''+$root.g13+''}}</view></block></scroll-view><view class="footer data-v-68270962"><block wx:if="{{$root.g14>0}}"><view class="selected-count data-v-68270962">{{''+$root.g15+": "+$root.g16+''}}</view></block><view class="buttons data-v-68270962"><button class="confirm-btn data-v-68270962" disabled="{{$root.g17===0}}" data-event-opts="{{[['tap',[['confirmSelection',['$event']]]]]}}" bindtap="__e">{{$root.g18}}</button></view></view></view></kt-popup></view>