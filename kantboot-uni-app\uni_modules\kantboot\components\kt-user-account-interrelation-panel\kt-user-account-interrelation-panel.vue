<template>
  <view
      class="chat-scroll-view"
      :style="{height}"
      id="chatScrollView">

    <view>
      <view class="header-box">
        <view class="header-item"
              :class="{
                'header-item-active': subsectionCurrent === 'mutualFollow'
              }"
              @click="selectSubsection('mutualFollow')">
          {{ mutualFollowText }}
        </view>
        <view class="header-item"
              :class="{
                'header-item-active': subsectionCurrent === 'follow'
              }"
              @click="selectSubsection('follow')">
          {{ followText }}
        </view>
        <view class="header-item"
              :class="{
                'header-item-active': subsectionCurrent === 'followed'
              }"
              @click="selectSubsection('followed')">
          {{ followedText }}
        </view>
      </view>
      <view style="height: 20rpx"></view>
      <view class="input-box">
        <image
            class="input-box-icon"
            :src="$kt.file.byPath('icon/search.svg')"
        ></image>
        <input
            :adjust-position="false"
            clearable
            v-model="keyword"
            class="input-box-input"
            :placeholder="$i18n.zhToGlobal('搜索')"
        />
        <image
            v-if="keyword"
            @click="keyword = ''"
            class="input-box-icon-clear"
            :src="$kt.file.byPath('icon/clear.svg')"
        ></image>

      </view>

    </view>
    <view style="height: 30rpx"></view>

    <view>
      <view v-if="userAccountId"
            class="box">
        <swiper :style="{
            height: 'calc(' + height + ' - ' + '120rpx)'
          }"
                :current-item-id="subsectionCurrent"
                @change="sectionChange"
        >
          <swiper-item item-id="mutualFollow">
            <view
                class="scroll-view"
                :style="{
                  height: 'calc(' + height + ' - ' + '120rpx)'
                }"
            >
              <mutual-follow-list
                  :class="{
                    'item-no-selected': subsectionCurrent !== 'mutualFollow'
                  }"
                  @select="select"
                  ref="list"
                  :id-selected-list="idSelectedList"
                  :keyword="keyword"
                  :userAccountId="userAccountId"
                  :custom-user-info-card="customUserInfoCard"
              >
                <template #userInfoCard="{ userAccount }">
                  <slot name="userInfoCard" :user-account="userAccount"></slot>
                </template>
              </mutual-follow-list>
            </view>

          </swiper-item>

          <swiper-item item-id="follow">
            <view
                class="scroll-view"
                :style="{
                  height: 'calc(' + height + ' - ' + '120rpx)'
                }">

            <follower-list
                :class="{
                    'item-no-selected': subsectionCurrent !== 'follow'
                  }"
                @select="select"
                ref="list"
                :id-selected-list="idSelectedList"
                :keyword="keyword"
                :userAccountId="userAccountId"
                :custom-user-info-card="customUserInfoCard">
              <template #userInfoCard="{ userAccount }">
                <slot name="userInfoCard" :user-account="userAccount"></slot>
              </template>
            </follower-list>
            </view>
          </swiper-item>

          <swiper-item item-id="followed">
            <view
                class="scroll-view"
                :style="{
                  height: 'calc(' + height + ' - ' + '120rpx)'
                }"
            >


              <followed-list
                  :class="{
                    'item-no-selected': subsectionCurrent !== 'followed'
                  }"
                  @select="select"
                  ref="list"
                  :id-selected-list="idSelectedList"
                  :keyword="keyword"
                  :userAccountId="userAccountId"
                  :custom-user-info-card="customUserInfoCard">
                <template #userInfoCard="{ userAccount }">
                  <slot name="userInfoCard" :user-account="userAccount"></slot>
                </template>
              </followed-list>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>
  </view>
</template>

<script>
import FollowedList from "./components/followed-list.vue";
import FollowerList from "./components/follower-list.vue";
import MutualFollowList from "./components/mutual-follow-list.vue";
import $i18n from "@/uni_modules/kantboot/libs/i18n";

export default {
  components: {MutualFollowList, FollowedList, FollowerList},
  props: {
    height: {
      type: String,
      default: '900rpx'
    },
    userAccountId: {
      type: String || Number,
      default: ''
    },
    manySelect: {
      type: Boolean,
      default: false
    },
    idSelectedListProp: {
      type: Array,
      default: () => []
    },
    initCode: {
      type: String,
      default: 'mutualFollow'
    },
    mutualFollowText: {
      type: String,
      default: $i18n.zhToGlobal("互相关注")
    },
    followText: {
      type: String,
      default: $i18n.zhToGlobal("我关注的人")
    },
    followedText: {
      type: String,
      default: $i18n.zhToGlobal("关注我的人")
    },
    /**
     * 是否自定义用户信息卡片
     */
    customUserInfoCard: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isLogin: false,
      keyword: '',
      idSelectedList: [],
      userAccountList: [],
      subsectionCurrent: "mutualFollow",
    };
  },
  watch: {
    initCode: {
      handler(val) {
        this.selectSubsection(val);
      }
    },
    idSelectedListProp: {
      handler(val) {
        this.idSelectedList = val;
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    if (this.initCode) {
      console.log(this.initCode, "初始化的code");
      this.selectSubsection(this.initCode);
    }
  },
  mounted() {
  },
  methods: {
    setIdSelectedList(list) {
      this.idSelectedList = list;
    },
    idSelectListPush(userAccountId) {

      for (let i = 0; i < this.idSelectedList.length; i++) {
        if (this.idSelectedList[i] + "" === userAccountId + "") {
          // 删除
          this.idSelectedList.splice(i, 1);
          return;
        }
      }
      this.idSelectedList.push(userAccountId);
    },
    select(userAccount) {
      if (this.manySelect) {
        this.idSelectListPush(userAccount.id);
        this.$emit('selectMany', this.idSelectedList);
      }
      this.$emit('select', userAccount);
    },
    selectSubsection(code) {
      this.subsectionCurrent = code;
    },
    sectionChange(e) {
      this.subsectionCurrent = e.detail.currentItemId;
    }
  }
}
</script>

<style lang="scss" scoped>

.header-box {
  text-align: center;

  .header-item {
    width: 33.33%;
    font-size: 28rpx;
    display: inline-block;
    cursor: pointer;
  }

  .header-item-active {
    color: #000000;
    font-weight: bold;
  }
}

.loading-icon-2 {
  width: 100px;
  height: 100px;
}

.box {
  padding: 20rpx;
  box-sizing: border-box;
}

.input-box {
  position: relative;
  background-color: #f8f8f8;
  padding: 10rpx;
  border-radius: 10rpx;

  .input-box-icon {
    position: absolute;
    width: 30rpx;
    height: 30rpx;
    left: 20rpx;
    top: 50%;
    transform: translateY(-50%);
  }

  .input-box-input {
    display: inline-block;
    width: calc(100% - 100rpx);
    margin-left: 60rpx;
  }

  .input-box-icon-clear {
    position: absolute;
    width: 40rpx;
    height: 40rpx;
    right: 20rpx;
    top: 50%;
    opacity: .2;
    transform: translateY(-50%);
    z-index: 1;
  }

}


.item-no-selected {
  transform: scale(.95);
}

.scroll-view {
  overflow: scroll; /* 允许滚动 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.scroll-view::-webkit-scrollbar {
  display: none !important;
}

</style>

