@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-2362d008 {
  width: 100%;
}
.message-box.data-v-2362d008 {
  position: relative;
  min-width: 100rpx;
  max-width: calc(100% - 100rpx - 100rpx);
  display: inline-block;
  padding: 20rpx;
  box-sizing: border-box;
  vertical-align: top;
  background-color: #FFFFFF;
  box-shadow: 0 10rpx 30rpx rgba(118, 118, 118, 0.05);
  border-radius: 20rpx;
  text-align: left;
}
.chat-list-item-content-text.data-v-2362d008 {
  word-wrap: break-word;
  white-space: pre-wrap;
}
.avatar-box.data-v-2362d008 {
  width: 100rpx;
  display: inline-block;
}
.chat-list-item-content-img.data-v-2362d008 {
  width: 300rpx;
  border-radius: 10rpx;
}
.chat-list-item-content-language-box.data-v-2362d008 {
  display: inline-block;
  text-align: right;
  margin-left: 12rpx;
  margin-right: 10rpx;
  margin-bottom: -20rpx;
}
.chat-list-item-content-language-box .chat-list-item-content-language-icon.data-v-2362d008 {
  cursor: pointer;
  width: 50rpx;
  height: 50rpx;
}
.chat-list-item-content-language-box.data-v-2362d008:active {
  opacity: 0.8;
}
.read-dot.data-v-2362d008 {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  bottom: 10rpx;
  right: 10rpx;
  background-color: rgba(0, 0, 0, 0.2);
}
.read-dot-self.data-v-2362d008 {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  bottom: 10rpx;
  left: 10rpx;
  background-color: #bbbbbb;
}
.split-line.data-v-2362d008 {
  width: 100%;
  height: 1rpx;
  background-color: #E5E5E5;
  margin: 10rpx 0;
}
.no-user-select.data-v-2362d008 {
  -webkit-user-select: none;
}
.un-read-dot.data-v-2362d008 {
  background-color: #bbbbbb;
}
.is-read-dot.data-v-2362d008 {
  background-color: #4CAF50;
}
