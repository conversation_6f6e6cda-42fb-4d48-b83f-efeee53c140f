<view class="data-v-3dfa0a0f"><u-popup vue-id="fd708e52-1" show="{{show}}" mode="bottom" bgColor="rgba(0,0,0,0)" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-3dfa0a0f" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup data-v-3dfa0a0f"><view style="height:30rpx;" class="data-v-3dfa0a0f"></view><kt-user-info-card vue-id="{{('fd708e52-2')+','+('fd708e52-1')}}" userInfo="{{userAccount}}" class="data-v-3dfa0a0f" bind:__l="__l"></kt-user-info-card><view style="height:70rpx;" class="data-v-3dfa0a0f"></view><kt-button vue-id="{{('fd708e52-3')+','+('fd708e52-1')}}" boxShadow="{{false}}" data-ref="unFollowBtn" data-event-opts="{{[['^click',[['unFollow']]]]}}" bind:click="__e" class="data-v-3dfa0a0f vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g0}}</kt-button><view style="height:100rpx;" class="data-v-3dfa0a0f"></view></view></u-popup></view>