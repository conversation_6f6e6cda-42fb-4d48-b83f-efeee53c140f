<template>
  <view>
    <view :class="clazz.navBarContainer" 
        :style="(backgroundColor ? 'background-color:'+backgroundColor+';' : null)">

      <view class="status-bar" :style="{
                height: statusBarHeight + 'px'
            }">
      </view>

      <view class="nav-bar" :style="{
                height: navHeight + 'px'
            }">
        <!-- 如果有icon就显示icon，没有就显示返回 -->
        <template v-if="icon">
          <image class="nav-bar-back-icon"
                 mode="aspectFill"
                 :src="icon"></image>
        </template>
        <template v-else>
          <image  v-show="!!isHasBack2" @click="toBack()" class="nav-bar-back-icon"
                 :src="$kt.file.byPath('kantboot/icon/back.svg')"></image>
          
          <image v-show="!isHasBack2" @click="toHome()" class="nav-bar-back-icon" style="width: 40rpx;height: 40rpx;border-radius: 10rpx;
                    transform: translateY(-50%)" :src="$kt.file.byPath('kantboot/icon/home.svg')"></image>
        </template>

        <text class="nav-bar-title"
        >
          {{ title }}
        </text>

        <!-- #ifdef MP-WEIXIN -->
        <view class="nav-bar-slot" :style="{
                    right: (menuButton.width - (-15)) + 'px',
                    top: 'calc(50% -'+' '+(slotHeight/2)+'px)'
                }"
              id="slot"
        >
          <image
                v-if="isHasI18n"
              @click="toLanguageSelect()"
              mode="aspectFill"
              class="menu-icon"
              :class="{
                'menu-icon-highlight': highlight
              }"
              :src="$kt.file.byPath('kantboot/icon/language.svg')"></image>
          <slot></slot>
        </view>
        <!-- #endif -->

        <!-- #ifdef H5 -->
        <view class="nav-bar-slot" :style="{
                    right: '30px',
                    top: 'calc(50% -'+' '+(slotHeight/2)+'px)'
                }"
              id="slot"
        >
          <image
          v-if="isHasI18n"

              @click="toLanguageSelect()"
              mode="aspectFill"
              class="menu-icon"
              :src="$kt.file.byPath('kantboot/icon/language.svg')"></image>
          <slot></slot>
        </view>
        <!-- #endif -->

        <!-- #ifdef APP -->
        <view class="nav-bar-slot" :style="{
                    right: '80rpx',
                    top: 'calc(50% -'+' '+(slotHeight/2)+'px)'
                }"
              id="slot"
        >
          <image
              v-if="isHasI18n"
              @click="toLanguageSelect()"
              mode="aspectFill"
              class="menu-icon"
              :src="$kt.file.byPath('kantboot/icon/language.svg')"></image>
          <slot></slot>
        </view>
        <!-- #endif -->

        <!-- #ifdef APP-PLUS -->
        <view class="nav-bar-slot" :style="{
                    right: '30rpx'
                }"
              id="slot"
        >
          <slot></slot>
        </view>
        <!-- #endif -->
      </view>
    </view>
    <view :style="'height:' + (statusBarHeight - (-navHeight)) + 'px'"></view>
    <kt-language-select-popup
    ref="ktLanguageSelectPopup"
    ></kt-language-select-popup>
  </view>
</template>

<script>
import router from "../../libs/router";
import style from "../../libs/style";

export default {
  name: "kt-nav-bar",
  props: {
    // 高亮
    highlight: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    backgroundColor: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    // 弧度
    borderRadius: {
      type: String,
      default: '0'
    },
    isHasBack: {
      type: Object|String|Boolean,
      default: 1
    },
    isHasI18n: {
      type: Object|String|Boolean,
      default: true
    },
    indexPath: {
      type: String,
      default: ''
    }
  },
  watch:{
    isHasBack:{
      handler(val) {
        if (val) {
          this.isHasBack = val;
        } else {
          this.isHasBack = false;
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      statusBarHeight: uni.getSystemInfoSync().statusBarHeight,
      navHeight: 48,
      // #ifdef MP-WEIXIN
      menuButton: wx.getMenuButtonBoundingClientRect(),
      // #endif
      slotHeight: 0,
      icon: '',
      isHasBack2: true,
      clazz:{
        navBarContainer: style.toggleClass('nav-bar-container')
      }
    };
  },
  created() {
    this.navHeight = 44;
    // #ifdef MP-WEIXIN
    this.navHeight = uni.getSystemInfoSync().system.indexOf("ios") != -1 ? 44 : 48;
    for (let i = 0; i < 100; i++) {
      setTimeout(()=>{
        this.menuButton = wx.getMenuButtonBoundingClientRect();
      },1000);
    }
    // #endif

    // 判断是否还有上一页
    let pages = getCurrentPages();
    if (pages.length > 1) {
      this.isHasBack2 = true;
    } else {
      this.isHasBack2 = false;
    }


  },
  // 初始化
  mounted() {
    this.createSlotHeight();

  },

  methods: {
    toLanguageSelect(){
      if(this.$kt.style.detectDeviceType()!=='pc'){
        this.$kt.router.toLanguageSelect();
        return;
      }
      this.$refs.ktLanguageSelectPopup.open();
    },
    createSlotHeight() {
		try{
      // #ifndef H5
      uni.createSelectorQuery().in(this).select('#slot').boundingClientRect((rect) => {
        this.slotHeight = rect.height;
        console.log(this.slotHeight);
      }).exec();
      // #endif
    }catch (e){
    }
    },
    getHeight() {
      return this.statusBarHeight + this.navHeight;
    },
    toBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    toHome() {
      if(this.indexPath){
        router.reLaunch(this.indexPath);
        return;
      }
      router.reLaunch(router.config.indexPath);
    }
  },
}
</script>

<style lang="scss" scoped>

.nav-bar-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 3000;
  background-color: #FFFFFF;
}

.nav-bar-container-mode-device-pc{
  position: absolute;

}

.nav-bar-container-mode-color-scheme-light {
  background-color: #fff;
  
  .nav-bar-title {
    color: #333333;
  }
  
  .menu-icon{
	  // 颜色反转
    filter: invert(0%);
  }
  
  .nav-bar-back-icon{
    filter: invert(0%);
  }

}

.nav-bar-container-mode-color-scheme-dark {
  background-color: #191919;

  .nav-bar-title {
    color: #ffffff;
    opacity: 0.8;
  }

  .menu-icon{
    // 颜色反转
    filter: invert(100%);
    opacity: 0.8;
  }

  .nav-bar-back-icon{
    filter: invert(100%);
    opacity: 0.8;
  }

}

.nav-bar {
  position: relative;
  z-index: 9999;
  .nav-bar-back-icon {
    position: absolute;
    width: 45rpx;
    height: 45rpx;
    left: 20rpx;
    top: 50%;
    z-index:9999;
    transform: translateY(-50%);
  }

  .nav-bar-back-icon:active {
    transform: scale(0.9) translateY(-50%);
  }

  .nav-bar-slot {
    position: absolute;
  }

  .nav-bar-title {
    position: absolute;
    left: 72rpx;
    font-size: 36rpx;
    top: 50%;
    transform: translateY(-50%);
    font-weight: bold;
  }

}

.menu-icon{
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  border-radius: 55%;
  top: 50%;
  transform: translateY(-50%);
  z-index:1000;
  /* #ifdef MP-WEIXIN */
  right: 0;
  /* #endif */
}


.menu-icon:active{
  transform: translateY(-50%) scale(0.9);
}

</style>