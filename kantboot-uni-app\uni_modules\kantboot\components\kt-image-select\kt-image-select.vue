<template>
  <view>
    <view :class="clazz.imageBox">
      <view
          v-if="!uploadFileLoading&&value.length < count"
          @click="addImage"
          class="image-box-add">
        <image
            class="image-box-add-image"
            :src="$kt.file.byPath('icon/add.svg')"></image>
      </view>
      <view
          v-if="uploadFileLoading"
          class="image-box-loading">
        <image
            class="image-box-add-loading"
            :src="$kt.file.byPath('icon/loading.svg')"></image>
      </view>

      <view
          v-for="(item, index) in value"
          :key="index"
          class="image-box-image"
          draggable="true"
          style="position: relative">
        <view class="image-box-image-remove">
          <image
              class="image-box-image-remove-icon"
              :src="$kt.file.byPath('icon/delete.svg')"
              @click.stop="value.splice(index, 1)"></image>
        </view>
        <image
            class="image-box-image-image"
            mode="aspectFill"
            @click="previewImage(index)"
            :src="$kt.file.visit(item)"></image>
        <view class="image-box-image-bottom">
          <view v-if="count>1">
            <!-- 左移 -->
            <image
                class="image-box-image-icon image-box-image-icon-left"
                :src="$kt.file.byPath('icon/arrowRight.svg')"
                v-if="index>0"
                @click.stop="imageLeft(item)"></image>
            <image
                class="image-box-image-icon image-box-image-icon-right"
                v-if="index<value.length-1"
                :src="$kt.file.byPath('icon/arrowRight.svg')"
                @click.stop="imageRight(item)"></image>

          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
export default {
  props:{
    value:{
      type:Array,
      default: []
    },
    fileGroupCode:{
      type:String,
      default:""
    },
    count:{
      type:Number,
      default: 9
    },
  },
  watch:{
    value:{
      handler(val) {
        this.requestParam.fileIdsOfImages = val;
        this.$emit("input", this.requestParam.fileIdsOfImages);
      },
      immediate: true,
      deep: true
    },
  },
  data() {
    return {
      clazz:{
        imageBox: this.$kt.style.toggleClass("image-box"),
      },
      uploadFileLoading:false,
      requestParam:{
        fileIdsOfImages:[]
      }
    };
  },
  methods: {
    // 图片预览
    previewImage(index) {
      let urls = [];
      for (let i = 0; i < this.value.length; i++) {
        urls.push(this.$kt.file.visit(this.value[i]));
      }
      uni.previewImage({
        current: index,
        urls: urls,
        success: (res) => {
          console.log(res);
        },
        fail: (err) => {
          console.log(err);
        }
      });

    },
    imageLeft(item) {
      const index = this.value.indexOf(item);
      if(index<=0){
        return;
      }
      const leftItem = this.value[index - 1]+"";
      this.value[index - 1] = item+"";
      this.value[index] = leftItem+"";
      try{
        // 重新渲染
        this.$forceUpdate();
      }catch (e) {
        console.log(e);
      }

    },
    imageRight(item) {
      const index = this.value.indexOf(item);
      if(index>=this.value.length-1){
        return;
      }
      const rightItem = this.value[index + 1]+"";
      this.value[index + 1] = item+"";
      this.value[index] = rightItem+"";
      try{
        // 重新渲染
        this.$forceUpdate();
      }catch (e) {
        console.log(e);
      }

    },
    addImage() {
      uni.chooseImage({
        count: this.count - this.value.length,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          console.log(res);
          this.uploadFileLoading = true;
          for (let i = 0; i < res.tempFilePaths.length; i++) {
            await this.addImageOne(res.tempFilePaths[i]);
          }
          this.uploadFileLoading = false;
        },
        fail: (err) => {
          console.log(err);
        }
      });
    },
    async addImageOne(filePath){
      await new Promise((resolve, reject) => {
        this.$request.uploadFile({
          data:{
            file: filePath,
            groupCode: this.fileGroupCode,
          },
          stateSuccess: (res1) => {
            this.value.push(res1.data.id);
            resolve(res1.data.id);
          },
          stateFail: (err) => {
            reject(err);
          },
        });
      });
    },
  },


}
</script>

<style lang="scss" scoped>

.image-box{
  // 超出不换行
  white-space: nowrap;
  // 滚动条
  overflow-x: auto;
  // 滚动条宽度
  scrollbar-width: thin;
  .image-box-add{
    position: relative;
    display: inline-block;
    width: 200rpx;
    height: 200rpx;
    background-color: #f5f5f5;
    border-radius: 10rpx;
    justify-content: center;
    align-items: center;
    margin-right: 30rpx;
    .image-box-add-image{
      position: absolute;
      width: 60rpx;
      height: 60rpx;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      opacity: .8;
    }
  }

  .image-box-loading{
    position: relative;
    display: inline-block;
    width: 200rpx;
    height: 200rpx;
    background-color: #f5f5f5;
    border-radius: 10rpx;
    justify-content: center;
    align-items: center;
    margin-right: 30rpx;
    .image-box-add-loading{
      position: absolute;
      width: 60rpx;
      height: 60rpx;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      opacity: .8;
      // 动画
      animation: loading 1s linear infinite;
    }
  }

  .image-box-image{
    position: relative;
    display: inline-block;
    width: 200rpx;
    height: 200rpx;
    background-color: #f5f5f5;
    border-radius: 10rpx;
    margin-right: 30rpx;
    .image-box-image-image{
      position: absolute;
      width: 100%;
      height: 100%;
      top: 50%;
      left: 50%;
      border-radius: 20rpx;
      transform: translate(-50%, -50%);
      opacity: .8;
    }

    .image-box-image-bottom{
      position: absolute;
      z-index: 1;
      bottom: 50rpx;
      left: 10rpx;
      width: 100%;
      //background-color: rgba(0,0,0,.6);
      box-sizing: border-box;
      border-radius: 0 0 20rpx 20rpx;
      .image-box-image-icon{
        padding: 10rpx;
        background-color: rgba(255,255,255,.5);
        width: 28rpx;
        height: 28rpx;
        opacity: .8;
        border-radius: 55%;
        margin-right: 20rpx;
        // 颜色反转
        filter: invert(1);
      }
      .image-box-image-icon-left{
        position: absolute;
        left: 0;
        transform: rotate(180deg);
      }
      .image-box-image-icon-right{
        position: absolute;
        right: 0;
        transform: rotate(0deg);
      }
    }

    .image-box-image-remove{
      position: absolute;
      top: 10rpx;
      right: 10rpx;
      width: 40rpx;
      height: 40rpx;
      background-color: rgba(0,0,0,.6);
      border-radius: 50%;
      justify-content: center;
      align-items: center;
      z-index: 2;
      .image-box-image-remove-icon{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 50%;
        height: 50%;
        opacity: .8;
        filter: invert(1);
      }
    }

  }



}

@keyframes loading {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.image-box-add:active{
  transform: scale(.97);
}

// 取消滚动条样式
.image-box::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

.image-box-mode-device-pc{
  .image-box-add{
    cursor: pointer;
  }
}
</style>
