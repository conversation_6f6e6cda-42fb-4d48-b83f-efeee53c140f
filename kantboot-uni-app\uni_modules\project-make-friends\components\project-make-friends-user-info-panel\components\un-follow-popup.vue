<template>
	<view>
        <u-popup :show="show" 
        mode="bottom"
        :bgColor="'rgba(0,0,0,0)'"
        @close="close" @confirm="confirm">
            <view class="popup">
                <view style="height: 30rpx;"></view>
                <kt-user-info-card
                :userInfo="userAccount"
                ></kt-user-info-card>
                <view style="height: 70rpx;"></view>

                <kt-button
                ref="unFollowBtn"
                @click="unFollow()"
            :boxShadow="false"
            >{{$i18n.zhToGlobal("取消关注")}}</kt-button>
            <view style="height: 100rpx;"></view>

            </view>
        </u-popup>
	</view>

</template>

<script>
import makeFriends from '@/uni_modules/project-make-friends/libs/load';

export default {
	data() {
		return {
            show: false,
            userAccount:{},
            viewSrc: '',
            isNickenameFocus: false
		}
	},
    mounted() {
        this.list = makeFriends.getCharacteristics();
        console.log(JSON.stringify(this.list));
    },
	methods: {
        unFollow(){
            this.$refs.unFollowBtn.loading(null,999999)
            this.$request.post('/user-interrelation-web/interrelation/unFollow',{
                data:{
                    userAccountId: this.userAccount.id
                }
            }).then(res=>{
                this.$emit('unFollow',this.userAccount.id);
                this.$refs.unFollowBtn.success(res.msg);
                this.close();
            }).catch(err=>{
                if(err.stateCode === "notFollowed"){
                    this.$refs.unFollowBtn.toNone();
                    this.close();
                }
                this.$refs.unFollowBtn.error(err.errMsg);
            })
        },
        nicknameFocus(){
            this.isNickenameFocus = true;
        },
        selectAvatar(){
            console.log('selectAvatar');
            // this.$kt.image.toImageClip().then(res=>{
            // }).catch(err=>{
            // });
            // uniapp选择图片
            uni.chooseImage({
                count: 1, //默认9
                success: (res) => {
                    // console.log(res);
                    // this.imgList = res.tempFilePaths;
                    // console.log(this.imgList);
                    console.log(res.tempFilePaths[0]);
                this.$kt.image.toImageClip(res.tempFilePaths[0],300,300).then(res=>{
                    this.viewSrc = res;
                }).catch(err=>{
                });

                }
            })
        },
        async submit(){
            this.$refs.confirmBtn.loading(null,999999)
            if(this.viewSrc){
                await new Promise((resolve,reject)=>{
                    this.$request.uploadFile(
                {
                    // filePath:this.viewSrc,
                    data:{
                        file: this.viewSrc,
                        groupCode: 'userAvatar',
                    },
                    stateSuccess:res=>{
                        console.log(res);
                        this.userAccount.fileIdOfAvatar = res.data.id;
                        resolve("");
                    },
                    stateError:err=>{
                        console.log(err);
                        reject("");
                    }
                });
                });

            }
            await this.$request.post('/project-make-friends-web/userAccount/setPersonalIntroduction',{data:{
                personalIntroduction: this.userAccount.personalIntroduction,
            }}).then(res=>{
                this.$kt.userAccount.requestSelf();
                this.$refs.confirmBtn.success(res.msg);
                this.close();
            }).catch(err=>{
                this.$refs.confirmBtn.error(res.errMsg);
            })
        },
        open(userAccount){
            this.viewSrc = '';
            this.userAccount = JSON.parse(JSON.stringify(userAccount));
            this.show = true;
        },
        close(){
            this.show = false;
        },
        confirm() {
            this.close();
            this.$emit('confirm', this.selectedYear);
        },
        bindYearChange(e) {
            this.selectedYearIndex = e.detail.value;
            this.selectedYear = this.years[this.selectedYearIndex];
        }
	}
}
</script>

<style lang="scss" scoped>
.avatar:active{
    transform: scale(0.9);
}
.popup{
    padding: 20rpx 40rpx 20rpx 40rpx;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    box-sizing: border-box;
}
.popup-title{
    padding: 20rpx;
    font-size: 34rpx;
    font-weight: bold;
    text-align: left;
    letter-spacing: 2rpx;
}
.picker {
    text-align: center;
    padding: 10rpx;
    box-sizing: border-box;
}

.bl-box {
    text-align: left;
    width: 100%;
    .bl-box-item {
        display: inline-block;
        margin: 10rpx;
        padding: 28rpx;
        border-radius: 20rpx;
        color: #333;
        box-sizing: border-box;
        background-color: #f5f5f5;

    }
    .bl-box-item-selected {
        background-color: #333;
        color: #fff;
    }
}

.input {
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    border-radius: 20rpx;
    background-color: #f5f5f5;
    font-size: 28rpx;
    text-align: center;
    letter-spacing: 2rpx;
}

.textarea{
    width: 100%;
    min-height: 300rpx;
    padding: 20rpx;
    box-sizing: border-box;
    text-align: left;
}

.gender-box {
    .gender-item {
        padding: 20rpx;
        box-sizing: border-box;
        border-radius: 30rpx;
        display: inline-block;
        width: calc(50% - 40rpx);
        color: #333;
        margin-left: 20rpx;
        background-color: #f5f5f5;
    }
    .gender-item-selected {
        color: #fff;
        background-color: #333;
    }
}

</style>

