<template>
  <view :class="clazz.container">
    <view :class="clazz.bg"></view>

    <view id="pageHomeHeader" style="width: 100%;top:0;left:0;">

      <view>
        <kt-status-bar-height :background-color="'rgba(0,0,0,0)'"></kt-status-bar-height>
        <view class="header-box" style="position: relative;" :style="{
          width: 'calc(100vw - ' + menuButtonWidth + 'px)',
          height: navHeight + 'px'
        }">

          <view class="scroll-view" :scroll-x="true" style="width: calc(100% - 100rpx);margin-left: 10rpx;">
            <view class="header-box-1">
              <view class="header-item header-item-selected">
                {{ $i18n.zhToGlobal('交友') }}
              </view>
              <view class="header-item">
                <!-- 购物 -->
                {{ $i18n.zhToGlobal('购物') }}
              </view>
              <view class="header-item">
                <!-- 生活 -->
                {{ $i18n.zhToGlobal('艺术品') }}
              </view>
              <!-- <view class="header-item">
                {{ $i18n.zhToGlobal('岗位') }}
              </view>
              <view class="header-item">
                {{ $i18n.zhToGlobal('家政') }}
              </view>
              <view class="header-item">
                {{ $i18n.zhToGlobal('同城') }}
              </view> -->
            </view>
          </view>
          <view style="width: 70rpx;height: 100%;position: absolute;right: 0;top: 50%;transform: translateY(-50%);">
            <image style="width: 50rpx;height: 50rpx;position: absolute;top:50%;transform: translateY(-50%);"
              :src="$kt.file.byPath('icon/addRound.svg')" mode="widthFix"></image>
          </view>
        </view>
      </view>
      <view>
        <view class="split"></view>

        <view class="second-box" style="position: relative;padding: 11rpx;">
          <draggable-tabs :tabs="tabList" :selected="selected" @tab-click="onTabClick"
            @tab-order-change="onTabOrderChange" />
          <image style="width: 40rpx;height: 40rpx;position: absolute;top:50%;transform: translateY(-50%);right: 100rpx;"
            class="second-icon" @click="toSearch()" :src="$kt.file.byPath('icon/search.svg')"></image>
          <image style="width: 40rpx;height: 40rpx;position: absolute;top:50%;transform: translateY(-50%);right: 50rpx;"
            class="three-icon" @click="toFilter()" :src="$kt.file.byPath('icon/filter.svg')"></image>
        </view>

      </view>
    </view>
    <view>
      <project-make-friends-recommend-filter-popup ref="filterPopup" @confirm="onFilterConfirm" />
      <swiper
        :key="swiperKey"
        :style="{
          height: isY() ? 'calc(100vh - ' + pageHomeHeaderHeight + 'px)' : 'calc(100vh - ' + pageHomeHeaderHeight + 'px - 100rpx)'
        }"
        @change="swiperChange"
        :current="currentSwiperIndex">
        <swiper-item v-for="tab in tabList" :key="tab.key" :item-id="tab.key">
          <project-make-friends-recommend-panel v-if="tab.key === 'recommend'"
            :height="isY() ? 'calc(100vh - ' + pageHomeHeaderHeight + 'px)' : 'calc(100vh - ' + pageHomeHeaderHeight + 'px - 100rpx)'"
            :filterParams="filterParams">
          </project-make-friends-recommend-panel>

          <kt-community-post-panel v-else-if="tab.key === 'friend'" @cardUserClick="cardUserClick"
            @cardClick="cardClick" @cardDotClick="cardDotClick" ref="communityPostPanel" :is-forbid-forward="true"
            :forward-default-click="false" @forwardClick="cardClick"
            :height="isY() ? 'calc(100vh - ' + pageHomeHeaderHeight + 'px)' : 'calc(100vh - ' + pageHomeHeaderHeight + 'px - 100rpx)'">
          </kt-community-post-panel>

          <kt-community-post-panel v-else-if="tab.key === 'privateFriend'" mode="mutualFollow"
            @cardUserClick="cardUserClick" @cardClick="cardClick" @cardDotClick="cardDotClick" :is-forbid-forward="true"
            :forward-default-click="false" @forwardClick="cardClick" ref="communityPostPanel"
            :height="isY() ? 'calc(100vh - ' + pageHomeHeaderHeight + 'px)' : 'calc(100vh - ' + pageHomeHeaderHeight + 'px - 100rpx)'">
          </kt-community-post-panel>
        </swiper-item>
      </swiper>


      <kt-community-post-operate-popup ref="communityPostOperatePopup">
      </kt-community-post-operate-popup>

      <!--      <community-post-panel-->
      <!--          v-show="selected === 'friend'"-->
      <!--          :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px - 100rpx)'"-->
      <!--          :style="{-->
      <!--        'margin-top': isY()?'730rpx':'50rpx'-->
      <!--      }"-->
      <!--          class="community-post-panel"-->
      <!--      ></community-post-panel>-->
    </view>

    <community-post-popup ref="communityPostPopup">
    </community-post-popup>

  </view>
</template>

<script>


import event from "@/uni_modules/kantboot/libs/event";
import ProjectMakeFriendsRecommendFilterPopup from '@/uni_modules/project-make-friends/components/project-make-friends-recommend-filter-popup.vue';
import DraggableTabs from '@/components/draggable-tabs/draggable-tabs.vue';
import tabOrderManager from '@/utils/tab-order-manager.js';

export default {
  data() {
    return {
      navHeight: 48,
      menuButtonWidth: 0,
      pageHomeHeaderHeight: 0,
      clazz: {
        container: this.$kt.style.toggleClass("container"),
        bg: this.$kt.style.toggleClass("bg"),
      },
      selected: 'recommend',
      filterParams: {},
      tabList: [],
      swiperKey: 0 // 用于强制swiper重新渲染
    }
  },
  components: {
    ProjectMakeFriendsRecommendFilterPopup,
    DraggableTabs,
  },
  computed: {
    currentSwiperIndex() {
      // 计算当前选中tab在tabList中的索引
      if (!this.tabList || this.tabList.length === 0) return 0;

      const index = this.tabList.findIndex(tab => tab.key === this.selected);
      const result = index >= 0 ? index : 0;

      console.log('计算swiper索引:', {
        selected: this.selected,
        tabList: this.tabList.map(tab => tab.key),
        index: result
      });

      return result;
    }
  },
  watch: {
    tabList: {
      handler(newTabList, oldTabList) {
        console.log('tabList发生变化:', {
          old: oldTabList ? oldTabList.map(tab => tab.key) : [],
          new: newTabList ? newTabList.map(tab => tab.key) : []
        });

        // 确保当前选中的tab仍然有效
        if (newTabList && newTabList.length > 0) {
          const currentTabExists = newTabList.some(tab => tab.key === this.selected);
          if (!currentTabExists) {
            // 如果当前选中的tab不存在了，选择第一个
            this.selected = newTabList[0].key;
            console.log('当前选中的tab不存在，切换到:', this.selected);
          }
        }

        // 强制swiper重新渲染
        this.swiperKey++;
        console.log('更新swiperKey:', this.swiperKey);
      },
      deep: true
    }
  },
  mounted() {
    // 初始化选项卡列表
    this.initTabList();
    // 加载保存的选项卡顺序
    this.loadTabOrder();

    // #ifdef MP-WEIXIN
    this.navHeight = uni.getSystemInfoSync().system.indexOf("ios") != -1 ? 44 : 48;
    // 获取胶囊信息
    this.menuButtonWidth = wx.getMenuButtonBoundingClientRect().width;
    console.log(this.menuButtonWidth, "获取胶囊信息");
    // #endif
    // 获取pageHomeHeader的高度
    this.$nextTick(() => {
      // uniapp的获取元素高度的方法
      uni.createSelectorQuery().in(this).select('#pageHomeHeader').boundingClientRect((rect) => {
        console.log(rect, "获取pageHomeHeader的高度")
        this.pageHomeHeaderHeight = rect.height;
      }).exec()
    });
    this.$kt.event.on('changeTabbar', () => {
      console.log("获取#pageHomeHeader的高度");
      this.$nextTick(() => {
        uni.createSelectorQuery()
          .in(this)
          .select("#pageHomeHeader")
          .boundingClientRect((res) => {
            console.log(res, "pageHomeHeader");
            this.pageHomeHeaderHeight = res.height;
          })
          .exec();
      });
    });


  },

  methods: {
    toSearch() {
      this.$kt.router.navTo("/pages/project-make-friends-pages/user-search/user-search", {
        code: this.initCode
      });
    },
    toFilter() {
      if (this.$refs.filterPopup && this.$refs.filterPopup.open) {
        this.$refs.filterPopup.open(this.filterParams);
      } else {
        uni.showToast({ title: '筛选弹窗未就绪', icon: 'none' });
      }
    },
    cardUserClick(user) {
      this.$kt.router.navTo("/pages/project-make-friends-pages/user-info/user-info", {
        userAccountId: user.id
      });
    },
    cardDotClick(post) {
      console.log(post, "post");
      if (!this.$kt.userAccount.getIsLogin()) {
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.$refs.communityPostOperatePopup.open(post);
    },
    cardClick(post) {
      this.$kt.router.navTo("/pages/project-make-friends-pages/post-detail/post-detail", {
        postId: post.id
      });
    },
    toPostPopup(post) {
      this.$refs.communityPostPopup.open(post);
    },
    swiperChange(e) {
      // 根据swiper的current索引来更新selected
      const currentIndex = e.detail.current;
      if (this.tabList && this.tabList[currentIndex]) {
        this.selected = this.tabList[currentIndex].key;
      }
    },

    onFilterConfirm(params) {
      this.filterParams = params;
      // 可在此处触发推荐面板刷新
    },
    onTabClick(tabKey) {
      this.selected = tabKey;
    },
    onTabOrderChange(newTabs) {
      console.log('接收到新的选项卡顺序:', newTabs.map(tab => tab.key));
      console.log('当前选中的tab:', this.selected);

      // 验证新的选项卡数据
      if (tabOrderManager.validateTabs(newTabs)) {
        // 更新本地数据
        this.tabList = newTabs;

        // 使用状态管理工具保存选项卡顺序
        const success = tabOrderManager.saveTabOrder(newTabs, 'makeFriendsHome');

        if (success) {
          console.log('选项卡顺序保存成功');
          console.log('新的swiper索引:', this.currentSwiperIndex);

          // 使用nextTick确保DOM更新后再显示提示
          this.$nextTick(() => {
            uni.showToast({
              title: '顺序已更新',
              icon: 'success',
              duration: 1000
            });
          });
        } else {
          console.error('选项卡顺序保存失败');
          uni.showToast({
            title: '保存失败',
            icon: 'error',
            duration: 1500
          });
        }
      } else {
        console.error('无效的选项卡数据:', newTabs);
        uni.showToast({
          title: '选项卡数据无效',
          icon: 'error',
          duration: 1500
        });
      }
    },
    initTabList() {
      const defaultTabs = [
        { key: 'recommend', label: this.$i18n.zhToGlobal('推荐') },
        { key: 'friend', label: this.$i18n.zhToGlobal('公开朋友圈') },
        { key: 'privateFriend', label: this.$i18n.zhToGlobal('好友朋友圈') }
      ];

      // 设置默认选项卡
      tabOrderManager.setDefaultTabs(defaultTabs);
      this.tabList = defaultTabs;
    },
    loadTabOrder() {
      // 使用状态管理工具加载选项卡顺序
      this.tabList = tabOrderManager.loadTabOrder('makeFriendsHome');
    },
    resetTabOrder() {
      // 重置选项卡顺序为默认顺序
      uni.showModal({
        title: '重置确认',
        content: '确定要重置选项卡顺序为默认顺序吗？',
        success: (res) => {
          if (res.confirm) {
            this.tabList = tabOrderManager.resetTabOrder('makeFriendsHome');
            uni.showToast({
              title: '已重置为默认顺序',
              icon: 'success',
              duration: 1500
            });
          }
        }
      });
    },
    isY() {
      // 转换为大写
      let deviceType = this.$kt.style.detectDeviceType().toUpperCase();
      return deviceType === 'PC' ||
        deviceType === 'TV';

    }
  }
}
</script>

<style lang="scss" scoped>
.bg {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  //background-color: #f9f9f9;
  //background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
  background-color: #FFFFFF;
}

.bg-mode-color-scheme-light {
  //background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}

.bg-mode-color-scheme-dark {
  background: #191919;
}

.header-box-1 {
  padding: 0 10rpx;
  box-sizing: border-box;
  // 不换行
  white-space: nowrap;

  .header-item {
    display: inline-block;
    font-size: 36rpx;
    letter-spacing: 3rpx;
    margin-right: 5rpx;
    padding: 10rpx 20rpx;
  }

  .header-item-selected {
    font-weight: bold;
  }
}

.scroll-view {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);

  // 超出滚动
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;

  // 隐藏滚动条
  &::-webkit-scrollbar {
    width: 0;
    height: 1rpx;
    display: none;
  }

  // 滚动
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

}

// 不显示滚动条
::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}

.second-box {
  width: 100%;

  .second-box-box {
    width: calc(100% - 100rpx);
    // 不换行
    white-space: nowrap;
    // 超出滚动
    overflow-x: auto;

    .second-box-item {
      display: inline-block;
      font-size: 32rpx;
      letter-spacing: 3rpx;
      margin-right: 5rpx;
      padding: 10rpx 20rpx;
      border-radius: 10rpx;
    }

    .second-box-item-selected {
      font-weight: bold;
    }
  }
}

.container-mode-device-pc {
  width: calc(100% - 240px - 400px);
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
}

.split {
  width: 100%;
  height: 3rpx;
  background-color: #eee;
}

.container-mode-color-scheme-dark {
  background-color: #191919;

  .split {
    background-color: #888888;
  }

  .header-box {
    background-color: #191919;
  }

  .scroll-view {
    background-color: #191919;
  }

  .second-icon {
    // 颜色反转
    filter: invert(1);
  }


  .header-box-1 {
    background-color: #191919;

    .header-item {
      color: #fff;
    }
  }

  .second-box {
    .second-box-box {
      .second-box-item {
        color: #fff;
      }

      .second-box-item-selected {
        color: #fff;
        font-weight: bold;
      }
    }
  }
}

.container-mode-device-pc {
  position: relative;
  width: calc(100% + 20px);
  padding: 0;
  box-sizing: border-box;

  .second-icon {
    margin-right: 10px;
  }
}
</style>
