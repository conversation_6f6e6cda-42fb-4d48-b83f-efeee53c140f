<template>
  <view class="kt-switch" @click="toggleSwitch">
    {{value}}
    <view :class="['switch-container', { 'switch-on': value }]">
      <view class="switch-handle"></view>
    </view>
  </view>
</template>

<script>
export default {
  name: "kt-switch",
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    toggleSwitch() {
      if (this.disabled){
        return;
      }
      this.value = !this.value;
      this.$emit("update:value", this.value);
    },
  },
};
</script>

<style lang="scss" scoped>
.kt-switch {
  display: inline-block;

  .switch-container {
    width: 50rpx;
    height: 30rpx;
    border-radius: 15rpx;
    background-color: #ccc;
    position: relative;
    transition: background-color 0.3s;

    &.switch-on {
      background-color: $kt-color-primary-light;
    }

    .switch-handle {
      width: 26rpx;
      height: 26rpx;
      border-radius: 50%;
      background-color: #fff;
      position: absolute;
      top: 2rpx;
      left: 2rpx;
      transition: left 0.3s;

      .switch-on & {
        left: 22rpx;
      }
    }
  }

  &.disabled {
    cursor: not-allowed;

    .switch-container {
      background-color: #e5e5e5;

      .switch-handle {
        background-color: #c7c7c7;
      }
    }
  }
}
</style>