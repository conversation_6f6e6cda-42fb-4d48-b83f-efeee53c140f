<kt-box class="form-item data-v-52a30ba1" vue-id="01bee985-1" title="{{$root.g0}}" bind:__l="__l" vue-slots="{{['default']}}"><kt-box class="in-form-item data-v-52a30ba1" vue-id="{{('01bee985-2')+','+('01bee985-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><kt-box class="form-item data-v-52a30ba1" vue-id="{{('01bee985-3')+','+('01bee985-2')}}" title="{{$root.g1}}" bind:__l="__l" vue-slots="{{['default']}}"><kt-image-select bind:input="__e" vue-id="{{('01bee985-4')+','+('01bee985-3')}}" count="{{1}}" file-group-code="fp" value="{{fileIdsOfAvatar}}" data-event-opts="{{[['^input',[['__set_model',['','fileIdsOfAvatar','$event',[]]]]]]}}" class="data-v-52a30ba1" bind:__l="__l"></kt-image-select><block wx:if="{{showParams}}"><view class="form-item-param data-v-52a30ba1">params.fileIdOfAvatar</view></block></kt-box><kt-box class="form-item data-v-52a30ba1" vue-id="{{('01bee985-5')+','+('01bee985-2')}}" title="{{$root.g2}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-item-input data-v-52a30ba1"><input class="input data-v-52a30ba1" type="text" placeholder="输入公众号名称" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['params']]]]]}}" value="{{params.name}}" bindinput="__e"/></view><block wx:if="{{showParams}}"><view class="form-item-param data-v-52a30ba1">{{params.name}}</view></block></kt-box><kt-box class="form-item data-v-52a30ba1" vue-id="{{('01bee985-6')+','+('01bee985-2')}}" title="{{$root.g3}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-item-input data-v-52a30ba1"><input class="input data-v-52a30ba1" type="text" placeholder="输入公众号描述" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['params']]]]]}}" value="{{params.description}}" bindinput="__e"/></view><block wx:if="{{showParams}}"><view class="form-item-param data-v-52a30ba1">{{params.description}}</view></block></kt-box><kt-box class="form-item data-v-52a30ba1" vue-id="{{('01bee985-7')+','+('01bee985-2')}}" title="{{$root.g4}}" bind:__l="__l" vue-slots="{{['default']}}"><kt-image-select bind:input="__e" vue-id="{{('01bee985-8')+','+('01bee985-7')}}" count="{{1}}" file-group-code="fp" value="{{fileIdsOfQrcode}}" data-event-opts="{{[['^input',[['__set_model',['','fileIdsOfQrcode','$event',[]]]]]]}}" class="data-v-52a30ba1" bind:__l="__l"></kt-image-select><block wx:if="{{showParams}}"><view class="form-item-param data-v-52a30ba1">params.fileIdOfQrcode</view></block></kt-box></kt-box></kt-box>