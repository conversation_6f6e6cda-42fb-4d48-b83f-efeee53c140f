<template>
    <view>
        <kt-popup
        @close="close"
        :zIndex="zIndex"
        ref="ktPopup">
            <view>
                <kt-login-panel
                    :wechat-login-method="wechatLoginMethod"
                    :border-radius="isPc()?'20rpx':'20rpx 20rpx 0 0'"
                    @loginSuccess="loginSuccess"
                ></kt-login-panel>
            </view>
        </kt-popup>
    </view>
</template>

<script>
import KtLoginPanel from "../kt-login-panel/kt-login-panel.vue";
import ktPopup from '../kt-popup/kt-popup.vue';
export default {
    name: "kt-login-popup",
    components: {
      ktPopup,
      KtLoginPanel,
    },
    props:{
      zIndex:{
        type: Number,
        default: 999999999
      },
      wechatLoginMethod:{
        type: String,
        default: "loginByPhone"
      }
    },
    data(){
        return {
        }
    },
    mounted(){
        this.$kt.event.on("login:success",()=>{
            this.close();
        });
    },
    methods:{
      isPc(){
          return this.$kt.style.detectDeviceType() === "pc";
      },
        loginSuccess(){
            this.close();
        },
        open(){
            this.$refs.ktPopup.open();
        },
        close(){
            this.$refs.ktPopup.close();
            this.$emit('close');
        },

    },
}
</script>

<style lang="scss" scoped>

</style>