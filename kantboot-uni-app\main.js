import App from './App'
import Vue from 'vue'

import kt from '@/uni_modules/kantboot/index.js';
import uView from '@/uni_modules/uview-ui';
import projectConfig from '@/uni_modules/project-config/index.js';

Vue.use(uView);
// 如此配置即可
uni.$u.config.unit = 'rpx'
Vue.prototype.$kt = kt;
Vue.prototype.$i18n = kt.i18n;
Vue.prototype.$request = kt.request;

Vue.config.productionTip = false;

projectConfig.projectCode = "makeFriends";

if(projectConfig.projectCode==="makeFriends"){
  projectConfig.initMakeFriendsConfig();
}

if(projectConfig.projectCode==="meet"){
  projectConfig.initMeetConfig();
}



App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount();
