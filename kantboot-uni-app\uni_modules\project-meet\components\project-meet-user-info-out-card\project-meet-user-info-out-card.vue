<template>
  <view :class="clazz.container">
    <view
      :style="{
        width: width,
        height: height,
      }"
      @click="userClick(userInfo)"
      class="user-info-card"
      :class="{
        'user-info-card-vip': isVip,
        'user-info-card-svip': isSvip,
      }"
    >
      <view class="user-tag-vip"
      v-if="isVip"
      >{{"VIP"}}</view>
      <view
          v-if="isSvip"
          class="user-tag-svip">
        {{"SVIP"}}
      </view>
      <image
        mode="aspectFill"
        class="user-info-card-avatar"
        v-if="userInfo.fileIdOfAvatar"
        :src="$kt.file.visit(userInfo.fileIdOfAvatar)"
      ></image>
      <image
          v-else
          mode="aspectFill"
          class="user-info-card-avatar"
          :src="$kt.file.byPath('image/logo.png')"
      ></image>


      <view class="dto"
      v-if="isOnline(userInfo)&&self&&self.isSubAccount"
      ></view>

      <view class="user-info-card-info">
        <view class="user-info-card-info-ng">
          <view class="user-info-card-info-clicks">
            <view
                @click.stop="toPrivateChat"
                class="user-info-card-info-clicks-item">
              <image :src="$kt.file.byPath('projectMeet/icon/message.svg')">
              </image>
            </view>
            <view
                @click.stop="toPrivateChatLove"
                class="user-info-card-info-clicks-item">
              <image :src="$kt.file.byPath('projectMeet/icon/like.svg')">
              </image>
            </view>
          </view>

          <view class="user-info-card-na-nickname">
            {{
              userInfo.nickname ? userInfo.nickname : $i18n.zhToGlobal("无昵称")
            }}
          </view>

          <view>
            <view class="user-info-card-na-info">
              {{
                $kt.date.getAge(userInfo.gmtBirthday) ||
                $i18n.zhToGlobal("未设置")
              }}{{ "·" }}{{ $i18n.zhToGlobal(userInfo.countryCn) }}
            </view>
          </view>

          <view
            v-if="false"
            class="user-info-card-na-gender"
            :class="{
              'user-info-card-na-gender-male': userInfo.genderCode === 'male',
              'user-info-card-na-gender-female':
                userInfo.genderCode === 'female',
            }"
          >
            {{ "♂" }}
          </view>
        </view>
        <view></view>
        <view style="height: 10rpx"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    width: {
      type: String,
      default: "300rpx",
    },
    height: {
      type: String,
      default: "500rpx",
    },

    userInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      },
      userAccountId: this.userInfo.id || "",
      isSvip: false,
      isVip: false,
    };
  },
  watch: {
    userInfo: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.userAccountId = newVal.id;
          this.checkVip();
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.self = this.$kt.userAccount.getSelf();
  },
  methods: {
    isOnline(userInfo){
      try {
        return userInfo.onlineShow.isOnlineOfShow;

      } catch (e) {
        return false
      }
    },
    checkVip() {

      let currentTime = new Date().getTime();

      this.isSvip = this.userInfo.gmtSvipExpire && this.userInfo.gmtSvipExpire > currentTime;
      this.isVip = this.userInfo.gmtVipExpire && this.userInfo.gmtVipExpire > currentTime;
    },
    async toPrivateChat(){
      this.userAccountId = this.userInfo.id || "";
      if (!this.userAccountId) {
        uni.showToast({
          title: this.$i18n.zhToGlobal("用户加载未完成"),
          icon: "none",
        });
        return;
      }
      if (this.$kt.style.detectDeviceType() === 'pc') {
        // this.$kt.router.navTo("/pages/project-meet-pages/body/body", {
        //   tabbarSelected:"messageMeet"
        // });
        uni.showLoading({
          // title: this.$i18n.zhToGlobal("正在加载"),
          title:""
        });

        let dialogId = 0;

        await this.$request.post("/functional-chat-web/dialog/getOneToOneByUserAccountId", {
          data: {userAccountId: this.userAccountId}
        }).then(res => {
          dialogId = res.data.id;
        }).catch(err => {
          this.$kt.toast.error(err);
        });


        // /functional-chat-web/dialogMessage/sendMessageBySelf
        // await this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf", {
        //   data: {
        //     items: [{
        //       content: "Let's see what sparks we can create!",
        //       type: "text",
        //     }],
        //     dialogId,
        //   }
        // }).catch(err => {
        //   this.$kt.toast.error(err);
        // });

        this.$kt.event.emit("projectMeetChatDialogMessageSend", dialogId
        );


        uni.hideLoading();

        return;
      }
      this.$kt.router.navTo("/pages/project-meet-pages/chat-dialog/chat-dialog", {
        userAccountId: this.userAccountId,
      });
    },
    async toPrivateChatLove() {
      this.userAccountId = this.userInfo.id || "";
      if (!this.userAccountId) {
        uni.showToast({
          title: this.$i18n.zhToGlobal("用户加载未完成"),
          icon: "none",
        });
        return;
      }
      if (this.$kt.style.detectDeviceType() === 'pc') {
        // this.$kt.router.navTo("/pages/project-meet-pages/body/body", {
        //   tabbarSelected:"messageMeet"
        // });
        uni.showLoading({
          title: "",
        });

        let dialogId = 0;

        await this.$request.post("/functional-chat-web/dialog/getOneToOneByUserAccountId", {
          data: {userAccountId: this.userAccountId}
        }).then(res => {
          dialogId = res.data.id;
        }).catch(err => {
          this.$kt.toast.error(err);
        });


        // /functional-chat-web/dialogMessage/sendMessageBySelf
        await this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf", {
          data: {
            items: [{
              content: "I have a crush on you.",
              type: "text",
            }],
            dialogId,
          }
        }).catch(err => {
          // this.$kt.toast.error(err);
          uni.showToast({
            title: err.errMsg,
            icon: "none",
          });
        });



        this.$kt.event.emit("projectMeetChatDialogMessageSend", dialogId
        );


        uni.hideLoading();

        return;
      }
      this.$kt.router.navTo("/pages/project-meet-pages/chat-dialog/chat-dialog", {
        userAccountId: this.userAccountId,
      });
    },
    isPc() {
      return this.$kt.style.detectDeviceType() === "pc";
    },
    userClick(userInfo) {
      this.$emit("userClick", userInfo);
    },
    getCharacteristics(userInfo) {
      // 获取前3个特征
      let characteristics = userInfo.characteristics || [];
      let characteristicsList = [];
      for (let i = 0; i < characteristics.length; i++) {
        if (i > 2) {
          break;
        }
        characteristicsList.push(characteristics[i]);
      }
      return characteristicsList;
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  display: inline-block;
}

.user-info-card {
  position: relative;
  display: inline-block;
  border-radius: 20rpx;
  // 超出不显示
  overflow: hidden;
  //background-color: #FFFFFF;
  // 渐变
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 1) 20%
  );

  width: 320rpx;
  height: 600rpx;
  //box-shadow: 0 10rpx 15rpx rgba(118, 118, 118, 0.1);

  .dto {
    position: absolute;
    top: 20rpx;
    left: 20rpx;
    background-color: #08d68b;
    width: 20rpx;
    height: 20rpx;
    border-radius: 55%;
    border: 3rpx solid #ffffff;
  }

  .user-info-card-avatar {
    width: 100%;
    height: calc(100% - 200rpx);
    border-radius: 20rpx 20rpx 0 0;
  }

  .user-info-card-info {
    position: relative;
    width: 100%;
    bottom: 0;
    left: 0;
    //background-color: rgba(0,0,0,0.5);
    z-index: 2;
    padding: 15rpx;
    // 渐变从上到下
    //background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
    .user-info-card-info-clicks {
      .user-info-card-info-clicks-item {
        position: relative;
        width: 60rpx;
        height: 60rpx;
        margin: 10rpx;
        display: inline-block;
        text-align: center;
        border-radius: 50%;
        background-color: #ffffff;
        box-shadow: 0 0 7rpx rgba(0, 0, 0, 0.2);
        image {
          position: absolute;
          width: 25rpx;
          height: 25rpx;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }

    .user-info-card-info-ng {
      text-align: left;
      color: #333333;
      font-size: 23rpx;
      //text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);
      .user-info-card-na-nickname {
        font-weight: bold;
        font-size: 30rpx;
        display: inline-block;
        max-width: calc(100% - 50rpx);
        // 超出显示省略号
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .user-info-card-na-info {
        display: inline-block;
        max-width: calc(100% - 50rpx);
        // 超出显示省略号
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .user-info-card-na-gender {
        vertical-align: top;
        display: inline-block;
        font-size: 20rpx;
        width: 35rpx;
        border-radius: 20rpx;
        text-align: center;
        margin-left: 10rpx;
        background-color: #3c9cff;
      }
      .user-info-card-na-gender-male {
        background-color: #3c9cff;
      }
      .user-info-card-na-gender-female {
        background-color: #ff5b5b;
      }
    }
  }
}

.container-mode-device-pc {
  box-shadow: 0 0 20px #daeaff;
  border-radius: 20rpx;
  .user-info-card {
    //border: 4rpx solid rgba(0,0,0,.1);
    box-sizing: border-box;
    box-shadow: none;
    cursor: pointer;
    .user-info-card-avatar {
      border-radius: 20rpx;
    }
  }
  .user-info-card:active {
    transform: scale(0.95);
  }
}

.user-info-card-vip {
  position: relative;
  // 紫色
  border: 5rpx solid #5a7ef6;
  box-sizing: border-box;
  .user-tag-vip{
    position: absolute;
    background-color: #5a7ef6;
    color: #ffffff;
    z-index: 1;
    right: 0rpx;
    top: 0rpx;
    border-radius: 0 0 0 20rpx;
    padding: 0rpx 10rpx 0rpx 20rpx;
  }
}

.user-info-card-svip {
  position: relative;
  box-sizing: border-box;
  // 金黄
  border: 5rpx solid #f2c931;

  .user-tag-svip{
    position: absolute;
    color: #ffffff;
    background-color: #f2c931;
    z-index: 1;
    right: 0rpx;
    top: 0rpx;
    border-radius: 0 0 0 20rpx;
    padding: 0rpx 10rpx 0rpx 20rpx;
  }
}
</style>