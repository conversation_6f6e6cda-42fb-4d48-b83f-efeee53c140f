@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.chat-scroll-view.data-v-485ae7a2 {
  position: relative;
}
.chat-list-item.data-v-485ae7a2 {
  padding: 20rpx;
  box-sizing: border-box;
}
.chat-list-item-avatar.data-v-485ae7a2 {
  vertical-align: top;
  position: relative;
}
.chat-list-item-avatar .chat-list-item-avatar-image.data-v-485ae7a2 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}
.chat-list-item-content.data-v-485ae7a2 {
  display: inline-block;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  max-width: 700rpx;
}
.chat-list-item-content-img.data-v-485ae7a2 {
  width: 300rpx;
  border-radius: 20rpx;
}
.chat-list-item-content-video.data-v-485ae7a2 {
  width: 450rpx;
  height: 300rpx;
  border-radius: 20rpx;
}
.time-view.data-v-485ae7a2 {
  position: relative;
  font-size: 24rpx;
  color: #999999;
}
.time-view .copy-icon.data-v-485ae7a2 {
  position: absolute;
  width: 40rpx;
  top: -10rpx;
  right: 0;
}
.time-view .copy-icon.data-v-485ae7a2:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.chat-list-item-self.data-v-485ae7a2 {
  text-align: right;
}
.chat-list-item-content-text.data-v-485ae7a2 {
  letter-spacing: 3rpx;
  font-size: 30rpx;
}
.think-box.data-v-485ae7a2 {
  color: #000000;
  font-size: 22rpx;
  top: 45rpx;
}
.loading-icon-2.data-v-485ae7a2 {
  width: 150rpx;
  height: 150rpx;
}
.chat-list-item-avatar-name.data-v-485ae7a2 {
  position: absolute;
  left: 80rpx;
  top: calc(50% - 10rpx);
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: calc(100% - 60rpx);
  font-size: 28rpx;
  color: #666666;
  letter-spacing: 3rpx;
}
.loading-icon-3.data-v-485ae7a2 {
  width: 50rpx;
  height: 50rpx;
  -webkit-filter: invert(1);
          filter: invert(1);
  -webkit-animation: loading-icon-ani-in-page-chat-data-v-485ae7a2 3s linear infinite;
          animation: loading-icon-ani-in-page-chat-data-v-485ae7a2 3s linear infinite;
}
@-webkit-keyframes loading-icon-ani-in-page-chat-data-v-485ae7a2 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
50% {
    -webkit-transform: rotate(720deg);
            transform: rotate(720deg);
}
100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
}
@keyframes loading-icon-ani-in-page-chat-data-v-485ae7a2 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
50% {
    -webkit-transform: rotate(720deg);
            transform: rotate(720deg);
}
100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
}
.chat-list-up-loading-box.data-v-485ae7a2 {
  text-align: center;
  padding: 20rpx;
  box-sizing: border-box;
}
.chat-list-up-loading-box .chat-list-up-loading-box-text.data-v-485ae7a2 {
  font-size: 28rpx;
  color: #999999;
}
.top_header.data-v-485ae7a2 {
  position: relative;
  background-color: #FFFFFF;
  z-index: 9999;
}
