<template>
  <view>
    <kt-popup :overlayClose="false" ref="ktPopup" @close="close" :zIndex="999999999">
      <view class="box">
        <view class="close-btn" @click="closePop">×</view>
        <view class="title">{{ $i18n.zhToGlobal("一键均分概率") }}</view>

        <!-- 成员信息展示 -->
        <view class="member-info-section">
          <view class="section-title">{{ $i18n.zhToGlobal("成员信息") }}</view>
          <view class="member-count">{{ $i18n.zhToGlobal("总人数") }}: {{ memberList.length }}{{ $i18n.zhToGlobal("人") }}</view>
          <view class="equal-probability">{{ $i18n.zhToGlobal("均分概率") }}: {{ equalProbability }}%</view>
          <view class="remainder-info" v-if="remainder > 0">
            {{ $i18n.zhToGlobal("余数") }}: {{ remainder }}%{{ $i18n.zhToGlobal("（将分配给创建人）") }}
          </view>
        </view>

        <!-- 概率分配预览 -->
        <view class="preview-section">
          <view class="section-title">{{ $i18n.zhToGlobal("分配预览") }}</view>
          <scroll-view class="member-list" scroll-y="true" style="max-height: 400rpx;">
            <view class="member-item" v-for="member in memberList" :key="member.id">
              <view class="member-left">
                <view class="member-avatar">
                  <kt-avatar
                      v-if="member.userInfo && member.userInfo.fileIdOfAvatar"
                      size="60rpx"
                      :src="$kt.file.visit(member.userInfo.fileIdOfAvatar)">
                  </kt-avatar>
                  <kt-avatar
                      v-else
                      size="60rpx"
                      :src="$kt.file.byPath('/image/logo.png')"
                  ></kt-avatar>
                </view>
                <view class="member-info">
                  <view class="member-name">{{ member.userInfo ? (member.userInfo.nickname || `用户${member.userAccountId}`) : `用户${member.userAccountId}` }}</view>
                  <view class="member-id">ID {{ member.userAccountId }}</view>
                  <view class="creator-tag" v-if="isCreator(member)">{{ $i18n.zhToGlobal('创建人') }}</view>
                </view>
              </view>
              <view class="member-right">
                <view class="probability-change">
                  <view class="old-probability">{{ $i18n.zhToGlobal("原") }}: {{ (member.probability * 100).toFixed(0) }}%</view>
                  <view class="arrow">→</view>
                  <view class="new-probability">{{ $i18n.zhToGlobal("新") }}: {{ getNewProbability(member) }}%</view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 底部操作按钮 -->
        <view class="footer">
          <view class="buttons">
            <button class="cancel-btn" @click="closePop" :disabled="processing">
              {{ $i18n.zhToGlobal('取消') }}
            </button>
            <button class="confirm-btn" @click="handleConfirm" :disabled="processing">
              {{ processing ? $i18n.zhToGlobal('处理中...') : $i18n.zhToGlobal('确认均分') }}
            </button>
          </view>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
import KtAvatar from "../../../kantboot/components/kt-avatar/kt-avatar.vue";

export default {
  components: {
    KtAvatar
  },
  data() {
    return {
      memberList: [],
      creatorUserId: null,
      processing: false,
      equalProbability: 0,
      remainder: 0
    };
  },
  methods: {
    open(data) {
      this.memberList = data.memberList || [];
      this.creatorUserId = data.creatorUserId;
      this.processing = false;
      this.calculateEqualProbability();
      this.$refs.ktPopup.open();
    },

    close() {
      this.memberList = [];
      this.creatorUserId = null;
      this.processing = false;
      this.equalProbability = 0;
      this.remainder = 0;
    },

    closePop() {
      this.$refs.ktPopup.close();
    },

    // 计算均分概率
    calculateEqualProbability() {
      if (this.memberList.length === 0) {
        this.equalProbability = 0;
        this.remainder = 0;
        return;
      }

      // 计算每人应得的整数百分比
      this.equalProbability = Math.floor(100 / this.memberList.length);
      // 计算余数
      this.remainder = 100 - (this.equalProbability * this.memberList.length);
    },

    // 判断是否为创建人
    isCreator(member) {
      return member.userAccountId === this.creatorUserId;
    },

    // 获取新概率
    getNewProbability(member) {
      let newProbability = this.equalProbability;
      // 如果是创建人，加上余数
      if (this.isCreator(member)) {
        newProbability += this.remainder;
      }
      return newProbability;
    },

    handleConfirm() {
      if (this.processing) return;

      this.processing = true;

      // 构建成员概率数据
      const memberProbabilities = this.memberList.map(member => ({
        userAccountId: member.userAccountId,
        probability: this.getNewProbability(member) / 100 // 转换为0-1之间的小数
      }));

      // 触发确认事件
      this.$emit('confirm', {
        memberProbabilities: memberProbabilities
      });

      // 关闭弹窗
      setTimeout(() => {
        this.processing = false;
        this.closePop();
      }, 100);
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 70vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
    padding-top: 20rpx;
  }

  .member-info-section {
    margin-bottom: 30rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 15rpx;
      color: #333;
    }

    .member-count, .equal-probability, .remainder-info {
      padding: 20rpx;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      font-size: 26rpx;
      color: #666;
      margin-bottom: 8rpx;
    }

    .equal-probability {
      color: #000000;
      font-weight: bold;
    }

    .remainder-info {
      color: #ff9500;
      font-weight: bold;
    }
  }

  .preview-section {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 15rpx;
      color: #333;
    }

    .member-list {
      .member-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx;
        margin-bottom: 15rpx;
        background-color: #f8f8f8;
        border-radius: 12rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .member-left {
          display: flex;
          align-items: center;
          flex: 1;

          .member-avatar {
            margin-right: 20rpx;
          }

          .member-info {
            flex: 1;

            .member-name {
              font-size: 30rpx;
              margin-bottom: 6rpx;
              font-weight: bold;
            }

            .member-id {
              font-size: 26rpx;
              color: #666;
              margin-bottom: 4rpx;
            }

            .creator-tag {
              display: inline-block;
              padding: 4rpx 12rpx;
              background-color: #d4edda;
              color: #155724;
              border-radius: 12rpx;
              font-size: 22rpx;
              font-weight: bold;
            }
          }
        }

        .member-right {
          .probability-change {
            display: flex;
            align-items: center;
            font-size: 24rpx;

            .old-probability {
              color: #999;
            }

            .arrow {
              margin: 0 10rpx;
              color: #666;
              font-size: 28rpx;
            }

            .new-probability {
              color: #000000;
              font-weight: bold;
            }
          }
        }
      }
    }
  }

  .footer {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;

    .buttons {
      display: flex;
      gap: 20rpx;

      button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 30rpx;
        border-radius: 40rpx;
        border: none;

        &.cancel-btn {
          background-color: #f5f5f5;
          color: #666;

          &[disabled] {
            background-color: #e0e0e0;
            color: #999;
          }
        }

        &.confirm-btn {
          background-color: #000000;
          color: #fff;

          &[disabled] {
            background-color: #cccccc;
            color: #fff;
          }
        }
      }
    }
  }
}

.close-btn {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
  z-index: 10;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
</style>
