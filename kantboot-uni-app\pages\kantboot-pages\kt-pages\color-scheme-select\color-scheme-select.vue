<template>
  <view class="content">
    <kt-nav-bar
        :is-has-i18n="false"
        :title="$i18n.zhToGlobal('颜色模式切换')"
    ></kt-nav-bar>
    <view :class="clazz.back"></view>
    <view>
      <view
          v-for="item in bodyData"
          @click="select(item)"
          :class="clazz.box"
      >
        <view
            class="box-name">
          {{ item.name }}
        </view>

        <image
            v-if="codeSelected+'' === item.code+''"
            class="box-select-icon"
            :src="$kt.file.byPath('icon/yesLeftTop.svg')"></image>
      </view>
    </view>
    <view style="height: 100rpx"></view>

  </view>
</template>

<script>
export default {
  data() {
    return {
      mode:{},
      clazz:{
        back: this.$kt.style.toggleClass("back"),
        box: this.$kt.style.toggleClass("box"),
      },
      bodyData: [{
        "code": "auto",
        "name": this.$i18n.zhToGlobal("跟随系统")
      },
      {
        "code": "light",
        // "name": "光亮模式"
        "name": this.$i18n.zhToGlobal("光亮模式")
      },
      {
        "code": "dark",
        // "name": "暗黑模式"
        "name": this.$i18n.zhToGlobal("暗黑模式")
      }],
      codeSelected: "",
    };
  },
  created() {
    this.mode = this.$kt.style.getMode();
    this.codeSelected = this.mode.colorScheme;
    // 切换标题
    uni.setNavigationBarTitle({
      title: this.$i18n.zhToGlobal("颜色模式切换")
    });
  },
  methods: {
    select(item) {
      this.codeSelected = item.code;
      this.mode.colorScheme = item.code;
      this.$kt.style.setMode(this.mode);
      setTimeout(()=>{
        this.$kt.router.toInto("reLaunch")
      },500);
    },
  }
}
</script>

<style lang="scss" scoped>

.back{
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: -1;
  top: 0;
  left: 0;
  background-color: #FFFFFF;


}

.back-mode-color-scheme-light {
  background-color: #FFFFFF;
}

.back-mode-color-scheme-dark {
  background-color: #191919;
}

.content {
  padding: 30rpx;
  box-sizing: border-box;
}

.box {
  position: relative;
  padding: 30rpx;
  font-size: 30rpx;
  border: 1rpx solid #f0f0f0;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  .box-select-icon{
    position: absolute;
    right: 0;
    top: 0;
    width: 60rpx;
    height: 60rpx;
    border-radius: 0 10rpx 0 0;
  }
  .box-name{
    color: #000000;
    font-weight: bold;
  }

  .box-code{
    color: #666666;
  }

  .box-i18n-name{
    position: absolute;
    bottom:20rpx;
    right:20rpx;
    font-size: 28rpx;
    color: #666666;
    display: inline-block;float: right;
  }
}

.box-mode-color-scheme-light {
  border: 1rpx solid #f0f0f0;
  .box-name{
    color: #000000;
  }
  .box-code{
    color: #666666;
  }
  .box-i18n-name{
    color: #666666;
  }
}

.box-mode-color-scheme-dark {
  border: none;
  background-color: #292929;
  .box-name{
    color: #F8F8F8;
  }
  .box-code{
    color: #F8F8F8;
  }
  .box-i18n-name{
    color: #F8F8F8;
  }
  .box-select-icon{
    // 颜色反转
    filter: invert(100%);
  }
}

.box:active {
  opacity: .8;
}

.btn{
  position: fixed;
  z-index:100;
  bottom: 0;
  width: 100%;
  padding: 30rpx;
  left: 0;
  text-align: center;
  color: #f0f0f0;
  letter-spacing: 5rpx;
  box-sizing: border-box;
  background-color: #15161c;
  //box-shadow: 0 -5rpx 30rpx rgba(0,0,0,1);
}

.btn:active{
  background-color: #15161c;
  color: #F8F8F8;
}

</style>
