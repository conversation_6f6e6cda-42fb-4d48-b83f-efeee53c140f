<view class="bar data-v-c061dc58"><block wx:if="{{hasVoice}}"><view class="bar-item data-v-c061dc58"><image class="panel-icon data-v-c061dc58" src="{{$root.g0}}" data-event-opts="{{[['tap',[['voice',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{hasImage}}"><view class="bar-item data-v-c061dc58"><image class="panel-icon data-v-c061dc58" src="{{$root.g1}}" data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{hasVideo}}"><view class="bar-item data-v-c061dc58"><image class="panel-icon data-v-c061dc58" src="{{$root.g2}}" data-event-opts="{{[['tap',[['chooseVideo']]]]}}" bindtap="__e"></image></view></block><block wx:if="{{hasUserAccount}}"><view class="bar-item data-v-c061dc58"><image class="panel-icon data-v-c061dc58" src="{{$root.g3}}" data-event-opts="{{[['tap',[['openUserAccountSelect']]]]}}" bindtap="__e"></image></view></block><block wx:if="{{hasGift}}"><view class="bar-item data-v-c061dc58"><image class="panel-icon data-v-c061dc58" src="{{$root.g4}}" data-event-opts="{{[['tap',[['chooseGift']]]]}}" bindtap="__e"></image></view></block><block wx:for="{{operateExtraArray}}" wx:for-item="item" wx:for-index="index"><view class="bar-item data-v-c061dc58"><image class="panel-icon data-v-c061dc58" src="{{item.iconSrc}}" data-event-opts="{{[['tap',[['$emit',['$0'],[[['operateExtraArray','',index,'emit']]]]]]]}}" bindtap="__e"></image></view></block><slot name="operateExtra"></slot></view>