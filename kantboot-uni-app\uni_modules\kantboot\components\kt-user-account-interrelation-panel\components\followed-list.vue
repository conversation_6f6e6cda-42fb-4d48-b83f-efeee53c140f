<template>
  <view>
    <u-loading-icon
        v-if="loading"
    mode="circle"
    :size="50"
    ></u-loading-icon>
    <view
    v-if="!loading&&(!list||list.length===0)"
    class="loading-box"
    >
      <image
          class="loading-image"
      :src="$kt.file.byPath('icon/interrelation.svg')"
      ></image>
      <view class="loading-text">{{$i18n.zhToGlobal("空空如也")}}</view>
    </view>
    <view v-for="item in list"
          @click="select(item.userAccountOfFollower)"
          class="box"
        :class="{
            'box-selected': isSelected(item.userAccountOfFollower)
        }"
    >
      <image
          v-if="isSelected(item.userAccountOfFollower)"
          :src="$kt.file.byPath('icon/yesLeftTop.svg')"
          class="box-selected-icon"></image>
      <kt-user-info-card
          v-if="!customUserInfoCard"
          :user-info="item.userAccountOfFollower"
      ></kt-user-info-card>
      <slot v-else name="userInfoCard" :user-account="item.userAccountOfFollower"></slot>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    userAccountId: {
      type: String || Number,
      default: ''
    },
    keyword: {
      type: String,
      default: ''
    },
    idSelectedList:{
      type: Array,
      default: () => []
    },
    /**
     * 是否自定义用户信息卡片
     */
    customUserInfoCard: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list:[],
      loading:true
    };
  },
  watch: {
    userAccountId: {
      handler(val) {
        this.getInit();
      },
      immediate: true,
      deep: true
    },
    keyword: {
      handler(val) {
        this.getInit();
      },
      immediate: true,
      deep: true
    },
    idSelectedList: {
      handler(val) {
        try{
          this.$forceUpdate();
        }catch (e) {

        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getInit();
  },
  mounted() {

  },
  methods: {
    select(item){
      this.$emit('select',item);
    },
    isSelected(userAccount){
      for(let i=0;i<this.idSelectedList.length;i++){
        if(this.idSelectedList[i]+""===userAccount.id+""){
          return true;
        }
      }
      return false;
    },
    search(){},
    getInit(){
      this.loading = true;
      this.list = [];
      this.$request.post('/user-interrelation-web/interrelation/getFollowedList',
          {data:{
              userAccountId:this.userAccountId,
              keyword:this.keyword}}
      ).then(res => {
        this.list = res.data;
        this.loading = false;
      }).catch(err => {
        uni.showToast({
          title: err.errMsg,
          icon: "none"
        });
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.box{
  padding-bottom: 20rpx;
  margin-bottom: 30rpx;
  cursor: pointer;
}
.box:active{
  transform: scale(0.97);
}
.loading-box{
  text-align: center;
}
.loading-image{
  margin-top: 30rpx;
  height: 100rpx;
  width: 100rpx;
  opacity: .7;
}
.loading-text{
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999999;
  letter-spacing: 2rpx;
}

.box-selected{
  position: relative;
  border: 5rpx solid #333333;
  padding: 30rpx 10rpx 30rpx 10rpx;
  border-radius: 20rpx;
  .box-selected-icon{
    position: absolute;
    top: 0;
    right: -2rpx;
    width: 60rpx;
    height: 60rpx;
    border-radius: 0 16rpx 0 0;
  }

}
</style>
