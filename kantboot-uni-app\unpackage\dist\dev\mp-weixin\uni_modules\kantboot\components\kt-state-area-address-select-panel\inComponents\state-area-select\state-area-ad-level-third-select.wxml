<view class="data-v-fc2949a4"><view class="{{['data-v-fc2949a4',clazz.box]}}"><view class="header data-v-fc2949a4"><view class="search-input-box data-v-fc2949a4"><input class="search-input data-v-fc2949a4" placeholder="{{$root.g0}}" data-event-opts="{{[['input',[['__set_model',['','keywords','$event',[]]],['input',['$event']]]]]}}" value="{{keywords}}" bindinput="__e"/></view><view style="height:20rpx;" class="data-v-fc2949a4"></view></view><view class="area-select-box data-v-fc2949a4" style="{{'height:'+(height)+';'}}"><kt-menu-box vue-id="88fee8d0-1" class="data-v-fc2949a4" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{listOfFilter}}" wx:for-item="item" wx:for-index="__i0__"><kt-menu vue-id="{{('88fee8d0-2-'+__i0__)+','+('88fee8d0-1')}}" title="{{item.name}}" is-right="{{false}}" data-event-opts="{{[['^click',[['select',['$0'],[[['listOfFilter','',__i0__]]]]]]]}}" bind:click="__e" class="data-v-fc2949a4" bind:__l="__l"></kt-menu></block></kt-menu-box></view></view></view>