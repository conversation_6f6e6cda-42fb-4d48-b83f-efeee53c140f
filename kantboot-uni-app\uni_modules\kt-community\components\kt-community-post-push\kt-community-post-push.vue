<template>
  <view>
    <view class="box">
      <view>
      <textarea
          v-model="requestParam.text"
          :placeholder="$i18n.zhToGlobal('输入内容')"
          class="box-textarea"
      ></textarea>
      </view>

      <view style="height: 30rpx"></view>

      <kt-image-select
          @input="requestParam.fileIdsOfImages = $event"
      file-group-code="fp"
      ></kt-image-select>

      <view style="height: 30rpx"></view>



      <view class="box-bottom">

        <kt-button
            @click="push"
            ref="pushButton"
            :border-radius="'20rpx'"
            :is-open-box-shadow="false"
        >
          {{$i18n.zhToGlobal('发布')}}
        </kt-button>

        <kt-community-post-permission-setting-box
            v-if="isShowPermissionSetting"
            v-model="requestParam.permission"
            @openPartVisible="openPartVisible"
            @openNotVisible="openNotVisible"
            ref="fpCommunityPostPermissionSettingBox">
          <template v-slot:userInfoCardOfPartVisible="{userAccountId}">
            <slot name="user-account-of-part-visible"
                  :user-account-id="userAccountId"></slot>
          </template>
          <template v-slot:userInfoCardOfNotVisible="{userAccountId}">
            <slot name="user-account-of-not-visible"
                  :user-account-id="userAccountId"></slot>
          </template>
        </kt-community-post-permission-setting-box>

      </view>
    </view>

  </view>
</template>

<script>
import enums from "../../libs/enums";
import api from "../../libs/api";

export default {
  props:{
    // 是否显示权限控制
    isShowPermissionSetting:{
      type:Boolean,
      default:true
    },
  },
  data() {
    return {
      api,
      enums,
      isMoreSetting: false,
      requestParam: {
        text: "",
        fileIdsOfImages: [],
        permission:{
          // 可见范围
          visibleScope: "all",
          // 可见类型
          visibleType: "public",
          // 禁止评论
          isForbidComment: false,
          // 禁止转发
          isForbidForward: false,
          // 禁止点赞
          isForbidLike: false,
          // 禁止收藏
          isForbidCollect: false,
          // 可见范围
          userAccountIdsOfPartVisible: [],
          userAccountIdsOfNotVisible: [],
        }
      },
      uploadFileLoading: false,
      dragIndex: null, // 用于记录拖动的索引
    };
  },
  methods: {
    /**
     * 打开部分可见的用户选择
     * @param event
     */
    openPartVisible(event){
      this.$emit("openPartVisible",event)
    },
    /**
     * 打开不可见的用户选择
     * @param event
     */
    openNotVisible(event){
      this.$emit("openNotVisible",event)
    },
    /**
     * 选择部分可见的用户
     * @param ids
     */
    selectPartIds(ids){
      this.$refs.fpCommunityPostPermissionSettingBox.selectPartIds(ids);
    },
    /**
     * 选择不可见的用户
     * @param ids 用户账号ID
     */
    selectNotIdSelect(ids){
      this.$refs.fpCommunityPostPermissionSettingBox.selectNotIdSelect(ids);
    },

    /**
     * 检测内容是否为空
     */
    checkContent() {
      if (this.requestParam.text === "" && this.requestParam.fileIdsOfImages.length === 0) {
        this.$refs.pushButton.error(this.$i18n.zhToGlobal("内容不能为空"));
        return false;
      }
      return true;
    },
    push(){
      if(!this.checkContent()){
        return;
      }
      this.$refs.pushButton.loading(null,9999999);
      console.log(this.requestParam.fileIdsOfImages,"this.requestParam.fileIdsOfImages");
      let requestParams = {
        permission:{
          visibleScope: this.requestParam.permission.visibleScope,
          visibleType: this.requestParam.permission.visibleType,
          isForbidComment: this.requestParam.permission.isForbidComment,
          isForbidForward: this.requestParam.permission.isForbidForward,
          isForbidLike: this.requestParam.permission.isForbidLike,
          isForbidCollect: this.requestParam.permission.isForbidCollect,
          userAccountIdsOfPartVisible: this.requestParam.permission.userAccountIdsOfPartVisible,
          userAccountIdsOfNotVisible: this.requestParam.permission.userAccountIdsOfNotVisible,
        },
        items:[
          {
            type:"text",
            content:this.requestParam.text,
          },
          {
            type:"images:id",
            content:JSON.stringify(this.requestParam.fileIdsOfImages),
          }
        ]
      };

      this.api.pushPost(requestParams).then(async (res) => {
        this.$kt.event.emit("fpCommunityPost:push", res.data);
        this.$refs.pushButton.success(res.msg);
        this.$emit("pushSuccess", res.data);
        // this.$kt.router.navBack();
      }).catch(err => {
        this.$refs.pushButton.error(err.errMsg);
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.box{
  padding: 20rpx;
  box-sizing: border-box;
  .box-textarea{
    width: 100%;
    height: calc(100vh - 700rpx);
    padding: 20rpx;
    box-sizing: border-box;
    border-radius: 10rpx;
    font-size: 32rpx;
    color: #333;
    border: none;
    outline: none;
  }
}

.menu-box{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  .menu-title{
    font-size: 28rpx;
    color: #333;
    vertical-align: top;
    .menu-icon{
      width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
      display: inline-block;
      vertical-align: top;
      .menu-icon-image{
        width: 100%;
        height: 100%;
        opacity: .8;
      }
    }
  }
  .menu-fixed{
    position: relative;
    .menu-fixed-tips{
      position: absolute;
      font-size: 28rpx;
      color: #333;
      text-align: right;
      width: 300rpx;
      right: 70rpx;
      vertical-align: top;
      margin-right: 5rpx;
    }
    .menu-fixed-content{
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      color: #666;
      .menu-fixed-content-icon{
        width: 20rpx;
        height: 20rpx;
      }
      .menu-fixed-content-icon-bottom{
        transform: rotate(90deg);
      }
      .menu-fixed-content-icon-top{
        transform: rotate(-90deg);
      }
    }
  }
}

.menu-box-box{
  padding: 0 20rpx 20rpx 20rpx;
}
</style>
