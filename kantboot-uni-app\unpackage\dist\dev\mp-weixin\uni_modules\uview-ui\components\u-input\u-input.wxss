@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-113bc24f, scroll-view.data-v-113bc24f, swiper-item.data-v-113bc24f {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-input.data-v-113bc24f {

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.u-input--radius.data-v-113bc24f, .u-input--square.data-v-113bc24f {
  border-radius: 4px;
}
.u-input--no-radius.data-v-113bc24f {
  border-radius: 0;
}
.u-input--circle.data-v-113bc24f {
  border-radius: 100px;
}
.u-input__content.data-v-113bc24f {
  flex: 1;

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.u-input__content__field-wrapper.data-v-113bc24f {
  position: relative;

  display: flex;

  flex-direction: row;
  margin: 0;
  flex: 1;
}
.u-input__content__field-wrapper__field.data-v-113bc24f {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
}
.u-input__content__clear.data-v-113bc24f {
  width: 20px;
  height: 20px;
  border-radius: 100px;
  background-color: #c6c7cb;

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: center;
  -webkit-transform: scale(0.82);
          transform: scale(0.82);
  margin-left: 4px;
}
.u-input__content__subfix-icon.data-v-113bc24f {
  margin-left: 4px;
}
.u-input__content__prefix-icon.data-v-113bc24f {
  margin-right: 4px;
}
