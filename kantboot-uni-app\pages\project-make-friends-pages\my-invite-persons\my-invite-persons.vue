<template>
  <view>
    <view
        class="header"
        id="headerInInvitePersons">
      <kt-nav-bar
          :title="$i18n.zhToGlobal('我的会员')"></kt-nav-bar>
    </view>
    <view>
      <view class="box">
        <!-- 搜索框 -->
        <view class="box-search">
          <view class="box-search-input">
            <input
                type="text"
                v-model="keyword"
                @input="input"
                :placeholder="$i18n.zhToGlobal('输入搜索内容')"
            ></input>
          </view>
        </view>
        <view
            class="box-item"
        v-for="userAccount in userAccountsFilter"
            @click="$kt.router.navTo('/pages/project-make-friends-pages/user-info/user-info?userAccountId='+userAccount.id)"
        >
          <kt-user-info-card
              :user-info="userAccount"
          ></kt-user-info-card>
          <view class="box-item-time">
            <view>{{$i18n.zhToGlobal("成为会员时间")}}{{": "}}{{$kt.date.format(userAccount.gmtCreate,"yyyy-MM-dd hh:mm:ss")}}</view>
          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<script>
export default {
  data() {
    return {
      userAccounts: [],
      userAccountsFilter: '',
      keyword: '',
    };
  },
  mounted() {
    this.getAllInviterBySelf();
  },
  methods: {
    input(e) {
      this.keyword=e.detail.value;
      this.filterUserAccounts();
    },
    // 过滤
    filterUserAccounts() {
      if (this.keyword) {
        this.userAccountsFilter = this.userAccounts.filter(user => {
          return user.nickname.includes(this.keyword) || user.introduction.includes(this.keyword);
        });
        return;
      }
      this.userAccountsFilter = this.userAccounts;

    },
// /user-account-web/userAccountInvite/getAllInviterBySelf
    getAllInviterBySelf() {
      this.$kt.request.post('/user-account-web/userAccountInvite/getAllInviterBySelf', {
        data: {}
      }).then((res) => {
        this.userAccounts = res.data;
        // 根据gmtCreate时间倒序
        this.userAccounts.sort((a, b) => b.gmtCreate - a.gmtCreate);
        this.filterUserAccounts();
      }).catch((err) => {
        uni.showToast({
          title: this.$i18n.zhToGlobal('获取邀请人失败'),
          icon: 'none'
        });
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.box{
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;
  .box-item{
    margin-bottom: 60rpx;
    .box-item-time{
      font-size: 28rpx;
      color: #999999;
      margin-top: 10rpx;
    }
  }
  .box-search{
    width: 100%;
    background-color: #F0F0F0;
    padding: 20rpx;
    box-sizing: border-box;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
  }
}
.box-item:active{
  opacity: .7;
}
</style>
