<view class="edit-box data-v-11ff8fb4"><u-popup vue-id="2df164c0-1" show="{{show}}" mode="bottom" bgColor="rgba(0,0,0,0)" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-11ff8fb4" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup data-v-11ff8fb4"><view class="popup-title data-v-11ff8fb4">{{$root.g0}}</view><scroll-view class="year-picker data-v-11ff8fb4" style="height:300px;" scroll-y="{{true}}"><block wx:for="{{160}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['select',[item- -1]]]]]}}" class="year-picker-item data-v-11ff8fb4" bindtap="__e">{{item- -1}}</view></block></scroll-view><view style="height:10px;" class="data-v-11ff8fb4"></view></view></u-popup></view>