import $request from "@/uni_modules/kantboot/libs/request";
import $kt from "@/uni_modules/kantboot";

let result = {};

/**
 * 帖子点赞
 * @param requestParams 请求参数
 * @param requestParams.id 帖子ID
 * @param requestParams.isLike 是否点赞（true 代表点赞，false 代表取消点赞）
 * @returns {*}
 */
result.toPostLike = (requestParams) => {
    return $request.post("/fp-community-web/post/like", {
        data: {
            postId: requestParams.postId,
            isLike: requestParams.isLike
        }
    });
}

/**
 * 帖子收藏
 * @param requestParams 请求参数
 * @param requestParams.id 帖子ID
 * @param requestParams.isCollect 是否收藏（true 代表收藏，false 代表取消收藏）
 * @returns {*}
 */
result.toPostCollect = (requestParams) => {
    return $request.post("/fp-community-web/post/collect", {
        data: {
            postId: requestParams.postId,
            isCollect: requestParams.isCollect
        }
    });
}

/**
 * 根据ID获取帖子
 * @param requestParams 请求参数
 * @param requestParams.id 帖子ID
 * @returns {*}
 */
result.getPostById = (requestParams) => {
    return $request.post("/fp-community-web/post/getById", {
        data: {
            id: requestParams.id
        }
    });
}

/**
 * 根据ID获取帖子关系
 * @param requestParams 请求参数
 * @param requestParams.postId 帖子ID
 */
result.getPostRelationshipBySelf = (requestParams) => {
    return $request.post("/fp-community-web/post/getRelationshipBySelf", {
        data: {
            postId: requestParams.postId
        }
    });
}

/**
 * 发布帖子
 */
result.pushPost = (requestParams) => {
    return $request.post("/fp-community-web/post/push", {
        data: requestParams
    });
}

/**
 * 删除帖子
 */
result.removePost = (requestParams) => {
    return $request.post("/fp-community-web/post/removeById", {
        data: {
            id: requestParams.id
        }
    });
}

/**
 * 修改权限
 * @param permission
 * @returns {Promise | Promise<unknown>}
 */
result.updatePostPermission = (permission) => {
    return $request.post("/fp-community-web/post/updatePermission", {
        data: permission
    });
}

/**
 * 获取帖子举报选项
 */
result.getPostReportOptions = () => {
    return new Promise((resolve, reject) => {
        $request.post("/fp-community-web/postReportOption/getAll")
            .then((res) => {
                let postReportOptions = res.data;

                $kt.i18n.getI18n({
                    topKey: "FpCommunityPostReportOption",
                    bottomKey: "name"
                }).then(res1 => {
                    let i18nMap = res1.map;
                    for (let i = 0; i < postReportOptions.length; i++) {
                        try {
                            let item = postReportOptions[i];
                            let map = i18nMap[item.id + ""];
                            item.name = map.content;
                        } catch (e) {
                            console.error(e);
                        }
                    }
                    console.log(res, "获取帖子举报选项");
                    resolve(res);
                });
            })
            .catch((err) => {
                reject(err);
            });
    });
}

// /fp-community-web/postReport/report
/**
 * 举报帖子
 * @param requestParams 请求参数
 * @param requestParams.postId 帖子ID
 * @param requestParams.postReportOptionId 帖子举报选项ID
 * @param requestParams.content 举报内容
 */
result.reportPost = (requestParams) => {
    return $request.post("/fp-community-web/postReport/report", {
        data: {
            postId: requestParams.postId,
            postReportOptionId: requestParams.postReportOptionId,
            content: requestParams.content
        }
    });
}

export default result;