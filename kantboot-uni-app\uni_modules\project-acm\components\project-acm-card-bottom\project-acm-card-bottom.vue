<template>
  <view>
    <view
        class="no-data"
    v-if="!loading&&commentList.length === 0"
    >
      <view class="in-no-data">
        {{$i18n.zhToGlobal("暂无解答")}}
      </view>
    </view>
    <view
        v-else
        class="box">
      <view class="title">{{"·"}}{{$i18n.zhToGlobal("答疑与追问")}}{{"·"}}</view>
      <view
          class="in-box"
          v-for="(item, index) in commentList">
        <kt-community-post-card
            :has-bottom-operation="false"
            :has-dot="false"
            :post="commentListNoId[index]"
        ></kt-community-post-card>
        <view class="line"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props:{
    postId:{
      type: String || Number,
      default: ''
    }
  },
  data() {
    return {
      // 评论列表
      commentList: [],
      commentListNoId: [],
      loading: true,
    };
  },
  watch: {
    postId: {
      handler(val) {
        this.getCommentList();
      },
      immediate: true,
      deep: true
    },
  },
  created() {
    this.getCommentList();
  },
  methods: {
    refresh(){
      this.getCommentList();
    },
    // 获取评论列表
    getCommentList() {
      this.loading = true;
      // /fp-community-web/postComment/getByPostId
      return this.$kt.request.post('/fp-community-web/postComment/getByPostId', {
       data:{postId: this.postId},
      }).then((res) => {
         let list = res.data;
         // 按照id从大到小排序
          list.sort((a, b) => {
            return b.id - a.id;
          });
          // 删除auditStatus不是pass的评论
          list = list.filter(item => item.auditStatus === 'pass');
          this.commentList = list;
          this.commentListNoId = JSON.parse(JSON.stringify(list));
          for (let i = 0; i < this.commentListNoId.length; i++) {
            this.commentListNoId[i].id = "";
          }
          this.loading = false;
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.no-data {
  position: relative;
  width: 100%;
  z-index:99;
  color: #f0f0f0;
  text-align: right;
  margin-top: -30rpx;
  .in-no-data {
    font-size: 22rpx;
    display: inline-block;
    background-color: rgba(0,0,0,.3);
    padding: 0 20rpx 0 20rpx;
    margin-right: 20rpx;
    border-radius: 20rpx;
  }
}

.box{
  padding: 10rpx;
  border-radius: 20rpx;
}

.title {
  font-size: 28rpx;
  font-weight: bold;
  margin: 20rpx 0 20rpx 0;
  text-align: center;
}


.in-box{
  position: relative;
  .line{
    position: absolute;
    width: 100rpx;
    height: 10px;
    background-color: #f0f0f0;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
    border-radius: 20rpx;
  }
}

</style>
