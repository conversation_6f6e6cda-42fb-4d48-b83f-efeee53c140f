<template>
  <view class="box"
        :style="{
          width: wh.width,
          height: wh.height,
        }">

    <!-- #ifdef MP-WEIXIN -->
    <image class="qrcode-image"
           @load="imageLoad"
          :src="svgSrc"
          :style="{
            width: wh.width,
            height: wh.height,
          }">
    </image>
    <image
        v-show="!loading"
        class="image-image"
    :src="imageSrc"
    ></image>
    <!-- #endif -->

    <!-- #ifndef MP-WEIXIN -->
    <canvas :id="uuid+'_canvas'"
            :canvas-id="uuid+'_canvas'"
            :style="{
              width: wh.width,
              height: wh.height,
            }"></canvas>
    <!-- #endif -->

    <view
        v-if="loading"
        class="loading-box">
      <image
          class="loading-img"
          :src="$kt.file.byPath('kantboot/icon/loading.svg')">
      </image>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: '200rpx'
    },
    content: {
      type: String,
      default: ''
    },
    imageSrc: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      uuid: "",
      svgSrc: "",
      loading: true,
      wh: {},
    };
  },
  created() {
    this.uuid = this.$kt.util.generateUUID();
    this.wh = this.getWH(this.size);
  },
  mounted() {
    this.svgSrc = this.getSvg(this.content);
    // #ifndef MP-WEIXIN
    this.$nextTick(() => {
      this.createCanvas();
    });
    // #endif
  },
  methods: {
    imageLoad() {
      this.loading = false;
      console.log("imageLoad");
      this.$emit("load", this.svgSrc);
    },
    createCanvas() {
      let canvas = null;
      this.loading = true;
      this.svgSrc = this.getSvg(this.content);
      let wh = this.getWH(this.size);
      let width = wh.width.substring(0, wh.width.length - 2);
      let height = wh.height.substring(0, wh.height.length - 2);
      canvas = uni.createCanvasContext(this.uuid + '_canvas', this);

      // 画入this.svgSrc
      canvas.drawImage(this.svgSrc, 0, 0, width / 1, height / 1);
      canvas.save();
      // 如果this.imageSrc不为空，则画入this.imageSrc
      if (this.imageSrc) {
        // 弧度
        canvas.drawImage(this.imageSrc,
            (width / 1 / 2 - width / 5 / 2),
            (height / 1 / 2 - height / 5 / 2),
            width / 5, height / 5,
        );
        canvas.save();
      }

      canvas.draw(false, () => {
        uni.canvasToTempFilePath({
          canvasId: this.uuid + '_canvas',
          success: (res) => {
            this.loading = false;

            this.$emit("load", res.tempFilePath);
          },
          fail: (err) => {
            console.error(err);
          }
        }, this);
      });


    },
    /**
     * 保存
     */
    save() {
      uni.canvasToTempFilePath({
        canvasId: this.uuid + '_canvas',
        success: (res) => {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: (res) => {
              uni.showToast({
                title: this.$i18n.zhToGlobal('保存成功'),
                icon: 'none'
              });
            },
            fail: (err) => {
              uni.showToast({
                title: this.$i18n.zhToGlobal('保存失败'),
                icon: 'none'
              })
            }
          })
        }
      })
    },

    /**
     * 生成对应的宽高
     */
    getWH(size) {
      let wh = 0;
      // 如果size是数字
      if (!isNaN(size)) {
        wh = this.$kt.util.rpxToPx(size) + "px";
      }
      // 如果size是rpx结尾
      else if (size.endsWith("rpx")) {
        let sizeNum = size.substring(0, size.length - 3);
        wh = this.$kt.util.rpxToPx(sizeNum) + "px";
      }
      // 如果size是px结尾
      else if (size.endsWith("px")) {
        wh = size;
      }
      return {
        width: wh,
        height: wh
      }
    },
    /**
     * 获取二维码图片
     * @param content 二维码内容
     */
    getSvg(content) {
      content = encodeURIComponent(content);
      return this.$kt.request.config.rootAddress + "/util-qrcode-web/qrCode/getSvg?content=" + content;
    },
  },

}
</script>

<style lang="scss" scoped>
.box {
  position: relative;
  display: inline-block;

  .loading-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, .8);
    z-index: 2;

    .loading-img {
      position: absolute;
      width: 30%;
      height: 30%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      // 颜色反转
      filter: invert(1);
      // 动画
      animation: loading-icon 2s linear infinite;
    }
  }
}

@keyframes loading-icon {
  0% {
    transform: translate(-50%, -50%) rotate(0deg) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) rotate(180deg) scale(.7);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg) scale(1);
  }
}

.image-image{
  position: absolute;
  width: 20%;
  height: 20%;
  border-radius: 10rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

</style>
