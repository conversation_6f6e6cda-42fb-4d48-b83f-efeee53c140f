
.scroll-view-x {
  width: 100%;
  // 超出不换行
  white-space: nowrap;
  // 超出滚动
  overflow-x: scroll;

  .view-item {
    padding: 10rpx 20rpx 10rpx 70rpx;
    display: inline-block;
    font-size: 32rpx;
    text-align: center;
    background-color: #fff;
    border: 1px solid #eee;
    margin-right: 10px;
    border-radius: 10rpx;
    vertical-align: top;
    position: relative;
    .view-item-icon{
        position: absolute;
        width: 40rpx;
        height: 40rpx;
        margin-right: 10rpx;
        top: 50%;
        left: 20rpx;
        transform: translateY(-50%);
    }
  }

  .view-item-selected {
    border: 1px solid #666666;
  }


}

.scroll-view-x::-webkit-scrollbar {
  display: none;
}

.form-item {
  .form-item-input {
    padding: 20rpx;
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 10rpx;

    .input {
      width: 100%;
      height: 100%;
      font-size: 32rpx;
      color: #333;
      border: none;
      outline: none;
    }

  }

  .form-item-param{
    font-size: 24rpx;
    color: #999999;
    margin-top: 10rpx;
    // 加个"·"在首位
    &:before {
      content: "·";
      margin-right: 10rpx;
    }
  }

  .in-form-item {
    padding: 20rpx;
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 10rpx;
  }
}

.form-mode-device-pc{
  .scroll-view-x{
    .view-item{

      cursor: pointer;
    }
  }
}