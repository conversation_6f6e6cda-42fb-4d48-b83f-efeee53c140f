<view class="data-v-9b410f24"><view id="headerInPostSelf" class="data-v-9b410f24"><project-acm-nav-bar vue-id="ccdfd350-1" is-has-i18n="{{false}}" title="{{$root.g0}}" class="data-v-9b410f24" bind:__l="__l"></project-acm-nav-bar></view><view class="bg data-v-9b410f24"></view><kt-community-post-panel vue-id="ccdfd350-2" height="{{'calc(100vh - '+headerHeight+'px)'}}" is-self="{{true}}" show-audit-status="{{true}}" data-event-opts="{{[['^cardClick',[['cardClick']]],['^cardDotClick',[['cardDotClick']]]]}}" bind:cardClick="__e" bind:cardDotClick="__e" class="data-v-9b410f24" bind:__l="__l"></kt-community-post-panel><kt-community-post-operate-popup vue-id="ccdfd350-3" remove-text="{{$root.g1}}" report-text="{{$root.g2}}" has-permission-setting="{{false}}" data-ref="communityPostOperatePopup" class="data-v-9b410f24 vue-ref" bind:__l="__l"></kt-community-post-operate-popup></view>