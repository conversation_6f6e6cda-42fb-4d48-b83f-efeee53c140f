@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-741f4ed6 {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx 20rpx 0 0;
  text-align: left;
}
.box-mode-device-pc.data-v-741f4ed6 {
  text-align: center;
  position: fixed;
  width: 800px;
  border-radius: 20rpx;
  padding: 20rpx;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.box-mode-device-pc .card.data-v-741f4ed6 {
  position: relative;
  width: 700rpx;
  display: inline-block;
  margin-bottom: 20rpx;
}
.second-box.data-v-741f4ed6 {
  width: 100%;
}
.second-box .second-box-box.data-v-741f4ed6 {
  width: calc(100% - 100rpx);
  white-space: nowrap;
  overflow-x: auto;
}
.second-box .second-box-box .second-box-item.data-v-741f4ed6 {
  position: relative;
  display: inline-block;
  font-size: 32rpx;
  letter-spacing: 3rpx;
  margin-right: 5rpx;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  cursor: pointer;
}
.second-box .second-box-box .second-box-item .login-btn.data-v-741f4ed6 {
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);
  cursor: pointer;
  color: #ffffff;
  background-color: #5A7EF6;
  display: inline-block;
  padding: 10rpx 20rpx 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-right: 10rpx;
}
.second-box .second-box-box .second-box-item .second-box-line.data-v-741f4ed6 {
  position: absolute;
  width: 40rpx;
  height: 7rpx;
  background-color: #000000;
  left: 50%;
  bottom: 0;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  border-radius: 20rpx;
}
.second-box .second-box-box .second-box-item-selected.data-v-741f4ed6 {
  font-weight: bold;
}
