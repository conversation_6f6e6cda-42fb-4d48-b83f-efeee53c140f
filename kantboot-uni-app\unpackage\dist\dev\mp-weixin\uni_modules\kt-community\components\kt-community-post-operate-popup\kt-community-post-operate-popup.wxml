<view class="data-v-f2db256e"><kt-popup bind:close="__e" vue-id="4bbbbcdd-1" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" class="data-v-f2db256e vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{!loading}}"><view class="{{['data-v-f2db256e',clazz.box]}}"><block wx:if="{{mode==='main'}}"><view class="data-v-f2db256e"><view style="height:30rpx;" class="data-v-f2db256e"></view><block wx:if="{{!isSelf&&hasReport}}"><view data-event-opts="{{[['tap',[['toReport']]]]}}" class="menu-box data-v-f2db256e" bindtap="__e"><view class="menu-title data-v-f2db256e"><view class="menu-icon data-v-f2db256e"><image class="menu-icon-image data-v-f2db256e" src="{{$root.g0}}"></image></view>{{''+$root.g1+''}}</view><view class="menu-fixed data-v-f2db256e"><view class="menu-fixed-tips data-v-f2db256e"></view><view class="menu-fixed-content data-v-f2db256e"><image class="menu-fixed-content-icon data-v-f2db256e" src="{{$root.g2}}"></image></view></view></view></block><block wx:if="{{!isSelf}}"><view class="menu-box data-v-f2db256e"><block wx:if="{{isFollowRequest}}"><kt-button style="width:100%;" vue-id="{{('4bbbbcdd-2')+','+('4bbbbcdd-1')}}" data-ref="followButton" class="data-v-f2db256e vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g3}}</kt-button></block><block wx:if="{{!isFollow&&!isFollowRequest}}"><kt-button style="width:100%;" bind:click="__e" vue-id="{{('4bbbbcdd-3')+','+('4bbbbcdd-1')}}" data-ref="followButton" data-event-opts="{{[['^click',[['toFollow']]]]}}" class="data-v-f2db256e vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g4}}</kt-button></block><block wx:if="{{isFollow&&!isFollowRequest}}"><kt-button style="width:100%;" bind:click="__e" vue-id="{{('4bbbbcdd-4')+','+('4bbbbcdd-1')}}" data-ref="followButton" data-event-opts="{{[['^click',[['toUnFollow']]]]}}" class="data-v-f2db256e vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g5}}</kt-button></block></view></block><block wx:if="{{isSelf&&hasPermissionSetting}}"><view data-event-opts="{{[['tap',[['toPermissionSetting']]]]}}" class="menu-box data-v-f2db256e" bindtap="__e"><view class="menu-title data-v-f2db256e"><view class="menu-icon data-v-f2db256e"><image class="menu-icon-image data-v-f2db256e" src="{{$root.g6}}"></image></view>{{''+$root.g7+''}}</view><view class="menu-fixed data-v-f2db256e"><view class="menu-fixed-tips data-v-f2db256e"></view><view class="menu-fixed-content data-v-f2db256e"><image class="menu-fixed-content-icon data-v-f2db256e" src="{{$root.g8}}"></image></view></view></view></block><block wx:if="{{isSelf}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="menu-box data-v-f2db256e" bindtap="__e"><view class="menu-title data-v-f2db256e"><view class="menu-icon data-v-f2db256e"><image class="menu-icon-image data-v-f2db256e" src="{{$root.g9}}"></image></view>{{''+$root.g10+''}}</view><view class="menu-fixed data-v-f2db256e"><view class="menu-fixed-tips data-v-f2db256e"></view><view class="menu-fixed-content data-v-f2db256e"></view></view></view></block><kt-modal vue-id="{{('4bbbbcdd-5')+','+('4bbbbcdd-1')}}" title="{{$root.g11}}" content="{{$root.g12}}" data-ref="modalOfDelete" data-event-opts="{{[['^confirm',[['toRemove']]]]}}" bind:confirm="__e" class="data-v-f2db256e vue-ref" bind:__l="__l"></kt-modal></view></block><block wx:if="{{mode==='permissionSetting'}}"><view class="data-v-f2db256e"><kt-community-post-permission-setting-box bind:updatePostPermissionSuccess="__e" bind:updatePostPermissionFail="__e" bind:input="__e" vue-id="{{('4bbbbcdd-6')+','+('4bbbbcdd-1')}}" data-ref="fpCommunityPostPermissionSettingBox" value="{{post.permission}}" data-event-opts="{{[['^updatePostPermissionSuccess',[['updatePostPermissionSuccess']]],['^updatePostPermissionFail',[['updatePostPermissionFail']]],['^input',[['__set_model',['$0','permission','$event',[]],['post']]]]]}}" class="data-v-f2db256e vue-ref" bind:__l="__l"></kt-community-post-permission-setting-box><kt-button vue-id="{{('4bbbbcdd-7')+','+('4bbbbcdd-1')}}" border-radius="20rpx" is-open-box-shadow="{{false}}" data-ref="updatePostPermissionButton" data-event-opts="{{[['^click',[['updatePostPermission']]]]}}" bind:click="__e" class="data-v-f2db256e vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g13}}</kt-button><view style="height:20rpx;" class="data-v-f2db256e"></view><view style="text-align:center;" class="data-v-f2db256e"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="to-back-btn data-v-f2db256e" bindtap="__e">{{$root.g14}}</view></view><view style="height:20rpx;" class="data-v-f2db256e"></view></view></block><block wx:if="{{mode==='report'}}"><view class="data-v-f2db256e"><view class="menu-box data-v-f2db256e"><view class="menu-title data-v-f2db256e"><view class="menu-icon data-v-f2db256e"><image class="menu-icon-image data-v-f2db256e" src="{{$root.g15}}"></image></view>{{''+$root.g16+''}}</view></view><view class="line data-v-f2db256e"></view><scroll-view class="report-scroll-view data-v-f2db256e" scroll-y="{{true}}"><kt-community-post-report vue-id="{{('4bbbbcdd-8')+','+('4bbbbcdd-1')}}" has-confirm-button="{{false}}" post-id="{{post.id}}" data-ref="ktCommunityPostReport" data-event-opts="{{[['^reportLoading',[['e2']]],['^reportSuccess',[['reportSuccess']]],['^reportFail',[['e3']]]]}}" bind:reportLoading="__e" bind:reportSuccess="__e" bind:reportFail="__e" class="data-v-f2db256e vue-ref" bind:__l="__l"></kt-community-post-report></scroll-view><u-divider vue-id="{{('4bbbbcdd-9')+','+('4bbbbcdd-1')}}" class="data-v-f2db256e" bind:__l="__l"></u-divider><kt-button bind:click="__e" vue-id="{{('4bbbbcdd-10')+','+('4bbbbcdd-1')}}" data-ref="confirmReportButton" data-event-opts="{{[['^click',[['e4']]]]}}" class="data-v-f2db256e vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g17}}</kt-button><view style="height:20rpx;" class="data-v-f2db256e"></view><view style="text-align:center;" class="data-v-f2db256e"><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="to-back-btn data-v-f2db256e" bindtap="__e">{{$root.g18}}</view></view><view style="height:30rpx;" class="data-v-f2db256e"></view></view></block></view></block></kt-popup></view>