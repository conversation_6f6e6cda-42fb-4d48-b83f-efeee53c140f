# 可拖拽选项卡功能说明

## 功能概述

已为交友页面的选项卡（推荐、公开朋友圈、好友朋友圈）添加了拖拽排序功能，支持电脑端和手机端操作。

## 主要特性

### 1. 拖拽排序
- **移动端**：长按选项卡并拖动来重新排序
- **PC端**：点击并拖动选项卡来重新排序
- 拖拽过程中有视觉反馈（透明度变化）

### 2. 数据同步
- 拖拽后底部内容（swiper）会自动跟随选项卡顺序更新
- 确保选项卡标题与对应内容始终保持一致

### 3. 状态持久化
- 拖拽后的顺序会自动保存到本地存储
- 下次打开页面时会恢复上次的排序
- 支持重置为默认顺序

### 4. 样式保持
- 保持原有的选项卡样式不变
- 只在拖拽时添加必要的视觉反馈
- 兼容暗色模式

## 技术实现

### 组件结构
```
components/draggable-tabs/draggable-tabs.vue  # 可拖拽选项卡组件
utils/tab-order-manager.js                    # 状态管理工具
pages/.../pageHome.vue                         # 主页面（已更新）
```

### 核心文件

#### 1. DraggableTabs 组件
- 处理触摸和鼠标事件
- 实现拖拽逻辑
- 保持原有样式类名

#### 2. TabOrderManager 工具类
- 管理选项卡顺序的保存和加载
- 数据验证和错误处理
- 支持多页面独立配置

#### 3. PageHome 页面更新
- 集成拖拽组件
- 处理顺序变化事件
- 确保 swiper 内容同步更新

## 使用方法

### 移动端操作
1. 长按要移动的选项卡
2. 拖动到目标位置
3. 松开手指完成排序

### PC端操作
1. 点击并按住要移动的选项卡
2. 拖动到目标位置
3. 松开鼠标完成排序

### 重置顺序
可以通过调用 `resetTabOrder()` 方法重置为默认顺序：
```javascript
this.resetTabOrder(); // 会弹出确认对话框
```

## 配置选项

### 本地存储键名
- 默认使用 `draggable_tab_order_makeFriendsHome` 作为存储键
- 可以通过修改 `pageKey` 参数来区分不同页面

### 拖拽阈值
- 移动端长按阈值：500ms（可在组件中调整）
- 拖拽敏感度：自动根据设备类型调整

## 兼容性

### 支持平台
- ✅ 微信小程序
- ✅ H5 (移动端/PC端)
- ✅ App (Android/iOS)
- ✅ 支付宝小程序
- ✅ 百度小程序

### 浏览器支持
- ✅ Chrome/Safari/Firefox (现代浏览器)
- ✅ 移动端浏览器
- ✅ 微信内置浏览器

## 注意事项

1. **数据一致性**：拖拽后会立即更新 swiper 内容，确保标题与内容对应
2. **性能优化**：使用了防抖和节流机制，避免频繁更新
3. **错误处理**：包含完整的错误处理和数据验证
4. **用户体验**：提供即时的视觉反馈和操作提示

## 调试信息

开发模式下会在控制台输出以下信息：
- 拖拽开始/结束事件
- 选项卡顺序变化
- 数据保存/加载状态
- 错误信息和警告

## 扩展性

该实现具有良好的扩展性，可以轻松：
- 添加更多选项卡
- 应用到其他页面
- 自定义拖拽动画
- 添加更多交互效果

## 维护建议

1. 定期清理本地存储中的过期数据
2. 监控拖拽性能，特别是在低端设备上
3. 根据用户反馈调整拖拽敏感度
4. 保持与原有样式系统的兼容性
