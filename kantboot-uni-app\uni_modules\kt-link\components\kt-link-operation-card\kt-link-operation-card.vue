<template>
  <view>
    <view :class="clazz.form">
      <kt-box
          :title="$i18n.zhToGlobal('选择外链类型')"
          class="form-item">
        <view class="scroll-view-x">
          <view
              v-for="(item, index) in options"
              class="view-item"
              @click="select(item)"
              :class="{
                'view-item-selected': requestParams.type === item.code
              }"
          >
            <image
                :src="item.icon"
                class="view-item-icon"></image>
            {{item.text}}</view>
        </view>
        <view
            v-if="showParams"
            class="form-item-param">{{'type'}}</view>
      </kt-box>
      <kt-box
          :title="$i18n.zhToGlobal('外链标题')"
          class="form-item"
      >
        <view class="form-item-input">
          <input
              v-model="requestParams.title"
              type="text"
              :placeholder="$i18n.zhToGlobal('输入外链标题')"
              class="input"/>
        </view>
        <view
            v-if="showParams"
            class="form-item-param">{{'title'}}</view>
      </kt-box>
      <kt-box
          :title="$i18n.zhToGlobal('外链卡片图片')"
          class="form-item"
      >
          <kt-image-select
              v-model="fileIdsOfImage"
              :count="1"
              file-group-code="fp">
          </kt-image-select>
        <view
            v-if="showParams"
            class="form-item-param">{{'fileIdOfImage'}}</view>
      </kt-box>

      <personal-wechat
      v-if="isSelected('personalWechat')"
      @changeParams="changeParams"
      :showParams="showParams"
      ></personal-wechat>

      <personal-wechat
          v-if="isSelected('enterpriseWechat')"
          @changeParams="requestParams.params = $event"
          :showParams="showParams"
      ></personal-wechat>

      <public-account-article
        v-if="isSelected('wechatPublicAccountArticle')"
        @changeParams="changeParams"
        :showParams="showParams"
      ></public-account-article>

      <public-account
        v-if="isSelected('wechatPublicAccount')"
        @changeParams="changeParams"
        :showParams="showParams"
      ></public-account>

      <h5-webpage
      v-if="isSelected('h5Webpage')"
      @changeParams="changeParams"
      :showParams="showParams"
      ></h5-webpage>

      <wechat-mini-program
        v-if="isSelected('wechatMiniProgram')"
        @changeParams="changeParams"
        :showParams="showParams"
      ></wechat-mini-program>

      <pre v-if="showParams">{{ requestParams }}</pre>
    </view>
    <kt-button
        @click="generate"
    ref="ktButton"
    >{{$i18n.zhToGlobal("生成")}}</kt-button>
  </view>
</template>

<script>
import PersonalWechat from "./in-components/personal-wechat.vue";
import PublicAccountArticle
  from "@/uni_modules/kt-link/components/kt-link-operation-card/in-components/public-account-article.vue";
import PublicAccount from "@/uni_modules/kt-link/components/kt-link-operation-card/in-components/public-account.vue";
import H5Webpage from "@/uni_modules/kt-link/components/kt-link-operation-card/in-components/h5-webpage.vue";
import WechatMiniProgram
  from "@/uni_modules/kt-link/components/kt-link-operation-card/in-components/wechat-mini-program.vue";
export default {
  components: {H5Webpage, PublicAccount, PublicAccountArticle, PersonalWechat, WechatMiniProgram},
  props:{
    // 是否显示参数
    showParams: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      clazz:{
        form: this.$kt.style.toggleClass("form"),
      },
      options: [
        {
          text: this.$i18n.zhToGlobal('个人微信'),
          code: 'personalWechat',
          icon: this.$kt.file.byPath('kantboot/icon/wechat.svg')
        },
        {text: this.$i18n.zhToGlobal('企业微信'), code: 'enterpriseWechat',
          icon: this.$kt.file.byPath('kantboot/icon/enterpriseWechat.svg')
        },
        {text: this.$i18n.zhToGlobal('公众号'), code: 'wechatPublicAccount',
          icon: this.$kt.file.byPath('kantboot/icon/publicAccount.svg')
        },
        {text: this.$i18n.zhToGlobal('公众号文章'), code: 'wechatPublicAccountArticle',
          icon: this.$kt.file.byPath('kantboot/icon/publicAccount.svg')
        },
        {text: this.$i18n.zhToGlobal('H5网页'), code: 'h5Webpage',
          icon: this.$kt.file.byPath('kantboot/icon/h5.svg')
        },
        {
          text: this.$i18n.zhToGlobal('小程序'), code: 'wechatMiniProgram',
          icon: this.$kt.file.byPath('kantboot/icon/wechatMiniProgram.svg')
        },
      ],
      fileIdsOfImage: [],
      requestParams:{
        fileIdOfImage:"",
        title:"",
        type:"personalWechat",
        params:"",
      },
    };
  },
  watch: {
    fileIdsOfImage: {
      handler(val) {
        this.requestParams.fileIdOfImage = val[0];
      },
      immediate: true,
      deep: true
    },
  },
  methods: {
    generate() {
      this.$refs.ktButton.loading(null,99999);
      this.$kt.request.post("/fp-link-web/link/generate",{
        data:{
          ...this.requestParams
        }
      }).then((res)=>{
        this.$refs.ktButton.success(res.msg);
      }).catch((err)=> {
        this.$refs.ktButton.error(err.msg);
      });
    },
    select(item) {
      this.requestParams.type = item.code;
    },
    isSelected(code) {
      return this.requestParams.type === code;
    },
    changeParams(e) {
      this.requestParams.params = e;
      this.$emit('changeParams', this.requestParams);
    },
  },
}
</script>

<style lang="scss" scoped>
@import "./css/kt-link-operation-card.scss";
</style>
