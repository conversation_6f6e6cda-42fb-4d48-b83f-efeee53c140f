<kt-popup bind:close="__e" vue-id="1a60da9f-1" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" class="data-v-405ce172 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-405ce172',clazz.box]}}"><view class="box-title data-v-405ce172">{{''+$root.g0+''}}</view><scroll-view class="box data-v-405ce172" style="height:400rpx;" scroll-y="{{true}}"><view class="menu-box data-v-405ce172"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item})}}" class="{{['menu-item','data-v-405ce172',(selected===item.code)?'menu-item-selected':'']}}" bindtap="__e">{{item.name}}</view></block></view></scroll-view></view></kt-popup>