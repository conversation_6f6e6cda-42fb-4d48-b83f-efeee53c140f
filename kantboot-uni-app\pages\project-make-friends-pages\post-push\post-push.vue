<template>
  <view>
    <project-acm-nav-bar
        :is-has-i18n="false"
        ref="navBar"
        :title="$i18n.zhToGlobal('提问')"></project-acm-nav-bar>

    <kt-community-post-push
        @pushSuccess="pushSuccess"
    ></kt-community-post-push>

  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    pushSuccess() {
      // setTimeout(()=>{
        uni.navigateBack()
      // },1000);
    },
  },
}
</script>

<style lang="scss">

</style>
