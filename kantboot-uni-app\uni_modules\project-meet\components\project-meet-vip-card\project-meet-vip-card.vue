<template>
  <view :class="clazz.box + (selected ? ' box-selected' : '')"
        @click="click">
    <view class="name">{{ info.name }}</view>
    <view class="price">{{"$"}}{{ info.price }}</view>
    <view class="tip">{{ info.tip }}</view>
  </view>
</template>

<script>
export default {
  props: {
    selected: false,
    width: {
      type: String,
      default: "200rpx"
    },
    height: {
      type: String,
      default: "230rpx"
    },
    info: {
      type: Object,
      default() {
        return {
          id: "1",
          name: "1 mouth VIP",
          price: "49.99",
          tip: "daily only$1.66"
        }
      }
    }
  },
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      }
    };
  },
  methods: {
    click() {
      this.$emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  display: inline-block;
  width: 200rpx;
  height: 230rpx;
  //background-color: #FFFFFF;
  // 渐变
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 1) 100%);

  // 超出省略号
  overflow: hidden;
  text-overflow: ellipsis;
  // 超出不换行
  white-space: nowrap;
  padding: 10rpx;
  box-sizing: border-box;
  border: 2rpx solid #F0F0F0;
  border-radius: 20rpx;

  .name {
    font-size: 24rpx;
    color: #000000;
    text-align: center;
    margin-top: 20rpx;
  }

  .price {
    font-size: 42rpx;
    color: #000000;
    text-align: center;
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    font-weight: bold;
  }

  .tip {
    color: #999999;
    background-color: #f0f0f0;
    border-radius: 20rpx;
    // 超出省略号
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 5rpx 10rpx;
    font-size: 20rpx;
    text-align: center;

  }
}

.box-selected {
  border: 2rpx solid #FF9900;
  border-radius: 20rpx;
  //background-color: #ff9900;
  // 渐变，从上到下
  background: linear-gradient(to bottom, rgba(255, 153, 0, 0.1) 0%, rgba(255, 153, 0, 0.5) 100%);
  .name {
    color: #ff5b5b;
    // 颜色阴影
    text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);
  }
  .price {
    color: #ff5b5b;
    // 颜色阴影
    text-shadow: 0 0 10rpx rgba(255, 0, 0, 0.2);
  }
  .tip {
    color: #FFFFFF;
    background-color: rgba(255,255,255,.1);
    // 颜色阴影
    text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);

  }

}

.box-mode-device-pc{
  width: 300rpx;
}


</style>
