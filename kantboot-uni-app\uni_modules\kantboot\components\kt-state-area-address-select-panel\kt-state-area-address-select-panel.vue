<template>
  <view>
    <view :class="clazz.box">
      <view class="header">
        <view
            class="select-box"
            v-if="stateAreaSelected.code">
          <view
              class="select-text"
          @click="stateAreaSelected.code='';stateAreaAddressSelected.code=''"
          >{{stateAreaSelected.name}}</view>
          <view
              v-if="stateAreaAddressSelected.code"
              @click="stateAreaAddressSelected.code='';stateAreaAddressSecondSelected.code=''"
              class="select-text"
          >{{" >"}}{{stateAreaAddressSelected.name}}
          </view>
          <view
              v-if="stateAreaAddressSecondSelected.code"
              @click="stateAreaAddressSecondSelected.code=''"
              class="select-text">
            {{" >"}}{{stateAreaAddressSecondSelected.name}}
          </view>

        </view>
        <state-area-select
            v-if="!stateAreaSelected.code"
            :height="height"
            @select="stateAreaSelect"
        ></state-area-select>
        <state-area-ad-level-top-select
        v-if="stateAreaSelected.code&&!stateAreaAddressSelected.code"
        :height="height"
        :stateArea="stateAreaSelected"
        @select="addressTopSelect"
        ></state-area-ad-level-top-select>
        <state-area-ad-level-second-select
            v-if="stateAreaSelected.code&&stateAreaAddressSelected.code&&!stateAreaAddressSecondSelected.code"
            :height="height"
            :state-area="stateAreaSelected"
            :parentAddress="stateAreaAddressSelected"
            @select="addressSecondSelect"
        ></state-area-ad-level-second-select>
        <state-area-ad-level-third-select
            v-if="stateAreaAddressSecondSelected.code"
            :height="height"
            :state-area="stateAreaSelected"
            :parentAddress="stateAreaAddressSecondSelected"
            @select="addressThirdSelect"
        ></state-area-ad-level-third-select>

        <view style="height: 20rpx"></view>
        <kt-button
            v-if="stateAreaSelected.code&&!stateAreaAddressSelected.code&&!stateAreaAddressSecondSelected.code"
            @click="select({'fullCode':stateAreaSelected.code})"
        >
          <view>
            {{stateAreaSelected.name}}
            <text
                style="font-size: 24rpx;margin-left: 10rpx"
            >{{$i18n.zhToGlobal("直接选择")}}</text>
          </view>
        </kt-button>

        <kt-button
            v-if="stateAreaAddressSelected.code&&!stateAreaAddressSecondSelected.code"
            @click="select(stateAreaAddressSecondSelected)"
        >
          <view>
            {{stateAreaAddressSelected.name}}
            <text
                style="font-size: 24rpx;margin-left: 10rpx"
            >
              {{$i18n.zhToGlobal("直接选择")}}
            </text>
          </view>
        </kt-button>
      </view>
    </view>
  </view>
</template>

<script>
import StateAreaSelect
  from "@/uni_modules/kantboot/components/kt-state-area-address-select-panel/inComponents/state-area-select/state-area-select.vue";
import StateAreaAdLevelTopSelect
  from "@/uni_modules/kantboot/components/kt-state-area-address-select-panel/inComponents/state-area-select/state-area-ad-level-top-select.vue.vue";
import StateAreaAdLevelSecondSelect
  from "@/uni_modules/kantboot/components/kt-state-area-address-select-panel/inComponents/state-area-select/state-area-ad-level-second-select.vue";
import StateAreaAdLevelThirdSelect
  from "@/uni_modules/kantboot/components/kt-state-area-address-select-panel/inComponents/state-area-select/state-area-ad-level-third-select.vue";

export default {
  components: {StateAreaSelect,StateAreaAdLevelTopSelect,StateAreaAdLevelSecondSelect,StateAreaAdLevelThirdSelect},
  props:{
    height: {
      type: String,
      default: "calc(100vh - 700rpx)"
    }
  },
  data() {
    return {
      stateAreaSelected:{
        code:''
      },
      stateAreaAddressSelected:{
        code:''
      },
      stateAreaAddressSecondSelected:{
        code:''
      },
      stateAreaAddressThirdSelected:{
        code:''
      },
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      },
    };
  },
  created() {
    this.getAll();
  },
  methods: {
    select(item) {
      this.$emit("select", item);
    },
    addressThirdSelect(item) {
      this.stateAreaAddressThirdSelected = item;
      this.$emit('select', item);
    },
    addressSecondSelect(item) {
      this.stateAreaAddressSecondSelected = item;
    },
    stateAreaSelect(item) {
      this.stateAreaSelected = item;
    },
    addressTopSelect(item) {
      this.stateAreaAddressSelected = item;
    },

  },
}
</script>

<style lang="scss" scoped>
.select-box{
  position: relative;
  display: flex;
  align-items: center;
  padding: 10rpx;
  border-radius: 5rpx;
  .select-text{
    margin-left: 10rpx;
    font-size: 26rpx;
    display: inline-block;

  }
}
.select-text-btn{
  right: 10rpx;
  display: inline-block;
  margin-left: 10rpx;
  padding-left: 10rpx;
  padding-right: 10rpx;
  border-radius: 10rpx;
  cursor: pointer;
  border: 1px solid #E5E5E5;
  color: #666666;
}
</style>
