<view class="chat-scroll-view data-v-485ae7a2" style="{{'height:'+(height)+';'}}" id="chatScrollView"><block wx:if="{{$root.g0}}"><view style="position:absolute;z-index:1;width:100%;height:100%;top:0;left:0;background-color:#fff;" class="data-v-485ae7a2"><view style="position:absolute;z-index:1;width:100%;top:50%;text-align:center;transform:translateY(-50%);" class="data-v-485ae7a2"><image class="loading-icon-2 data-v-485ae7a2" mode="widthFix" src="{{$root.g1}}"></image></view></view></block><view id="{{'top_header_'+uuid}}" class="data-v-485ae7a2"><block wx:if="{{dialog&&dialog.userAccountId}}"><view data-event-opts="{{[['tap',[['userCardClick',[['o',['id',dialog.userAccountId]]]]]]]}}" class="top_header data-v-485ae7a2" style="padding:20rpx;box-sizing:border-box;" bindtap="__e"><block wx:if="{{!isCustomerService}}"><kt-user-info-card vue-id="79fae4b8-1" showAge="{{false}}" user-account-id="{{dialog.userAccountId}}" requestRemark="{{true}}" class="data-v-485ae7a2" bind:__l="__l"></kt-user-info-card></block><block wx:if="{{isCustomerService}}"><kt-user-info-card vue-id="79fae4b8-2" user-info="{{$root.a0}}" requestRemark="{{true}}" class="data-v-485ae7a2" bind:__l="__l"></kt-user-info-card></block></view></block></view><block wx:if="{{!loading}}"><scroll-view class="scroll-view data-v-485ae7a2 vue-ref" style="{{'height:'+('calc('+height+' - '+topHeaderHeight+'px)')+';'}}" refresher-enabled="{{refresherEnabled}}" scroll-y="{{true}}" scroll-into-view="{{scrollIntoViewId}}" data-ref="scrollView" data-event-opts="{{[['refresherrefresh',[['onRefresherrefresh']]],['scroll',[['onScroll',['$event']]]],['scrolltolower',[['onScrolltolower',['$event']]]],['scrolltoupper',[['onScrolltoupper',['$event']]]]]}}" bindrefresherrefresh="__e" bindscroll="__e" bindscrolltolower="__e" bindscrolltoupper="__e"><view class="data-v-485ae7a2"><view class="chat-list-container data-v-485ae7a2"><block wx:for="{{$root.l0}}" wx:for-item="item1" wx:for-index="index" wx:key="index"><view class="chat-list-item data-v-485ae7a2" style="{{'text-align:'+(item1.$orig.userAccountId==item1.g2.id?'right':'left')+';'}}"><view id="{{item1.m0+''}}" class="data-v-485ae7a2"></view><card-of-user-account generic:scoped-slots-messageItem="kt-chat-dialog-panel-card-of-user-account-messageItem" data-vue-generic="scoped" vue-id="{{'79fae4b8-3-'+index}}" copy="{{copy}}" has-read="{{hasRead}}" isAcceptRead="{{!unreadMap[item1.$orig.id+'']&&item1.$orig.id}}" message="{{item1.$orig}}" class="data-v-485ae7a2" bind:__l="__l" vue-slots="{{['messageItem']}}"></card-of-user-account></view></block></view><view style="height:150rpx;" class="data-v-485ae7a2"></view><view id="{{scrollIntoViewIdFinal}}" class="data-v-485ae7a2"></view></view></scroll-view></block></view>