@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-2d3d8b4e {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  font-size: 20px;
  color: #000000;
  padding: 20rpx;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
}
.title.data-v-2d3d8b4e {
  width: 100%;
  text-align: left;
  font-size: 30rpx;
  color: #000000;
  font-weight: bold;
  letter-spacing: 2rpx;
}
.grid-item.data-v-2d3d8b4e {
  display: inline-block;
  width: calc(25% - 20rpx);
  height: 150rpx;
  margin: 40rpx 10rpx 40rpx 10rpx;
  font-size: 25rpx;
  color: #666666;
  text-align: center;
  cursor: pointer;
}
.grid-item:active .grid-item-image.data-v-2d3d8b4e {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.grid-item-selected .grid-item-image.data-v-2d3d8b4e {
  box-sizing: border-box;
  background-color: rgba(118, 118, 118, 0.07);
  padding: 10rpx;
  border-radius: 20rpx;
}
.grid-item-image.data-v-2d3d8b4e {
  display: inline-block;
  width: 100rpx;
  height: 100rpx;
}
.grid-item-image .grid-item-image-image.data-v-2d3d8b4e {
  width: 100%;
  height: 100%;
}
.box-mode-device-pc.data-v-2d3d8b4e {
  border-radius: 20rpx;
}
.my-gold.data-v-2d3d8b4e {
  position: absolute;
  font-size: 25rpx;
  color: #000000;
  top: 20rpx;
  right: 20rpx;
}
.my-gold .my-gold-icon.data-v-2d3d8b4e {
  display: inline-block;
  width: 50rpx;
  height: 50rpx;
  margin-right: 10rpx;
}
.my-gold .my-gold-icon .my-gold-icon-image.data-v-2d3d8b4e {
  width: 100%;
  height: 100%;
}
.my-gold .my-gold-text.data-v-2d3d8b4e {
  display: inline-block;
  vertical-align: top;
  margin-top: 10rpx;
  font-weight: bold;
}
.recharge-btn.data-v-2d3d8b4e {
  width: 100%;
  height: 60rpx;
  color: #000000;
  text-align: center;
  line-height: 60rpx;
  font-size: 25rpx;
  border-radius: 10rpx;
  cursor: pointer;
}
.recharge-btn.data-v-2d3d8b4e:active {
  opacity: 0.5;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
