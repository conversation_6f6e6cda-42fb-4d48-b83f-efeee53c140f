<view class="data-v-5a7deb82"><kt-popup vue-id="f53e82b2-1" zIndex="{{zIndex}}" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-5a7deb82 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-5a7deb82',clazz.box]}}"><project-meet-register-panel vue-id="{{('f53e82b2-2')+','+('f53e82b2-1')}}" border-radius="{{$root.m0?'20rpx':'20rpx 20rpx 0 0'}}" data-event-opts="{{[['^registerSuccess',[['registerSuccess']]]]}}" bind:registerSuccess="__e" class="data-v-5a7deb82" bind:__l="__l"></project-meet-register-panel></view></kt-popup></view>