<template>
  <view class="box"
  :style="{
    'text-align': message.userAccountId+'' === self.id+'' ? 'right' : 'left',
  }"
  >
    <view
        v-if="message.userAccountId + '' !== self.id+''"
        class="avatar-box">
      <kt-avatar
          v-if="userInfo.fileIdOfAvatar"
          size="80rpx"
          :src="$kt.file.visit(userInfo.fileIdOfAvatar)"
      ></kt-avatar>
        <kt-avatar
            v-else
            size="80rpx"
            :src="$kt.file.byPath('/image/logo.png')"
        ></kt-avatar>

    </view>

    <view class="message-box">
      <view class="message-box-item">
        <view v-for="item in message.items">
          <view v-if="item.type==='userAccount:id'"
                class="user-item"
          @click="$kt.router.navTo('/pages/user-info/user-info',{'userAccountId':item.content})"
          >
            <kt-user-info-card
                :user-account-id="item.content"
            ></kt-user-info-card>
          </view>
          <view v-if="item.type==='image:id'">
            <image
                class="chat-list-item-content-img"
                :src="$kt.file.visit(item.content)"
                @click="previewImage($kt.file.visit(item.content))"
                mode="widthFix"
            ></image>
          </view>
          <view v-if="item.type==='video:id'">
            <video
                class="chat-list-item-content-img"
                :src="$kt.file.visit(item.content)"
                mode="widthFix"
                controls
            ></video>
          </view>
          <view
              style="position:relative;"
              :style="{
                paddingRight:!isSelf ? '70rpx' : '0',
              }"
              v-if="item.type==='text'">
            <view>
              <view
                  v-if="isSelf"
                  style="text-align: left;"
                  class="chat-list-item-content-language-box">
                <image
                    @click="$refs.ktLanguageSelectPopup.open()"
                    class="chat-list-item-content-language-icon"
                    :src="$kt.file.byPath('/kantboot/icon/language.svg')"
                ></image>
              </view>
              <text
                  :selectable="copy"
                  class="chat-list-item-content-text"
                :class="{
                    'no-user-select':!copy
                }"
              >{{ item.content }}</text>
              <view
                  v-if="!isSelf"
                  style="position: absolute;right: 0;top:0"
                  class="chat-list-item-content-language-box chat-list-item-content-language-box-no-self">
                <image
                    @click="$refs.ktLanguageSelectPopup.open()"
                    class="chat-list-item-content-language-icon"
                    :src="$kt.file.byPath('/kantboot/icon/language.svg')"
                ></image>
              </view>
            </view>
            <view
            v-if="item.translatedText"
            >
              <view class="split-line"></view>
              {{ item.translatedText }}
            </view>


          </view>
          <view>
            <slot name="messageItem"
                  :item="item"
                  :message="message"></slot>
          </view>
        </view>
      </view>

      <view
      v-if="hasRead"
      >
        <view
            v-if="isSelf"
            class="read-dot read-dot-self"
          :class="{
              'is-read-dot':isAcceptRead,
              'un-read-dot':!isAcceptRead
          }"
        >
        </view>

      </view>
    </view>

    <view
        v-if="message.userAccountId+''===self.id+''"
        class="avatar-box">
      <kt-avatar
          v-if="self.fileIdOfAvatar"
          size="80rpx"
          :src="$kt.file.visit(self.fileIdOfAvatar)"
      ></kt-avatar>
      <kt-avatar
          v-else
          size="80rpx"
          :src="$kt.file.byPath('/image/logo.png')"></kt-avatar>

      <view
          v-if="self.isSubAccount&&message.id"
          style="margin-top: 10rpx;cursor: pointer"
      @click="setDeletedStatus(true)"
      >{{$i18n.zhToGlobal("撤回")}}</view>

    </view>


    <kt-language-select-popup
    ref="ktLanguageSelectPopup"
    :reset="false"
    @select="selectLanguage"
    ></kt-language-select-popup>

  </view>
</template>

<script>

export default {
  props: {
    message: {
      type: Object,
      default: {}
    },
    isAcceptRead: {
      type: Boolean,
      default: false
    },
    hasRead:{
      type: Boolean,
      default: true
    },
    // 是否可复制
    copy: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      userInfo: {},
      self:{},
      isSelf:true
    }
  },
  watch: {
    message: {
      handler(val) {
        // if(this.message.id&&this.message.targetLanguageCode&&!this.message.translatedText) {
        //   this.selectLanguage({
        //     code: this.message.targetLanguageCode
        //   });
        // }
        if(this.message.id){
          for(let i=0;i<this.message.items.length;i++){
            if(this.message.items[i].type==='text'&&this.message.items[i].targetLanguageCode
                &&this.message.userAccountId+''===this.self.id+''
                &&!this.message.items[i].translatedText) {
              this.selectLanguage({
                code: this.message.items[i].targetLanguageCode
              });
            }
          }
        }

      },
      deep: true,
      immediate: true
    },
  },
  created() {
    this.self = this.$kt.userAccount.getSelf();
    this.isSelf = this.self.id == this.message.userAccountId;
    this.$kt.userAccount.getById(this.message.userAccountId).then(res => {
      this.userInfo = res;
      console.log(this.userInfo, "获取用户信息");
      try {
        // 重新渲染
        this.$forceUpdate();
      } catch (e) {
      }
    }).catch(err => {
      uni.showToast({
        title: err.errMsg,
        icon: "none"
      });
    });

    //   this.selectLanguage({
    //     code: this.message.targetLanguageCode
    // });


  },
  methods: {
    setDeletedStatus(status) {
      uni.showLoading({
        title: this.$i18n.zhToGlobal("正在撤回"),
        mask: true
      });
      this.$kt.request.post("/functional-chat-web/admin/chatDialogMessage/setDeletedStatus", {
        data: {
          messageId: this.message.id,
          deleted: status
        }
      }).then(res => {
        uni.hideLoading();
        try {
          // 重新渲染
          this.$forceUpdate();
        } catch (e) {
          console.error("Force update failed:", e);
        }
      }).catch(res => {
        uni.showToast({
          title: res.errMsg,
          icon: "none"
        });
        uni.hideLoading();
      })
    },
    selectLanguage(language) {
      this.$refs.ktLanguageSelectPopup.close();
      this.$kt.request.post("/functional-chat-web/dialogMessageItem/translate", {
        data:{
          messageItemId:this.message.items[0].id,
          targetLanguageCode:language.code
        }
      }).then(res => {
        this.message.items[0] = res.data;
        try {
          // 重新渲染
          this.$forceUpdate();
        } catch (e) {
          console.error("Force update failed:", e);
        }
      }).catch(res=>{
        // uni.showToast({
        //   title: res.errMsg,
        //   icon: "none"
        // });
        this.$kt.event.emit("translate:"+res.stateCode)
      })
    },
    // 预览图片
    previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
}

.message-box {
  position: relative;
  min-width: 100rpx;
  max-width: calc(100% - 100rpx - 100rpx);
  display: inline-block;
  padding: 20rpx;
  box-sizing: border-box;
  vertical-align: top;
  // 气泡框
  background-color: #FFFFFF;
  box-shadow: 0 10rpx 30rpx rgba(118, 118, 118, 0.05);
  border-radius: 20rpx;
  text-align: left;
}

.chat-list-item-content-text{
  word-wrap: break-word;
  //word-break: break-all;
  white-space: pre-wrap;


}

.avatar-box {
  width: 100rpx;
  display: inline-block;
}

.chat-list-item-content-img{
  width: 300rpx;
  border-radius: 10rpx;
}

.user-item:active{
  //transform: scale(0.97);
}

.chat-list-item-content-language-box{
  display: inline-block;
  text-align: right;
  margin-left: 12rpx;
  margin-right: 10rpx;
  margin-bottom: -20rpx;
  .chat-list-item-content-language-icon{
    cursor: pointer;
    width: 50rpx;
    height: 50rpx;
  }
}

.chat-list-item-content-language-box:active{
  opacity: .8;
}

.read-dot{
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  bottom: 10rpx;
  right: 10rpx;
  background-color: rgba(0,0,0,.2);
}

.read-dot-self{
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  bottom: 10rpx;
  left: 10rpx;
  background-color: #bbbbbb;
}

.split-line{
  width: 100%;
  height: 1rpx;
  background-color: #E5E5E5;
  margin: 10rpx 0;
}
.no-user-select{
  -webkit-user-select: none;
}

.un-read-dot{
  background-color: #bbbbbb;
}

.is-read-dot{
  // 绿色
  background-color: #4CAF50;
}
</style>
