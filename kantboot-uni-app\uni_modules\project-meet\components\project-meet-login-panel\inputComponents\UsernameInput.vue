<template>
  <view :class="clazz.loginInputBox">
    <view class="icon-box">

      <view
          @click="toChangPhoneAreaCode"
          class="icon">
        <image class="icon-img" :src="$kt.file.byPath('icon/username.svg')" mode="widthFix"></image>
      </view>

    </view>

    <view class="input-box">
      <input
          autofocus
          @blur="addUserLog"
          class="input"
          @input="inputInput"
          v-model="param.to"
          :adjust-position="false"
          :placeholder="$i18n.zhToGlobal('用户名')"
          type="text"></input>
    </view>
  </view>
</template>

<script>

export default {
  props: ["bodyData"],
  data() {
    return {
      clazz: {
        loginInputBox: this.$kt.style.toggleClass("login-input-box")
      },
      param: {
        phoneAreaCode: "86",
        to: '',
        methodCode: "phone"
      }
    };
  },
  created() {
    this.$emit("change", this.param);
  },
  methods: {
    addUserLog(){
      this.$emit("addUserLog");
    },
    isPhone(to) {
      if (!to || to.indexOf("@") === -1) {
        return true;
      }
      return false;
    },
    inputInput(e) {
      this.param.to = e.detail.value;
      if (this.isPhone(this.param.to)) {
        this.param.methodCode = "phone";
      } else {
        this.param.methodCode = "email";
      }
      this.$emit("change", this.param);
    },
    toChangPhoneAreaCode() {
      uni.showToast({
        title: this.$i18n.zhToGlobal("当前仅支持‘+86’"),
        icon: "none"
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../css/loginInput";
</style>
