<template>
  <view
  :class="clazz.box"
  >

<!--    <kt-box-->
<!--        v-if="false"-->
<!--        :title="$i18n.zhToGlobal('性别')">-->
<!--      <view class="gender">-->
<!--        <view class="gender-item"-->
<!--              :class="{-->
<!--                'gender-item-active': bodyData.genderCode === 'male'-->
<!--              }"-->
<!--              @click="bodyData.genderCode='male'"-->
<!--        >-->
<!--            <image-->
<!--                :src="$kt.file.byPath('/projectMeet/icon/male.png')"-->
<!--                mode="aspectFill"-->
<!--                class="gender-item-img"></image>-->
<!--            <view class="gender-item-text">{{$i18n.zhToGlobal("男")}}</view>-->
<!--        </view>-->
<!--        <view class="gender-item"-->
<!--              @click="bodyData.genderCode='female'"-->
<!--              :class="{-->
<!--                'gender-item-active': bodyData.genderCode === 'female'-->
<!--              }"-->
<!--        >-->
<!--          <image-->
<!--              :src="$kt.file.byPath('/projectMeet/icon/female.png')"-->
<!--              mode="aspectFill"-->
<!--              class="gender-item-img"-->
<!--          ></image>-->
<!--          <view class="gender-item-text">{{$i18n.zhToGlobal("女")}}</view>-->
<!--        </view>-->

<!--      </view>-->

<!--    </kt-box>-->
    <kt-box
        :title="$i18n.zhToGlobal('头像')">
      <kt-image-select
          v-if="$kt.style.detectDeviceType()==='pc'"
          count="1"
          v-model="fileIdsOfAvatar"
          :file-group-code="'userAvatar'"
      ></kt-image-select>
      <view
          v-else
          @click="selectAvatar()" style="display: inline-block;position: relative">
        <kt-avatar
            class="avatar"
            size="220rpx"
            :src="viewSrc?viewSrc:$kt.file.visit(bodyData.fileIdOfAvatar)"
        ></kt-avatar>
      </view>
    </kt-box>
    <kt-box
        :title="$i18n.zhToGlobal('生日')">
      <project-meet-birthday-setting
          v-model="bodyData.gmtBirthday"
      ref="projectMeetBirthdaySetting"></project-meet-birthday-setting>
    </kt-box>
    <kt-box
        :title="$i18n.zhToGlobal('体重')">
      <kt-menu
          :icon="$kt.file.byPath('icon/weight.svg')"
          @click="$refs.projectMeetWeightPopup.open()"
      :title="$i18n.zhToGlobal('体重')"
      :content="bodyData.weight || $i18n.zhToGlobal('未设置')"
      >
      </kt-menu>
    </kt-box>
    <kt-box
        :title="$i18n.zhToGlobal('身高')">
      <kt-menu
          :icon="$kt.file.byPath('icon/height.svg')"
          @click="$refs.projectMeetHeightPopup.open()"
          :title="$i18n.zhToGlobal('身高')"
          :content="bodyData.height || $i18n.zhToGlobal('未设置')"
      >
      </kt-menu>
    </kt-box>
    <kt-box
    :title="$i18n.zhToGlobal('国家')"
    >
      <kt-menu
          :icon="$kt.file.byPath('icon/earth.svg')"
          @click="$refs.projectMeetCountryPopup.open()"
          :title="$i18n.zhToGlobal('国家')"
          :content="$i18n.zhToGlobal(bodyData.countryCn) || $i18n.zhToGlobal('未设置')"
      >
      </kt-menu>
    </kt-box>

    <kt-box
        v-if="false"
    :title="$i18n.zhToGlobal('昵称')">
      <input
      class="input"
      v-model="bodyData.nickname"
      :placeholder="$i18n.zhToGlobal('输入昵称')"
      ></input>
    </kt-box>
    <kt-box
      :title="$i18n.zhToGlobal('问候语')">
      <textarea
      class="textarea"
      v-model="bodyData.introduction"
      :placeholder="$i18n.zhToGlobal('输入问候语')"
      ></textarea>
    </kt-box>
    <kt-box
    :title="$i18n.zhToGlobal('兴趣爱好')">
      <view class="select">
        <view
            v-for="(item, index) in interestsList"
            class="select-item"
          :class="{
            'select-item-active': isHasInterest(item.id)
          }"
            @click="selectInterest(item.id)"
        >{{item.name}}</view>
      </view>
    </kt-box>
    <kt-box
        :title="$i18n.zhToGlobal('期待的关系')">
      <view class="select">
        <view
            v-for="(item, index) in expectedRelationshipList"
            class="select-item"
        :class="{
            'select-item-active': isHasExpectedRelationship(item.id)
          }"
            @click="selectExpectedRelationship(item.id)"
        >{{item.name}}</view>
      </view>
    </kt-box>

    <kt-box
    v-if="hasSubmitButton"
    >
      <kt-button
          @click="submit()"
      ref="submitButton"
      >{{$i18n.zhToGlobal("确定")}}</kt-button>
    </kt-box>

    <project-meet-weight-popup
    ref="projectMeetWeightPopup"
    v-model="bodyData.weight"
    @input="bodyData.weight = $event"
    ></project-meet-weight-popup>

    <project-meet-height-popup
        ref="projectMeetHeightPopup"
        v-model="bodyData.height"
        @input="bodyData.height = $event"></project-meet-height-popup>

    <project-meet-country-popup
        ref="projectMeetCountryPopup"
        v-model="bodyData.countryCn"
        @input="bodyData.countryCn = $event"></project-meet-country-popup>


  </view>
</template>

<script>

import userAccount from "../../../kantboot/libs/userAccount";

export default {
  computed: {
    userAccount() {
      return userAccount
    }
  },
  props: {
    hasSubmitButton: {
      type: Boolean,
      default: true,
    },
    height: {
      type: String,
      default: '100%',
    },
  },
  data() {
    return {
      bodyData:{
        genderCode:"male",
        fileIdOfAvatar:"",
        nickname:"",
        gmtBirthday:"",
        gmtBirthdayStr:"2021-01-01",
        introduction:"",
        // 兴趣爱好的ids
        interestsIds:[],
        // 期待的关系的ids
        expectedRelationshipIds:[],
        // 体重
        weight: "",
        // 身高
        height: "",
        // 国家，保存的时候采用中文
        countryCn:""
      },
      fileIdsOfAvatar:[],
      clazz:{
        box:this.$kt.style.toggleClass("a-box"),
      },
      // 兴趣爱好
      interestsList:[],
      // 期待的关系
      expectedRelationshipList:[],
      viewSrc:'',
    };
  },
  watch: {
    fileIdsOfAvatar(val) {
      this.bodyData.fileIdOfAvatar = val[0] || '';
    },
    bodyData: {
      handler(val) {
        this.$emit('change', val);
      },
      deep: true,
    },

  },
  created() {
    this.getInterestsList();
    this.getExpectedRelationshipList();
  },
  methods: {
    birthdayChange(e){
      console.log(e,"ppp");
      this.bodyData.gmtBirthdayStr = e;
      this.bodyData.gmtBirthday = new Date(this.bodyData.gmtBirthdayStr).getTime();
      // 重新渲染
      try {
        this.$forceUpdate();
      } catch (e) {
        console.error(e);
      }
    },
    selectAvatar(){
      uni.chooseImage({
        count: 1, //默认9
        success: (res) => {
          // console.log(res);
          // this.imgList = res.tempFilePaths;
          // console.log(this.imgList);
          console.log(res.tempFilePaths[0]);
          this.$kt.image.toImageClip(res.tempFilePaths[0], 300, 300).then(res => {
            this.viewSrc = res;
            this.uploadAvatar(this.viewSrc)
          }).catch(err => {
          });

        }
      })
    },
    async uploadAvatar(filePath){
      setTimeout(()=>{
        this.$refs.submitButton.loading(null, 999999);
      },1);
      new Promise((resolve, reject)=>{
        this.$request.uploadFile({
          data:{
            file: filePath,
            groupCode: "fp",
          },
          stateSuccess: (res1) => {
            this.bodyData.fileIdOfAvatar = res1.data.id;
            this.$emit("change",this.bodyData)
            try{
              this.$forceUpdate();
            }catch (e) {

            }
            this.$refs.submitButton.toNone();
            resolve(res1.data.id);
          },
          stateFail: (err) => {
            this.$refs.submitButton.toNone();

            reject(err);

          },
        });
      })
    },
    setBodyData(val) {
      this.bodyData = val;
      if(this.bodyData.fileIdOfAvatar){
         this.fileIdsOfAvatar = [this.bodyData.fileIdOfAvatar];
      }
      if(this.bodyData.gmtBirthday){
        this.bodyData.gmtBirthdayStr = this.$kt.date.format(this.bodyData.gmtBirthday, 'yyyy-MM-dd');
      }
      // 重新渲染
      try {
        this.$forceUpdate();
      } catch (e) {
        console.error(e);
      }
    },
    async submit(){
      this.$refs.submitButton.loading();
      this.$kt.request.post('/project-meet-web/userAccount/initUserInfo',{
        data:{
          id: this.$kt.userAccount.getSelf().id,
          ...this.requestParams
        }
      }).then(res => {
        alert(res.id);
        this.$kt.userAccount.requestSelf();
        this.$refs.submitButton.success();
        this.$emit('submitSuccess');
      }).catch(err => {
        console.log(err);
      });
    },
    getInterestsList() {
      this.$kt.request.post('/project-meet-web/interests/getAll').then(res => {
        this.interestsList = res.data;
        try{
          this.$forceUpdate();
        }catch (e) {
        }

      }).catch(err => {
        console.log(err);
      });
    },
    getExpectedRelationshipList() {
      this.$kt.request.post('/project-meet-web/expectedRelationship/getAll').then(res => {
        this.expectedRelationshipList = res.data;
        try{
          this.$forceUpdate();
        }catch (e) {
        }
      }).catch(err => {

      })
    },
    isHasInterest(id) {
      if (!this.bodyData.interestsIds) {
        this.bodyData.interestsIds = [];
      }
      for (let i = 0; i < this.bodyData.interestsIds.length; i++) {
        if (this.bodyData.interestsIds[i] === id) {
          return true;
        }
      }

      return false;

    },
    isHasExpectedRelationship(id) {
      if (!this.bodyData.expectedRelationshipIds) {
        this.bodyData.expectedRelationshipIds = [];
      }
      for (let i = 0; i < this.bodyData.expectedRelationshipIds.length; i++) {
        if (this.bodyData.expectedRelationshipIds[i] === id) {
          return true;
        }
      }
      return false;
    },
    selectInterest(id) {
      if (this.isHasInterest(id)) {
        // this.bodyData.interestsIds = this.bodyData.interestsIds.filter(item => item !== id);
        // 删除掉选择的
        for (let i = 0; i < this.bodyData.interestsIds.length; i++) {
          if (this.bodyData.interestsIds[i] === id) {
            this.bodyData.interestsIds.splice(i, 1);
            break;
          }
        }
      } else {
        this.bodyData.interestsIds.push(id);
      }
    },
    selectExpectedRelationship(id) {
      // 只允许一个
      if (this.bodyData.expectedRelationshipIds.length > 0) {
        this.bodyData.expectedRelationshipIds = [];
      }

      if (this.isHasExpectedRelationship(id)) {
        // this.bodyData.expectedRelationshipIds = this.bodyData.expectedRelationshipIds.filter(item => item !== id);
        // 删除掉选择的
        for (let i = 0; i < this.bodyData.expectedRelationshipIds.length; i++) {
          if (this.bodyData.expectedRelationshipIds[i] === id) {
            this.bodyData.expectedRelationshipIds.splice(i, 1);
            break;
          }
        }
      } else {
        this.bodyData.expectedRelationshipIds.push(id);
      }
    },

  },
};
</script>

<style lang="scss" scoped>
.a-box{
  // 超出滚动
  //overflow-y: scroll;
}

//.a-box::-webkit-scrollbar{
//  display: none;
//}

.gender{
  display: flex;
  align-items: center;
  .gender-item{
    text-align: center;
    margin-right: 40rpx;
    opacity: .5;
    .gender-item-img{
      width: 200rpx;
      height: 200rpx;
    }
    .gender-item-text{
      font-size: 30rpx;
    }
  }

  .gender-item-active{
    opacity: 1;
  }

}

.input{
  width: 100%;
  height: 80rpx;
  border-radius: 10rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
  box-sizing: border-box;
}

.textarea{
  width: 100%;
  height: 200rpx;
  border-radius: 10rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
  box-sizing: border-box;
}

.a-box-mode-device-pc{
  .gender{
    .gender-item{
      cursor: pointer;
    }
  }
}

.select{
  .select-item{
    display: inline-block;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    padding: 10rpx 20rpx;
    border-radius: 10rpx;
    font-size: 28rpx;
    background-color: #f9f9f9;
    cursor: pointer;
  }
  .select-item-active{
    background-color: #333333;
    color: #ffffff;
  }
}
</style>
