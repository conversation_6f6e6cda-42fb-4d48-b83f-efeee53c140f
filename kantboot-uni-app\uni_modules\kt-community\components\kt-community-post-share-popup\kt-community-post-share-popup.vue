<template>
  <view>
    <kt-popup ref="ktPopup" @click.stop="none()">
      <view v-if="bindParams.postId" class="box">
        <view class="title">
          {{ $i18n.zhToGlobal("分享帖子") }}
        </view>

        <view class="input-box">
          <textarea @click.stop="none()" ref="input" class="input" v-model="bindParams.text"
            @input="bindParams.text = $event.detail.value" :placeholder="$i18n.zhToGlobal('输入分享内容')"></textarea>
        </view>
        <view style="height: 20rpx"></view>

        <view>
          <kt-community-post-card :post-id="bindParams.postId" :has-bottom-operation="false"
            :has-dot="false"></kt-community-post-card>
        </view>
        <view style="height: 20rpx"></view>
        <view>
          <kt-button @click="push()" ref="confirmButton">{{ $i18n.zhToGlobal("确定分享") }}</kt-button>
        </view>
        <view style="height: 30rpx"></view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
import event from "@/uni_modules/kt-community/libs/event";
export default {
  data() {
    return {
      postId: "",
      bindParams: {
        text: "",
        postId: ""
      },
      event: event,
    };
  },
  mounted() {

  },
  methods: {
    none() { },
    open(postId) {
      this.bindParams.postId = postId;
      this.$refs.ktPopup.open();
    },
    close() {
      this.$refs.ktPopup.close();
    },
    // /fp-community-web/post/push
    push() {
      let items = [];
      if (this.bindParams.text) {
        items.push({
          type: "text",
          content: this.bindParams.text
        })
      }
      if (this.bindParams.postId) {
        items.push({
          type: "postShare",
          content: this.bindParams.postId
        })
      }
      this.$refs.confirmButton.loading(null, 99999);
      // /fp-community-web/post/push
      this.$request.post("/fp-community-web/post/push", {
        data: {
          items: items
        }
      }).then(res => {
        this.$refs.confirmButton.success(res.msg);
        this.event.emitPostPush(res.data);
        setTimeout(() => {
          this.close();
        }, 100);
      })

    }
  },
}
</script>

<style lang="scss" scoped>
.box {
  padding: 20rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 20rpx 20rpx 0 0;

  .title {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .input-box {
    .input {
      width: 100%;
      height: 200rpx;
      border-radius: 10rpx;
      background-color: #f9f9f9;
      padding: 20rpx;
      box-sizing: border-box;
      font-size: 28rpx;
      color: #333;
    }
  }

}
</style>
