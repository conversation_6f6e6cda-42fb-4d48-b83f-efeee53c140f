@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.card.data-v-0a67bad5 {
  padding: 30rpx 30rpx 30rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.05);
  background: linear-gradient(to bottom, white, rgba(255, 255, 255, 0.2) 100%);
}
.card .bg-img.data-v-0a67bad5 {
  display: none;
}
.card .bg-img-2.data-v-0a67bad5 {
  display: none;
}
.card-header.data-v-0a67bad5 {
  color: #333333;
  position: relative;
  z-index: 1;
  font-size: 27rpx;
  letter-spacing: 2rpx;
  font-weight: bold;
}
.card-header .card-header-change.data-v-0a67bad5 {
  float: right;
  display: inline-block;
  font-size: 25rpx;
  font-weight: lighter;
  color: #000000;
}
.card-header .card-header-change .change-icon.data-v-0a67bad5 {
  display: inline-block;
}
.card-header .card-header-change .change-icon-loading.data-v-0a67bad5 {
  -webkit-animation: change-icon-loading-in-meet-user-match-card-data-v-0a67bad5 1.5s infinite;
          animation: change-icon-loading-in-meet-user-match-card-data-v-0a67bad5 1.5s infinite;
}
@-webkit-keyframes change-icon-loading-in-meet-user-match-card-data-v-0a67bad5 {
0% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    opacity: 1;
    -webkit-transform: rotate(750deg);
            transform: rotate(750deg);
}
}
@keyframes change-icon-loading-in-meet-user-match-card-data-v-0a67bad5 {
0% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    opacity: 1;
    -webkit-transform: rotate(750deg);
            transform: rotate(750deg);
}
}
.card-content.data-v-0a67bad5 {
  position: relative;
  z-index: 2;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.card-content-item.data-v-0a67bad5 {
  display: inline-block;
}
.card-content-item-loading.data-v-0a67bad5 {
  -webkit-transform: rotateY(90deg);
          transform: rotateY(90deg);
  border-radius: 20rpx;
  -webkit-filter: blur(0px);
          filter: blur(0px);
  -webkit-animation: card-content-item-loading-in-meet-user-match-card-data-v-0a67bad5 1s;
          animation: card-content-item-loading-in-meet-user-match-card-data-v-0a67bad5 1s;
}
@-webkit-keyframes card-content-item-loading-in-meet-user-match-card-data-v-0a67bad5 {
0% {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg);
}
50% {
    opacity: 0.3;
    background-color: rgba(0, 0, 0, 0.3);
    -webkit-transform: rotateY(50deg);
            transform: rotateY(50deg);
}
100% {
    opacity: 0.7;
    background-color: rgba(0, 0, 0, 0.1);
    -webkit-transform: rotateY(85deg);
            transform: rotateY(85deg);
}
}
@keyframes card-content-item-loading-in-meet-user-match-card-data-v-0a67bad5 {
0% {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg);
}
50% {
    opacity: 0.3;
    background-color: rgba(0, 0, 0, 0.3);
    -webkit-transform: rotateY(50deg);
            transform: rotateY(50deg);
}
100% {
    opacity: 0.7;
    background-color: rgba(0, 0, 0, 0.1);
    -webkit-transform: rotateY(85deg);
            transform: rotateY(85deg);
}
}
.card-content-item-loading-finish.data-v-0a67bad5 {
  -webkit-transform: rotateY(0deg);
          transform: rotateY(0deg);
  border-radius: 20rpx;
  -webkit-animation: card-content-item-loading-finish-in-meet-user-match-card-data-v-0a67bad5 1s;
          animation: card-content-item-loading-finish-in-meet-user-match-card-data-v-0a67bad5 1s;
}
@-webkit-keyframes card-content-item-loading-finish-in-meet-user-match-card-data-v-0a67bad5 {
0% {
    background-color: rgba(0, 0, 0, 0.5);
    -webkit-transform: rotateY(85deg);
            transform: rotateY(85deg);
}
100% {
    background-color: rgba(0, 0, 0, 0);
    -webkit-transform: rotateY(-360deg);
            transform: rotateY(-360deg);
}
}
@keyframes card-content-item-loading-finish-in-meet-user-match-card-data-v-0a67bad5 {
0% {
    background-color: rgba(0, 0, 0, 0.5);
    -webkit-transform: rotateY(85deg);
            transform: rotateY(85deg);
}
100% {
    background-color: rgba(0, 0, 0, 0);
    -webkit-transform: rotateY(-360deg);
            transform: rotateY(-360deg);
}
}
.card-btn-box.data-v-0a67bad5 {
  text-align: center;
}
.card-btn.data-v-0a67bad5 {
  position: relative;
  z-index: 2;
  display: inline-block;
  border-radius: 40rpx;
  color: #FFFFFF;
  text-align: center;
  padding: 10rpx 50rpx 10rpx 50rpx;
  background-color: #5A7EF6;
}
.card-btn.data-v-0a67bad5:active {
  -webkit-transform: scale(0.99);
          transform: scale(0.99);
}
.card-mode-device-pc.data-v-0a67bad5 {
  position: relative;
  margin-top: -20rpx;
  background: linear-gradient(to bottom, white, rgba(255, 255, 255, 0.9) 100%);
}
.card-mode-device-pc .card-btn.data-v-0a67bad5 {
  cursor: pointer;
}
.card-mode-device-pc .card-header.data-v-0a67bad5 {
  color: #FFFFFF;
  text-shadow: 0 3rpx 5rpx rgba(0, 0, 0, 0.5);
}
.card-mode-device-pc .card-header .card-header-change.data-v-0a67bad5 {
  cursor: pointer;
  color: #666666;
  text-shadow: none;
}
.card-mode-device-pc .bg-img.data-v-0a67bad5 {
  opacity: 0.8;
  position: absolute;
  display: block;
  z-index: 0;
  width: 100%;
  left: 0;
  top: 0;
  border-radius: 20rpx;
}
.card-mode-device-pc .bg-img-2.data-v-0a67bad5 {
  opacity: 0.5;
  position: absolute;
  display: block;
  z-index: 0;
  width: 100%;
  height: 150%;
  left: 0;
  bottom: 0;
  border-radius: 20rpx;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.card-mode-device-pc .card-btn.data-v-0a67bad5 {
  width: 40%;
  padding: 20rpx;
  box-sizing: border-box;
}
.card-tips.data-v-0a67bad5 {
  color: #333333;
  text-align: center;
  font-size: 25rpx;
}
.loading-icon.data-v-0a67bad5 {
  width: 45rpx;
  height: 45rpx;
  -webkit-filter: invert(1);
          filter: invert(1);
  -webkit-animation: loading-icon-in-meet-user-match-card-data-v-0a67bad5 1s infinite;
          animation: loading-icon-in-meet-user-match-card-data-v-0a67bad5 1s infinite;
}
@-webkit-keyframes loading-icon-in-meet-user-match-card-data-v-0a67bad5 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes loading-icon-in-meet-user-match-card-data-v-0a67bad5 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
