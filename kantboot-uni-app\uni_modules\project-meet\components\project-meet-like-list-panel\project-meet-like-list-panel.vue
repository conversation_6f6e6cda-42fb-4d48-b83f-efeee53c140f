<template>
  <view :class="clazz.box">

    <view class="box-title">{{$i18n.zhToGlobal("点赞列表")}}</view>

    <view v-if="list.length === 0"
          class="no-data"
    >
      <view>
        <image
            class="no-data-image"
            :src="$kt.file.byPath('icon/book.svg')"
        ></image>
      </view>
      <view
          class="no-data-text"
      >{{ $i18n.zhToGlobal("空空如也") }}
      </view>

    </view>

    <view
    v-for="item in list"
    >
      <view class="list-item">

        <view
        >
          <kt-user-info-card
          :user-info="item.userAccount"
          ></kt-user-info-card>
        </view>

        <view>
          <kt-community-post-card
              :post="item.post"
              :has-dot="false"
              :has-bottom-operation="false">
          </kt-community-post-card>
        </view>
        <view class="like-item-time">
          <view class="list-item-time-time">
            {{$i18n.zhToGlobal("点赞时间")}}{{": "}}{{$kt.date.format(item.gmtCreate,"yyyy-MM-dd hh:mm:ss")}}
          </view>
        </view>

      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box")
      },
      list: [],
    };
  },
  mounted() {
    this.getAllBySelf();
  },
  methods: {
    getAllBySelf(){
      this.$kt.request.post("/fp-community-web/postLike/getAllBySelf",{
        data:{
        }
      }).then(res=>{
        this.list = res.data;
      }).catch(err => {
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.box{
  padding: 20rpx;
  .box-title{
    font-weight: bold;
    font-size: 32rpx;
    margin-bottom: 20rpx;
  }
}

.list-item{
  padding: 20rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eeeeee;
}
.like-item-time{
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
  text-align: center;
  .list-item-time-time{
    display: inline-block;
    padding: 10rpx;
  }
}

.box-mode-device-pc{
  position: fixed;
  width: 600px;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-radius: 20rpx;
  top:50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.no-data {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 50rpx;

  .no-data-image {
    width: 100rpx;
    height: 100rpx;
    opacity: .6;
  }
}

</style>
