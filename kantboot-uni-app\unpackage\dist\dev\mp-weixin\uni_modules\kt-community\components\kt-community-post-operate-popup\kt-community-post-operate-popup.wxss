@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-f2db256e {
  position: fixed;
  width: 100%;
  background-color: #FFFFFF;
  left: 0;
  bottom: 0;
  padding: 20rpx 20rpx 20rpx 20rpx;
  box-sizing: border-box;
  border-radius: 30rpx 30rpx 0 0;
}
.box .btn.data-v-f2db256e {
  border-radius: 10rpx;
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 30rpx;
  color: #333333;
  border: 1px solid #E5E5E5;
  margin-bottom: 30rpx;
}
.box .report-scroll-view.data-v-f2db256e {
  height: calc(100vh - 500rpx);
}
.menu-box.data-v-f2db256e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.menu-box .menu-title.data-v-f2db256e {
  font-size: 28rpx;
  color: #333;
  vertical-align: top;
}
.menu-box .menu-title .menu-icon.data-v-f2db256e {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  display: inline-block;
  vertical-align: top;
}
.menu-box .menu-title .menu-icon .menu-icon-image.data-v-f2db256e {
  width: 100%;
  height: 100%;
  opacity: 0.8;
}
.menu-box .menu-fixed.data-v-f2db256e {
  position: relative;
}
.menu-box .menu-fixed .menu-fixed-tips.data-v-f2db256e {
  position: absolute;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  width: 300rpx;
  right: 70rpx;
  vertical-align: top;
  margin-right: 5rpx;
}
.menu-box .menu-fixed .menu-fixed-content.data-v-f2db256e {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}
.menu-box .menu-fixed .menu-fixed-content .menu-fixed-content-icon.data-v-f2db256e {
  width: 20rpx;
  height: 20rpx;
}
.menu-box .menu-fixed .menu-fixed-content .menu-fixed-content-icon-bottom.data-v-f2db256e {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.menu-box .menu-fixed .menu-fixed-content .menu-fixed-content-icon-top.data-v-f2db256e {
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
}
.menu-box.data-v-f2db256e:active {
  opacity: 0.8;
}
.menu-box-box.data-v-f2db256e {
  padding: 0 20rpx 20rpx 20rpx;
}
.to-back-btn.data-v-f2db256e {
  font-size: 28rpx;
  color: #666;
  cursor: pointer;
}
.box-mode-device-pc.data-v-f2db256e {
  position: fixed;
  border-radius: 20rpx;
  width: 400px;
  left: 50%;
  bottom: 50%;
  -webkit-transform: translate(-50%, 50%);
          transform: translate(-50%, 50%);
}
.box-mode-device-pc .menu-box.data-v-f2db256e {
  cursor: pointer;
}
.box-mode-device-pc .report-scroll-view.data-v-f2db256e {
  max-height: 500px;
}
