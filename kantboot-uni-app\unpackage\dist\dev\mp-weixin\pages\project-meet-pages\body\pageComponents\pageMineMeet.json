{"component": true, "usingComponents": {"kt-nav-bar": "/uni_modules/kantboot/components/kt-nav-bar/kt-nav-bar", "kt-user-info-card": "/uni_modules/kantboot/components/kt-user-info-card/kt-user-info-card", "project-meet-user-account-number-grid": "/uni_modules/project-meet/components/project-meet-user-account-number-grid/project-meet-user-account-number-grid", "kt-no-login": "/uni_modules/kantboot/components/kt-no-login/kt-no-login", "kt-setting-popup": "/uni_modules/kantboot/components/kt-setting-popup/kt-setting-popup", "project-meet-user-match-popup": "/uni_modules/project-meet/components/project-meet-user-match-popup/project-meet-user-match-popup", "project-meet-album-popup": "/uni_modules/project-meet/components/project-meet-album-popup/project-meet-album-popup", "project-meet-user-info-popup": "/uni_modules/project-meet/components/project-meet-user-info-popup/project-meet-user-info-popup", "project-meet-vip-popup": "/uni_modules/project-meet/components/project-meet-vip-popup/project-meet-vip-popup", "project-meet-gold-popup": "/uni_modules/project-meet/components/project-meet-gold-popup/project-meet-gold-popup", "project-meet-points-popup": "/uni_modules/project-meet/components/project-meet-points-popup/project-meet-points-popup", "user-info-popup": "/pages/project-meet-pages/body/components/user-info-popup", "user-info-card-edit": "/pages/project-meet-pages/body/components/user-info-card-edit", "user-info-card-setting": "/pages/project-meet-pages/body/components/user-info-card-setting", "user-info-self-introduction": "/pages/project-meet-pages/body/components/user-info-self-introduction", "interests-edit-popup": "/pages/project-meet-pages/body/components/interests-edit-popup", "expected-relationship-popup": "/pages/project-meet-pages/body/components/expected-relationship-popup"}}