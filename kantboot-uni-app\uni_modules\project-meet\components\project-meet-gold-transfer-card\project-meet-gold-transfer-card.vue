<template>
  <view class="box">
    <view>
      <image
          class="icon"
          mode="widthFix"
          :src="$kt.file.byPath('projectMeet/icon/gold.png')">
      </image>
    </view>
    <view class="text">
      {{$i18n.zhToGlobal("转赠金币")}}{{":"}} {{number}}
    </view>
  </view>
</template>

<script>
export default {
  props:{
    number:{
      type:Number,
      default:0
    }
  },
  data() {
    return {};
  }
}
</script>

<style lang="scss" scoped>
.box{
  text-align: center;
}
.icon{
  width: 100rpx;
  height: 100rpx;
}
.text{
  color: #666666;
  font-size: 24rpx;
}
</style>
