<template>
  <view :class="clazz.container">
    <view :class="clazz.bg">
      <image
          v-if="$kt.style.detectDeviceType()==='mobile'"
          class="bg-image"
          mode="aspectFill"
          :src="$kt.file.byPath('meet/bg.png')"
      ></image>
      <image
          v-if="$kt.style.detectDeviceType()==='mobile'"
          class="bg-image-2"
          mode="aspectFill"
          :src="$kt.file.byPath('meet/bg.png')"
      ></image>
      <image
          v-if="$kt.style.detectDeviceType()==='mobile'"
          class="bg-image-3"
          mode="aspectFill"
          :src="$kt.file.byPath('meet/bg.png')"
      ></image>

      <view class="bg-bg"></view>

    </view>
    <view id="pageHomeHeader" style="width: 100%;top:0;left:0;">

      <view>
        <kt-status-bar-height
            :background-color="'rgba(0,0,0,0)'"
        ></kt-status-bar-height>
      </view>
      <view>
        <view
            class="split"
        ></view>

        <view class="second-box" style="position: relative;padding: 11rpx;">
          <view class="second-box-box">
            <view class="second-box-item"
                  :class="{
            'second-box-item-selected': selected === 'friend'
          }"
                  @click="selected = 'friend'"
            >{{ $i18n.zhToGlobal('公开动态') }}
            </view>
            <view class="second-box-item"
                  :class="{
            'second-box-item-selected': selected === 'privateFriend'
          }"
                  @click="selected = 'privateFriend'"
            >{{ $i18n.zhToGlobal('我的关注') }}
            </view>
          </view>
          <image
              v-if="false"
              style="width: 40rpx;height: 40rpx;position: absolute;top:50%;transform: translateY(-50%);right: 50rpx;"
              class="second-icon"
              :src="$kt.file.byPath('icon/search.svg')"
          ></image>

        </view>
      </view>
    </view>
    <view class="community-post-box">
      <swiper
          :style="{
        height: isY()?'calc(100vh - '+pageHomeHeaderHeight+'px - 50px)':'calc(100vh - '+pageHomeHeaderHeight+'px - 100rpx)'
      }"
          @change="swiperChange"
          :current-item-id="selected"
      >
        <swiper-item
            item-id="friend">
          <project-meet-post-panel
              @cardUserClick="toUserInfoPage"
              :isForbidForward="false"
              :is-forbid-collect="false"
              :card-background="cardBackground"
              @cardClick="cardClick"
              @cardDotClick="cardDotClick"
              ref="communityPostPanel"
              :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px - 50px)':'calc(100vh - '+pageHomeHeaderHeight+'px - 100rpx)'">
          </project-meet-post-panel>
        </swiper-item>
        <swiper-item
            item-id="privateFriend">
          <project-meet-post-panel
              mode="follow"
              @cardUserClick="toUserInfoPage"
              :card-background="cardBackground"
              @cardClick="cardClick"
              @cardDotClick="cardDotClick"
              ref="communityPostPanel"
              :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px - 50px)':'calc(100vh - '+pageHomeHeaderHeight+'px - 100rpx)'">
          </project-meet-post-panel>
        </swiper-item>
      </swiper>

      <view
          v-if="$kt.style.detectDeviceType() === 'mobile'"
          @click="toPush"
          class="push-box">
        {{$i18n.zhToGlobal("+")}}
      </view>

      <kt-community-post-operate-popup
          ref="communityPostOperatePopup">
      </kt-community-post-operate-popup>

    </view>

    <community-post-popup ref="communityPostPopup">
    </community-post-popup>

    <project-meet-post-detail-popup
        height="calc(100vh - 500rpx)"
    ref="ktCommunityPostDetailPopup"
    ></project-meet-post-detail-popup>

    <project-meet-user-info-popup
        ref="usPopup"
    ></project-meet-user-info-popup>


  </view>
</template>

<script>


export default {
  data() {
    return {
      navHeight: 48,
      menuButtonWidth: 0,
      pageHomeHeaderHeight: 0,
      clazz: {
        container: this.$kt.style.toggleClass("container"),
        bg: this.$kt.style.toggleClass("bg"),
      },
      selected: 'friend',
      cardBackground: 'rgba(255,255,255,.5)'
    }
  },
  mounted() {
    // #ifdef MP-WEIXIN
    this.navHeight = uni.getSystemInfoSync().system.indexOf("ios") != -1 ? 44 : 48;
    // 获取胶囊信息
    this.menuButtonWidth = wx.getMenuButtonBoundingClientRect().width;
    console.log(this.menuButtonWidth, "获取胶囊信息");
    // #endif
    // 获取pageHomeHeader的高度
    this.$nextTick(() => {
      // uniapp的获取元素高度的方法
      uni.createSelectorQuery().in(this).select('#pageHomeHeader').boundingClientRect((rect) => {
        console.log(rect, "获取pageHomeHeader的高度")
        this.pageHomeHeaderHeight = rect.height;
      }).exec()
    });
    this.$kt.event.on('changeTabbar', () => {
      console.log("获取#pageHomeHeader的高度");
      this.$nextTick(() => {
        uni.createSelectorQuery()
            .in(this)
            .select("#pageHomeHeader")
            .boundingClientRect((res) => {
              console.log(res, "pageHomeHeader");
              this.pageHomeHeaderHeight = res.height;
            })
            .exec();
      });
    });
  },

  methods: {
    toUserInfoPage(userAccount) {
      if (this.isY()) {
        this.$refs.usPopup.open(userAccount.id);
        // this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info");
      } else {
        this.$kt.router.navTo('/pages/project-meet-pages/user-info/user-info', {'userAccountId': userAccount.id})
      }
    },
    cardDotClick(post){
      console.log(post,"post");
      if(!this.$kt.userAccount.getIsLogin()){
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.$refs.communityPostOperatePopup.open(post);
    },
    cardClick(post){
      if(this.$kt.style.detectDeviceType() === 'mobile') {
        this.$kt.router.navTo("/pages/project-meet-pages/post-detail/post-detail",{
          postId: post.id
        });
        return;
      }
      this.$refs.ktCommunityPostDetailPopup.open(post.id);
    },
    toPostPopup(post) {
      this.$refs.communityPostPopup.open(post);
    },
    swiperChange(e){
      this.selected = e.target.currentItemId;
    },
    isY() {
      // 转换为大写
      let deviceType = this.$kt.style.detectDeviceType().toUpperCase();
      return deviceType === 'PC' ||
          deviceType === 'TV';

    },
    toPush() {
      this.$kt.router.navTo("/pages/project-meet-pages/post-push/post-push");
    }
  }
}
</script>

<style lang="scss" scoped>
.header-box-1 {
  padding: 0 10rpx;
  box-sizing: border-box;
  // 不换行
  white-space: nowrap;

  .header-item {
    display: inline-block;
    font-size: 36rpx;
    letter-spacing: 3rpx;
    margin-right: 5rpx;
    padding: 10rpx 20rpx;
  }

  .header-item-selected {
    font-weight: bold;
  }
}

.scroll-view {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);

  // 超出滚动
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;
  // 隐藏滚动条
  &::-webkit-scrollbar {
    width: 0;
    height: 1rpx;
    display: none;
  }

  // 滚动
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

}

// 不显示滚动条
::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}

.second-box {
  width: 100%;

  .second-box-box {
    width: calc(100% - 100rpx);
    // 不换行
    white-space: nowrap;
    // 超出滚动
    overflow-x: auto;

    .second-box-item {
      display: inline-block;
      font-size: 32rpx;
      letter-spacing: 3rpx;
      margin-right: 5rpx;
      padding: 10rpx 20rpx;
      border-radius: 10rpx;
    }

    .second-box-item-selected {
      font-weight: bold;
    }
  }
}

.container-mode-device-pc {
  width: calc(100% - 240px - 400px);
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
  .community-post-box{
    width: 700px;
    margin-left: 150px;
  }
  .second-box-item{
    cursor: pointer;
  }
}

.split {
  width: 100%;
  height: 3rpx;
  background-color: #eee;
}

.container-mode-color-scheme-dark {
  background-color: #191919;

  .split{
    background-color: #888888;
  }

  .header-box {
    background-color: #191919;
  }

  .scroll-view {
    background-color: #191919;
  }

  .second-icon {
    // 颜色反转
    filter: invert(1);
  }

  .header-box-1 {
    background-color: #191919;

    .header-item {
      color: #fff;
    }
  }

  .second-box {
    .second-box-box {
      .second-box-item {
        color: #fff;
      }

      .second-box-item-selected {
        color: #fff;
        font-weight: bold;
      }
    }
  }
}

.container-mode-device-pc {
  position: relative;
  width: calc(100% + 20px);
  padding: 0;
  box-sizing: border-box;

  .second-icon {
    margin-right: 10px;
  }
}


.bg {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  //background-color: #f9f9f9;
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);

  .bg-image {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    right: 0;
    z-index: -2;
  }

  .bg-image-2 {
    position: fixed;
    width: 100vw;
    height: 100vh;
    bottom: 25vh;
    right: 0;
    // 上下翻转
    transform: scaleY(-1);
    z-index: -2;
  }

  .bg-image-3 {
    position: fixed;
    width: 100vw;
    height: 100vh;
    bottom: 0;
    right: 0;
    z-index: -2;
    // 上下翻转
    transform: scaleY(-1);
  }

  .bg-bg {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    right: 0;
    z-index: -1;
    // 渐变，从上到下
    background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0) 100rpx,
            rgba(255, 255, 255, 1) 100%
    );
  }
}

.bg-mode-color-scheme-light {
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}

.bg-mode-color-scheme-dark {
  background: #191919;
}

.push-box{
  position: fixed;
  bottom: 180rpx;
  right: 20rpx;
  font-size: 60rpx;
  color: #ffffff;
  width: 70rpx;
  height: 70rpx;
  text-align: center;
  line-height: 70rpx;
  border-radius: 50%;
  background: linear-gradient(180deg, #1383ff 0%, #e7e1ff 100%);
  text-shadow: 0 0 5rpx rgba(0, 0, 0, 0.2);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.push-box:active{
  transform: scale(.96);
}

</style>