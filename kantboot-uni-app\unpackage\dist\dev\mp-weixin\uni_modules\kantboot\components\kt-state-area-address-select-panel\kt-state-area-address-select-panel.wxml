<view class="data-v-32311d3c"><view class="{{['data-v-32311d3c',clazz.box]}}"><view class="header data-v-32311d3c"><block wx:if="{{stateAreaSelected.code}}"><view class="select-box data-v-32311d3c"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="select-text data-v-32311d3c" bindtap="__e">{{stateAreaSelected.name}}</view><block wx:if="{{stateAreaAddressSelected.code}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="select-text data-v-32311d3c" bindtap="__e">{{'>'+stateAreaAddressSelected.name+''}}</view></block><block wx:if="{{stateAreaAddressSecondSelected.code}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="select-text data-v-32311d3c" bindtap="__e">{{''+" >"+stateAreaAddressSecondSelected.name+''}}</view></block></view></block><block wx:if="{{!stateAreaSelected.code}}"><state-area-select vue-id="13f18838-1" height="{{height}}" data-event-opts="{{[['^select',[['stateAreaSelect']]]]}}" bind:select="__e" class="data-v-32311d3c" bind:__l="__l"></state-area-select></block><block wx:if="{{stateAreaSelected.code&&!stateAreaAddressSelected.code}}"><state-area-ad-level-top-select vue-id="13f18838-2" height="{{height}}" stateArea="{{stateAreaSelected}}" data-event-opts="{{[['^select',[['addressTopSelect']]]]}}" bind:select="__e" class="data-v-32311d3c" bind:__l="__l"></state-area-ad-level-top-select></block><block wx:if="{{stateAreaSelected.code&&stateAreaAddressSelected.code&&!stateAreaAddressSecondSelected.code}}"><state-area-ad-level-second-select vue-id="13f18838-3" height="{{height}}" state-area="{{stateAreaSelected}}" parentAddress="{{stateAreaAddressSelected}}" data-event-opts="{{[['^select',[['addressSecondSelect']]]]}}" bind:select="__e" class="data-v-32311d3c" bind:__l="__l"></state-area-ad-level-second-select></block><block wx:if="{{stateAreaAddressSecondSelected.code}}"><state-area-ad-level-third-select vue-id="13f18838-4" height="{{height}}" state-area="{{stateAreaSelected}}" parentAddress="{{stateAreaAddressSecondSelected}}" data-event-opts="{{[['^select',[['addressThirdSelect']]]]}}" bind:select="__e" class="data-v-32311d3c" bind:__l="__l"></state-area-ad-level-third-select></block><view style="height:20rpx;" class="data-v-32311d3c"></view><block wx:if="{{stateAreaSelected.code&&!stateAreaAddressSelected.code&&!stateAreaAddressSecondSelected.code}}"><kt-button bind:click="__e" vue-id="13f18838-5" data-event-opts="{{[['^click',[['select',[['o',['fullCode',stateAreaSelected.code]]]]]]]}}" class="data-v-32311d3c" bind:__l="__l" vue-slots="{{['default']}}"><view class="data-v-32311d3c">{{''+stateAreaSelected.name+''}}<text style="font-size:24rpx;margin-left:10rpx;" class="data-v-32311d3c">{{$root.g0}}</text></view></kt-button></block><block wx:if="{{stateAreaAddressSelected.code&&!stateAreaAddressSecondSelected.code}}"><kt-button bind:click="__e" vue-id="13f18838-6" data-event-opts="{{[['^click',[['select',['$0'],['stateAreaAddressSecondSelected']]]]]}}" class="data-v-32311d3c" bind:__l="__l" vue-slots="{{['default']}}"><view class="data-v-32311d3c">{{''+stateAreaAddressSelected.name+''}}<text style="font-size:24rpx;margin-left:10rpx;" class="data-v-32311d3c">{{''+$root.g1+''}}</text></view></kt-button></block></view></view></view>