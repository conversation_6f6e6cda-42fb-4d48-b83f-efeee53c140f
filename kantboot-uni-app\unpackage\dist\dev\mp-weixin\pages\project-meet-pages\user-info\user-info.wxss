
.body {
  position: relative;
  top: -20rpx;
  background-color: white;
  border-radius: 10px 10px 0 0;
}
.body_title {
  /* background-color: aqua; */
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.body_title_left {
  margin-top: 20rpx;
  margin-left: 20rpx;
  display: flex;
  align-items: flex-end;
}
.body_title_right {
  margin-top: 20rpx;
  margin-right: 20rpx;
  display: flex;
  color: rgb(133, 133, 133);
  font-size: 24rpx;
}
.body_title_name {
  font-size: 32rpx;
  font-weight: bold;
}
.body_title_age {
  margin-left: 20rpx;
  background: rgb(238, 238, 238);
  border-radius: 14rpx;
  font-size: 20rpx;
  min-width: 60rpx;
  padding: 10rxp;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  height: 40rpx;
}
.body_info {
  display: flex;
  font-size: 22rpx;
  margin-top: 20rpx;
  margin-left: 20rpx;
}
.body_adjective {
  margin-top: 40rpx;
  padding-left: 20rpx;
  margin-bottom: 30rpx;
}
.body_adjective_title {
  display: flex;
  align-items: center;
}
.body_adjective_icn {
  height: 32rpx;
  width: 8rpx;
  background: #ff5f79;
  border-radius: 4rpx;
}
.body_adjective_text {
  margin-left: 15rpx;
  color: rgba(0, 0, 0, 0.9);
  font-weight: 700;
  font-size: 32rpx;
}
.body_adjective_body {
  margin-top: 30rpx;
  display: flex;
}
.adjective_body_icon {
  padding: 16rpx 28rpx;
  background-color: #eef2fe;
  font-size: 28rpx;
  color: #5a7ef6;
  border-radius: 30rpx;
  width: -webkit-max-content;
  width: max-content;
  margin-right: 20rpx;
}
.foot_box {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  width: 100%;
  height: 200rpx;
  border-radius: 10rpx;
  background: white;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 16px 0px;
  display: flex;
}
.foot_btn {
  width: 224rpx;
  height: 86rpx;
  background: #5a7ef6;
  border-radius: 42rpx;
  text-align: center;
  line-height: 86rpx;
  font-size: 30rpx;
  color: #fff;
  font-weight: 700;
}

