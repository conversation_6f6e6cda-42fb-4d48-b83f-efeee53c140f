@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-11572c3c {
  width: 100%;
  padding: 10rpx 20rpx 0 20rpx;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 0 20rpx 0 rgba(118, 118, 118, 0.1);
}
.box .title.data-v-11572c3c {
  font-size: 30rpx;
  text-align: center;
  color: #000;
  line-height: 40rpx;
  font-weight: bold;
  margin: 10rpx;
}
.box-mode-device-pc.data-v-11572c3c {
  padding: 10px 40px 0 40px;
}
.box-mode-color-scheme-light.data-v-11572c3c {
  background-color: #FFFFFF;
  color: #333333;
}
.box-mode-color-scheme-dark.data-v-11572c3c {
  background-color: #292929;
  color: #f5f5f5;
}
.box-mode-color-scheme-dark .title.data-v-11572c3c {
  color: #f5f5f5;
}
.other-box.data-v-11572c3c {
  margin-top: 20rpx;
  width: 100%;
  text-align: center;
}
.other-box .other-title.data-v-11572c3c {
  font-size: 24rpx;
  color: #666666;
}
.other-box .other-icon-box.data-v-11572c3c {
  display: inline-block;
  width: 100%;
  text-align: center;
  margin-top: 20rpx;
}
.other-box .other-icon-box .icon.data-v-11572c3c {
  width: 80rpx;
  height: 80rpx;
}
.logo-box.data-v-11572c3c {
  text-align: center;
  width: 100%;
}
.logo.data-v-11572c3c {
  display: inline-block;
  width: 200rpx;
  height: 100rpx;
  margin: 0 auto;
}
