<template>
  <view>
    <view
        class="header"
        id="projectMeetPagesHeader">
      <kt-nav-bar
          :title="$i18n.zhToGlobal('对话')"></kt-nav-bar>
    </view>
    <view class="bg"></view>
    <view>
      <kt-chat-dialog-panel
          :height="'calc(100vh - '+headerHeight+'px - '+footerHeight+'px - 150rpx)'"
          :dialog-id="dialogId"
      ></kt-chat-dialog-panel>
    </view>
    <view id="projectMeetPageFooter"
          class="footer">

      <kt-send-input
          :has-voice="false"
          @change="change"
          @send="send"
          mode="panel"
          ref="sendInput"
      ></kt-send-input>

    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dialogId:"",
	  userAccountId:"",
      headerHeight:"",
      footerHeight:"",
      dialog:{},
    };
  },
  onLoad(options) {
    this.dialogId = options.dialogId;
	this.userAccountId = options.userAccountId;
	if(this.userAccountId){
		this.getOneToOneByUserAccountId();
	}
    this.getById();
  },
  mounted() {
    // 获取navBarPostDetail的高度
    this.$nextTick(() => {

      uni.createSelectorQuery()
          .select('#projectMeetPagesHeader')
          .boundingClientRect((res) => {
            this.headerHeight = res.height
          }).exec();

      uni.createSelectorQuery()
          .select('#projectMeetPageFooter')
          .boundingClientRect((res) => {
            this.footerHeight = res.height
          }).exec();

    });

  },
  methods: {
	getOneToOneByUserAccountId(){
		  this.$refs.sendInput.toLoading();
		  // /fp-community-web/post/push
		  this.$kt.request.post("/functional-chat-web/dialog/getOneToOneByUserAccountId",{
		    data:{
		      userAccountId:this.userAccountId
		    }
		  }).then((res)=>{
			  this.dialog=res.data;
			  this.dialogId = this.dialog.id;
		    this.$refs.sendInput.clear();
		    this.$refs.sendInput.toNone();
		  }).catch((res)=>{
		  
		  })
	},
    change(){
      // 获取navBarPostDetail的高度
      this.$nextTick(() => {

        uni.createSelectorQuery()
            .select('#projectMeetPagesHeader')
            .boundingClientRect((res) => {
              this.headerHeight = res.height
            }).exec();

        uni.createSelectorQuery()
            .select('#projectMeetPageFooter')
            .boundingClientRect((res) => {
              this.footerHeight = res.height
            }).exec();

      });

      setTimeout(()=>{
        uni.createSelectorQuery()
            .select('#projectMeetPagesHeader')
            .boundingClientRect((res) => {
              this.headerHeight = res.height
            }).exec();

        uni.createSelectorQuery()
            .select('#projectMeetPageFooter')
            .boundingClientRect((res) => {
              this.footerHeight = res.height
            }).exec();
      },100);

      setTimeout(()=>{
        uni.createSelectorQuery()
            .select('#projectMeetPagesHeader')
            .boundingClientRect((res) => {
              this.headerHeight = res.height
            }).exec();

        uni.createSelectorQuery()
            .select('#projectMeetPageFooter')
            .boundingClientRect((res) => {
              this.footerHeight = res.height
            }).exec();
      },1000);

      setTimeout(()=>{
        uni.createSelectorQuery()
            .select('#projectMeetPagesHeader')
            .boundingClientRect((res) => {
              this.headerHeight = res.height
            }).exec();

        uni.createSelectorQuery()
            .select('#projectMeetPageFooter')
            .boundingClientRect((res) => {
              this.footerHeight = res.height
            }).exec();
      },1000);

    },
    send(param){
      this.$refs.sendInput.toLoading();
      // /fp-community-web/post/push
      this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf",{
        data:{
          items:[param],
          dialogId:this.dialogId
        }
      }).then((res)=>{
        this.$refs.sendInput.clear();
        this.$refs.sendInput.toNone();
      }).catch((res)=>{

      })
    }
  },
}
</script>

<style lang="scss" scoped>
.bg{
  position: fixed;
  top:0;
  left:0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-color: #f0f0f0;
}

.footer{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index:99999;
  background-color: #FFFFFF;
}

.header{
  background-color: #FFFFFF;
}
</style>

