<template>
    <kt-box>
      <view :class="clazz.title">{{title}}</view>
      <view :class="clazz.inBox">
        <slot></slot>
      </view>
    </kt-box>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      clazz:{
        title:this.$kt.style.toggleClass('title'),
        inBox:this.$kt.style.toggleClass('in-box')
      }
    };
  }
}
</script>

<style lang="scss" scoped>
.in-box {
  background-color: #FFFFFF;
  padding: 20rpx;
  border-radius: 10rpx;
}

.in-box-mode-color-scheme-light {
  background-color: #FFFFFF;
}

.in-box-mode-color-scheme-dark {
  background-color: #292929;
}

.title {
  font-size: 28rpx;
  color: $kt-color-primary-light;
  margin-bottom: 20rpx;
}

.title-mode-color-scheme-light {
  color: $kt-color-primary-light;
}

.title-mode-color-scheme-dark {
  color: $kt-color-primary-dark;
}

</style>
