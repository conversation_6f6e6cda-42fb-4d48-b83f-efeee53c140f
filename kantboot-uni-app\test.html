<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>Coze Chat Bot</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        html,
        body {
            height: 100%;
            margin: 0;
        }
    </style>
</head>

<body>
    <!-- 容器将自动创建，无需额外的 DOM 元素 -->
    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.6/libs/cn/index.js"></script>
    <script>
        let cozeWebSDK = new CozeWebSDK.WebChatClient({
            config: {
                bot_id: '7502176244868726784',
                isIframe: false,
            },
            componentProps: {
                title: 'Coze Chatbot',
            },
            auth: {
                type: 'token',
                token: 'pat_l276twRDbvnRja2lEj3PnwPgrM5i6q3BIqlu2a1ZiLGzI9MReMuFnTt6Ho69elER',
                onRefreshToken: function () {
                    // 返回新的 token，或直接返回旧 token（如果你没有刷新逻辑）
                    return 'pat_l276twRDbvnRja2lEj3PnwPgrM5i6q3BIqlu2a1ZiLGzI9MReMuFnTt6Ho69elER';
                }
            },
            ui: {
                header: {
                    isNeedClose: false,
                },
                asstBtn: {
                    isNeed: false,
                }
            },

        });

        cozeWebSDK.showChatBot();


        // setInterval(()=>{

        //     // 获取所有selectable=true的标签，并改为button
        //     let selectableTags = document.querySelectorAll('taro-text-core[selectable="true"]');
        //     selectableTags.forEach(tag => {
        //         // tag.innerHTML = "<button>123</button>"
        //         if(tag.innerText==="完成，领取奖励"){
        //             tag.innerHTML = "<button>完成，领取奖励</button>"
        //         }
        //         if(tag.innerText==="没做，结束"){
        //             tag.innerHTML = "<button>没做，结束</button>"
        //         }

        //     });
        //     // // 找到iframe元素
        //     // let iframe = document.querySelector('iframe');
        //     // // 获取iframe的contentWindow对象
        //     // let iframeWindow = iframe.contentWindow;
        //     // // 获取iframe的document对象
        //     // let iframeDocument = iframeWindow.document;
        //     // let selectableTags = iframeDocument.getElementsByTagName("li")
        //     // selectableTags.forEach(tag => {
        //     //     tag.innerHTML = "<button>123</button>"
        //     // });

        // },1000);

    </script>
</body>

</html>