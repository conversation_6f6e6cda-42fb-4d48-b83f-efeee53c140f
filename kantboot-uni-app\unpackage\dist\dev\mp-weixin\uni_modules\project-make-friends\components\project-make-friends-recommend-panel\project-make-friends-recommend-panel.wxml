<scroll-view class="user-recommend-panel data-v-433c2846" style="{{'height:'+(height)+';'}}" scroll-y="true" s="{{true}}" croll-with-animation="true" show-scrollbar="{{false}}" refresher-triggered="{{refresherTriggered}}" refresher-enabled="{{refresherEnabled}}" lower-threshold="{{$root.g0}}" data-event-opts="{{[['scrolltolower',[['getAfter',['$event']]]],['refresherrefresh',[['onRefresherrefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="user-list-box data-v-433c2846"><block wx:for="{{userList}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['toUserInfoPage',['$0'],[[['userList','',__i0__]]]]]]]}}" class="{{['data-v-433c2846',clazz.userBox]}}" bindtap="__e"><project-make-friends-user-info-out-card vue-id="{{'24496180-1-'+__i0__}}" userInfo="{{item}}" class="data-v-433c2846" bind:__l="__l"></project-make-friends-user-info-out-card></view></block><block wx:if="{{alfterLoading}}"><u-loading-icon vue-id="24496180-2" mode="circle" size="50rpx" class="data-v-433c2846" bind:__l="__l"></u-loading-icon></block><block wx:if="{{isBottom}}"><view style="text-align:center;color:#999999;font-size:24rpx;" class="data-v-433c2846">{{''+$root.g1+''}}</view></block><view style="height:50rpx;" class="data-v-433c2846"></view></view></scroll-view>