<template>
  <view>
    <kt-popup
        ref="ktPopup">
      <view :class="clazz.box">
        <view class="box-title">{{$i18n.zhToGlobal('访客')}}</view>
        <view style="height: 20rpx"></view>
        <view class="scroll-box">
          <view style="margin-bottom: 20rpx">
            <project-meet-visit-panel></project-meet-visit-panel>
          </view>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      }
    };
  },
  mounted() {
  },
  methods: {
    open() {
      this.$refs.ktPopup.open();
    },
  },
}
</script>

<style lang="scss" scoped>
.box{
  .box-title{
    font-weight: bold;
  }
  .scroll-box{
    height: 500px;
    overflow: auto;
  }

}
.box-mode-device-pc{
  position: fixed;
  width: 500px;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-radius: 20rpx;
  top:50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
