<view class="content data-v-551d181e" style="{{'width:'+(size)+';'+('height:'+(size)+';')}}"><kt-image class="{{['t-head','data-v-551d181e',(isBodyImageLoad?1:0)?'opacity':'']}}" style="{{'border:'+(border)+';'}}" vue-id="4e866ca8-1" borderRadius="55%" fileId="{{fileId}}" src="{{src}}" mode="aspectFill" data-event-opts="{{[['^load',[['bodyImageLoad']]]]}}" bind:load="__e" bind:__l="__l"></kt-image><block wx:if="{{online}}"><view class="radius radius-line data-v-551d181e"></view></block></view>