import request from "@/uni_modules/kantboot/libs/request";

let result = {};

/**
 * 根据邮箱+密码登录
 * /user-account-web/userAccountLogin/loginByEmailAndPassword
 * @param {object} data
 */
result.loginByEmailAndPassword = (data) => {
    return request.post(
        "/user-account-web/userAccountLogin/loginByEmailAndPassword",
        {data:{email: data?.email, password: data?.password}}
    );
}

/**
 * 根据邮箱+验证码登录
 * /user-account-web/userAccountLogin/loginByEmailAndVerificationCode
 * @param {object} data
 */
result.loginByEmailAndVerificationCode = (data) => {
    return request.post(
        "/user-account-web/userAccountLogin/loginByEmailAndVerificationCode",
        {data:{email: data?.email, verificationCode: data?.verificationCode}}
    );
}

/**
 * 发送邮箱验证码
 */
result.sendLoginVerificationCodeByEmail = (data) => {
    return request.post(
        "/user-account-web/userAccountLogin/sendLoginVerificationCodeByEmail",
        {data:{email: data?.email}}
    );
}

/**
 * 发送短信验证码
 * /user-account-web/userAccountLogin/sendLoginVerificationCodeByPhone
 */
result.sendLoginVerificationCodeByPhone = (data) => {
    return request.post(
        "/user-account-web/userAccountLogin/sendLoginVerificationCodeByPhone",
        {data:{ phoneAreaCode:data?.phoneAreaCode ,phone: data?.phone}}
    );
}

/**
 * 根据手机号+验证码登录
 * /user-account-web/userAccountLogin/loginByPhoneAndVerificationCode
 * @param {object} data
 */
result.loginByPhoneAndVerificationCode = (data) => {
    return request.post(
        "/user-account-web/userAccountLogin/loginByPhoneAndVerificationCode",
        {data:{phoneAreaCode:data?.phoneAreaCode,phone: data?.phone, verificationCode: data?.verificationCode}}
    );
}

/**
 * 根据手机号+密码登录
 * /user-account-web/userAccountLogin/loginByPhoneAndPassword
 * @param {object} data
 */
result.loginByPhoneAndPassword = (data) => {
    return request.post(
        "/user-account-web/userAccountLogin/loginByPhoneAndPassword",
        {data:{phone: data?.phone, password: data?.password}}
    );
}

export default result;