<template>
  <view>
    <project-acm-nav-bar
        :is-has-i18n="false"
    :title="$i18n.zhToGlobal('课程详情')"
    ></project-acm-nav-bar>

    <view class="bg"></view>
    {{course.permissionTagCode}}
    <view class="box">
      <kt-course-detail
          :course-id="courseId"
      ></kt-course-detail>
    </view>

    <view class="box-z">

      <view class="box-z-box">
        <view>
          <image
              class="box-z-box-icon"
              :src="$kt.file.byPath('/icon/vip.svg')"
          ></image>
        </view>
        <kt-button
            class="box-z-btn"
            ref="payButton"
            @click="toPay"
        >{{$i18n.zhToGlobal("成为会员,解锁适配")}}</kt-button>
      </view>
    </view>

    <kt-pay-popup
    ref="ktPayPopup"
    :pay-methods="['wechat']"
    ></kt-pay-popup>

    <kt-no-login></kt-no-login>
  </view>
</template>

<script>
export default {
  data() {
    return {
      courseId: null,
      course:{

      },
    };
  },
  onLoad(options) {
    this.courseId = options.courseId;
    this.getById();
  },
  methods: {
    toPay(){
      // /project-acm-web/order/generateUpgradeVipOrder
      // this.$refs.ktPayPopup.open();
      this.$refs.payButton.loading(null,9999999999);
      this.$request.post("/project-acm-web/order/generateUpgradeVipOrder",{
        data:{
        }
      }).then((res)=>{
        this.$refs.payButton.toNone();
        this.$refs.ktPayPopup.open(res.data);
      })
    },
// /fp-course-web/course/getById
    getById(){
      this.$kt.request.post('/fp-course-web/course/getById', {
        data:{id: this.courseId}
      }).then(res => {
        this.course = res.data;
      })
    }

  },
}
</script>

<style lang="scss" scoped>

.box{
  padding: 20rpx;
  box-sizing: border-box;
}

.bg{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #ffffff 700rpx, #f0f0f0 100%);
  z-index: -1;
}

.box-z{
  padding: 30rpx;
  position: fixed;
  background-color: #FFFFFF;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  top:0;
  left: 0;
  .box-z-box{
    position: absolute;
    top:50%;
    left: 50%;
    width: calc(100% - 150rpx);
    transform: translate(-50%,-50%);
    text-align: center;
    .box-z-box-icon{
      display: inline-block;
      width: 200rpx;
      height: 200rpx;
      // 动画
      animation: icon-rotate 2s infinite linear;
    }
  }
}

@keyframes icon-rotate {
  0% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
  100% {
    opacity: 1;
  }
}

</style>
