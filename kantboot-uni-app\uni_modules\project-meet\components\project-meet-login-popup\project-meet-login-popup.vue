<template>
  <view>
    <kt-popup
        @close="close"
        :zIndex="zIndex"
        ref="ktPopup">
      <view>
        <project-meet-login-panel
            :border-radius="isPc()?'20rpx':'20rpx 20rpx 0 0'"
            @loginSuccess="loginSuccess"
        ></project-meet-login-panel>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  name: "kt-login-popup",
  components: {
  },
  props:{
    zIndex:{
      type: Number,
      default: 999999999
    },
  },
  data(){
    return {
    }
  },
  mounted(){
    this.$kt.event.on("login:success",()=>{
      this.close();
    });
  },
  methods:{
    isPc(){
      return this.$kt.style.detectDeviceType() === "pc";
    },
    loginSuccess(){
      this.close();
    },
    open(){
      this.$refs.ktPopup.open();
    },
    close(){
      this.$refs.ktPopup.close();
      this.$emit('close');
    },

  },
}
</script>

<style lang="scss" scoped>

</style>