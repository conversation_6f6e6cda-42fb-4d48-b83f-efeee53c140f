<template>
  <view>
    <kt-popup
    ref="ktPopup">
      <view :class="clazz.box">
        <project-meet-user-match-card></project-meet-user-match-card>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      }
    };
  },
  mounted() {
  },
  methods: {
    open(){
      this.$refs.ktPopup.open();
    }
  },
}
</script>

<style lang="scss" scoped>
.box{
  position: fixed;
  top:50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #FFFFFF;
  padding: 20rpx 0 20rpx 0;
  border-radius: 20rpx 20rpx 0 0;
}
.box-mode-device-pc {
  width: 800px;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 0;
}
</style>
