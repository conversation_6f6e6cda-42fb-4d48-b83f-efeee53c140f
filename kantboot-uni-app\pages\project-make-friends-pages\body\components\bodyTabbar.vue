<template>
  <view :class="clazz.boxBox">
    <view style="height: 15rpx"></view>
    <view>
      <view :class="clazz.recommendationHeader" v-if="isPc()">
        <view class="recommendation-title">为您推荐合适的朋友</view>
        <view class="recommendation-subtitle">社交平台</view>
      </view>
      <view class="box" v-for="(item, index) in tabbarList" @click="changeTabbar(item.code)">

        <view v-if="item.code !== 'push'">
          <view class="unread-tag" v-if="item.code === 'message' && unreadCount > 0">{{ unreadCount }}</view>
          <view>
            <view class="icon-box">
              <image v-show="tabbarSelected !== item.code" mode="aspectFit" class="icon" :src="item.iconPath">
              </image>
              <image v-show="tabbarSelected === item.code" mode="aspectFit" class="icon-selected"
                :src="item.selectedIconPath"></image>
            </view>
            <!--            <view class="text">{{item.text}}</view>-->
            <view class="text" :class="{
              'text-selected': tabbarSelected === item.code
            }">
              <kt-one-line-text v-if="!isPc()" width="100rpx" font-size="24rpx" :text="item.text"></kt-one-line-text>

              <kt-one-line-text v-if="isPc()" width="100rpx" font-size="22px" :text="item.text"></kt-one-line-text>

            </view>
            <view style="height: 30rpx"></view>
          </view>

        </view>

        <view v-else-if="item.code === 'push'">
          <view v-if="!isPc()">
            <view @click="$kt.router.navTo('/pages/project-make-friends-pages/post-push/post-push')" style="position: absolute;left:50%;
                      bottom:calc(50% + 5rpx);
                      opacity: .8;
                      transform: translate(-50%, -50%) scale(1.4);
                " class="icon-selected">
              <image mode="aspectFit" class="icon" :src="$kt.file.byPath('icon/addSquare.svg')">
              </image>
            </view>
          </view>

          <view v-if="isPc()" class="push-btn">
            {{ $i18n.zhToGlobal("发布") }}
          </view>

        </view>
      </view>

    </view>
  </view>
</template>

<script>
export default {
  props: {
    selected: {
      type: String,
      default: 'all'
    }
  },
  data() {
    return {
      clazz: {
        boxBox: this.$kt.style.toggleClass("box-box"),
        recommendationHeader: this.$kt.style.toggleClass("recommendation-header"),
      },
      tabbarSelected: 'all',
      unreadCount: 0,
      // tabbar列表：首页、全部、查询、我的
      tabbarList: [
        // {
        //   iconPath: this.$kt.file.byPath('tabbar/home.svg'),
        //   selectedIconPath: this.$kt.file.byPath('tabbar/home-selected.svg'),
        //   text: this.$i18n.zhToGlobal("首页"),
        //   code: 'home',
        //   navigateBarColor: '#000000'
        // },
        // {
        //   iconPath: this.$kt.file.byPath('tabbar/message.svg'),
        //   selectedIconPath: this.$kt.file.byPath('tabbar/message-selected.svg'),
        //   text: this.$i18n.zhToGlobal("消息"),
        //   code: 'message',
        //   navigateBarColor: '#000000'
        // },
        // {
        //   text: this.$i18n.zhToGlobal("发帖"),
        //   code: 'push',
        //   navigateBarColor: '#000000'
        // },

        // {
        //   iconPath: this.$kt.file.byPath('tabbar/ai.svg'),
        //   selectedIconPath: this.$kt.file.byPath('tabbar/ai-selected.svg'),
        //   // text: "Find",
        //   text: this.$i18n.zhToGlobal("发现"),
        //   code: 'find',
        //   navigateBarColor: '#000000'
        // },
        // {
        //   iconPath: this.$kt.file.byPath('tabbar/mine.svg'),
        //   selectedIconPath: this.$kt.file.byPath('tabbar/mine-selected.svg'),
        //   // text: "Mine",
        //   text: this.$i18n.zhToGlobal("我的"),
        //   code: 'mine',
        //   navigateBarColor: '#000000'
        // },
        // {
        //   iconPath: this.$kt.file.byPath('tabbar/home.svg'),
        //   selectedIconPath: this.$kt.file.byPath('tabbar/home-selected.svg'),
        //   // text: "Home",
        //   text: this.$i18n.zhToGlobal("首页"),
        //   code: 'homeCourse',
        //   navigateBarColor: '#000000'
        // },
        // {
        //   iconPath: this.$kt.file.byPath('tabbar/practice.svg'),
        //   selectedIconPath: this.$kt.file.byPath('tabbar/practice-selected.svg'),
        //   // text: "Practice",
        //   text: this.$i18n.zhToGlobal("发帖"),
        //   code: 'postCourse',
        //   navigateBarColor: '#000000'
        // },
        // {
        //   iconPath: this.$kt.file.byPath('tabbar/ai.svg'),
        //   selectedIconPath: this.$kt.file.byPath('tabbar/ai-selected.svg'),
        //   // text: "Find",
        //   text: this.$i18n.zhToGlobal("发现"),
        //   code: 'findCourse',
        //   navigateBarColor: '#000000'
        // },
        // {
        //   iconPath: this.$kt.file.byPath('tabbar/message.svg'),
        //   selectedIconPath: this.$kt.file.byPath('tabbar/message-selected.svg'),
        //   text: this.$i18n.zhToGlobal("消息"),
        //   code: 'messageCourse',
        //   navigateBarColor: '#000000'
        // },
        // {
        //   iconPath: this.$kt.file.byPath('tabbar/mine.svg'),
        //   selectedIconPath: this.$kt.file.byPath('tabbar/mine-selected.svg'),
        //   // text: "Mine",
        //   text: this.$i18n.zhToGlobal("我的"),
        //   code: 'mineCourse',
        //   navigateBarColor: '#000000'
        // },
        {
          iconPath: this.$kt.file.byPath('tabbar/home.svg'),
          selectedIconPath: this.$kt.file.byPath('tabbar/home-selected.svg'),
          // text: "Home",
          text: this.$i18n.zhToGlobal("首页"),
          code: 'home',
          navigateBarColor: '#000000'
        },
        {
          iconPath: this.$kt.file.byPath('tabbar/ai.svg'),
          selectedIconPath: this.$kt.file.byPath('tabbar/ai-selected.svg'),
          // text: "Find",
          text: this.$i18n.zhToGlobal("发现"),
          code: 'find',
          navigateBarColor: '#000000'
        },
        {
          iconPath: this.$kt.file.byPath('tabbar/practice.svg'),
          selectedIconPath: this.$kt.file.byPath('tabbar/practice-selected.svg'),
          // text: "Practice",
          text: this.$i18n.zhToGlobal("发帖"),
          code: 'push',
          navigateBarColor: '#000000'
        },
        {
          iconPath: this.$kt.file.byPath('tabbar/message.svg'),
          selectedIconPath: this.$kt.file.byPath('tabbar/message-selected.svg'),
          text: this.$i18n.zhToGlobal("消息"),
          code: 'message',
          navigateBarColor: '#000000'
        },
        {
          iconPath: this.$kt.file.byPath('tabbar/mine.svg'),
          selectedIconPath: this.$kt.file.byPath('tabbar/mine-selected.svg'),
          // text: "Mine",
          text: this.$i18n.zhToGlobal("我的"),
          code: 'mine',
          navigateBarColor: '#000000'
        },
      ]
    }
  },
  mounted() {
    this.$kt.event.on("chat:unread:count", (count) => {
      this.unreadCount = count;
    });
    // 如果是pc端，则删除code是push的
    if (this.isPc()) {
      this.tabbarList = this.tabbarList.filter(item => item.code !== 'push');
      // 往最后面添加一个push
      this.tabbarList.push({
        text: this.$i18n.zhToGlobal("发帖"),
        code: 'push',
        navigateBarColor: '#000000'
      });
    }

    this.$kt.event.on("FunctionalChatDialogMessage.handleRelationship", (res) => {
      // alert(JSON.stringify(res));
      this.unreadCount = res.userAccountRelationship.unreadCount;
      // // 获取现在的标题-uniapp
      // uni.setNavigationBarTitle({
      //   title: this.$i18n.zhToGlobal("消息") + "（" + res.userAccountRelationship.unreadCount + "）"
      // });
      // #ifdef H5
      // 获取标题
      let documentTitle = document.title + "";
      document.title = this.$i18n.zhToGlobal("未读消息") + " (" + res.userAccountRelationship.unreadCount + ")";
      setTimeout(() => {
        document.title = documentTitle;
      }, 1000);
      // #endif

    });

    this.getUnreadCountSelf();


  },
  methods: {
    async getUnreadCountSelf() {
      this.$kt.request.post("/functional-chat-web/relationship/getUnreadCountSelf").then(res => {
        if (res.data == null) {
          this.unreadCount = 0;
          return;
        }
        this.unreadCount = res.data.unreadCount;
      }).catch(err => {
        this.$kt.message.error(err.errMsg);
      });
    },
    isPc() {
      return this.$kt.style.detectDeviceType() === "pc";
    },
    changeTabbar(code) {
      if (code === 'push') {
        this.$kt.router.navTo('/pages/post-push/post-push');
        return;
      }

      this.tabbarSelected = code;

      this.$emit('changeTabbar', code);
      this.$kt.event.emit('changeTabbar', code);
    }
  },
  watch: {
    selected: {
      handler: function (val) {
        this.tabbarSelected = val;
      },
      immediate: true
    }
  }
}
</script>


<style scoped lang="scss">
.box-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  z-index: 999;
  text-align: center;
}

.box-box-mode-device-mobile,
.box-box-mode-device-pad {
  text-align: center;
  // 超出不换行
  white-space: nowrap;
  // 滚动条
  overflow-x: auto;
  // 滚动条宽度
  scrollbar-width: thin;

  .box {
    width: calc(750rpx / 5);
    text-align: center;

    .icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
}

.box-box-mode-device-pc {
  scrollbar-color: #000000 #FFFFFF;
  width: 240px;
  height: 100%;
  padding: 30rpx 30rpx 30rpx 60rpx;
  box-sizing: border-box;

  .box {
    display: block;
    cursor: pointer;
    width: 100%;
    text-align: left;
    margin-bottom: 30rpx;
  }

  .box:hover {
    //color: #000000;
    opacity: .8;
  }

  .icon-box {
    vertical-align: top;
    display: inline-block;
    margin-right: 10px;

  }

  .icon {
    width: 30px;
    height: 30px;
  }

  .icon-selected {
    width: 30px;
    height: 30px;
  }

  .text {
    margin-top: 2rpx;
    vertical-align: top;
    display: inline-block;
  }
}

.box-box-mode-color-scheme-dark {
  background-color: #191919;

  .box {
    .icon-selected {
      // 颜色反转
      filter: invert(1);
    }

    .text-selected {
      color: #FFFFFF;
    }
  }
}

// 滚动条不显示
::-webkit-scrollbar {
  display: none;
}



.box {
  position: relative;
  text-align: center;
  display: inline-block;
  width: 150rpx;
}

.unread-tag {
  position: absolute;
  right: 20rpx;
  border-radius: 20rpx;
  background-color: #FF0000;
  color: #FFFFFF;
  font-size: 20rpx;
  text-align: center;
  padding: 0 10rpx 0 10rpx;
}

.icon {
  width: 40rpx;
  height: 40rpx;
}

.text {
  font-size: 20rpx;
  color: #AAAAAA;
}

.icon-selected {
  width: 40rpx;
  height: 40rpx;
}

.text-selected {
  font-size: 20rpx;
  color: #000000;
}

.push-btn {
  width: 100%;
  background-color: #1da1f2;
  padding: 20rpx;
  box-sizing: border-box;
  color: #FFFFFF;
  text-align: center;
  cursor: pointer;
  border-radius: 30rpx;
  letter-spacing: 2rpx;
}

.recommendation-header {
  margin-bottom: 18px;
}

.recommendation-title {
  color: #1da1f2;
  font-size: 20px;
  text-align: center;
  margin-bottom: 4px;
}

.recommendation-subtitle {
  color: #1da1f2;
  font-size: 18px;
  text-align: center;
}

// 暗色模式支持
.recommendation-header-mode-color-scheme-dark {
  .recommendation-title {
    color: #1da1f2;
  }

  .recommendation-subtitle {
    color: #1da1f2;
  }
}
</style>