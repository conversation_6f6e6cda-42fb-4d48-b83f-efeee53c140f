<template>
  <view>
    <kt-popup
        ref="ktPopup"
        @close="close"
    >
      <view
          v-if="!loading"
          :class="clazz.box">
        <view v-if="mode==='main'">
          <view style="height: 30rpx"></view>


          <view
              v-if="!isSelf&&hasReport"
              @click="toReport()"
              class="menu-box">

            <view
                class="menu-title">
              <view class="menu-icon">
                <image
                    class="menu-icon-image"
                    :src="$kt.file.byPath('fpCommunity/icon/report.svg')"></image>
              </view>
              {{reportText||$i18n.zhToGlobal("举报帖子")}}
            </view>

            <view class="menu-fixed">
              <view class="menu-fixed-tips">
              </view>
              <view class="menu-fixed-content">
                <image
                    class="menu-fixed-content-icon"
                    :src="$kt.file.byPath('fpCommunity/icon/arrowRight.svg')"></image>
              </view>
            </view>
          </view>

          <view
              v-if="!isSelf"
              class="menu-box">
            <kt-button
            v-if="isFollowRequest"
            ref="followButton"
            style="width: 100%;"
            >{{$i18n.zhToGlobal('加载中')}}</kt-button>
            <kt-button
                v-if="!isFollow&&!isFollowRequest"
                ref="followButton"
                @click="toFollow()"
                style="width: 100%"
            >{{$i18n.zhToGlobal("关注")}}</kt-button>
            <kt-button
                v-if="isFollow&&!isFollowRequest"
                ref="followButton"
                @click="toUnFollow()"
                style="width: 100%">{{$i18n.zhToGlobal("取消关注")}}</kt-button>
          </view>

          <view
              v-if="isSelf&&hasPermissionSetting"
              @click="toPermissionSetting()"
              class="menu-box">
            <view class="menu-title">
              <view class="menu-icon">
                <image
                    class="menu-icon-image"
                    :src="$kt.file.byPath('fpCommunity/icon/setting.svg')"></image>
              </view>
              {{permissionSettingText||$i18n.zhToGlobal("权限设置")}}
            </view>
            <view class="menu-fixed">
              <view class="menu-fixed-tips">
              </view>
              <view class="menu-fixed-content">
                <image
                    class="menu-fixed-content-icon"
                    :src="$kt.file.byPath('fpCommunity/icon/arrowRight.svg')"></image>
              </view>
            </view>
          </view>

          <view
              v-if="isSelf"
              @click="$refs.modalOfDelete.open()"
              class="menu-box">
            <view class="menu-title">
              <view class="menu-icon">
                <image
                    class="menu-icon-image"
                    :src="$kt.file.byPath('fpCommunity/icon/remove.svg')"></image>
              </view>
              {{removeText||$i18n.zhToGlobal("删除帖子")}}
            </view>
            <view class="menu-fixed">
              <view class="menu-fixed-tips">
              </view>
              <view class="menu-fixed-content">
              </view>
            </view>
          </view>

          <kt-modal
              ref="modalOfDelete"
              @confirm="toRemove()"
          :title="$i18n.zhToGlobal('提示')"
          :content="$i18n.zhToGlobal('是否确定删除')"
          ></kt-modal>

        </view>
        <view v-if="mode==='permissionSetting'">
          <kt-community-post-permission-setting-box
              v-model="post.permission"
              @updatePostPermissionSuccess="updatePostPermissionSuccess"
              @updatePostPermissionFail="updatePostPermissionFail"
          ref="fpCommunityPostPermissionSettingBox">
          </kt-community-post-permission-setting-box>
          <kt-button
          :border-radius="'20rpx'"
          :is-open-box-shadow="false"
          ref="updatePostPermissionButton"
          @click="updatePostPermission()"
          >{{$i18n.zhToGlobal("确定修改")}}</kt-button>
          <view style="height: 20rpx"></view>

          <view style="text-align: center">
            <view class="to-back-btn"
            @click="mode='main'"
            >{{$i18n.zhToGlobal("返回")}}</view>
          </view>

          <view style="height: 20rpx"></view>

        </view>
        <view
            v-if="mode === 'report'">
          <view
              class="menu-box">

            <view
                class="menu-title">
              <view class="menu-icon">
                <image
                    class="menu-icon-image"
                    :src="$kt.file.byPath('fpCommunity/icon/report.svg')"></image>
              </view>
              {{reportText||$i18n.zhToGlobal("举报帖子")}}
            </view>

          </view>
          <view class="line"></view>
          <scroll-view
              class="report-scroll-view"
          scroll-y>
            <kt-community-post-report
                :has-confirm-button="false"
                :post-id="post.id"
                ref="ktCommunityPostReport"
                @reportLoading="($event) => $refs.confirmReportButton.loading()"
                @reportSuccess="reportSuccess"
                @reportFail="($event) => $refs.confirmReportButton.error($event.errMsg)"
            ></kt-community-post-report>
          </scroll-view>
          <u-divider></u-divider>
          <kt-button
          ref="confirmReportButton"
          @click="$refs.ktCommunityPostReport.report()"
          >{{$i18n.zhToGlobal("确定举报")}}</kt-button>
          <view style="height: 20rpx"></view>
          <view style="text-align: center">
            <view class="to-back-btn"
                  @click="mode='main'"
            >{{$i18n.zhToGlobal("返回")}}</view>
          </view>
          <view style="height: 30rpx"></view>



        </view>


      </view>


    </kt-popup>
  </view>
</template>

<script>
import api from "../../libs/api";
import event from '../../libs/event';

export default {
  name:"fp-community-post-operate-popup",
  props:{
    /**
     * 是否含有举报
     */
    hasReport:{
      type:Boolean,
      default:true
    },
    /**
     * 是否含有权限设置
     */
    hasPermissionSetting: {
      type: Boolean,
      default: true
    },
    /**
     * 删除的文本
     */
    removeText: {
      type: String,
      default: ""
    },
    /**
     * 举报的文本
     */
    reportText: {
      type: String,
      default: ""
    },
    /**
     * 权限设置的文本
     */
    permissionSettingText: {
      type: String,
      default: ""
    },

  },
  data() {
    return {
      api,
      event,
      show:false,
      post:{
        permission:{}
      },
      self:{},
      isSelf:false,
      /**
       * 模式
       * main 主要模式
       * permissionSetting 权限设置模式
       */
      mode:"main",
      clazz:{
        box: this.$kt.style.toggleClass("box")
      },
      isFollowRequest:true,
      isFollow:false,
      loading:false
    };
  },
  created() {
  },
  methods:{
    getIsFollow(){
      this.isFollowRequest = true;
      // this.$refs.followButton.loading(null,99999);
      this.$request.post("/user-interrelation-web/interrelation/isFollow",{
        data:{
          userAccountId: this.post.userAccountIdOfUploader
        }
      }).then(res=>{
        this.isFollowRequest = false;
        this.$refs.followButton.toNone();
        this.isFollow = res.data;
      }).catch(err=>{
        this.isFollowRequest = false;
        this.isFollow = false;
      });
    },
    toFollow(){
      this.$refs.followButton.loading(null,99999);
      this.$request.post("/user-interrelation-web/interrelation/follow",{
        data:{
          userAccountId: this.post.userAccountIdOfUploader
        }
      }).then(res=>{
        // this.$refs.followButton.success(res.msg);
        setTimeout(()=>{
          this.getIsFollow();
        },1000);

      }).catch(err=>{
        this.$refs.followButton.error(err.errMsg);
      });
    },
    toUnFollow(){
      this.$refs.followButton.loading(null,99999);
      this.$request.post("/user-interrelation-web/interrelation/unFollow",{
        data:{
          userAccountId: this.post.userAccountIdOfUploader
        }
      }).then(res=>{
        // this.$refs.followButton.success(res.msg);
        setTimeout(()=>{
          this.getIsFollow();
        },1000);
      }).catch(err=>{
        this.$refs.followButton.error(err.errMsg);
      });
    },
    reportSuccess(res){
      this.$refs.confirmReportButton.success(res.msg);
      setTimeout(()=>{
        this.close();
      },500);
    },
    async updatePostPermission(){
      this.$refs.updatePostPermissionButton.loading(null,99999);
      await this.$refs.fpCommunityPostPermissionSettingBox.updatePostPermission();
    },
    toBack(){
      this.mode = "main";
    },
    updatePostPermissionSuccess(res){
      this.$refs.updatePostPermissionButton.success(res.msg);
      setTimeout(()=>{
        this.toBack();
      },500);
    },
    updatePostPermissionFail(err){
      this.$refs.updatePostPermissionButton.fail(err.errMsg);
    },
    async open(post){
      this.mode = "main";
      this.show = true;
      this.$refs.ktPopup.open();

      this.post = post;
      this.loading = true;
      await this.api.getPostById({id:this.post.id}).then((res)=>{
        this.post = res.data;
        if(this.$kt.userAccount.getIsLogin()){
          this.isSelf = res.data.userAccountIdOfUploader+"" === this.$kt.userAccount.getSelf().id+"";
        }else{
          this.isSelf = false;
        }
        this.getIsFollow();
        this.loading = false;
      }).catch(()=>{
        this.loading = false;
      })

    },
    close(){
      this.show = false;
      this.$refs.ktPopup.close();
    },
    /**
     * 前往权限设置
     */
    toPermissionSetting(){
      this.mode = "permissionSetting";
      this.$refs.fpCommunityPostPermissionSettingBox.setParam(JSON.parse(JSON.stringify(this.post)));
    },
    toReport(){
      this.mode = "report";
    },
    /**
     * 前往取消
     */
    toRemove(){
      uni.showLoading({
        zIndex:********
      });
      this.api.removePost({
        id:this.post.id
      }).then(res=>{
        this.$emit("removeSuccess",this.post);
        this.event.emitPostPush(this.post);
        // this.$kt.event.emit("fpCommunityPost:remove", this.post);
        uni.hideLoading();
        this.close();
      }).catch(err=>{
        uni.hideLoading();
        uni.showToast({
          title: err.errMsg,
          icon:"none"
        });
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.box{
  position: fixed;
  width: 100%;
  background-color: #FFFFFF;
  left:0;
  bottom: 0;
  padding: 20rpx 20rpx 20rpx 20rpx;
  box-sizing: border-box;
  border-radius: 30rpx 30rpx 0 0;

  .btn{
    border-radius: 10rpx;
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-size: 30rpx;
    color: #333333;
    border: 1px solid #E5E5E5;
    margin-bottom: 30rpx;
  }

  .report-scroll-view{
    height: calc(100vh - 500rpx)
  }
}


.menu-box{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  .menu-title{
    font-size: 28rpx;
    color: #333;
    vertical-align: top;
    .menu-icon{
      width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
      display: inline-block;
      vertical-align: top;
      .menu-icon-image{
        width: 100%;
        height: 100%;
        opacity: .8;
      }
    }
  }
  .menu-fixed{
    position: relative;
    .menu-fixed-tips{
      position: absolute;
      font-size: 28rpx;
      color: #333;
      text-align: right;
      width: 300rpx;
      right: 70rpx;
      vertical-align: top;
      margin-right: 5rpx;
    }
    .menu-fixed-content{
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      color: #666;
      .menu-fixed-content-icon{
        width: 20rpx;
        height: 20rpx;
      }
      .menu-fixed-content-icon-bottom{
        transform: rotate(90deg);
      }
      .menu-fixed-content-icon-top{
        transform: rotate(-90deg);
      }
    }
  }
}

.menu-box:active{
  opacity: .8;
}

.menu-box-box{
  padding: 0 20rpx 20rpx 20rpx;
}

.to-back-btn{
  font-size: 28rpx;
  color: #666;
  cursor: pointer;
}

.box-mode-device-pc{
  position: fixed;
  border-radius: 20rpx;
  width: 400px;
  left:50%;
  bottom: 50%;
  transform: translate(-50%,50%);

  .menu-box{
    cursor: pointer;
  }

  .report-scroll-view{
    max-height: 500px;
  }
}


</style>
