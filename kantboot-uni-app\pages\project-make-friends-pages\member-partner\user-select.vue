<template>
  <view>
    <view
        class="header"
        id="headerInUserSelect">
      <kt-nav-bar :title="$i18n.zhToGlobal('选择用户')" :showBack="true"></kt-nav-bar>
    </view>

    <!-- 内容区域 -->
    <view class="content-box">
      <!-- 搜索框 -->
      <view class="box-search">
        <view class="search-box">
          <u-input
            :placeholder="isInitialized ? $i18n.zhToGlobal('请输入ID或手机号搜索用户') : $i18n.zhToGlobal('正在加载组信息...')"
            @confirm="searchUsers"
            v-model="searchKeyword"
            @input="onSearchInput"
            :disabled="!isInitialized">
            <template slot="suffix">
              <button @tap="searchUsers" class="search-btn" :disabled="!searchKeyword.trim() || !isInitialized">搜索</button>
            </template>
          </u-input>
        </view>
      </view>

      <!-- 已选择用户 -->
      <view class="selected-section" v-if="selectedUsers.length > 0">
        <view class="selected-header">
          <text class="selected-title">{{ $i18n.zhToGlobal('已选择') }}({{ selectedUsers.length }})</text>
          <view class="clear-btn" @click="clearSelected">
            <text>{{ $i18n.zhToGlobal('清空') }}</text>
          </view>
        </view>
        <view class="selected-list">
          <view class="selected-item" v-for="user in selectedUsers" :key="user.id">
            <view class="selected-user-info">
              <kt-avatar :src="$kt.file.visit(user.fileIdOfAvatar)" size="60rpx"></kt-avatar>
              <view class="user-info-content">
                <text class="selected-name">{{ user.nickname || user.id }}</text>
                <text class="current-user-tag" v-if="isCurrentUser(user)">{{ $i18n.zhToGlobal('(我)') }}</text>
              </view>
              <view class="user-actions">
                <text class="remove-icon" v-if="!(mode === 'createGroup' && isCurrentUser(user))" @click="handleUserClick(user)">×</text>
              </view>
            </view>
            <!-- 概率显示和操作 -->
            <view class="probability-display-section">
              <text class="probability-label">{{ $i18n.zhToGlobal('概率') }}:</text>
              <text class="probability-value">{{ user.probability }}%</text>
              <view class="probability-actions">
                <text class="edit-probability-btn" @click="editUserProbability(user)">{{ $i18n.zhToGlobal('修改') }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 用户列表 -->
      <view class="user-list-section">
        <view v-if="userList.length === 0 && !isLoading && searchKeyword" class="empty-tip">
          <text>{{ $i18n.zhToGlobal('暂无数据') }}</text>
        </view>

        <view v-if="!searchKeyword" class="search-tip">
          <text v-if="isInitialized">{{ $i18n.zhToGlobal('请输入关键词搜索用户') }}</text>
          <text v-else>{{ $i18n.zhToGlobal('正在加载组信息，请稍后...') }}</text>
        </view>

        <view class="user-item" v-for="user in userList" :key="user.id" @click="toggleUser(user)">
          <view class="user-card">
            <kt-user-info-card :user-info="user"></kt-user-info-card>
            <view class="user-actions">
              <view class="user-id">ID: {{ user.id }}</view>
              <view class="user-phone" v-if="user.phone">{{ user.phoneAreaCode }} {{ user.phone }}</view>
              <view class="select-checkbox" :class="{ 'selected': isUserSelected(user) }">
                <text v-if="isUserSelected(user)">✓</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <u-loading-icon v-if="isLoading" mode="circle" size="50rpx"></u-loading-icon>
        <view v-if="isBottom && userList.length > 0" class="load-more-tip">
          {{ $i18n.zhToGlobal('没有更多了') }}
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions" v-if="selectedUsers.length > 0">
      <view class="action-info">
        <text>{{ $i18n.zhToGlobal('已选择') }} {{ selectedUsers.length }} {{ $i18n.zhToGlobal('人') }}</text>
      </view>
      <view class="action-btn" @click="confirmSelection">
        <text>{{ mode === 'createGroup' ? $i18n.zhToGlobal('确认创建') : $i18n.zhToGlobal('确认添加') }}</text>
      </view>
    </view>

    <!-- 全局加载遮罩 -->
    <u-loading-page
      :loading="globalLoading"
      :text="loadingText"
      :custom-style="{ zIndex: 10000 }">
    </u-loading-page>

    <!-- 概率设置弹窗 -->
    <project-make-friends-partner-popup
      ref="probabilityPopup"
      @confirm="onProbabilityConfirm">
    </project-make-friends-partner-popup>

    <!-- 项目名称输入弹窗 -->
    <project-make-friends-project-name-popup
      ref="projectNamePopup"
      @confirm="onProjectNameConfirm">
    </project-make-friends-project-name-popup>
  </view>
</template>

<script>
import ProjectMakeFriendsPartnerPopup from '@/uni_modules/project-make-friends/components/project-make-friends-partner-popup/project-make-friends-partner-popup.vue'
import ProjectMakeFriendsProjectNamePopup from '@/uni_modules/project-make-friends/components/project-make-friends-project-name-popup/project-make-friends-project-name-popup.vue'

export default {
  components: {
    ProjectMakeFriendsPartnerPopup,
    ProjectMakeFriendsProjectNamePopup
  },
  data() {
    return {
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      },
      mode: 'addToGroup', // 模式：addToGroup-添加到组, createGroup-创建新组
      groupId: null, // 邀约组ID（addToGroup模式使用）
      searchKeyword: '', // 搜索关键词
      userList: [], // 用户列表
      selectedUsers: [], // 已选择的用户
      isLoading: false, // 是否加载中
      isBottom: false, // 是否到底部
      pageNum: 1,
      pageSize: 20,
      searchTimer: null, // 搜索防抖定时器
      globalLoading: false, // 全局加载状态
      loadingText: '处理中...', // 加载提示文本
      creatorUserId: null, // 创建人用户ID
      creatorProbability: 100, // 创建人当前概率
      existingMemberIds: [], // 已存在的成员ID列表
      isInitialized: false, // 是否已初始化完成
      projectName: '', // 项目名称
    }
  },
  computed: {
    // 计算创建人的可分配概率
    availableProbability() {
      if (this.mode === 'createGroup') {
        const currentUser = this.selectedUsers.find(u => this.isCurrentUser(u));
        return currentUser ? currentUser.probability : 100;
      } else {
        // 添加到现有组模式，使用创建人的概率
        return this.creatorProbability;
      }
    }
  },
  onLoad(options) {
    // 获取传入的参数
    if (options.mode) {
      this.mode = options.mode;
    }
    if (options.groupId) {
      this.groupId = options.groupId;
    }

    // 默认添加当前用户
    this.addCurrentUser();

    if (this.mode === 'addToGroup') {
      // 如果是添加到现有组，需要获取组信息和创建人信息
      this.initializeAddToGroupMode();
    } else {
      // 创建新组模式直接标记为已初始化
      this.isInitialized = true;
    }
  },
  onReachBottom() {
    this.loadMore();
  },
  methods: {
    // 添加当前用户到选择列表
    addCurrentUser() {
      this.self = this.$kt.userAccount.getSelf()
      // 在添加到现有组模式下，不自动添加当前用户，因为当前用户已经在组中
      if (this.mode === 'createGroup') {
        const currentUser = {
          id: this.self.id,
          nickname: '我',
          fileIdOfAvatar: null,
          probability: 100 // 创建新组模式下当前用户100%概率
        };
        this.selectedUsers.push(currentUser);
      }
      // 添加到现有组模式下不添加当前用户到选择列表
    },

    // 搜索输入
    onSearchInput(e) {
      this.searchKeyword = e.detail.value;
      this.debounceSearch();
    },

    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.searchUsers();
      }, 500);
    },

    // 搜索用户
    searchUsers() {
      if (!this.searchKeyword.trim()) {
        this.userList = [];
        return;
      }

      // 如果是添加到现有组模式，需要等待初始化完成
      if (this.mode === 'addToGroup' && !this.isInitialized) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('正在加载组信息，请稍后...'),
          icon: 'none'
        });
        return;
      }

      this.isLoading = true;
      this.pageNum = 1;
      this.isBottom = false;

      // 根据模式选择不同的接口
      const apiUrl = this.mode === 'addToGroup'
        ? '/project-make-friends-web/invitationGroupCollaborator/getAvailableUsersForGroup'
        : '/project-make-friends-web/userTransferConsent/getList';

      const requestData = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        searchText: this.searchKeyword.trim()
      };

      // 如果是添加到现有组模式，添加邀约组ID参数
      if (this.mode === 'addToGroup') {
        requestData.invitationGroupId = this.groupId;
      }

      this.$request.post(apiUrl, {
        data: requestData
      }).then(res => {
        if (res.state === 2000) {
          this.userList = res.data || [];
          if (this.userList.length < this.pageSize) {
            this.isBottom = true;
          }
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('搜索失败'),
            icon: 'none'
          });
        }
        this.isLoading = false;
      }).catch(err => {
        console.error('搜索用户失败:', err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
        this.isLoading = false;
      });
    },

    // 加载更多
    loadMore() {
      if (this.isLoading || this.isBottom || !this.searchKeyword.trim()) return;

      this.pageNum++;
      this.isLoading = true;

      // 根据模式选择不同的接口
      const apiUrl = this.mode === 'addToGroup'
        ? '/project-make-friends-web/invitationGroupCollaborator/getAvailableUsersForGroup'
        : '/project-make-friends-web/userTransferConsent/getList';

      const requestData = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        searchText: this.searchKeyword.trim()
      };

      // 如果是添加到现有组模式，添加邀约组ID参数
      if (this.mode === 'addToGroup') {
        requestData.invitationGroupId = this.groupId;
      }

      this.$request.post(apiUrl, {
        data: requestData
      }).then(res => {
        if (res.state === 2000) {
          const newData = res.data || [];
          this.userList = [...this.userList, ...newData];
          
          if (newData.length < this.pageSize) {
            this.isBottom = true;
          }
        }
        this.isLoading = false;
      }).catch(err => {
        console.error('加载更多失败:', err);
        this.isLoading = false;
      });
    },

    // 切换用户选择状态
    toggleUser(user) {
      if (this.isUserSelected(user)) {
        this.removeUser(user);
      } else {
        // 两种模式都显示概率设置弹窗
        this.showProbabilityModal(user);
      }
    },

    // 添加用户（通过概率弹窗）
    addUser(user, probability) {
      if (!this.isUserSelected(user)) {
        const userWithProbability = {
          ...user,
          probability: probability
        };
        this.selectedUsers.push(userWithProbability);

        if (this.mode === 'createGroup') {
          // 创建新组模式：从当前用户的概率中扣减
          const currentUser = this.selectedUsers.find(u => this.isCurrentUser(u));
          if (currentUser) {
            currentUser.probability -= probability;
          }
        } else {
          // 添加到现有组模式：从创建人的概率中扣减
          this.creatorProbability -= probability;
        }
      }
    },



    // 移除用户
    removeUser(user) {
      // 创建新组模式下，当前登录用户不可删除
      if (this.mode === 'createGroup' && this.isCurrentUser(user)) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('创建人不可删除'),
          icon: 'none'
        });
        return;
      }

      const index = this.selectedUsers.findIndex(u => u.id === user.id);
      if (index > -1) {
        const removedUser = this.selectedUsers[index];

        if (this.mode === 'createGroup') {
          // 创建新组模式：将删除用户的概率加回到当前用户
          const currentUser = this.selectedUsers.find(u => this.isCurrentUser(u));
          if (currentUser) {
            currentUser.probability += removedUser.probability;
          }
        } else {
          // 添加到现有组模式：将删除用户的概率加回到创建人
          this.creatorProbability += removedUser.probability;
        }

        this.selectedUsers.splice(index, 1);
      }
    },

    // 检查用户是否已选择
    isUserSelected(user) {
      return this.selectedUsers.some(u => u.id === user.id);
    },

    // 检查是否为当前登录用户
    isCurrentUser(user) {
      return user.id === (this.self ? this.self.id : null);
    },

    // 处理用户点击事件
    handleUserClick(user) {
      // 创建新组模式下，当前用户不可删除；添加到现有组模式下，所有用户都可以删除
      if (!(this.mode === 'createGroup' && this.isCurrentUser(user))) {
        this.removeUser(user);
      }
    },

    // 清空选择
    clearSelected() {
      if (this.mode === 'createGroup') {
        // 创建新组模式保留当前用户，并重置概率为100%
        const currentUser = this.selectedUsers.find(u => u.id === this.self.id);
        if (currentUser) {
          currentUser.probability = 100;
          this.selectedUsers = [currentUser];
        }
      } else {
        // 添加到现有组模式清空所有选择，重置创建人概率
        this.selectedUsers = [];
        this.getCreatorProbability(); // 重新获取创建人概率
      }
    },

    // 显示概率输入弹窗
    showProbabilityModal(user) {
      if (this.availableProbability <= 0) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('创建人可分配概率为0，无法添加更多用户'),
          icon: 'none'
        });
        return;
      }

      this.$refs.probabilityPopup.open({
        user: user,
        availableProbability: this.availableProbability
      });
    },

    // 概率确认回调
    onProbabilityConfirm(data) {
      if (data.isEdit) {
        // 修改现有用户概率
        this.updateUserProbability(data.user, data.probability, data.oldProbability);
      } else {
        // 添加新用户
        this.addUser(data.user, data.probability);
      }
    },

    // 编辑用户概率
    editUserProbability(user) {
      // 计算可用概率（创建人概率 + 要编辑用户的概率）
      const currentUser = this.selectedUsers.find(u => this.isCurrentUser(u));
      const availableProbability = currentUser ? currentUser.probability + user.probability : user.probability;

      this.$refs.probabilityPopup.open({
        user: user,
        availableProbability: availableProbability,
        isEdit: true,
        currentProbability: user.probability
      });
    },

    // 更新用户概率
    updateUserProbability(user, newProbability, oldProbability) {
      const userIndex = this.selectedUsers.findIndex(u => u.id === user.id);
      if (userIndex > -1) {
        // 更新用户概率
        this.selectedUsers[userIndex].probability = newProbability;

        if (this.mode === 'createGroup') {
          // 创建新组模式：调整当前用户的概率
          const currentUser = this.selectedUsers.find(u => this.isCurrentUser(u));
          if (currentUser) {
            const probabilityDiff = oldProbability - newProbability;
            currentUser.probability += probabilityDiff;
          }
        } else {
          // 添加到现有组模式：调整创建人的概率
          const probabilityDiff = oldProbability - newProbability;
          this.creatorProbability += probabilityDiff;
        }
      }
    },

    // 获取组信息
    getGroupInfo() {
      return new Promise((resolve, reject) => {
        this.$request.post('/project-make-friends-web/invitationGroup/getById', {
          data: {
            id: this.groupId
          }
        }).then(res => {
          if (res.state === 2000) {
            this.creatorUserId = res.data.creatorUserAccountId;
            // 获取创建人的当前概率
            return this.getCreatorProbability();
          } else {
            reject(new Error(res.msg || '获取组信息失败'));
          }
        }).then(() => {
          resolve();
        }).catch(err => {
          console.error('获取组信息失败:', err);
          reject(err);
        });
      });
    },

    // 获取创建人当前概率
    getCreatorProbability() {
      return new Promise((resolve, reject) => {
        this.$request.post('/project-make-friends-web/invitationRelation/getMembersByGroupId', {
          data: {
            invitationGroupId: this.groupId,
            pageNum: 1,
            pageSize: 100
          }
        }).then(res => {
          if (res.state === 2000) {
            const members = res.data || [];
            const creatorMember = members.find(m => m.userAccountId === this.creatorUserId);
            if (creatorMember) {
              this.creatorProbability = creatorMember.probability * 100; // 转换为百分比
            }
            resolve();
          } else {
            reject(new Error(res.msg || '获取创建人概率失败'));
          }
        }).catch(err => {
          console.error('获取创建人概率失败:', err);
          reject(err);
        });
      });
    },

    // 初始化添加到现有组模式
    async initializeAddToGroupMode() {
      try {
        // 并行获取组信息和现有成员
        await Promise.all([
          this.getGroupInfo(),
          this.getExistingMembers()
        ]);
        this.isInitialized = true;
      } catch (error) {
        console.error('初始化失败:', error);
        uni.showToast({
          title: this.$i18n.zhToGlobal('初始化失败，请重试'),
          icon: 'none'
        });
      }
    },

    // 获取现有成员ID列表
    getExistingMembers() {
      return new Promise((resolve, reject) => {
        this.$request.post('/project-make-friends-web/invitationRelation/getAllMembersByGroupId', {
          data: {
            invitationGroupId: this.groupId
          }
        }).then(res => {
          if (res.state === 2000) {
            const members = res.data || [];
            this.existingMemberIds = members.map(m => m.userAccountId);
            console.log('获取到现有成员ID列表:', this.existingMemberIds);
            resolve(members);
          } else {
            reject(new Error(res.msg || '获取现有成员失败'));
          }
        }).catch(err => {
          console.error('获取现有成员失败:', err);
          reject(err);
        });
      });
    },

    // 确认选择
    confirmSelection() {
      if (this.selectedUsers.length === 0) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('请选择至少一个用户'),
          icon: 'none'
        });
        return;
      }

      if (this.mode === 'createGroup') {
        // 创建新组模式：验证总概率是否为100%
        const totalProbability = this.selectedUsers.reduce((sum, user) => sum + user.probability, 0);
        if (Math.abs(totalProbability - 100) > 0.01) {
          uni.showToast({
            title: this.$i18n.zhToGlobal(`总概率必须为100%，当前为${totalProbability.toFixed(1)}%`),
            icon: 'none'
          });
          return;
        }
      } else {
        // 添加到现有组模式：验证总概率不能超过创建人的可用概率
        const totalProbability = this.selectedUsers.reduce((sum, user) => sum + user.probability, 0);
        if (totalProbability > this.creatorProbability) {
          uni.showToast({
            title: this.$i18n.zhToGlobal(`总概率不能超过${this.creatorProbability.toFixed(1)}%，当前为${totalProbability.toFixed(1)}%`),
            icon: 'none'
          });
          return;
        }
      }

      if (this.mode === 'addToGroup') {
        this.addUsersToGroup();
      } else if (this.mode === 'addCollaborator') {
        this.addCollaborators();
      } else if (this.mode === 'createGroup') {
        // 创建组模式：先显示项目名称输入弹窗
        this.showProjectNameModal();
      }
    },

    // 添加用户到现有组
    addUsersToGroup() {
      this.globalLoading = true;
      this.loadingText = this.$i18n.zhToGlobal('正在添加用户...');

      // 构建成员数据列表，包含用户ID和概率
      const memberDataList = this.selectedUsers.map(user => ({
        userAccountId: user.id,
        probability: parseFloat(user.probability) / 100 // 转换为0-1之间的小数
      }));

      // 使用批量添加接口并自动调整创建人概率
      this.$request.post('/project-make-friends-web/invitationRelation/addMembersToGroupWithCreatorAdjustment', {
        data: {
          invitationGroupId: this.groupId,
          memberDataList: memberDataList,
          creatorUserAccountId: this.creatorUserId,
          isInitialCreation: 0,
          participationStatus: 1 // 默认设置为已同意状态
        }
      }).then(res => {
        if (res.state === 2000) {
          const addedMembers = res.data || [];
          const successCount = addedMembers.length;

          uni.showToast({
            title: this.$i18n.zhToGlobal(`成功添加${successCount}个用户`),
            icon: 'success'
          });

          // 触发成功事件通知父页面
          const eventChannel = this.getOpenerEventChannel();
          if (eventChannel) {
            eventChannel.emit('addSuccess', {
              successCount: successCount,
              totalCount: this.selectedUsers.length
            });
          }

          setTimeout(() => {
            uni.navigateBack();
          }, 500);
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('添加失败'),
            icon: 'none',
            duration: 3000
          });
        }
        this.globalLoading = false;
      }).catch(err => {
        console.error('添加用户失败:', err);

        // 检查错误详情
        let errorMessage = this.$i18n.zhToGlobal('网络异常，请稍后重试');
        if (err.response && err.response.data && err.response.data.msg) {
          errorMessage = err.response.data.msg;
        } else if (err.message) {
          errorMessage = err.message;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
        this.globalLoading = false;
      });
    },

    // 创建新组
    createNewGroup() {
      this.globalLoading = true;
      this.loadingText = this.$i18n.zhToGlobal('正在创建组...');

      // 构建用户数据，包含概率信息
      const usersWithProbability = this.selectedUsers.map(user => ({
        userAccountId: user.id,
        probability: parseFloat(user.probability) / 100 // 转换为0-1之间的小数
      }));

      this.$request.post('/project-make-friends-web/invitationGroup/createNewInvitationGroupWithProbability', {
        data: {
          users: usersWithProbability,
          defaultParticipationStatus: 1 // 默认设置为已同意状态
        }
      }).then(res => {
        if (res.state === 2000) {
          uni.showToast({
            title: this.$i18n.zhToGlobal('创建成功'),
            icon: 'success'
          });
          setTimeout(() => {
            // 跳转到新创建的组页面
            uni.redirectTo({
              url: `/pages/project-make-friends-pages/member-partner/member-partner-content?invitationGroupId=${res.data.id}`
            });
          }, 500);
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('创建失败'),
            icon: 'none'
          });
        }
        this.globalLoading = false;
      }).catch(err => {
        console.error('创建组失败:', err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
        this.globalLoading = false;
      });
    },

    // 获取页面间通信的事件通道
    getOpenerEventChannel() {
      try {
        // 在uni-app中获取事件通道
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        return currentPage && currentPage.getOpenerEventChannel ? currentPage.getOpenerEventChannel() : null;
      } catch (e) {
        console.log('获取事件通道失败:', e);
        return null;
      }
    },

    // 显示项目名称输入弹窗
    showProjectNameModal() {
      this.$refs.projectNamePopup.open();
    },

    // 项目名称确认回调
    onProjectNameConfirm(data) {
      this.projectName = data.projectName;
      this.createNewGroupWithName();
    },

    // 创建新组（带项目名称）
    createNewGroupWithName() {
      this.globalLoading = true;
      this.loadingText = this.$i18n.zhToGlobal('正在创建组...');

      // 构建用户数据，包含概率信息
      const usersWithProbability = this.selectedUsers.map(user => ({
        userAccountId: user.id,
        probability: parseFloat(user.probability) / 100 // 转换为0-1之间的小数
      }));

      this.$request.post('/project-make-friends-web/invitationGroup/createNewInvitationGroupWithProbabilityAndName', {
        data: {
          groupName: this.projectName.trim(),
          users: usersWithProbability,
          defaultParticipationStatus: 1 // 默认设置为已同意状态
        }
      }).then(res => {
        if (res.state === 2000) {
          uni.showToast({
            title: this.$i18n.zhToGlobal('创建成功'),
            icon: 'success'
          });
          setTimeout(() => {
            // 跳转到新创建的组页面
            uni.redirectTo({
              url: `/pages/project-make-friends-pages/member-partner/member-partner-content?invitationGroupId=${res.data.id}`
            });
          }, 500);
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('创建失败'),
            icon: 'none'
          });
        }
        this.globalLoading = false;
      }).catch(err => {
        console.error('创建组失败:', err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
        this.globalLoading = false;
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.back {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: #F0F0F0;
  top: 0;
  left: 0;
  z-index: -1;
  overflow: hidden;
}

.header-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}

.content-box {
  padding-bottom: 140rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 搜索框样式 - 与member-partner.vue一致 */
.box-search {
  width: calc(100% - 40rpx);
  background-color: #F0F0F0;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  padding: 10rpx;
}

/* 搜索按钮样式 */
.search-btn {
  background-color: #000000;
  height: 30px;
  line-height: 30px;
  color: #ffffff;

  &[disabled] {
    background-color: #cccccc;
    color: #ffffff;
    opacity: 0.6;
  }
}

/* 已选择用户区域 */
.selected-section {
  margin: 0 20rpx 20rpx 20rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .selected-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .selected-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #333333;
    }

    .clear-btn {
      padding: 8rpx 16rpx;
      background-color: #F0F0F0;
      border-radius: 16rpx;
      font-size: 24rpx;
      color: #666666;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .selected-list {
    display: flex;
    flex-direction: column;
    gap: 15rpx;

    .selected-item {
      background-color: #F8F9FA;
      border-radius: 12rpx;
      padding: 15rpx;

      .selected-user-info {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;

        .user-info-content {
          display: flex;
          align-items: center;
          flex: 1;
          margin-left: 10rpx;

          .selected-name {
            font-size: 24rpx;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .current-user-tag {
            font-size: 20rpx;
            color: #FF9500;
            margin-left: 5rpx;
          }
        }

        .user-actions {
          .remove-icon {
            font-size: 30rpx;
            color: #999999;
            padding: 10rpx;

            &:active {
              opacity: 0.7;
            }
          }
        }
      }

      .probability-display-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 10rpx;
        padding: 10rpx 0;

        .probability-label {
          font-size: 24rpx;
          color: #666666;
          margin-right: 10rpx;
          min-width: 80rpx;
        }

        .probability-value {
          font-size: 24rpx;
          color: #000000;
          font-weight: bold;
          flex: 1;
        }

        .probability-actions {
          .edit-probability-btn {
            font-size: 22rpx;
            color: #FFFFFF;
            padding: 8rpx 16rpx;
            background-color: #000000;
            border-radius: 12rpx;
            border: 1rpx solid #000000;

            &:active {
              opacity: 0.8;
              background-color: #333333;
            }
          }
        }

        .probability-note {
          font-size: 20rpx;
          color: #FF9500;
          margin-left: 10rpx;
          font-weight: normal;
        }
      }
    }
  }
}

/* 用户列表区域 */
.user-list-section {
  padding: 0 20rpx;

  .empty-tip, .search-tip {
    text-align: center;
    padding: 100rpx 20rpx;
    color: #999999;
    font-size: 28rpx;
  }

  .user-item {
    background-color: #FFFFFF;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    &:active {
      opacity: 0.8;
    }

    .user-card {
      position: relative;

      .user-actions {
        margin-top: 15rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .user-id, .user-phone {
          font-size: 26rpx;
          color: #666666;
        }

        .select-checkbox {
          width: 50rpx;
          height: 50rpx;
          border: 2rpx solid #E0E0E0;
          border-radius: 25rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 30rpx;
          color: #FFFFFF;

          &.selected {
            background-color: #000000;
            border-color: #000000;
          }
        }
      }
    }
  }

  .load-more-tip {
    text-align: center;
    color: #999999;
    font-size: 24rpx;
    padding: 20rpx;
  }
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 998; /* 降低z-index，确保在加载遮罩下方 */
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-sizing: border-box;

  .action-info {
    flex: 1;
    font-size: 28rpx;
    color: #666666;
  }

  .action-btn {
    padding: 25rpx 40rpx;
    background-color: #000000;
    color: #FFFFFF;
    border-radius: 25rpx;
    font-size: 30rpx;
    font-weight: bold;
    min-width: 200rpx;
    text-align: center;

    &:active {
      opacity: 0.8;
    }
  }
}

/* 项目名称输入弹窗样式 */
.project-name-modal-content {
  padding: 20rpx;

  .project-name-input-label {
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 20rpx;
  }

  .project-name-input {
    width: 100%;
    padding: 20rpx;
    border: 1rpx solid #E0E0E0;
    border-radius: 8rpx;
    font-size: 28rpx;
    background-color: #FFFFFF;
  }
}

</style>
