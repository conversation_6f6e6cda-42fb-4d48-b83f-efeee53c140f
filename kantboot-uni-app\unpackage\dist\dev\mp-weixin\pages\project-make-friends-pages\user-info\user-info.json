{"usingComponents": {"kt-nav-bar": "/uni_modules/kantboot/components/kt-nav-bar/kt-nav-bar", "project-make-friends-user-info-panel": "/uni_modules/project-make-friends/components/project-make-friends-user-info-panel/project-make-friends-user-info-panel", "birth-date-picker": "/pages/project-make-friends-pages/user-info/components/birth-date-picker", "user-info-popup": "/pages/project-make-friends-pages/user-info/components/user-info-popup", "user-info-card-edit": "/pages/project-make-friends-pages/user-info/components/user-info-card-edit", "vegetarian-picker": "/pages/project-make-friends-pages/user-info/components/vegetarian-picker", "interest-popup": "/pages/project-make-friends-pages/user-info/components/interest-popup", "personal-introduction-popup": "/pages/project-make-friends-pages/user-info/components/personal-introduction-popup", "un-follow-popup": "/pages/project-make-friends-pages/user-info/components/un-follow-popup", "characteristic-popup": "/pages/project-make-friends-pages/user-info/components/characteristic-popup"}}