<template>
  <view>
    <kt-popup ref="ktPopup">
      <view class="box">
        <view class="box-title">
          {{ $i18n.zhToGlobal("设置") }}
        </view>
        <view class="in-box">
          <scroll-view
          :style="'height: 500px;'"
              :scroll-y="true"
              class="in-box-user-info-card"
          >
          <kt-setting-panel
              :has-account="hasAccount"
              :has-phone="hasPhone"
              :has-email="hasEmail"
              :no-has-old-password="noHasOldPassword"
              :has-change-password="hasChangePassword"
              :has-data-manage="hasDataManage"
              :has-app="hasApp"
              :has-language="hasLanguage"
              :has-color-mode="hasColorMode"
              :has-about="hasAbout"
              :has-user-agreement="hasUserAgreement"
              :has-privacy-agreement="hasPrivacyAgreement"
              :user-agreement-url="userAgreementUrl"
              :privacy-agreement-url="privacyAgreementUrl"
          ></kt-setting-panel>
          </scroll-view>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  props: {
    /**
     * 是否有账户信息
     */
    hasAccount: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有手机信息
     */
    hasPhone: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有邮箱信息
     */
    hasEmail: {
      type: Boolean,
      default: true
    },
    hasChangePassword: {
      type: Boolean,
      default: true
    },
    noHasOldPassword: {
      type: Boolean,
      default: false
    },
    /**
     * 是否有数据管理
     */
    hasDataManage: {
      type: Boolean,
      default: true
    },

    /**
     * 是否有应用信息
     */
    hasApp: {
      type: Boolean,
      default: true
    },

    /**
     * 是否有语言信息
     */
    hasLanguage: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有颜色模式信息
     */
    hasColorMode: {
      type: Boolean,
      default: true
    },

    /**
     * 是否有关于信息
     */
    hasAbout: {
      type: Boolean,
      default: true
    },


    /**
     * 是否有用户协议
     */
    hasUserAgreement: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有隐私协议
     */
    hasPrivacyAgreement: {
      type: Boolean,
      default: true
    },
    /**
     * 使用者协议网址
     */
    userAgreementUrl: {
      type: String,
      default: ""
    },
    /**
     * 隐私协议网址
     */
    privacyAgreementUrl: {
      type: String,
      default: ""
    }
  },
  data() {
    return {};
  },
  methods: {
    open() {
      this.$refs.ktPopup.open();
    }
  },
}
</script>

<style lang="scss" scoped>

.box{
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 20rpx;
  .box-title{
    padding: 20rpx;
    font-size: 32rpx;
    font-weight: 500;
  }
  .in-box{
  }
}
</style>
