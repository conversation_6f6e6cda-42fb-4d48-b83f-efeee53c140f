/**
 * 选项卡顺序管理工具
 * 用于管理可拖拽选项卡的顺序状态和持久化
 */

const TAB_ORDER_STORAGE_KEY = 'draggable_tab_order';

class TabOrderManager {
  constructor() {
    this.defaultTabs = [];
    this.currentTabs = [];
  }

  /**
   * 初始化默认选项卡
   * @param {Array} tabs 默认选项卡数组
   */
  setDefaultTabs(tabs) {
    this.defaultTabs = [...tabs];
    this.currentTabs = [...tabs];
  }

  /**
   * 从本地存储加载选项卡顺序
   * @param {String} pageKey 页面标识符，用于区分不同页面的选项卡顺序
   * @returns {Array} 加载的选项卡数组
   */
  loadTabOrder(pageKey = 'default') {
    try {
      const storageKey = `${TAB_ORDER_STORAGE_KEY}_${pageKey}`;
      const savedOrder = uni.getStorageSync(storageKey);
      
      if (savedOrder && Array.isArray(savedOrder) && savedOrder.length > 0) {
        // 验证保存的顺序是否包含所有必需的选项卡
        const savedKeys = savedOrder.map(tab => tab.key);
        const defaultKeys = this.defaultTabs.map(tab => tab.key);
        
        // 检查是否所有默认选项卡都存在于保存的顺序中
        const hasAllTabs = defaultKeys.every(key => savedKeys.includes(key));
        
        // 检查是否有多余的选项卡
        const hasExtraTabs = savedKeys.some(key => !defaultKeys.includes(key));
        
        if (hasAllTabs && !hasExtraTabs) {
          this.currentTabs = savedOrder;
          return savedOrder;
        } else {
          console.warn('保存的选项卡顺序与默认配置不匹配，使用默认顺序');
        }
      }
    } catch (error) {
      console.error('加载选项卡顺序失败:', error);
    }
    
    // 如果加载失败或验证失败，返回默认顺序
    this.currentTabs = [...this.defaultTabs];
    return this.currentTabs;
  }

  /**
   * 保存选项卡顺序到本地存储
   * @param {Array} tabs 要保存的选项卡数组
   * @param {String} pageKey 页面标识符
   * @returns {Boolean} 保存是否成功
   */
  saveTabOrder(tabs, pageKey = 'default') {
    try {
      if (!Array.isArray(tabs) || tabs.length === 0) {
        console.error('无效的选项卡数据');
        return false;
      }

      // 验证选项卡数据的完整性
      const isValid = tabs.every(tab => 
        tab && 
        typeof tab === 'object' && 
        typeof tab.key === 'string' && 
        typeof tab.label === 'string'
      );

      if (!isValid) {
        console.error('选项卡数据格式无效');
        return false;
      }

      const storageKey = `${TAB_ORDER_STORAGE_KEY}_${pageKey}`;
      uni.setStorageSync(storageKey, tabs);
      this.currentTabs = [...tabs];
      
      console.log('选项卡顺序保存成功:', tabs);
      return true;
    } catch (error) {
      console.error('保存选项卡顺序失败:', error);
      return false;
    }
  }

  /**
   * 重置选项卡顺序为默认顺序
   * @param {String} pageKey 页面标识符
   * @returns {Array} 重置后的选项卡数组
   */
  resetTabOrder(pageKey = 'default') {
    try {
      const storageKey = `${TAB_ORDER_STORAGE_KEY}_${pageKey}`;
      uni.removeStorageSync(storageKey);
      this.currentTabs = [...this.defaultTabs];
      console.log('选项卡顺序已重置为默认');
      return this.currentTabs;
    } catch (error) {
      console.error('重置选项卡顺序失败:', error);
      return this.defaultTabs;
    }
  }

  /**
   * 获取当前选项卡顺序
   * @returns {Array} 当前选项卡数组
   */
  getCurrentTabs() {
    return [...this.currentTabs];
  }

  /**
   * 获取默认选项卡顺序
   * @returns {Array} 默认选项卡数组
   */
  getDefaultTabs() {
    return [...this.defaultTabs];
  }

  /**
   * 检查选项卡顺序是否已被修改
   * @returns {Boolean} 是否已修改
   */
  isOrderModified() {
    if (this.currentTabs.length !== this.defaultTabs.length) {
      return true;
    }

    return this.currentTabs.some((tab, index) => 
      tab.key !== this.defaultTabs[index].key
    );
  }

  /**
   * 验证选项卡数组的有效性
   * @param {Array} tabs 要验证的选项卡数组
   * @returns {Boolean} 是否有效
   */
  validateTabs(tabs) {
    if (!Array.isArray(tabs) || tabs.length === 0) {
      return false;
    }

    return tabs.every(tab => 
      tab && 
      typeof tab === 'object' && 
      typeof tab.key === 'string' && 
      tab.key.trim() !== '' &&
      typeof tab.label === 'string' && 
      tab.label.trim() !== ''
    );
  }

  /**
   * 清除所有页面的选项卡顺序缓存
   */
  clearAllCache() {
    try {
      const storageInfo = uni.getStorageInfoSync();
      const keysToRemove = storageInfo.keys.filter(key => 
        key.startsWith(TAB_ORDER_STORAGE_KEY)
      );
      
      keysToRemove.forEach(key => {
        uni.removeStorageSync(key);
      });
      
      console.log('已清除所有选项卡顺序缓存');
    } catch (error) {
      console.error('清除缓存失败:', error);
    }
  }
}

// 创建单例实例
const tabOrderManager = new TabOrderManager();

export default tabOrderManager;
