<template>
  <view>
    <view class="header" id="headerInChatPage">
      <kt-nav-bar
          :background-color="'rgba(0,0,0,0)'"
          :title="$i18n.zhToGlobal('聊天')"
          :isHasBack="true"
          :isHasI18n="false"></kt-nav-bar>

      <view class="box">
        <chat-message-header
            :dialog="chatDialog"
        ></chat-message-header>
      </view>

    </view>
    <view
        class="bg"
    ></view>
    <view
        :style="{
			height: 'calc(100vh - '+headerHeight+'px)',}"
        class="loading"
        v-if="false"
    >
      <image
          :src="$kt.file.byPath('icon/chat.svg')"
          class="loading-image"></image>
    </view>

    <view>
      <chat-message-panel
          ref="chatPanel"
          :dialogId="dialogId"
          :height="'calc(100vh - '+headerHeight+'px - '+footerHeight+'px)'"
      ></chat-message-panel>
    </view>

    <view
        id="footerInChatPage"
        class="chat-footer-container">
      <chat-input-box
          :dialog-id="chatDialog.id"
          @change="getHeight()"
          @sendMessage="sendMessageHandle()"
          @sendMessageSuccess="sendMessageHandle()"
      ></chat-input-box>
      <view style="height: 20rpx"></view>
      <kt-keyboard-size
          ref="keyboardSize"
          @show="onKeyboardShow()"
          @hide="getHeight()"
      ></kt-keyboard-size>
    </view>

  </view>
</template>

<script>

export default {
  async onLoad(options) {
    // 获取用户id
    this.userAccountId = options.userAccountId;
    this.dialogId = options.dialogId;
    this.loading = true;
    if (this.userAccountId) {
      await this.getChatByUserAccountId();
    } else if (this.dialogId) {
      await this.getChatById();
    }
    this.loading = false;

  },
  data() {
    return {
      userAccountId: "",
      userAccount: {},
      dialogId: "",
      chatDialog: {},
      loading: true,
      headerHeight: 0,
      footerHeight: 0,
      requestParam: {
        value: "",
        placeholder: this.$i18n.zhToGlobal("请输入")
      },
      pageHeight: 0,
      keyboardHeight: 0
    }
  },
  onShow() {
  },
  mounted() {
    this.getHeight();
  },
  methods: {
    sendMessageHandle() {
      this.$refs.chatPanel.moveToEndForce();
    },
    onKeyboardShow() {
      this.getHeight();
      // setTimeout(()=>{
      // 	// 将聊天面板强制移动到底部
      // 	this.$refs.chatPanel.moveToEndForce();
      // },500);
    },
    getHeight() {
      this.keyboardHeight = this.$refs.keyboardSize.getHeight();
      this.getPageHeight();
      this.getHeaderHeight();
      this.getFooterHeight();
      setTimeout(() => {
        this.keyboardHeight = this.$refs.keyboardSize.getHeight();

        this.getPageHeight();
        this.getHeaderHeight();
        this.getFooterHeight();
      }, 50);
      setTimeout(() => {
        this.keyboardHeight = this.$refs.keyboardSize.getHeight();

        this.getPageHeight();
        this.getHeaderHeight();
        this.getFooterHeight();
      }, 100);
      setTimeout(() => {
        this.keyboardHeight = this.$refs.keyboardSize.getHeight();

        this.getPageHeight();
        this.getHeaderHeight();
        this.getFooterHeight();
      }, 200);
      setTimeout(() => {
        this.keyboardHeight = this.$refs.keyboardSize.getHeight();

        this.getPageHeight();
        this.getHeaderHeight();
        this.getFooterHeight();
      }, 500);
      setTimeout(() => {
        this.keyboardHeight = this.$refs.keyboardSize.getHeight();

        this.getPageHeight();
        this.getHeaderHeight();
        this.getFooterHeight();
      }, 1000);
      setTimeout(() => {
        this.keyboardHeight = this.$refs.keyboardSize.getHeight();

        this.getPageHeight();
        this.getHeaderHeight();
        this.getFooterHeight();
      }, 2000);

    },
    // 获取页面高度
    getPageHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.pageHeight = res.windowHeight;
        }
      });
    },
    // 获取头部高度
    getHeaderHeight() {
      this.$nextTick(() => {
        uni.createSelectorQuery().select("#headerInChatPage").boundingClientRect((res) => {
          this.headerHeight = res.height;
        }).exec();
      });
    },
    // 获取底部高度
    getFooterHeight() {
      this.$nextTick(() => {
        uni.createSelectorQuery().select("#footerInChatPage").boundingClientRect((res) => {
          this.footerHeight = res.height;
        }).exec();
      });
    },
    // /functional-chat-web/chatDialog/getByUserAccountId
    async getChatByUserAccountId() {
      await this.$request.post("/functional-chat-web/dialog/getOneToOneByUserAccountId", {
        data: {userAccountId: this.userAccountId}
      }).then(res => {
        this.chatDialog = res.data;
        this.dialogId = res.data.id;
      }).catch(err => {
        this.$kt.toast.error(err);
      });
    },
    async getChatById() {
      await this.$request.post("/functional-chat-web/dialog/getById", {
        data: {id: this.dialogId}
      }).then(res => {
        this.chatDialog = res.data;
        this.dialogId = res.data.id;
        if (this.chatDialog.type === "oneToOne") {
          let oneToOneIdentifierSplit = this.chatDialog.oneToOneIdentifier.split("&");
          let userAccountId = oneToOneIdentifierSplit[0].split(":")[1];
          if (userAccountId + "" === this.$kt.userAccount.getSelf().id + "") {
            userAccountId = oneToOneIdentifierSplit[1].split(":")[1];
          }
          this.userAccountId = userAccountId;
        }

      }).catch(err => {
        this.$kt.toast.error(err);
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.chat-footer-container {
  // 渐变
  background: linear-gradient(to bottom, rgba(255, 255, 255, .3) 20%, rgba(255, 255, 255, 1) 80%, rgba(255, 255, 255, 1) 100%);
}

.box {
  padding: 30rpx;
  box-sizing: border-box;
}

.bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f9f9f9;
  z-index: -1;
}

.header {
  // 渐变
  //background: linear-gradient(to bottom, rgba(255, 255, 255, 1) 20%, rgba(255, 255, 255, 1) 80%, rgba(255, 255, 255, 0) 100%);
  background-color: #FFFFFF;
}

.loading {
  position: relative;

  .loading-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -100%);
    animation: loadingInChatPage 1s infinite linear;
  }
}

@keyframes loadingInChatPage {
  0% {
    opacity: .3;
  }
  50% {
    opacity: .2;
  }
  100% {
    opacity: .3;
  }
}


.back {
  background-color: #fafafa;
  position: fixed;
  top: 0;
  left: -50rpx;
  width: calc(100% + 100rpx);
  height: 100%;
  z-index: -1;
}

.loading-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  z-index: 999;
}

.loading-icon {
  animation: loading-icon-ani-in-page-chat 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200rpx;
  height: 200rpx;
}

@keyframes loading-icon-ani-in-page-chat {
  0% {
    opacity: .5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: .5;
  }
}

.b-icon {
  width: 40rpx;
  height: 40rpx;
}

.back-img {
  width: 90rpx;
  height: 90rpx;
  margin-left: 30rpx;
  opacity: .1;
}

.back-img-1 {
  margin-left: 30rpx;
  margin-top: 60rpx;
}

.back-img-2 {
  margin-left: 70rpx;
  width: 130rpx;
  height: 130rpx;
  margin-top: 30rpx;
}

.nav-bar-box {
  // 渐变，从上往下，20%，50%，80%
  background: linear-gradient(to bottom, rgba(255, 255, 255, 1) 20%, rgba(255, 255, 255, 1) 80%, rgba(255, 255, 255, 0) 100%);
}

.language-box {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 22rpx;
  padding: 10rpx;
  color: #999999;
}
</style>
