<template>
	<view>
        <u-datetime-picker
                :title="$i18n.zhToGlobal('出生时间')"
                :show="show"
                :closeOnClickOverlay="true"
                @cancel="close"
                @close="close"
                @confirm="confirm"
                v-model="valueTime"
                :minDate="minDate"
                :maxDate="maxDate"
                visibleItemCount="10"
                confirmColor="#333"
                mode="datetime"
        ></u-datetime-picker>
	</view>
</template>

<script>
export default {
	data() {
		return {
      show: false,
      valueTime: '',
      userAccount:{},
      minDate: new Date(1900, 0, 1, 0, 0, 0).getTime(),
      maxDate: new Date((new Date().getFullYear())-1, 11, 31, 23, 59, 59).getTime(),
		}
	},
    mounted() {
        console.log(new Date().getFullYear(),"===");
    },
	methods: {
        async confirm(res){
            console.log(res.value);
            this.show = false;
            this.$emit('confirm', res.value);
            await this.$request.post('/user-account-web/userAccount/editCommonInfoSelf',{data:{
                gmtBirthday: res.value
            }}).then(res=>{
                this.$kt.userAccount.requestSelf();
                this.$refs.confirmBtn.success(res.msg);
                this.close();
            }).catch(err=>{
                this.$refs.confirmBtn.error(res.errMsg);
            })
        },
        open(userAccount){
            this.show = true;
            this.userAccount = JSON.parse(JSON.stringify(userAccount));
            this.valueTime = this.userAccount.gmtBirthday;
            this.minDate = this.valueTime;
            this.maxDate = this.valueTime;
            setTimeout(()=>{
                this.minDate = new Date(1900,0,1,0,0,0).getTime();
                this.maxDate = new Date(new Date().getFullYear()+1,11,31,23,59,59).getTime();
            },100);
            console.log(this.valueTime);
            // 重新渲染
            try{
                this.$forceUpdate();
            }catch(err){
                console.log(err);
            }
        },
        close(){
            this.show = false;
        },
	}
}
</script>

<style lang="scss" scoped>
.popup{
    padding: 20rpx 40rpx 20rpx 40rpx;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    box-sizing: border-box;
}
.popup-title{
    padding: 20rpx;
    font-size: 34rpx;
    font-weight: bold;
    text-align: left;
    letter-spacing: 2rpx;
}
.year-picker {
    text-align: center;
    padding: 10rpx;
    box-sizing: border-box;
}

.year-picker-item{
    padding: 30rpx;
    border: 1rpx solid #eee;
    // background-color: #f0f0f0;
    border-radius: 10rpx;
    margin-bottom: 30rpx;
    color: #666666;
}

.year-picker-item:active{
    background-color: #eee;
}

</style>

