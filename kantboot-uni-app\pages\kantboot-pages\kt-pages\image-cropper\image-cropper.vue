<template>
  <view>
    <view
        style="position: fixed;z-index:1000;width: 100vw;height: 100vh">
      <QfImageCropper
          :src="imgSrc"
          :width="width"
          :height="height"
          fileType="jpg"
          @crop="handleCrop"
      ></QfImageCropper>
      <view
          @click="close"
          class="cancel"
      >{{ $i18n.zhToGlobal("取消") }}
      </view>
    </view>
  </view>
</template>

<script>
import QfImageCropper from './qf-image-cropper/components/qf-image-cropper/qf-image-cropper';

export default {
  components: {
    QfImageCropper,
  },
  onLoad(option) {
    this.uuid = option.uuid;
    this.imgSrc = option.imgSrc;
    this.width = option.width;
    this.height = option.height;
  },
  data() {
    return {
      uuid: "",
      imgSrc: "",
      width: 200,
      height: 200,
    };
  },
  methods: {
    open(imgSrc) {
      console.log(imgSrc);
      this.imgSrc = imgSrc;
      setTimeout(() => {
        this.show = true;
      }, 100);
    },
    close() {
      this.show = false;
      uni.navigateBack();
    },
    handleCrop(res) {
      console.log(res.tempFilePath);
      // #ifdef MP-WEIXIN
      this.$kt.event.emit(this.uuid, res.tempFilePath);
      uni.navigateBack();
      this.show = false;
      return;
      // #endif

      // #ifndef APP
      this.$kt.base64.base64ToPath(res.tempFilePath).then((path) => {
        this.$kt.event.emit(this.uuid, path);
        uni.navigateBack();
        this.show = false;
      });
      // #endif
      // #ifdef APP
      event.emit(this.uuid, res.tempFilePath);
      uni.navigateBack();
      this.show = false;
      // #endif


    }
  }

}
</script>

<style lang="scss" scoped>
.cancel {
  position: fixed;
  z-index: 10000;
  color: #FFFFFF;
  bottom: 30rpx;
  left: 20rpx;
  font-size: 30rpx;
}

.cancel:active {
  transform: scale(.9);
}
</style>
