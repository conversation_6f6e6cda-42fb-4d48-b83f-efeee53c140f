<template>
  <view>
    <kt-popup ref="usPopup">
      <view class="meet-album-box">
        <projectMeetUserInfo
        ref="projectMeetUserInfo"
        ></projectMeetUserInfo>
      </view>
    </kt-popup>
  </view>
</template>

<script>
import projectMeetUserInfo from "../project-meet-user-info/project-meet-user-info.vue";
export default {
  components: { projectMeetUserInfo },
  data() {
    return {};
  },
  methods: {
    open(userAccountId) {
      this.$refs.usPopup.open();
      setTimeout(()=>{
        // 设置用户账号ID
        if (userAccountId) {
          this.$refs.projectMeetUserInfo.setUserAccountId(userAccountId);
        } else {
          this.$refs.projectMeetUserInfo.setUserAccountId(this.$kt.userAccount.getSelf().id);
        }

      },0);
    },
    close() {
      this.$refs.usPopup.close();
    },
  },
};
</script>

<style lang="scss" scoped>
.meet-album-box {
    width: 400px;
    height: 700px;
    min-height: 500px;
    overflow: auto;
  background-color: white;
  border-radius: 40rpx;
}

// 取消滚动条
.meet-album-box::-webkit-scrollbar {
  display: none;
}
</style>
