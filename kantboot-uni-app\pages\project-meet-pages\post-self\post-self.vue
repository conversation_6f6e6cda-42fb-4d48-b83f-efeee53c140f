<template>
  <view>
    <view id="headerInPostSelf">
      <kt-nav-bar
          :title="$kt.i18n.zhToGlobal('我的动态')">
      </kt-nav-bar>
    </view>

    <view class="bg"></view>
    <project-meet-post-self-panel
    :header-height="headerHeight"
    ></project-meet-post-self-panel>

  </view>
</template>

<script>
export default {
  data() {
    return {
      headerHeight: 0,
    };
  },
  mounted() {
    // 获取header的高度
    uni.createSelectorQuery().in(this).select('#headerInPostSelf').boundingClientRect((rect) => {
      console.log(rect,"获取headerHeight的高度")
      this.headerHeight = rect.height;
    }).exec()
  },
  methods: {
  },
}
</script>

<style lang="scss" scoped>
.bg{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  // 渐变
  background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}
</style>
