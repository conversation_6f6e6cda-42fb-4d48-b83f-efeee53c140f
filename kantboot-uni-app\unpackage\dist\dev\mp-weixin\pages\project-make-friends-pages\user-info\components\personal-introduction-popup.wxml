<view class="data-v-11ba2831"><u-popup vue-id="258b5ef5-1" show="{{show}}" mode="bottom" bgColor="rgba(0,0,0,0)" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-11ba2831" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup data-v-11ba2831"><view class="popup-title data-v-11ba2831">{{$root.g0}}</view><view class="picker data-v-11ba2831"><block wx:if="{{!isNickenameFocus}}"><view class="data-v-11ba2831"><view class="input-box data-v-11ba2831"><textarea class="input textarea data-v-11ba2831" auto-height="{{true}}" maxlength="1500" adjust-position="{{false}}" type="textarea" placeholder="{{$root.g1}}" data-event-opts="{{[['input',[['__set_model',['$0','personalIntroduction','$event',[]],['userAccount']]]]]}}" value="{{userAccount.personalIntroduction}}" bindinput="__e"></textarea></view></view></block></view><view style="height:10px;" class="data-v-11ba2831"></view><block wx:if="{{!isNickenameFocus}}"><kt-button bind:click="__e" vue-id="{{('258b5ef5-2')+','+('258b5ef5-1')}}" data-ref="confirmBtn" data-event-opts="{{[['^click',[['submit']]]]}}" class="data-v-11ba2831 vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g2}}</kt-button></block><view style="height:10px;" class="data-v-11ba2831"></view><kt-keyboard-size vue-id="{{('258b5ef5-3')+','+('258b5ef5-1')}}" class="data-v-11ba2831" bind:__l="__l"></kt-keyboard-size></view></u-popup></view>