@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-88fa20da {
  position: relative;
  display: inline-block;
}
.user-info-card.data-v-88fa20da {
  position: relative;
  display: inline-block;
  border-radius: 20rpx;
  overflow: hidden;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.3) 0%, white 20%);
  width: 320rpx;
  height: 600rpx;
}
.user-info-card .dto.data-v-88fa20da {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background-color: #08d68b;
  width: 20rpx;
  height: 20rpx;
  border-radius: 55%;
  border: 3rpx solid #ffffff;
}
.user-info-card .user-info-card-avatar.data-v-88fa20da {
  width: 100%;
  height: calc(100% - 200rpx);
  border-radius: 20rpx 20rpx 0 0;
}
.user-info-card .user-info-card-info.data-v-88fa20da {
  position: relative;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 2;
  padding: 15rpx;
}
.user-info-card .user-info-card-info .user-info-card-info-clicks .user-info-card-info-clicks-item.data-v-88fa20da {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  margin: 10rpx;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  background-color: #ffffff;
  box-shadow: 0 0 7rpx rgba(0, 0, 0, 0.2);
}
.user-info-card .user-info-card-info .user-info-card-info-clicks .user-info-card-info-clicks-item image.data-v-88fa20da {
  position: absolute;
  width: 25rpx;
  height: 25rpx;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.user-info-card .user-info-card-info .user-info-card-info-ng.data-v-88fa20da {
  text-align: left;
  color: #333333;
  font-size: 23rpx;
}
.user-info-card .user-info-card-info .user-info-card-info-ng .user-info-card-na-nickname.data-v-88fa20da {
  font-weight: bold;
  font-size: 30rpx;
  display: inline-block;
  max-width: calc(100% - 50rpx);
  overflow: hidden;
  text-overflow: ellipsis;
}
.user-info-card .user-info-card-info .user-info-card-info-ng .user-info-card-na-info.data-v-88fa20da {
  display: inline-block;
  max-width: calc(100% - 50rpx);
  overflow: hidden;
  text-overflow: ellipsis;
}
.user-info-card .user-info-card-info .user-info-card-info-ng .user-info-card-na-gender.data-v-88fa20da {
  vertical-align: top;
  display: inline-block;
  font-size: 20rpx;
  width: 35rpx;
  border-radius: 20rpx;
  text-align: center;
  margin-left: 10rpx;
  background-color: #3c9cff;
}
.user-info-card .user-info-card-info .user-info-card-info-ng .user-info-card-na-gender-male.data-v-88fa20da {
  background-color: #3c9cff;
}
.user-info-card .user-info-card-info .user-info-card-info-ng .user-info-card-na-gender-female.data-v-88fa20da {
  background-color: #ff5b5b;
}
.container-mode-device-pc.data-v-88fa20da {
  box-shadow: 0 0 20px #daeaff;
  border-radius: 20rpx;
}
.container-mode-device-pc .user-info-card.data-v-88fa20da {
  box-sizing: border-box;
  box-shadow: none;
  cursor: pointer;
}
.container-mode-device-pc .user-info-card .user-info-card-avatar.data-v-88fa20da {
  border-radius: 20rpx;
}
.container-mode-device-pc .user-info-card.data-v-88fa20da:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.user-info-card-vip.data-v-88fa20da {
  position: relative;
  border: 5rpx solid #5a7ef6;
  box-sizing: border-box;
}
.user-info-card-vip .user-tag-vip.data-v-88fa20da {
  position: absolute;
  background-color: #5a7ef6;
  color: #ffffff;
  z-index: 1;
  right: 0rpx;
  top: 0rpx;
  border-radius: 0 0 0 20rpx;
  padding: 0rpx 10rpx 0rpx 20rpx;
}
.user-info-card-svip.data-v-88fa20da {
  position: relative;
  box-sizing: border-box;
  border: 5rpx solid #f2c931;
}
.user-info-card-svip .user-tag-svip.data-v-88fa20da {
  position: absolute;
  color: #ffffff;
  background-color: #f2c931;
  z-index: 1;
  right: 0rpx;
  top: 0rpx;
  border-radius: 0 0 0 20rpx;
  padding: 0rpx 10rpx 0rpx 20rpx;
}
