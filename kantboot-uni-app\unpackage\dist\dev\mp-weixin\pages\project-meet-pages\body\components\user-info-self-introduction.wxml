<view class="data-v-639c19e4"><kt-popup bind:close="__e" bind:confirm="__e" vue-id="0fe62090-1" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" class="data-v-639c19e4 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-639c19e4',clazz.popup]}}"><view class="box data-v-639c19e4"><view class="box-title data-v-639c19e4">{{$root.g0}}</view><textarea class="box-textarea data-v-639c19e4" maxlength="3000" placeholder="{{$root.g1}}" data-event-opts="{{[['input',[['__set_model',['$0','selfIntroduction','$event',[]],['userAccount']]]]]}}" value="{{userAccount.selfIntroduction}}" bindinput="__e"></textarea><view style="height:30rpx;" class="data-v-639c19e4"></view><view class="data-v-639c19e4"><kt-button bind:click="__e" vue-id="{{('0fe62090-2')+','+('0fe62090-1')}}" data-ref="ktButton" data-event-opts="{{[['^click',[['confirm']]]]}}" class="data-v-639c19e4 vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g2}}</kt-button></view><view style="height:30rpx;" class="data-v-639c19e4"></view></view></view></kt-popup></view>