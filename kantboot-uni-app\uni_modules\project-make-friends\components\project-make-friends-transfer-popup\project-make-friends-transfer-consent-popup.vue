<template>
  <view>
    <kt-popup :overlayClose="false" ref="ktPopup" @close="close" :zIndex="999999999">
      <view class="box">
        <view class="close-btn" @click="closePop">×</view>
        <view class="title">{{ $i18n.zhToGlobal("转移申请") }}</view>

        <!-- 转移申请信息 -->
        <view class="transfer-info" v-if="transferData">
          <view class="info-section">
            <view class="section-title">{{ $i18n.zhToGlobal("原上级用户") }}</view>
            <view class="user-card">
              <image class="user-avatar"
                :src="transferData.originalInviterUserAccount && transferData.originalInviterUserAccount.fileIdOfAvatar ? $kt.file.visit(transferData.originalInviterUserAccount.fileIdOfAvatar) : '/static/default-avatar.png'"
                mode="aspectFill"></image>
              <view class="user-details">
                <view class="user-nickname">{{ transferData.originalInviterUserAccount ? (transferData.originalInviterUserAccount.nickname || $i18n.zhToGlobal('用户')) : '' }}</view>
                <view class="user-id">ID: {{ transferData.originalInviterUserAccount ? transferData.originalInviterUserAccount.id : '' }}</view>
                <view class="user-phone" v-if="transferData.originalInviterUserAccount && transferData.originalInviterUserAccount.phone">
                  {{ transferData.originalInviterUserAccount.phoneAreaCode }} {{ transferData.originalInviterUserAccount.phone }}
                </view>
              </view>
            </view>
          </view>

          <view class="info-section">
            <view class="section-title">{{ $i18n.zhToGlobal("转移对象") }}</view>
            <view class="user-card">
              <image class="user-avatar"
                :src="transferData.targetInviterUserAccount && transferData.targetInviterUserAccount.fileIdOfAvatar ? $kt.file.visit(transferData.targetInviterUserAccount.fileIdOfAvatar) : '/static/default-avatar.png'"
                mode="aspectFill"></image>
              <view class="user-details">
                <view class="user-nickname">{{ transferData.targetInviterUserAccount ? (transferData.targetInviterUserAccount.nickname || $i18n.zhToGlobal('用户')) : '' }}</view>
                <view class="user-id">ID: {{ transferData.targetInviterUserAccount ? transferData.targetInviterUserAccount.id : '' }}</view>
                <view class="user-phone" v-if="transferData.targetInviterUserAccount && transferData.targetInviterUserAccount.phone">
                  {{ transferData.targetInviterUserAccount.phoneAreaCode }} {{ transferData.targetInviterUserAccount.phone }}
                </view>
              </view>
            </view>
          </view>
          <view class="info-section">
            <view class="section-title">{{ $i18n.zhToGlobal("申请时间") }}</view>
            <view class="time-text">{{ formatTime(transferData.gmtCreate) }}</view>
          </view>
        </view>

        <!-- 备注输入 -->
        <!-- <view class="remark-section">
          <view class="section-title">{{ $i18n.zhToGlobal("处理备注") }}</view>
          <u-input 
            v-model="processRemark" 
            type="textarea" 
            :placeholder="$i18n.zhToGlobal('请输入处理备注（可选）')"
            :maxlength="200"
            :height="80">
          </u-input>
        </view> -->

        <!-- 底部操作按钮 -->
        <view class="footer">
          <view class="buttons">
            <button class="reject-btn" @click="handleReject" :disabled="processing">
              {{ processing && currentAction === 'reject' ? $i18n.zhToGlobal('处理中...') : $i18n.zhToGlobal('拒绝') }}
            </button>
            <button class="approve-btn" @click="handleApprove" :disabled="processing">
              {{ processing && currentAction === 'approve' ? $i18n.zhToGlobal('处理中...') : $i18n.zhToGlobal('同意') }}
            </button>
          </view>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      transferData: null,
      processRemark: '',
      processing: false,
      currentAction: ''
    };
  },
  methods: {
    open(data) {
      this.transferData = data;
      this.processRemark = '';
      this.processing = false;
      this.currentAction = '';
      this.$refs.ktPopup.open();
    },

    close() {
      this.transferData = null;
      this.processRemark = '';
      this.processing = false;
      this.currentAction = '';
    },

    closePop() {
      this.$refs.ktPopup.close();
    },

    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      return date.toLocaleString('zh-CN');
    },

    handleApprove() {
      this.processTransfer(1, 'approve');
    },

    handleReject() {
      this.processTransfer(2, 'reject');
    },

    processTransfer(status, action) {
      if (this.processing || !this.transferData) return;

      this.processing = true;
      this.currentAction = action;

      this.$request.post("/project-make-friends-web/userTransferConsent/processTransfer", {
        data: {
          consentId: this.transferData.id,
          status: status, // 1-同意，2-拒绝
          remark: this.processRemark
        }
      }).then(res => {
        this.processing = false;
        this.currentAction = '';
        
        if (res.state === 2000) {
          uni.showToast({
            title: status === 1 ? this.$i18n.zhToGlobal('已同意转移') : this.$i18n.zhToGlobal('已拒绝转移'),
            icon: 'success'
          });
          
          // 触发处理完成事件
          this.$emit('processed', {
            status: status,
            remark: this.processRemark
          });
          
          // 关闭弹窗
          this.closePop();
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('处理失败'),
            icon: 'none'
          });
        }
      }).catch(err => {
        this.processing = false;
        this.currentAction = '';
        console.error(err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 90vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
    padding-top: 20rpx;
  }

  .transfer-info {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 20rpx;

    .info-section {
      margin-bottom: 30rpx;

      .section-title {
        font-size: 28rpx;
        font-weight: bold;
        margin-bottom: 15rpx;
        color: #333;
      }

      .user-card {
        display: flex;
        align-items: center;
        padding: 20rpx;
        background-color: #f8f8f8;
        border-radius: 12rpx;

        .user-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .user-details {
          flex: 1;

          .user-nickname {
            font-size: 30rpx;
            margin-bottom: 6rpx;
            font-weight: bold;
          }

          .user-id,
          .user-phone {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 4rpx;
          }
        }
      }

      .remark-text,
      .time-text {
        padding: 20rpx;
        background-color: #f8f8f8;
        border-radius: 12rpx;
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }

  .remark-section {
    margin-bottom: 30rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 15rpx;
      color: #333;
    }
  }

  .footer {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;

    .buttons {
      display: flex;
      gap: 20rpx;

      button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 30rpx;
        border-radius: 40rpx;
        border: none;

        &.reject-btn {
          background-color: #f5f5f5;
          color: #666;

          &[disabled] {
            background-color: #e0e0e0;
            color: #999;
          }
        }

        &.approve-btn {
          background-color: #000000;
          color: #fff;

          &[disabled] {
            background-color: #cccccc;
            color: #fff;
          }
        }
      }
    }
  }
}

.close-btn {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
  z-index: 10;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
</style>
