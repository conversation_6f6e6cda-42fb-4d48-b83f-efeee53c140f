<template>
  <view>
    <view
        class="kt-popup-back"
        @click.stop="toOverlayClose"
        :class="{'kt-popup-back-closing': closing}"
        :style="{zIndex: zIndex}"
        v-if="show"></view>
    <view
        v-if="show"
        :style="{zIndex: zIndex+1}"
        :class="clazz.ktPopupSlot+' '+(closing?'kt-popup-slot-closing':'')"
    >
      <slot></slot>
    </view>

  </view>
</template>

<script>
export default {
  props: {
    // 遮盖是否关闭
    overlayClose: {
      type: Boolean,
      default: true
    },
    zIndex: {
      type: Number,
      default: 99999999
    },
  },
  data() {
    return {
      show: false,
      closing: false,
      clazz: {
        ktPopupSlot: this.$kt.style.toggleClass("kt-popup-slot"),
      }
    }
  },
  mounted() {
  },
  methods: {
    toOverlayClose() {
      if (this.overlayClose) {
        this.close();
      }
    },
    open() {
      this.show = true;
      this.closing = false;
    },
    close() {
      if (this.closing) {
        return;
      }
      this.closing = true;
      setTimeout(() => {
        this.show = false;
        this.closing = false;
      }, 300);
      this.$emit('close');
    },

  },
}
</script>

<style lang="scss" scoped>
.kt-popup-back {
  position: fixed;
  top: -50vh;
  left: -50vw;
  width: 200vw;
  height: 200vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999999;
  // 动画
  animation: ktPopupBackAni 0.3s ease-in-out;
}

@keyframes ktPopupBackAni {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.kt-popup-slot {
  animation: ktPopupSlotAni 0.3s ease-in-out;
}

.kt-popup-slot-mode-device-mobile{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
}

.kt-popup-slot-mode-device-pc{
  position: fixed;
  top: 50%;
  left: 50%;
  width: 400px;
  transform: translate(-50%, -50%);
}

@keyframes ktPopupSlotAni {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.kt-popup-back-closing {
  animation: ktPopupBackAniClosing 0.3s ease-in-out;
}

@keyframes ktPopupBackAniClosing {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.kt-popup-slot-closing {
  animation: ktPopupSlotAniClosing 0.3s ease-in-out;
}

@keyframes ktPopupSlotAniClosing {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>