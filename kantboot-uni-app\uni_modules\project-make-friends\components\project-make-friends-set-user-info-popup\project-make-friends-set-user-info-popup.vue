<template>
  <view>
    <kt-popup
    ref="ktPopup"
    >
      <view class="box">
        <scroll-view
        :scroll-y="true"
        style="height: calc(100vh - 300rpx)">
          <project-make-friends-user-info-panel
          :user-account-id="self.id"
          ></project-make-friends-user-info-panel>
        </scroll-view>
      </view>
    </kt-popup>

  </view>
</template>

<script>
export default {
  data() {
    return {
      self:{}
    };
  },
  created() {
    this.self = this.$kt.userAccount.getSelf();
    if(!this.self.nickname){
      this.open();
    }
  },
  methods: {

    open() {
      this.$refs.ktPopup.open();
    },
    close() {
      this.$refs.ktPopup.close();
    },
    confirm() {
      this.$refs.ktPopup.confirm();
    }
  },
}
</script>

<style lang="scss" scoped>
.box{
  padding: 20rpx 40rpx 20rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
}
</style>
