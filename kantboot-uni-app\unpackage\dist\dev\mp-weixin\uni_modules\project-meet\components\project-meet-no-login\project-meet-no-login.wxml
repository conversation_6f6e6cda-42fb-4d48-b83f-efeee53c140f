<view class="data-v-3f15af75"><view hidden="{{!(!isLogin)}}" class="{{['data-v-3f15af75',clazz.box]}}" style="{{'z-index:'+(zIndex)+';'}}"><block wx:if="{{isInit}}"><view data-event-opts="{{[['tap',[['toLogin']]]]}}" class="to-login-btn data-v-3f15af75" bindtap="__e">{{$root.g0}}</view></block><view class="data-v-3f15af75"><image class="bg-img data-v-3f15af75" mode="aspectFill" src="{{$root.g1}}"></image></view><view class="{{['data-v-3f15af75',clazz.back]}}"></view><block wx:if="{{isInit}}"><view class="in-box data-v-3f15af75"><view class="in-box-title-title data-v-3f15af75"><image class="in-box-title-title-icon data-v-3f15af75" src="{{$root.g2}}"></image><view class="in-box-title-title-text data-v-3f15af75">MEETOV</view></view><view style="height:20rpx;" class="data-v-3f15af75"></view><view class="in-box-title data-v-3f15af75">{{$root.g3}}</view><view style="height:40rpx;" class="data-v-3f15af75"></view><view class="data-v-3f15af75"><view data-event-opts="{{[['tap',[['toRegister']]]]}}" class="in-box-btn data-v-3f15af75" bindtap="__e">{{$root.g4}}</view></view></view></block><image class="to-language-btn data-v-3f15af75" src="{{$root.g5}}" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e"></image><block wx:if="{{isInit}}"><view class="contact-us data-v-3f15af75"><text data-event-opts="{{[['tap',[['toEmail',['<EMAIL>']]]]]}}" bindtap="__e" class="data-v-3f15af75">{{''+$root.g6+": "+"<EMAIL>"+''}}</text></view></block></view><project-meet-login-popup vue-id="b8be7f9e-1" data-ref="ktLoginPopup" class="data-v-3f15af75 vue-ref" bind:__l="__l"></project-meet-login-popup><project-meet-register-popup bind:registerSuccess="__e" vue-id="b8be7f9e-2" data-ref="ktRegisterPopup" data-event-opts="{{[['^registerSuccess',[['registerSuccess']]]]}}" class="data-v-3f15af75 vue-ref" bind:__l="__l"></project-meet-register-popup><kt-language-select-popup vue-id="b8be7f9e-3" data-ref="ktLanguageSelectPopup" class="data-v-3f15af75 vue-ref" bind:__l="__l"></kt-language-select-popup></view>