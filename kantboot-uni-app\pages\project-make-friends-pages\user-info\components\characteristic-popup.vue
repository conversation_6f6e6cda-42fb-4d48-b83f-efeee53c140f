<template>
	<view>
        <u-popup :show="show" 
        mode="bottom"
        :bgColor="'rgba(0,0,0,0)'"
        @close="close" @confirm="confirm">
            <view class="popup">
                <view class="popup-title">{{$i18n.zhToGlobal("个人特点")}}</view>
                <scroll-view
                    style="max-height: 500px;"
                    :scroll-y="true"
                    class="picker">
                    <view 
                    class="bl-box">
                        <view 
                        @click="select(item)"
                        class="bl-box-item"
                        :class="{'bl-box-item-selected':isSelected(item)}"
                        v-for="item in list"
                        v-if="item.name"
                        >{{ $kt.util.firstLetterUpper(item.name) }}</view>
                    </view>
                </scroll-view>
				
                <view style="height: 10px;"></view>
				<kt-button
                @click="submit()"
                ref="confirmBtn"
                >{{ $i18n.zhToGlobal("确定") }}</kt-button>
                <view style="height: 10px;"></view>
            </view>
        </u-popup>
	</view>

</template>

<script>
import makeFriends from '@/uni_modules/project-make-friends/libs/load';

export default {
	data() {
		return {
            show: false,
            selectedYearIndex: 0,
            list: [],
            listSelect:[]
		}
	},
    mounted() {
        this.list = makeFriends.getCharacteristics();
        console.log(JSON.stringify(this.list));
    },
	methods: {
        submit(){
            for(let i=0;i<this.listSelect.length;i++){
                if(!this.listSelect[i]){
                    // 删除
                    this.listSelect.splice(i,1);
                }
            }
            this.$refs.confirmBtn.loading(999999);
            this.$request.post('/project-make-friends-web/userAccount/setCharacteristics',{data:{characteristicIds:this.listSelect}}).then(res=>{
                this.$refs.confirmBtn.success(res.msg);
                this.$kt.userAccount.requestSelf();
                this.close();
            }).catch(err=>{
                this.$refs.confirmBtn.error(res.errMsg);
            })
        },
        select(item){

            for(let i=0;i<this.listSelect.length;i++){
                if(this.listSelect[i]+"" == item.id+""){
                    this.listSelect.splice(i,1);
                    return;
                }
            }
            this.listSelect.push(item.id);
        },
        isSelected(item){
            for(let i=0;i<this.listSelect.length;i++){
                if(this.listSelect[i]+"" == item.id+""){
                    return true;
                }
            }
            return false;
        },
        open(listSelect2){
            if(!listSelect2){
                listSelect2 = [];
            }
            this.show = true;
            // this.listSelect = JSON.parse(JSON.stringify(listSelect));
            this.listSelect = [];
            for(let i=0;i<listSelect2.length;i++){

                this.listSelect.push(listSelect2[i].characteristicId);
            }
        },
        close(){
            this.show = false;
        },
        confirm() {
            this.close();
            this.$emit('confirm', this.selectedYear);
        },
        bindYearChange(e) {
            this.selectedYearIndex = e.detail.value;
            this.selectedYear = this.years[this.selectedYearIndex];
        }
	}
}
</script>

<style lang="scss" scoped>
.popup{
    padding: 20rpx 40rpx 20rpx 40rpx;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    box-sizing: border-box;
}
.popup-title{
    padding: 20rpx;
    font-size: 34rpx;
    font-weight: bold;
    text-align: left;
    letter-spacing: 2rpx;
}
.picker {
    text-align: center;
    padding: 10rpx;
    box-sizing: border-box;
}

.bl-box {
    text-align: left;
    width: 100%;
    .bl-box-item {
        display: inline-block;
        margin: 10rpx;
        padding: 28rpx;
        border-radius: 20rpx;
        color: #333;
        box-sizing: border-box;
        background-color: #f5f5f5;

    }
    .bl-box-item-selected {
        background-color: #333;
        color: #fff;
    }
}

</style>

