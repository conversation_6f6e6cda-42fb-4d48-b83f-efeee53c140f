<view class="data-v-07a907d6"><kt-popup vue-id="ed76f0de-1" zIndex="{{zIndex}}" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-07a907d6 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="data-v-07a907d6"><project-meet-login-panel vue-id="{{('ed76f0de-2')+','+('ed76f0de-1')}}" border-radius="{{$root.m0?'20rpx':'20rpx 20rpx 0 0'}}" data-event-opts="{{[['^loginSuccess',[['loginSuccess']]]]}}" bind:loginSuccess="__e" class="data-v-07a907d6" bind:__l="__l"></project-meet-login-panel></view></kt-popup></view>