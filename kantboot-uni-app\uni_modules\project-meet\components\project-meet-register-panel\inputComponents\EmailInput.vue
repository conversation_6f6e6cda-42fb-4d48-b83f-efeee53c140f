<template>
  <view :class="clazz.loginInputBox">
    <view class="icon-box">

      <view
          @click="toChangPhoneAreaCode"
          class="icon">
        <view v-if="isPhone(param.to)">
          {{ "+" }}{{ param.phoneAreaCode }}
        </view>
        <image
            v-show="!isPhone(param.to)"
            class="icon-img" :src="$kt.file.byPath('icon/email.svg')" mode="widthFix"></image>
      </view>

    </view>

    <view class="input-box">
      <input
          @blur="addUserLog"
          autofocus
          class="input"
          @input="inputInput"
          v-model="param.email"
          :adjust-position="false"
          :placeholder="$i18n.zhToGlobal('电子邮箱')"
          type="text"></input>
    </view>
  </view>
</template>

<script>

export default {
  props: ["bodyData"],
  data() {
    return {
      clazz: {
        loginInputBox: this.$kt.style.toggleClass("login-input-box")
      },
      param: {
        phoneAreaCode: "86",
        email: '',
        methodCode: "phone"
      }
    };
  },
  created() {
    this.$emit("change", this.param);
  },
  methods: {
    addUserLog() {
      this.$emit("addUserLog");
    },
    isPhone(to) {
      // if (!to || to.indexOf("@") === -1) {
      //   return true;
      // }
      return false;
    },
    inputInput(e) {
      this.param.email = e.detail.value;
      this.$emit("change", this.param);
    },
    toChangPhoneAreaCode() {
      uni.showToast({
        title: this.$i18n.zhToGlobal("当前仅支持‘+86’"),
        icon: "none"
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../css/loginInput";
</style>
