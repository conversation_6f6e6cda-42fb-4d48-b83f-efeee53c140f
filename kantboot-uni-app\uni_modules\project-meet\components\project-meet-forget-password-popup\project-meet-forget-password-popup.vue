<template>
  <view>
    <kt-popup ref="ktPopup"
              :z-index="*********">
      <view
          :class="clazz.box">

        <view class="title">{{ $i18n.zhToGlobal("忘记密码") }}</view>
        <view>
          <view style="height: 30rpx"></view>
          <kt-user-info-card
              :user-account-id="userAccount.id"
          ></kt-user-info-card>
          <view style="height: 30rpx"></view>
        </view>

        <view>
          <!-- 输入邮箱 -->
          <input
              class="input"
              :placeholder="$i18n.zhToGlobal('输入邮箱')"
              v-model="requestParam.email"
              @input="requestParam.email = $event.detail.value"
          />
        </view>
        <view
            v-if="userAccount.id"
        >
          <view style="height: 30rpx"></view>
          <view>
            <!-- 密码 -->
            <input
                class="input"
                :placeholder="$i18n.zhToGlobal('输入密码')"
                type="password"
                v-model="requestParam.password"
                @input="requestParam.password = $event.detail.value"></input>
          </view>
          <view style="height: 30rpx"></view>
          <view>
            <!-- 确认密码 -->
            <input
                class="input"
                :placeholder="$i18n.zhToGlobal('确认密码')"
                type="password"
                v-model="requestParam.confirmPassword"
                @input="requestParam.confirmPassword = $event.detail.value"
            />
          </view>

        </view>

        <view style="height: 30rpx"></view>


        <view
            v-if="userAccount.id"
        >
          <kt-button
              @click="submit()"
              ref="ktButton"
          >{{ $i18n.zhToGlobal("确定修改") }}
          </kt-button>
        </view>

      </view>
    </kt-popup>
  </view>
</template>

<script>
import $kt from "@/uni_modules/kantboot";

export default {
  data() {
    return {
      clazz: {
        box: this.$kt.style.toggleClass("box"),
      },
      requestParam: {
        email: "",
        password: "",
        // 确认密码
        confirmPassword: "",
      },
      userAccount: {
        id: null,
        email: "",
      },
    };
  },
  props: {},
  watch: {
    // 监听邮箱
    requestParam: {
      handler(val) {
        if (this.checkEmail(val.email)) {
          this.getByEmail();
        }
      },
      immediate: true,
      deep: true,
    },
  },

  mounted() {
  },
  methods: {
    /**
     * 校验有效格式是否正确
     */
    checkEmail(email) {
      // 正则表达式
      const reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/;
      return reg.test(email);
    },
    getByEmail() {
      // if(!this.requestParam.email){
      //   this.$refs.ktButton.error(this.$i18n.zhToGlobal("未输入的邮箱"));
      //   return;
      // }
      //
      // if (!this.checkEmail(this.requestParam.email)) {
      //   this.$refs.ktButton.error(this.$i18n.zhToGlobal("邮箱格式不正确"));
      //   return;
      // }

      // /user-account-web/userAccount/getByEmail
      this.$kt.request.post("/user-account-web/userAccount/getByEmail", {
        data: {email: this.requestParam.email}
      }).then(res => {
        this.userAccount = res.data;
      }).catch(err => {
        this.userAccount.id = null;
      });

    },
    open() {
      this.$refs.ktPopup.open();

    },
    close() {
      this.$refs.ktPopup.close();
    },
    getUsernameText(){
      if(!this.userAccount.id) {
        return "未找到账号";
      }else{
        return this.userAccount.username;
      }
    },
    submit() {
      // 判断密码是否为空
      if (!this.requestParam.password) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未输入密码"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "forgetPassword",
            operationCode: "forgetPasswordClickButNotFinish",
            safeInputContent: "未输入密码" + " 账号: "+ this.getUsernameText(),
            inputContent: "未输入密码 邮箱: "+this.requestParam.email
                +" 密码: "+this.requestParam.password + " 确认的密码: "+this.requestParam.confirmPassword + " 账号: "+ this.getUsernameText(),

            // operationCode: "loginInput",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击了重置密码但是未完成",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }

      // 判断是否输入了确认密码
      if (!this.requestParam.confirmPassword) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未输入确认密码"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "forgetPassword",
            operationCode: "forgetPasswordClickButNotFinish",
            safeInputContent: "未输入确认密码" + " 账号: "+ this.getUsernameText(),
            inputContent: "未输入确认密码 邮箱: "+this.requestParam.email
                +" 密码: "+this.requestParam.password + " 确认的密码: "+this.requestParam.confirmPassword + " 账号: "+ this.getUsernameText(),

            // operationCode: "loginInput",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击了重置密码但是未完成",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }

      // 判断确认的密码和密码是否一致
      if (this.requestParam.password !== this.requestParam.confirmPassword) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("两次输入的密码不一致"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "forgetPassword",
            operationCode: "forgetPasswordClickButNotFinish",
            safeInputContent: "两次输入的密码不一致" + " 账号: "+ this.getUsernameText(),
            inputContent: "两次输入的密码不一致 邮箱: "+this.requestParam.email
                +" 密码: "+this.requestParam.password + " 确认的密码: "+this.requestParam.confirmPassword + " 账号: "+this.getUsernameText(),

            // operationCode: "loginInput",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击了重置密码但是未完成",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;

      }
      if (!this.requestParam.email) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未输入邮箱"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "forgetPassword",
            operationCode: "forgetPasswordClickButNotFinish",
            safeInputContent: "两次输入的密码不一致" + " 账号: "+ this.getUsernameText(),
            inputContent: "两次输入的密码不一致 邮箱: "+this.requestParam.email
                +" 密码: "+this.requestParam.password + " 确认的密码: "+this.requestParam.confirmPassword + " 账号: "+ this.getUsernameText(),
            // operationCode: "loginInput",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击了重置密码但是未完成",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }
      this.$refs.ktButton.loading();
      this.$request.post("/project-meet-web/userAccount/updatePasswordByUsername", {
        data: {
          username: this.userAccount.username,
          password: this.requestParam.password,
        },
      }).then( async (res) => {
        this.$refs.ktButton.success(res.msg);
        this.$kt.userAccount.setIsLogin(true);
        this.$kt.request.setToken(res.data.token);
        await this.$kt.userAccount.requestSelf();
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "login",
            operationCode: "loginSuccess",
            safeInputContent: "重置密码后登录成功" + " 账号: "+ this.getUsernameText(),
            inputContent: "重置密码后登录成功 邮箱: "+this.requestParam.email
                +" 密码: "+this.requestParam.password + " 确认的密码: "+this.requestParam.confirmPassword + " 账号: "+ this.getUsernameText(),
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "登录成功",
            levelCode: "success",
            levelText: "成功",
          }
        });

        $kt.event.emit("login:success");

        this.close();
      }).catch(err => {
        this.$refs.ktButton.error(err.errMsg);
      });

    },
  },
}
</script>

<style lang="scss" scoped>
.box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
  height: calc(100vh - 300rpx);
}

.box-mode-device-pc {
  border-radius: 20rpx;
}

.title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #ccc;
  border-radius: 10rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}


.box-mode-device-pc {
  position: fixed;
  bottom: 0;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  height: 500px;
  padding: 60rpx;
  box-sizing: border-box;
}

</style>
