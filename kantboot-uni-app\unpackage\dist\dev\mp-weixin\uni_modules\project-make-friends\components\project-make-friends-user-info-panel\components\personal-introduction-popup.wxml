<view class="data-v-b85774a2"><u-popup vue-id="52961d92-1" show="{{show}}" mode="bottom" bgColor="rgba(0,0,0,0)" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-b85774a2" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup data-v-b85774a2"><view class="popup-title data-v-b85774a2">{{$root.g0}}</view><view class="picker data-v-b85774a2"><block wx:if="{{!isNickenameFocus}}"><view class="data-v-b85774a2"><view class="input-box data-v-b85774a2"><textarea class="input textarea data-v-b85774a2" auto-height="{{true}}" maxlength="1500" adjust-position="{{false}}" type="textarea" placeholder="{{$root.g1}}" data-event-opts="{{[['input',[['__set_model',['$0','personalIntroduction','$event',[]],['userAccount']]]]]}}" value="{{userAccount.personalIntroduction}}" bindinput="__e"></textarea></view></view></block></view><view style="height:10px;" class="data-v-b85774a2"></view><block wx:if="{{!isNickenameFocus}}"><kt-button bind:click="__e" vue-id="{{('52961d92-2')+','+('52961d92-1')}}" data-ref="confirmBtn" data-event-opts="{{[['^click',[['submit']]]]}}" class="data-v-b85774a2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g2}}</kt-button></block><view style="height:10px;" class="data-v-b85774a2"></view><kt-keyboard-size vue-id="{{('52961d92-3')+','+('52961d92-1')}}" class="data-v-b85774a2" bind:__l="__l"></kt-keyboard-size></view></u-popup></view>