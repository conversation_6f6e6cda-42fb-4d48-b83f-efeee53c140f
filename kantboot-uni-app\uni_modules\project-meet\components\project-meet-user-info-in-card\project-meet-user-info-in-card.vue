<template>
  <view :class="clazz.container">
    <view
        :style="{
          width: width,
          height: height
        }"
        class="user-info-card">
      <image
          mode="aspectFill"
          v-if="userInfo.fileIdOfAvatar"
          class="user-info-card-avatar"
          :src="$kt.file.visit(userInfo.fileIdOfAvatar)"
      ></image>
      <image
          mode="aspectFill"
          v-else
          class="user-info-card-avatar"
          :src="$kt.file.byPath('image/logo.png')">
      </image>

      <view class="user-info-card-info">
        <view class="user-info-card-info-ng">
          <view class="user-info-card-na-nickname">
            {{ userInfo.nickname ? userInfo.nickname : $i18n.zhToGlobal("无昵称") }}
          </view>
          <view class="user-info-card-na-gender"
                :class="{
            'user-info-card-na-gender-male': userInfo.genderCode === 'male',
            'user-info-card-na-gender-female': userInfo.genderCode === 'female'
          }"
          >
            {{"♂"}}
          </view>
        </view>
        <view></view>
        <view class="dto"></view>
        <view style="height: 10rpx"></view>
      </view>


    </view>

  </view>
</template>

<script>

export default {
  props: {
    width: {
      type: String,
      default: "340rpx"
    },
    height: {
      type: String,
      default: "400rpx"
    },

    userInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      }
    }
  },
  methods: {
    getCharacteristics(userInfo) {
      // 获取前3个特征
      let characteristics = userInfo.characteristics || [];
      let characteristicsList = [];
      for (let i = 0; i < characteristics.length; i++) {
        if (i > 2) {
          break;
        }
        characteristicsList.push(characteristics[i]);
      }
      return characteristicsList;
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  display: inline-block;
}

.user-info-card {
  position: relative;
  display: inline-block;
  border-radius: 20rpx;
  // 超出不显示
  overflow: hidden;
  background-color: #FFFFFF;
  width: 320rpx;
  height: 400rpx;
  box-shadow: 0 10rpx 15rpx rgba(118, 118, 118, 0.1);

  .user-info-card-avatar {
    width: 100%;
    height: 100%;
  }

  .user-info-card-info {
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
    //background-color: rgba(0,0,0,0.5);
    z-index: 2;
    padding: 15rpx;
    // 渐变从上到下
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);

    .dto{
      // 科技绿
      background-color: #08d68b;
      width: 10rpx;
      height: 10rpx;
      border-radius: 10rpx;
    }

    .user-info-card-info-ng {
      text-align: left;
      color: #FFFFFF;
      font-size: 23rpx;
      //text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);
      .user-info-card-na-nickname{
        display: inline-block;
        max-width: calc(100% - 40rpx);
        // 超出显示省略号
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .user-info-card-na-gender{
        vertical-align: top;
        display: inline-block;
        font-size: 20rpx;
        width: 35rpx;
        border-radius: 20rpx;
        text-align: center;
        margin-left: 10rpx;
        background-color: #3c9cff;
      }
      .user-info-card-na-gender-male{
        background-color: #3c9cff;
      }
      .user-info-card-na-gender-female{
        background-color: #ff5b5b;
      }

    }

  }
}

.container-mode-device-pc{
  .user-info-card {
    border: 4rpx solid rgba(0,0,0,.1);
    box-sizing: border-box;
    box-shadow: none;
    cursor: pointer;
  }
  .user-info-card:active{
    transform: scale(0.95);
  }
}


</style>