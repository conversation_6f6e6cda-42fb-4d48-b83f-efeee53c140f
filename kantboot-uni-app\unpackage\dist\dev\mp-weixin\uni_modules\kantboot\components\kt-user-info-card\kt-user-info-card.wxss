@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-info-card.data-v-43e339f4 {
  min-width: 500rpx;
  width: 100%;
  text-align: left;
}
.user-info-card-nickname.data-v-43e339f4 {
  display: inline-block;
  font-weight: bold;
  font-size: 36rpx;
  letter-spacing: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 200rpx);
}
.user-info-card-age.data-v-43e339f4 {
  display: inline-block;
  font-size: 28rpx;
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
}
.user-info-card-gender.data-v-43e339f4 {
  display: inline-block;
  margin-left: 20rpx;
  vertical-align: top;
  margin-top: 5rpx;
  width: 30rpx;
  height: 30rpx;
}
.user-info-card-gender .user-info-card-gender-icon.data-v-43e339f4 {
  width: 100%;
  height: 100%;
}
.user-info-card-intro.data-v-43e339f4 {
  font-size: 30rpx;
  color: #999;
  letter-spacing: 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  max-height: 80rpx;
}
.container-mode-color-scheme-dark .user-info-card .user-info-card-nickname.data-v-43e339f4 {
  color: #ccc;
}
.container-mode-color-scheme-dark .user-info-card .user-info-card-intro.data-v-43e339f4 {
  color: #AAAAAA;
}
