<template>
  <view>
    <project-acm-nav-bar
        :is-has-i18n="false"
    :title="$i18n.zhToGlobal('邀请')"
    ></project-acm-nav-bar>

    <view class="invite-box">
      <kt-invite-panel
      ></kt-invite-panel>
    </view>

    <view
        v-if="self.userAccountIdOfInviter"
        class="inter-box">
      <view
          class="title">{{"·"}}{{$i18n.zhToGlobal("我的邀请人")}}{{"·"}}</view>
      <kt-user-info-card
          :user-account-id="self.userAccountIdOfInviter"
      ></kt-user-info-card>
    </view>

    <kt-no-login></kt-no-login>
  </view>
</template>

<script>
export default {
  data() {
    return {
      self:{},
      intoPath:'/pages/pages-body/into/into',
    };
  },
  onLoad() {
    this.getInit();
    this.$kt.event.on("login:success", () => {
      this.getInit();
    });
  },
  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.$i18n.zhToGlobal("你好"),
      path: this.intoPath + '?userAccountIdOfInviter=' + this.self.id,

    }
  },
  onShareAppMessage() {
    return {
      title: this.$i18n.zhToGlobal("你好"),
      path: this.intoPath + '?userAccountIdOfInviter=' + this.self.id,
    }
  },
  methods: {
    async getInit() {
      if (this.$kt.userAccount.getIsLogin()) {
        this.self = this.$kt.userAccount.getSelf();
        await this.$kt.userAccount.requestSelf();
        this.self = this.$kt.userAccount.getSelf();
      }

    }
  },
}
</script>

<style lang="scss" scoped>
.invite-box{
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
}

.inter-box{
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 40rpx;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 100;
  .title{
    font-size: 23rpx;
    color: #333333;
    text-align: center;
    margin-bottom: 20rpx;
  }
}
</style>
