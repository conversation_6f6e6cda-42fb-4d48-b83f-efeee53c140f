@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-fab12aaa {
  width: 100%;
  height: 100%;
}
.video.data-v-fab12aaa {
  width: 100%;
  height: 400rpx;
}
.scroll-view-x.data-v-fab12aaa {
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
}
.scroll-view-x .view-item.data-v-fab12aaa {
  display: inline-block;
  width: 270rpx;
  background-color: #f6f7f8;
  padding: 20rpx;
  border-radius: 20rpx;
  color: #666666;
  margin-right: 20rpx;
  text-overflow: ellipsis;
}
.scroll-view-x .view-item .item-index.data-v-fab12aaa {
  font-size: 26rpx;
}
.scroll-view-x .view-item .item-title.data-v-fab12aaa {
  font-size: 28rpx;
}
.scroll-view-x .view-item-selected.data-v-fab12aaa {
  box-sizing: border-box;
  color: #303133;
  font-weight: bold;
}
.scroll-view-x.data-v-fab12aaa::-webkit-scrollbar {
  display: none;
}
.box-2.data-v-fab12aaa {
  height: 150rpx;
  padding: 0 10rpx 0 10rpx;
}
.box-2 .box-tag.data-v-fab12aaa {
  display: inline-block;
  color: #666666;
  font-size: 28rpx;
  vertical-align: top;
  margin-top: 2rpx;
  margin-right: 10rpx;
  background-color: #f6f7f8;
  padding: 0 10rpx;
  border-radius: 20rpx;
}
.box-2 .box-title.data-v-fab12aaa {
  color: #18191c;
  font-size: 32rpx;
  font-weight: bold;
}
.box-2 .box-desc.data-v-fab12aaa {
  margin-top: 10rpx;
  color: #666666;
  font-size: 28rpx;
}
.box-3.data-v-fab12aaa {
  padding: 0 20rpx 0 20rpx;
  box-sizing: border-box;
}
.button-box.data-v-fab12aaa {
  padding: 90rpx;
  box-sizing: border-box;
}
