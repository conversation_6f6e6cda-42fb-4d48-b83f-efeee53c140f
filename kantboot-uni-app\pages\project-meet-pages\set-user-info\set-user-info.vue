<template>
  <view>
    <kt-nav-bar
        :is-has-back="false"
        :is-has-i18n="false"
        :icon="$kt.file.byPath('projectMeet/icon/userInfo.svg')"
    :title="$i18n.zhToGlobal('信息初始化')"
    ></kt-nav-bar>
    <project-meet-set-user-info-panel
    ref="projectMeetSetUserInfoPanel"

    ></project-meet-set-user-info-panel>
  </view>
</template>

<script>
import projectMeetSetUserInfoPanel from "uni_modules/project-meet/components/project-meet-set-user-info-panel/project-meet-set-user-info-panel.vue";
export default {
  components: { projectMeetSetUserInfoPanel },
  mounted() {
    this.$refs.projectMeetSetUserInfoPanel.close();
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style>
</style>
