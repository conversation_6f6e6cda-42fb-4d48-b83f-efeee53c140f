@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-input-box.data-v-097602ea {
  position: relative;
  height: 55px;
  border: 5rpx solid #F0F0F0;
  border-radius: 20rpx;
  font-size: 32rpx;
  padding: 30rpx 20rpx 30rpx 20rpx;
  box-sizing: border-box;
}
.login-input-box .icon-box.data-v-097602ea {
  display: inline-block;
  width: 90rpx;
  height: 100%;
  vertical-align: top;
  text-align: center;
  color: #000000;
  font-weight: bold;
  letter-spacing: 1px;
  font-size: 30rpx;
}
.login-input-box .icon-box .icon.data-v-097602ea {
  position: relative;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.login-input-box .icon-box .icon .icon-img.data-v-097602ea {
  width: 40rpx;
}
.login-input-box .input-box.data-v-097602ea {
  position: relative;
  display: inline-block;
  width: calc(100% - 90rpx);
  height: 100%;
}
.login-input-box .input-box .input.data-v-097602ea {
  position: relative;
  width: 100%;
  font-size: 30rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.icon-box-btn.data-v-097602ea:active {
  opacity: 0.4;
}
.icon-img-loading.data-v-097602ea {
  -webkit-animation: loading-data-v-097602ea 1s infinite;
          animation: loading-data-v-097602ea 1s infinite;
}
@-webkit-keyframes loading-data-v-097602ea {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes loading-data-v-097602ea {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.login-input-box-mode-color-scheme-light.data-v-097602ea {
  border: 5rpx solid #F0F0F0;
  color: #000000;
}
.login-input-box-mode-color-scheme-light .icon-box.data-v-097602ea {
  -webkit-filter: invert(0);
          filter: invert(0);
}
.login-input-box-mode-color-scheme-dark.data-v-097602ea {
  color: #FFFFFF;
  border: 3rpx solid #404a56;
}
.login-input-box-mode-color-scheme-dark .icon-box.data-v-097602ea {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.tips.data-v-097602ea {
  font-size: 24rpx;
  color: #666666;
  margin-top: 10rpx;
  text-align: left;
}
.tips-mode-color-scheme-light.data-v-097602ea {
  color: #666666;
}
.tips-mode-color-scheme-dark.data-v-097602ea {
  color: #b9b9b9;
}
