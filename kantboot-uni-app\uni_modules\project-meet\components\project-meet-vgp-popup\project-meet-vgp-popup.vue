<template>
  <view>
    <kt-popup
        ref="ktPopup">
      <view
          :class="clazz.box"
      >
        <view style="height: 10rpx"></view>
        <view class="second-box">
          <view
              class="second-box-box">
            <view class="second-box-item"
                  :class="{
                    'second-box-item-selected': selected === 'vip'
                  }"
                  @click="selected = 'vip'"
            >{{ "VIP/SVIP" }}
              <view
                  v-if="selected === 'vip'"
                  class="second-box-line"></view>
            </view>
            <view class="second-box-item"
                  :class="{
            'second-box-item-selected': selected === 'gold'}"
                  @click="selected = 'gold'"
            >{{ $i18n.zhToGlobal('金币') }}
              <view
                  v-if="selected === 'gold'"
                  class="second-box-line"></view>
            </view>
            <view class="second-box-item"
                  :class="{
            'second-box-item-selected': selected === 'points'}"
                  @click="selected = 'points'"
            >{{ $i18n.zhToGlobal('积分') }}
              <view
                  v-if="selected === 'points'"
                  class="second-box-line"></view>

            </view>

          </view>
        </view>

        <view style="height: 50rpx"></view>

        <view v-show="selected === 'vip'">
          <project-meet-vip-panel
              v-if="$kt.style.detectDeviceType() === 'mobile'"
              :user-account-id="userAccountId"
              :height="'calc(100vh - 300rpx)'"
              @paySuccess="paySuccess()"
          ></project-meet-vip-panel>
          <project-meet-vip-panel
              v-if="$kt.style.detectDeviceType() === 'pc'"
              :user-account-id="userAccountId"
              :height="'calc(100vh - 200px)'"
              @paySuccess="paySuccess()"
          ></project-meet-vip-panel>
        </view>

        <view
        v-show="selected === 'gold'">
          <project-meet-gold-panel
              v-if="$kt.style.detectDeviceType() === 'mobile'"
              :user-account-id="userAccountId"
              :height="'calc(100vh - 300rpx)'"
              @paySuccess="paySuccess()"
          ></project-meet-gold-panel>
          <project-meet-gold-panel
              v-if="$kt.style.detectDeviceType() === 'pc'"
              :user-account-id="userAccountId"
              :height="'calc(100% - 500px)'"
              @paySuccess="paySuccess()"
          ></project-meet-gold-panel>

        </view>

        <view
        v-show="selected === 'points'">
          <project-meet-points-panel
              v-if="$kt.style.detectDeviceType() === 'mobile'"
              :user-account-id="userAccountId"
              :height="'calc(100vh - 300rpx)'"
              @paySuccess="paySuccess()"
          ></project-meet-points-panel>
          <project-meet-points-panel
              v-if="$kt.style.detectDeviceType() === 'pc'"
              :user-account-id="userAccountId"
              :height="'calc(100% - 500px)'"
              @paySuccess="paySuccess()"
          ></project-meet-points-panel>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  props: {
    userAccountId: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      },
      selected: 'vip',
    };
  },
  methods: {
    open(){
      console.log("open");
      this.$refs.ktPopup.open();
    },
    paySuccess(){
      setTimeout(()=>{
        this.$refs.ktPopup.close();
      },2500);
      this.$emit("paySuccess");
    }
  },
}
</script>

<style lang="scss" scoped>
.box{
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx 20rpx 0 0;
  text-align: left;
}
.box-mode-device-pc {
  text-align: center;
  .card{
    position: relative;
    width: 700rpx;
    display: inline-block;
    margin-bottom: 20rpx;
  }
  position: fixed;
  width: 800px;
  border-radius: 20rpx;
  padding: 20rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}


.second-box {
  width: 100%;

  .second-box-box {
    width: calc(100% - 100rpx);
    // 不换行
    white-space: nowrap;
    // 超出滚动
    overflow-x: auto;

    .second-box-item {
      position: relative;
      display: inline-block;
      font-size: 32rpx;
      letter-spacing: 3rpx;
      margin-right: 5rpx;
      padding: 10rpx 20rpx;
      border-radius: 10rpx;
      cursor: pointer;

      .login-btn {
        border: 2rpx solid #FFFFFF;
        box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);
        cursor: pointer;
        color: #ffffff;
        background-color: #5A7EF6;
        display: inline-block;
        padding: 10rpx 20rpx 10rpx 20rpx;
        border-radius: 30rpx;
        font-size: 24rpx;
        margin-right: 10rpx;
      }

      .second-box-line {
        position: absolute;
        width: 40rpx;
        height: 7rpx;
        background-color: #000000;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        border-radius: 20rpx;
      }
    }

    .second-box-item-selected {
      font-weight: bold;
    }
  }
}
</style>
