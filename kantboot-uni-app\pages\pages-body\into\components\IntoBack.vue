<template>
  <view :class="clazz.box">
    <kt-status-bar-height style="opacity: 0"></kt-status-bar-height>
      <view class="open-box">

          <view class="emoji-item"
                v-for="(emoji, index) in emojisStrList"
                :key="index">{{ emoji }}
        </view>

      </view>
    <view
        :class="clazz.back"></view>
    <view
        v-if="false"
        class="logo-box">
      <image class="img" :src="$kt.file.byPath('image/logo.png')" mode="widthFix"></image>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      cursor: 0,
      content: '',
      emojis: "SADOMASO小众SWITH字母DOMSUBK9SPDDLG多元LESGAY异同双单Hétérosexuel蜡烛Homosexuel鞭BisexuelTransgenreAsexuel",
      emojisStrList: "",
      clazz:{
        box: this.$kt.style.toggleClass("box"),
        back: this.$kt.style.toggleClass("back"),
      }
    }
  },

  mounted() {
    setTimeout(()=>{
      this.toText()
    },10);
  },
  methods: {
    async toText(){
      this.emojisStrList = this.emojis+this.emojis+this.emojis+this.emojis+this.emojis;
    }
  },
  watch: {}
}
</script>

<style lang="scss" scoped>
.box {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #f2f2f2;
  padding: 20rpx;
  box-sizing: border-box;
  z-index:-1
}

.box-mode-color-scheme-light {
  background-color: #f2f2f2;
}

.box-mode-color-scheme-dark {
  background-color: #191919;
}

.textarea {
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  font-size: 32rpx;
  background-color: #FFFFFF;
  border-radius: 10px;
}

.open-box {
  text-align: center;
  box-sizing: border-box;
  color: rgba(0, 0, 0, .8);

  .emoji-item {
    display: inline-block;
    width: 50rpx;
    height: 50rpx;
    text-align: center;
    font-size: 30rpx;
    font-weight: lighter;
  }

  .emoji-item:active {
    transform: scale(.9);
    background-color: #000000;
    border-radius: 10rpx;
  }
}

.back {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255,255,255,1);
  // 国家电网蓝
  //background-color: #3ca7a4;
  //background-color: #000008;
  //background-color: rgba(0,0,0,.2);
  z-index: 1;
}

.back-mode-color-scheme-light {
  background-color: rgba(255,255,255,1);
}

.back-mode-color-scheme-dark {
  background-color: #191919;
}


.logo-box{
  position: fixed;
  top: calc(50% - 150rpx - 300rpx - 100rpx);
  left:0;
  z-index: 2;
  opacity: 1;
  width: 100%;
  padding: 60rpx;
  box-sizing: border-box;


  .img{
    left: 50%;
    transform: translateX(-50%);
    width: 300rpx;
    //background-color: #f0f0f0;
    border-radius: 55%;
    // 渐变 透明 不透明 透明
    background: linear-gradient(90deg, rgba(240,240,240,0), rgba(240,240,240,1), rgba(240,240,240,0));
  }

  .text{
    font-size: 40rpx;
    text-align: left;
    margin-top: -50rpx;
  }

}


</style>
