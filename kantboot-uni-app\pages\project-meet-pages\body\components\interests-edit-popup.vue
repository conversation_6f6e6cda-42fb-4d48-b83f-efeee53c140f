<template>
  <view>
    <kt-popup
        ref="ktPopup"
        @close="close" @confirm="confirm">
      <view :class="clazz.popup">
        <view class="box">
          <view class="box-title">{{$i18n.zhToGlobal("兴趣爱好")}}</view>

          <view>
              <view class="box-tags">
                <view
                    v-for="item in interestsList"
                    class="tag"
                    :class="{
                      'tag-selected': hasInterests(item)
                    }"
                    @click="interestsPush(item.id)"
                >{{item.name}}</view>
              </view>
          </view>

          <view style="height: 30rpx"></view>
          <view>
            <kt-button
                ref="ktButton"
                @click="confirm()"
            >{{$i18n.zhToGlobal("确定修改")}}</kt-button>
          </view>
          <view style="height: 30rpx"></view>
        </view>
      </view>
    </kt-popup>
  </view>

</template>

<script>

export default {
  data() {
    return {
      show: false,
      userAccount: {
        interestsIds: [],
      },
      clazz:{
        popup:this.$kt.style.toggleClass("popup")
      },
      interestsList: [],
      interestsMap: {},
    }
  },
  mounted() {
  },
  methods: {
    // 是否包含兴趣爱好
    hasInterests(item) {
      if (!this.userAccount.interestsIds) {
        this.userAccount.interestsIds = [];
      }
      return this.userAccount.interestsIds.includes(item.id);
    },
    interestsPush(item){
      if (this.userAccount.interestsIds.includes(item)) {
        this.userAccount.interestsIds = this.userAccount.interestsIds.filter(id => id !== item);
      } else {
        this.userAccount.interestsIds.push(item);
      }
    },
    getInterests() {
      this.$request
          .post("/project-meet-web/interests/getAll", { data: {} })
          .then((res) => {
            this.interestsList = res.data;
            this.interestsMap = {};
            for (let i = 0; i < this.interestsList.length; i++) {
              const item = this.interestsList[i];
              this.interestsMap[item.id] = item.name;
            }
          });
    },
    open(userAccount) {
      this.getInterests();

      this.show = true;
      this.userAccount = JSON.parse(JSON.stringify(userAccount));
      this.$refs.ktPopup.open();
    },
    close() {
      this.show = false;
      this.$refs.ktPopup.close();
    },
    confirm() {
      this.$refs.ktButton.loading(null,999999);
      this.$request.post('/project-meet-web/userAccount/setInterests', {
        data:{interestsIds: this.userAccount.interestsIds}
      }).then(res => {
        this.$refs.ktButton.success(res.msg);
        this.close();
        this.$emit('confirm', this.userAccount.selfIntroduction);
      }).catch(err => {
        this.$refs.ktButton.error(err.errMsg);
      });
    },

  }
}
</script>

<style lang="scss" scoped>
.avatar:active {
  transform: scale(0.9);
}

.popup {
  padding: 20rpx 40rpx 20rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
}

.popup-title {
  padding: 20rpx;
  font-size: 34rpx;
  font-weight: bold;
  text-align: left;
  letter-spacing: 2rpx;
}

.picker {
  text-align: center;
  padding: 10rpx;
  box-sizing: border-box;
}

.bl-box {
  text-align: left;
  width: 100%;

  .bl-box-item {
    display: inline-block;
    margin: 10rpx;
    padding: 28rpx;
    border-radius: 20rpx;
    color: #333;
    box-sizing: border-box;
    background-color: #f5f5f5;

  }

  .bl-box-item-selected {
    background-color: #333;
    color: #fff;
  }
}

.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  font-size: 28rpx;
  text-align: center;
  letter-spacing: 2rpx;
}

.textarea {
  width: 100%;
  min-height: 150rpx;
  padding: 20rpx;
  box-sizing: border-box;
  text-align: left;
}

.gender-box {
  .gender-item {
    padding: 20rpx;
    box-sizing: border-box;
    border-radius: 30rpx;
    display: inline-block;
    width: calc(50% - 40rpx);
    color: #333;
    margin-left: 20rpx;
    background-color: #f5f5f5;
  }

  .gender-item-selected {
    color: #fff;
    background-color: #333;
  }
}
.popup-mode-device-pc{
  border-radius: 20rpx;
}
.nickname-input{
  // 禁止手势
  cursor: not-allowed;
}

.box-2{
  box-sizing: border-box;
}

.box-title{
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20rpx;
}

.box-tags{
  width: 100%;
  font-size: 28rpx;
  padding: 10rpx;
  box-sizing: border-box;

  .tag{
    display: inline-block;
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
    background-color: #F0F0F0;
    color: #999999;
    margin: 10rpx;
  }

  .tag:active{
    transform: scale(0.9);
  }

  .tag-selected{
    background-color: #000000;
    color: #FFFFFF;
  }
}
</style>

