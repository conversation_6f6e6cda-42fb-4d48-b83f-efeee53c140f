@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.scroll-view-x.data-v-269f50ac {
  width: 100%;
  white-space: nowrap;
  overflow-x: scroll;
}
.scroll-view-x .view-item.data-v-269f50ac {
  padding: 10rpx 20rpx 10rpx 70rpx;
  display: inline-block;
  font-size: 32rpx;
  text-align: center;
  background-color: #fff;
  border: 1px solid #eee;
  margin-right: 10px;
  border-radius: 10rpx;
  vertical-align: top;
  position: relative;
}
.scroll-view-x .view-item .view-item-icon.data-v-269f50ac {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  top: 50%;
  left: 20rpx;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.scroll-view-x .view-item-selected.data-v-269f50ac {
  border: 1px solid #666666;
}
.scroll-view-x.data-v-269f50ac::-webkit-scrollbar {
  display: none;
}
.form-item .form-item-input.data-v-269f50ac {
  padding: 20rpx;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 10rpx;
}
.form-item .form-item-input .input.data-v-269f50ac {
  width: 100%;
  height: 100%;
  font-size: 32rpx;
  color: #333;
  border: none;
  outline: none;
}
.form-item .form-item-param.data-v-269f50ac {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}
.form-item .form-item-param.data-v-269f50ac:before {
  content: "·";
  margin-right: 10rpx;
}
.form-item .in-form-item.data-v-269f50ac {
  padding: 20rpx;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 10rpx;
}
.form-mode-device-pc .scroll-view-x .view-item.data-v-269f50ac {
  cursor: pointer;
}
