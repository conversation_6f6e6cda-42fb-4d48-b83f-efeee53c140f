<template>
  <view>
    <view
        @click="selectAgreement()"
        :class="clazz.agreementBox">
      <view
          class="agreement-text"
          :style="param.notAgree ? 'color: red;' : ''"
      >

        <view
            :style="param.notAgree ? 'border: 1px solid red;' : ''"
            class="dot-box">
          <view
              v-if="param.agree"
              class="dot"></view>
        </view>
        <!-- 我已阅读并同意 -->
        <text
        >
          {{$i18n.zhToGlobal("我已阅读并同意")}}
        </text>
        <text>
          {{":"}}
        </text>
        <!-- 用户协议 -->
        <text
            @click="toY1()"
            style="font-weight: bold;margin: 0 5rpx 0 5rpx">
          {{$i18n.zhToGlobal("用户协议")}}
        </text>
        <!-- 隐私政策 -->
        <text style="font-weight: bold;margin: 0 5rpx 0 15rpx"
        @click="toY2()"
        >
          {{$i18n.zhToGlobal("隐私政策")}}
        </text>
      </view>
    </view>

  </view>
</template>

<script>
import $i18n from "@/uni_modules/kantboot/libs/i18n";

export default {
  data() {
    return {
      $i18n,
      clazz:{
        agreementBox: this.$kt.style.toggleClass("agreement-box"),
      },
      param:{
        agree: false,
        // 是否未同意，用来警告
        notAgree: false,
      }
    };
  },
  created() {
    this.$emit("change", {"agree":this.param.agree});
    this.$kt.event.on("loginPage:selectAgreement:warning", () => {
      this.warning();
    });
  },
  methods: {
    toY1(){
      // #ifdef H5
      window.open("http://www.baidu.com");
      // #endif
    },
    toY2(){
      // #ifdef H5
      window.open("http://www.baidu.com");
      // #endif

    },
    selectAgreement() {
      this.param.notAgree = false;
      this.param.agree = !this.param.agree;
      this.$kt.event.emit("loginPage:selectAgreement", this.param.agree);
      this.$emit("change", {"agree":this.param.agree});
    },
    isAgree() {
      return this.param.agree;
    },
    isNotAgree() {
      return this.param.notAgree;
    },
    // 发出警告
    warning() {
      this.param.notAgree = true;
      setTimeout(() => {
        this.param.notAgree = false;
      }, 2000);
    },
  },
}
</script>

<style lang="scss" scoped>

.agreement-box {
  margin-top: 20rpx;
  width: 100%;
  text-align: center;

  .agreement-text {
    display: inline-block;
    font-size: 24rpx;
    vertical-align: top;
    color: #666666;
  }
}

.agreement-box:active {
  //opacity: .8;
}


.dot-box {
  display: inline-block;
  vertical-align: top;
  width: 20rpx;
  height: 20rpx;
  margin-right: 10rpx;
  border-radius: 50%;
  border: 1px solid #000000;
  margin-top: 8rpx;
  position: relative;

  .dot {
    position: absolute;
    width: 15rpx;
    height: 15rpx;
    border-radius: 50%;
    background-color: #666666;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.agreement-box-mode-color-scheme-light {
  color: #000;
  .agreement-text {
    color: #000;
  }
  .dot-box {
    border: 1px solid #000;
    .dot {
      background-color: #000;
    }
  }
}

.agreement-box-mode-color-scheme-dark {
  color: #f9f9f9;
  .agreement-text {
    color: #f9f9f9;
  }
  .dot-box {
    border: 1px solid #fff;
    .dot {
      background-color: #fff;
    }
  }
}

</style>
