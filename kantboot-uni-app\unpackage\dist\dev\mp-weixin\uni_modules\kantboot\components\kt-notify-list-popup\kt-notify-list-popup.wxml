<view class="data-v-ecd1347c"><kt-popup bind:close="__e" vue-id="ad5458f8-1" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" class="data-v-ecd1347c vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-ecd1347c',clazz.box]}}"><view class="box-title data-v-ecd1347c">{{$root.g0}}</view><scroll-view class="scroll-view data-v-ecd1347c" scroll-y="{{true}}"><block wx:if="{{show}}"><kt-notify-list-panel vue-id="{{('ad5458f8-2')+','+('ad5458f8-1')}}" data-ref="ktNotifyListPanel" class="data-v-ecd1347c vue-ref" bind:__l="__l"></kt-notify-list-panel></block></scroll-view></view></kt-popup></view>