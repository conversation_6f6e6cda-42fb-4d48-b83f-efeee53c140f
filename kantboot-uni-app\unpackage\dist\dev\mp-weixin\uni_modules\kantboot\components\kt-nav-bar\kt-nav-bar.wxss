@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.nav-bar-container.data-v-8627e27c {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 3000;
  background-color: #FFFFFF;
}
.nav-bar-container-mode-device-pc.data-v-8627e27c {
  position: absolute;
}
.nav-bar-container-mode-color-scheme-light.data-v-8627e27c {
  background-color: #fff;
}
.nav-bar-container-mode-color-scheme-light .nav-bar-title.data-v-8627e27c {
  color: #333333;
}
.nav-bar-container-mode-color-scheme-light .menu-icon.data-v-8627e27c {
  -webkit-filter: invert(0%);
          filter: invert(0%);
}
.nav-bar-container-mode-color-scheme-light .nav-bar-back-icon.data-v-8627e27c {
  -webkit-filter: invert(0%);
          filter: invert(0%);
}
.nav-bar-container-mode-color-scheme-dark.data-v-8627e27c {
  background-color: #191919;
}
.nav-bar-container-mode-color-scheme-dark .nav-bar-title.data-v-8627e27c {
  color: #ffffff;
  opacity: 0.8;
}
.nav-bar-container-mode-color-scheme-dark .menu-icon.data-v-8627e27c {
  -webkit-filter: invert(100%);
          filter: invert(100%);
  opacity: 0.8;
}
.nav-bar-container-mode-color-scheme-dark .nav-bar-back-icon.data-v-8627e27c {
  -webkit-filter: invert(100%);
          filter: invert(100%);
  opacity: 0.8;
}
.nav-bar.data-v-8627e27c {
  position: relative;
  z-index: 9999;
}
.nav-bar .nav-bar-back-icon.data-v-8627e27c {
  position: absolute;
  width: 45rpx;
  height: 45rpx;
  left: 20rpx;
  top: 50%;
  z-index: 9999;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.nav-bar .nav-bar-back-icon.data-v-8627e27c:active {
  -webkit-transform: scale(0.9) translateY(-50%);
          transform: scale(0.9) translateY(-50%);
}
.nav-bar .nav-bar-slot.data-v-8627e27c {
  position: absolute;
}
.nav-bar .nav-bar-title.data-v-8627e27c {
  position: absolute;
  left: 72rpx;
  font-size: 36rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  font-weight: bold;
}
.menu-icon.data-v-8627e27c {
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  border-radius: 55%;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 1000;
  right: 0;
}
.menu-icon.data-v-8627e27c:active {
  -webkit-transform: translateY(-50%) scale(0.9);
          transform: translateY(-50%) scale(0.9);
}
