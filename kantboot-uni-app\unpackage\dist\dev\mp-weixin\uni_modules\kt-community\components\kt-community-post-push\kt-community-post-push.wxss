@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-fc9ed52a {
  padding: 20rpx;
  box-sizing: border-box;
}
.box .box-textarea.data-v-fc9ed52a {
  width: 100%;
  height: calc(100vh - 700rpx);
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  font-size: 32rpx;
  color: #333;
  border: none;
  outline: none;
}
.menu-box.data-v-fc9ed52a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.menu-box .menu-title.data-v-fc9ed52a {
  font-size: 28rpx;
  color: #333;
  vertical-align: top;
}
.menu-box .menu-title .menu-icon.data-v-fc9ed52a {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  display: inline-block;
  vertical-align: top;
}
.menu-box .menu-title .menu-icon .menu-icon-image.data-v-fc9ed52a {
  width: 100%;
  height: 100%;
  opacity: 0.8;
}
.menu-box .menu-fixed.data-v-fc9ed52a {
  position: relative;
}
.menu-box .menu-fixed .menu-fixed-tips.data-v-fc9ed52a {
  position: absolute;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  width: 300rpx;
  right: 70rpx;
  vertical-align: top;
  margin-right: 5rpx;
}
.menu-box .menu-fixed .menu-fixed-content.data-v-fc9ed52a {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}
.menu-box .menu-fixed .menu-fixed-content .menu-fixed-content-icon.data-v-fc9ed52a {
  width: 20rpx;
  height: 20rpx;
}
.menu-box .menu-fixed .menu-fixed-content .menu-fixed-content-icon-bottom.data-v-fc9ed52a {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.menu-box .menu-fixed .menu-fixed-content .menu-fixed-content-icon-top.data-v-fc9ed52a {
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
}
.menu-box-box.data-v-fc9ed52a {
  padding: 0 20rpx 20rpx 20rpx;
}
