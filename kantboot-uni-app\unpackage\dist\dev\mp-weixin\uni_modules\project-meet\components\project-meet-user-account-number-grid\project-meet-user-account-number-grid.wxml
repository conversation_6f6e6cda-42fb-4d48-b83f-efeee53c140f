<view class="box data-v-0aaa48cb"><view class="grid data-v-0aaa48cb"><view data-event-opts="{{[['tap',[['toInterrelation',['follow']]]]]}}" class="grid-item data-v-0aaa48cb" bindtap="__e"><view class="num data-v-0aaa48cb">{{$root.m0}}</view><view class="text data-v-0aaa48cb">{{$root.g0}}</view></view><view data-event-opts="{{[['tap',[['toPostSelf']]]]}}" class="grid-item data-v-0aaa48cb" bindtap="__e"><view class="num data-v-0aaa48cb">{{userAccountRelationship.postCount||'0'}}</view><view class="text data-v-0aaa48cb">{{$root.g1}}</view></view><view data-event-opts="{{[['tap',[['toInterrelation',['followed']]]]]}}" class="grid-item data-v-0aaa48cb" bindtap="__e"><view class="num data-v-0aaa48cb">{{$root.m1}}</view><view class="text data-v-0aaa48cb">{{$root.g2}}</view></view><view data-event-opts="{{[['tap',[['toLike',['like']]]]]}}" class="grid-item data-v-0aaa48cb" bindtap="__e"><view class="num data-v-0aaa48cb">{{userAccountRelationship.likeCount||'0'}}</view><view class="text data-v-0aaa48cb">{{$root.g3}}</view></view><view data-event-opts="{{[['tap',[['toVisit']]]]}}" class="grid-item data-v-0aaa48cb" bindtap="__e"><view class="num data-v-0aaa48cb">{{userAccountRelationship.visitCount||'0'}}</view><view class="text data-v-0aaa48cb">{{$root.g4}}</view></view></view><kt-user-account-interrelation-popup bind:select="__e" vue-id="4a9f519b-1" data-ref="ktUserAccountInterrelationPopup" data-event-opts="{{[['^select',[['selectUserAccount']]]]}}" class="data-v-0aaa48cb vue-ref" bind:__l="__l"></kt-user-account-interrelation-popup><project-meet-user-info-popup vue-id="4a9f519b-2" data-ref="projectMeetUserInfoPopup" class="data-v-0aaa48cb vue-ref" bind:__l="__l"></project-meet-user-info-popup><project-meet-like-list-popup vue-id="4a9f519b-3" data-ref="projectMeetLikeListPopup" class="data-v-0aaa48cb vue-ref" bind:__l="__l"></project-meet-like-list-popup><project-meet-post-self-popup vue-id="4a9f519b-4" data-ref="projectMeetPostSelfPopup" class="data-v-0aaa48cb vue-ref" bind:__l="__l"></project-meet-post-self-popup><project-meet-visit-popup vue-id="4a9f519b-5" data-ref="projectMeetVisitPopup" class="data-v-0aaa48cb vue-ref" bind:__l="__l"></project-meet-visit-popup></view>