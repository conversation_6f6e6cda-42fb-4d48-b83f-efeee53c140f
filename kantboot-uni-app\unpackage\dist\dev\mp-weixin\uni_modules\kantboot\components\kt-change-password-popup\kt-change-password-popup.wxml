<view class="data-v-b72e5afc"><kt-popup vue-id="2e9c0278-1" zIndex="{{999999999}}" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-b72e5afc vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-b72e5afc',clazz.box]}}"><view class="title data-v-b72e5afc">{{$root.g0}}</view><view class="in-box data-v-b72e5afc"><block wx:if="{{!noHasOldPassword}}"><view class="in-box-input data-v-b72e5afc"><image class="in-box-input-icon data-v-b72e5afc" src="{{$root.g1}}"></image><block wx:if="{{!oldPasswordShow}}"><input class="in-box-input-input data-v-b72e5afc" type="password" placeholder="{{$root.g2}}" data-event-opts="{{[['input',[['__set_model',['$0','oldPassword','$event',[]],['requestParams']],['e0',['$event']]]]]}}" value="{{requestParams.oldPassword}}" bindinput="__e"/></block><block wx:if="{{oldPasswordShow}}"><input class="in-box-input-input data-v-b72e5afc" type="text" placeholder="{{$root.g3}}" data-event-opts="{{[['input',[['__set_model',['$0','oldPassword','$event',[]],['requestParams']],['e1',['$event']]]]]}}" value="{{requestParams.oldPassword}}" bindinput="__e"/></block><block wx:if="{{!oldPasswordShow}}"><image class="in-box-input-icon data-v-b72e5afc" src="{{$root.g4}}"></image></block><block wx:if="{{oldPasswordShow}}"><image class="in-box-input-icon data-v-b72e5afc" src="{{$root.g5}}"></image></block><view data-event-opts="{{[['tap',[['oldPasswordShowChange']]]]}}" class="in-box-input-show-change data-v-b72e5afc" bindtap="__e"></view></view></block></view><view class="in-box data-v-b72e5afc"><view class="in-box-input data-v-b72e5afc"><image class="in-box-input-icon data-v-b72e5afc" src="{{$root.g6}}"></image><block wx:if="{{!passwordShow}}"><input class="in-box-input-input data-v-b72e5afc" type="password" placeholder="{{$root.g7}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['requestParams']],['e2',['$event']]]]]}}" value="{{requestParams.password}}" bindinput="__e"/></block><block wx:if="{{passwordShow}}"><input class="in-box-input-input data-v-b72e5afc" type="text" placeholder="{{$root.g8}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['requestParams']],['e3',['$event']]]]]}}" value="{{requestParams.password}}" bindinput="__e"/></block><block wx:if="{{!passwordShow}}"><image class="in-box-input-icon data-v-b72e5afc" src="{{$root.g9}}"></image></block><block wx:if="{{passwordShow}}"><image class="in-box-input-icon data-v-b72e5afc" src="{{$root.g10}}"></image></block><view data-event-opts="{{[['tap',[['passwordShowChange']]]]}}" class="in-box-input-show-change data-v-b72e5afc" bindtap="__e"></view></view></view><view class="in-box data-v-b72e5afc"><view class="in-box-input data-v-b72e5afc"><image class="in-box-input-icon data-v-b72e5afc" src="{{$root.g11}}"></image><block wx:if="{{!passwordConfirmShow}}"><input class="in-box-input-input data-v-b72e5afc" type="password" placeholder="{{$root.g12}}" data-event-opts="{{[['input',[['__set_model',['','passwordConfirm','$event',[]]],['e4',['$event']]]]]}}" value="{{passwordConfirm}}" bindinput="__e"/></block><block wx:if="{{passwordConfirmShow}}"><input class="in-box-input-input data-v-b72e5afc" type="text" placeholder="{{$root.g13}}" data-event-opts="{{[['input',[['__set_model',['','passwordConfirm','$event',[]]],['e5',['$event']]]]]}}" value="{{passwordConfirm}}" bindinput="__e"/></block><block wx:if="{{!passwordConfirmShow}}"><image class="in-box-input-icon data-v-b72e5afc" src="{{$root.g14}}"></image></block><block wx:if="{{passwordConfirmShow}}"><image class="in-box-input-icon data-v-b72e5afc" src="{{$root.g15}}"></image></block><view data-event-opts="{{[['tap',[['passwordConfirmShowChange']]]]}}" class="in-box-input-show-change data-v-b72e5afc" bindtap="__e"></view></view></view><view style="height:30rpx;" class="data-v-b72e5afc"></view><kt-button bind:click="__e" vue-id="{{('2e9c0278-2')+','+('2e9c0278-1')}}" data-ref="ktButton" data-event-opts="{{[['^click',[['toBind']]]]}}" class="data-v-b72e5afc vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{''+$root.g16+''}}</kt-button></view></kt-popup></view>