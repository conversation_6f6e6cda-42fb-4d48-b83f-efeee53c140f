<view class="data-v-eeb32b80"><kt-popup vue-id="e7b14274-1" data-ref="ktPopup" class="data-v-eeb32b80 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-eeb32b80',clazz.box]}}"><view class="data-v-eeb32b80"><view class="title data-v-eeb32b80">{{$root.g0}}</view><view class="data-v-eeb32b80"><view class="remark-input-box data-v-eeb32b80"><input class="remark-input data-v-eeb32b80" type="text" placeholder="{{$root.g1}}" data-event-opts="{{[['input',[['__set_model',['$0','remark','$event',[]],['bodyData']],['e0',['$event']]]]]}}" value="{{bodyData.remark}}" bindinput="__e"/></view></view><view style="height:50rpx;" class="data-v-eeb32b80"></view><view class="data-v-eeb32b80"><kt-button bind:click="__e" vue-id="{{('e7b14274-2')+','+('e7b14274-1')}}" data-ref="ktButton" data-event-opts="{{[['^click',[['confirm']]]]}}" class="data-v-eeb32b80 vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g2}}</kt-button></view><view style="height:50rpx;" class="data-v-eeb32b80"></view></view></view></kt-popup></view>