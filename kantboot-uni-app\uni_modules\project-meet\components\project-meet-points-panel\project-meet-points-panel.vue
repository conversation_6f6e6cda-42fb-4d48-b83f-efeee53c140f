<template>
  <view>
    <scroll-view
        :class="clazz.userRecommendPanel"
        :scroll-y="true"
        :style="{ height }"
    >
      <view :class="clazz.panel">
        <view class="header">
          <view class="panel-header" style="margin-top: 25rpx;">
            <kt-user-info-card
                :user-account-id="userAccountId"
            ></kt-user-info-card>
          </view>
          <view
              v-if="getIsSelf()&&hasSelfNumber"
              class="header_bottom">
            <view class="header_global">{{ $i18n.zhToGlobal("我的积分") }}{{": "}}
              {{parseInt(userAccount.projectMeetPointsTotal || '0') }}
              </view>
          </view>
        </view>
        <view class="bottom">
          <view class="bottom_title">{{ $i18n.zhToGlobal("充值积分") }}</view>
          <view class="bottom_list">
            <view
                class="bottom_list_item"
                v-for="(item, index) in list"
                @click="select(item.id)"
                :key="index"
                :class="{
              active_class: selected === item.id,
            }"
            >
              <view class="bottom_text_box">
                <view class="bottom_text_box">
                  <view class="bottom_text_top">
                    <text style="font-size: 32rpx">{{ item.number }}</text>
                    <text style="font-size: 24rpx;margin-left: 10rpx">{{ $i18n.zhToGlobal("积分") }}</text>
                  </view>
                  <view class="bottom_text_bottom">
                    {{ "$" + item.price.toFixed(2) }}
                  </view>
                </view>
              </view>
              <!-- {{item.price}} -->
            </view>
          </view>
          <view class="bottom_buttom">
            <view class="bottom_buttom_class">
              <kt-button ref="toPayBtn" class="pay-btn" @click="createOrder()"
              >{{ $i18n.zhToGlobal("确认充值") }}
              </kt-button>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <kt-pay-popup
        @paySuccess="paySuccess()"
        ref="ktPayPopup"
    ></kt-pay-popup>
  </view>
</template>

<script>
import userAccount from "@/uni_modules/kantboot/libs/userAccount";

export default {
  computed: {
    userAccount() {
      return userAccount
    }
  },
  components: {},
  props: {
    userAccountId:{
      type: [String, Number],
      default: "",
    },
    height: {
      type: String,
      default: "100vh",
    },
    hasSelfNumber: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      clazz: {
        panel: this.$kt.style.toggleClass("panel"),
      },
      selected: -1,
      list: [
      ],
      userAccount:{

      }
    };
  },
  async created() {
    this.init();
    this.initUserAccount();
    this.$kt.event.on("userAccount:getById",()=>{
      this.initUserAccount();
    });
    if(this.$kt.userAccount.getSelf().id+""===this.userAccountId+""){
      await this.$kt.userAccount.requestSelf();
      this.userAccount = this.$kt.userAccount.getSelf();

      this.$kt.event.on("ProjectMeetUserAccount:handlePoints",async (res)=>{
        // this.userAccount = res;
        await this.$kt.userAccount.requestSelf();
        this.userAccount = this.$kt.userAccount.getSelf();
      });
    }
  },
  methods: {
    getIsSelf() {
      // 获取自己的用户账号ID
      const selfUserAccountId = this.$kt.userAccount.getSelf().id;
      return this.userAccountId === selfUserAccountId;
    },
    async paySuccess(){
      await this.$kt.userAccount.requestSelf();
      this.userAccount = this.$kt.userAccount.getSelf();
    },
    initUserAccount(){
      this.userAccount = this.$kt.userAccount.getSelf();
    },
    init() {
      this.initList();
    },
    async initList() {
      await this.$request
          .post("/project-meet-web/pointsRecharge/getAll")
          .then((res) => {
            this.list = res.data;
            // 默认选择第一个
            if (this.list.length > 2) {
              this.selected = this.list[2].id;
            }
          });
    },
    select(id) {
      this.selected = id;
      console.log(this.selected);
    },
    async createOrder(){
      this.$refs.toPayBtn.loading("正在创建订单");
      // /project-meet-web/goldRecharge/generateOrder
      await this.$request.post("/project-meet-web/pointsRecharge/generateOrder",{
        data:{
          id: this.selected,
          userAccountIdOfGetter: this.userAccountId
        }
      }).then((res)=>{
        console.log(res,"res----");
        this.$refs.ktPayPopup.open(res.data);
      }).catch((err)=>{
        console.log(err,"err----");
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.header {
  position: relative;
  padding: 0 30rpx 30rpx 30rpx;
  .header_bottom {
    position: absolute;
    top:0rpx;
    right: 20rpx;
    border-radius: 16rpx;
    padding-left: 30rpx;
    font-size: 26rpx;
  }
}
.bottom {
  padding: 0 30rpx 150rpx 30rpx;
  .bottom_title {
    font-size: 32rpx;
    font-weight: bold;
    padding-bottom: 20rpx;
  }
  .bottom_list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .bottom_list_item {
    width: 219rpx;
    height: 219rpx;
    box-sizing: border-box;
    // 渐变，从白到透明
    background:
        linear-gradient(
                to bottom,
                rgba(255, 255, 255, 0.2) 0%,
                rgba(255, 255, 255, 0.7) 100%
        );


    border-radius: 25rpx;
    color: #222;
    display: flex;
    justify-content: center;
    margin-bottom: 20rpx;
    // padding: 40rpx 10rpx;
  }
  .active_class {
    background: linear-gradient(
            to bottom,
            #fc4847 0%,
            rgba(255, 153, 0, 0.1) 100%
    );
    border: 1px solid #fc4847;
    color:#FFFFFF;
    text-shadow: 0 0 10rpx #fc4847;
  }
  .bottom_text_box {
    width: 200rpx;
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: center;
    align-items: center;
  }
  .bottom_text_top {
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }
  .bottom_text_bottom {
    font-size: 40rpx;
    font-weight: bold;
  }
  .bottom_buttom {
    font-size: 26rpx;
    .bottom_buttom_class {
      margin-top: 50rpx;
    }
  }
}
.panel-mode-device-pc {
  .bottom_list {
    display: flex;
    flex-wrap: wrap;
    padding: 0 34rpx;
  }
  .bottom_list_item {
    border: 1px solid #eeeeee;
    flex: 0 0 30%;
    cursor: pointer;
  }
}

.panel-mode-device-pc{
  padding: 0 100px 0 100px;
  box-sizing: border-box;

  .bottom_buttom_class{
    padding: 0 200px 0 200px;
  }
}

</style>
