<template>
  <view>
    <kt-popup
    ref="ktPopup">
      <view
      :class="clazz.box"
      >
        <view class="title">{{$i18n.zhToGlobal("帖子发布")}}</view>
        <kt-community-post-push
            @pushSuccess="pushSuccess"
        :is-show-permission-setting="isShowPermissionSetting"
        ></kt-community-post-push>
        <view style="height: 40rpx"></view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  props: {
    isShowPermissionSetting: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      clazz: {
        box: this.$kt.style.toggleClass("box"),
      },
    };
  },
  methods: {
    open() {
      this.$refs.ktPopup.open();
    },
    close() {
      this.$refs.ktPopup.close();
    },
    pushSuccess() {
      this.$emit("pushSuccess");
      this.close();
    },
  },
}
</script>

<style lang="scss" scoped>
.box-mode-device-pc {
  position: fixed;
  width: 400px;
  background-color: #fff;
  border-radius: 20rpx;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 30rpx;
  .title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
}
</style>
