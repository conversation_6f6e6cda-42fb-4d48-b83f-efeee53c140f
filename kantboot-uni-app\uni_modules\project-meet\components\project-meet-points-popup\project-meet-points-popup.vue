<template>
  <view>
    <kt-popup
        ref="ktPopup">
      <view
          :class="clazz.box"
      >
        <view style="height: 50rpx"></view>
        <project-meet-points-panel
            :has-self-number="hasSelfNumber"
            v-if="$kt.style.detectDeviceType() === 'mobile'"
            :user-account-id="userAccountId"
            :height="'calc(100vh - 300rpx)'"
            @paySuccess="paySuccess()"
        ></project-meet-points-panel>
        <project-meet-points-panel
            :has-self-number="hasSelfNumber"
            v-if="$kt.style.detectDeviceType() === 'pc'"
            :user-account-id="userAccountId"
            :height="'calc(100% - 500px)'"
            @paySuccess="paySuccess()"
        ></project-meet-points-panel>

      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  props: {
    userAccountId: {
      type: [String, Number],
      default: "",
    },
    hasSelfNumber: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      }
    };
  },
  methods: {
    open(){
      // setTimeout(()=>{
      //   this.$refs.ktPayPanel.getOrder(orderId);
      // },100);
      // console.log("open");
      this.$refs.ktPopup.open();
    },
    paySuccess(){
      setTimeout(()=>{
        this.$refs.ktPopup.close();
      },2500);
      this.$emit("paySuccess");
    }
  },
}
</script>

<style lang="scss" scoped>
.box{
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx 20rpx 0 0;
}
.box-mode-device-pc {
  text-align: center;
  .card{
    position: relative;
    width: 700rpx;
    display: inline-block;
    margin-bottom: 20rpx;
  }
  position: fixed;
  width: 800px;
  border-radius: 20rpx;
  padding: 20rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
