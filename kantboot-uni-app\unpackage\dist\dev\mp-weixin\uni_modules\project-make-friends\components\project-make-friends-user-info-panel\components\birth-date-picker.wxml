<view class="data-v-5f3027f0"><u-datetime-picker vue-id="3c9615fe-1" title="{{$root.g0}}" show="{{show}}" closeOnClickOverlay="{{true}}" minDate="{{minDate}}" maxDate="{{maxDate}}" visibleItemCount="10" confirmColor="#333" mode="datetime" value="{{valueTime}}" data-event-opts="{{[['^cancel',[['close']]],['^close',[['close']]],['^confirm',[['confirm']]],['^input',[['__set_model',['','valueTime','$event',[]]]]]]}}" bind:cancel="__e" bind:close="__e" bind:confirm="__e" bind:input="__e" class="data-v-5f3027f0" bind:__l="__l"></u-datetime-picker></view>