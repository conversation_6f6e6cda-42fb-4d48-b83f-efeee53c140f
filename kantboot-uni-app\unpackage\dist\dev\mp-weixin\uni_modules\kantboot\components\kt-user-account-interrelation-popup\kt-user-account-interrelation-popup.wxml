<view class="data-v-7a271a16"><kt-popup vue-id="6a86aca0-1" height="{{height}}" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-7a271a16 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-7a271a16',clazz.box]}}"><view style="height:20rpx;" class="data-v-7a271a16"></view><view class="title data-v-7a271a16">{{''+$root.g0+''}}</view><view style="height:20rpx;" class="data-v-7a271a16"></view><block wx:if="{{isLogin}}"><view class="panel-box data-v-7a271a16"><kt-user-account-interrelation-panel vue-id="{{('6a86aca0-2')+','+('6a86aca0-1')}}" many-select="{{manySelect}}" id-selected-list-prop="{{idSelectedList}}" user-account-id="{{userAccountSelf.id}}" height="{{'calc('+height+' - '+'100rpx)'}}" init-code="{{initCode}}" data-ref="userAccountInterrelationPanel" data-event-opts="{{[['^select',[['select']]]]}}" bind:select="__e" class="data-v-7a271a16 vue-ref" bind:__l="__l"></kt-user-account-interrelation-panel></view></block><block wx:if="{{manySelect}}"><view class="bottom-btn-box data-v-7a271a16"><kt-button bind:click="__e" vue-id="{{('6a86aca0-3')+','+('6a86aca0-1')}}" data-event-opts="{{[['^click',[['close']]]]}}" class="data-v-7a271a16" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g1}}</kt-button></view></block></view></kt-popup></view>