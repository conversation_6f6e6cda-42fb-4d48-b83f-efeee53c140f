<template>
  <view>
    <view class="fixed">
      <kt-nav-bar
          :background-color="'rgba(0,0,0,0)'"
          :title="$i18n.zhToGlobal('登录')"></kt-nav-bar>
    </view>

    <login-back></login-back>
    
    <login-bottom-card></login-bottom-card>
  </view>
</template>

<script>
import LoginBack from "./components/LoginBack.vue";
import LoginBottomCard from "./components/LoginBottomCard.vue";
export default {
  components: {
    LoginBack,
    LoginBottomCard
  },
  data() {
    return {};
  },
  created() {
    this.$kt.event.on("login:success", () => {
      uni.navigateBack();
    });
  },
}
</script>

<style lang="scss" scoped>
.fixed{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}
</style>
