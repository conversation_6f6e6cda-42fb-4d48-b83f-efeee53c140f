<template>
  <view>
    <project-acm-nav-bar
        :is-has-i18n="false"
        :title="$i18n.zhToGlobal('问题详情')"
    ></project-acm-nav-bar>
    <view class="box" v-if="postId">
      <kt-community-post-card
      :post-id="postId"
      @dotClick="cardDotClick"
      ref="projectAcmCard"
      :has-bottom-operation="false"
      >
        <template v-slot:bottom>
          <project-acm-card-bottom
          :postId="postId"
          ref="projectAcmCardBottom"
          ></project-acm-card-bottom>
        </template>
      </kt-community-post-card>
      <kt-community-post-operate-popup
          @removeSuccess="removeSuccess"
          :remove-text="$kt.i18n.zhToGlobal('删除')"
          :report-text="$kt.i18n.zhToGlobal('举报')"
          :has-permission-setting="false"
          ref="communityPostOperatePopup">
      </kt-community-post-operate-popup>
    </view>

    <view style="height: 100rpx"></view>

    <view
        v-if="isSelf||self.isInnerStaff"
        class="bottom">
      <kt-image-select
          v-if="bindParams.imageIds.length > 0"
          file-group-code="fp"
          v-model="bindParams.imageIds"
      ></kt-image-select>
      <kt-send-input
          :placeholder="placeholder"
          mode="panel"
          ref="sendInput"
          :has-video="false"
          :has-voice="false"
          :has-user-account="false"
          @send="send"
      ></kt-send-input>
    </view>

    <view
    v-else
    class="bottom"
    >
      <kt-button
          open-type="share"
      :is-open-box-shadow="false"
      >{{$i18n.zhToGlobal("分享")}}</kt-button>
    </view>

  </view>
</template>

<script>
export default {
  data() {
    return {
      placeholder: this.$i18n.zhToGlobal("追问"),
      postId: null,
      post: {},
      bindParams:{
        imageIds: [],
      },
      isSelf: false,
      isLogin: false,
      self: {},
      currentPath: "/pages/project-acm-pages/post-detail/post-detail",
    };
  },
  onLoad(options) {
    this.postId = options.postId;
    this.getByPostId();
    this.isLogin = this.$kt.userAccount.getIsLogin();
    if(this.isLogin){
      this.self = this.$kt.userAccount.getSelf();
    }
  },
  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.$kt.userAccount.getSelf().nickname,
      path: this.currentPath+'?userAccountIdOfDirect='+this.userAccount.id+'&postId='+this.postId,
    }
  },
  onShareAppMessage() {
    return {
      title: this.$kt.userAccount.getSelf().nickname,
      path: this.currentPath+'?userAccountIdOfDirect='+this.userAccount.id+'&postId='+this.postId,
    }
  },
  methods: {
    removeSuccess(){
      uni.navigateBack();
    },
    getByPostId(){
      this.$request.post("/fp-community-web/post/getById", {
        data: {
          id: this.postId,
        }
      }).then((res) => {
        this.post = res.data;
        this.isSelf = this.post.userAccountIdOfUploader === this.self.id;
        if(this.post.userAccountIdOfUploader === this.self.id) {
          this.placeholder = this.$i18n.zhToGlobal("追问");
        }
        if(this.post.userAccountIdOfUploader !== this.self.id) {
          this.placeholder = this.$i18n.zhToGlobal("回答");
        }
        try{
          this.$forceUpdate();
        }catch (e) {

        }
      }).catch((err) => {
        uni.showToast({
          title: err.errMsg,
          icon: "none",
        });
      });
    },
    send(ktFormat){
      if(ktFormat.type==="image:id"){
        this.bindParams.imageIds.push(ktFormat.content);
      }
      if(ktFormat.type==="text"){
        // TODO
        this.$refs.sendInput.loading = true;
        this.$request.post("/fp-community-web/postComment/sendPostComment", {
          data:{
            postId: this.postId,
            items:[{
              type: "text",
              content: ktFormat.content,
            },{
              type: "images:id",
              content: JSON.stringify(this.bindParams.imageIds),
            }]
          }
        }).then((res) => {
          this.$refs.sendInput.loading = false;
          this.$refs.sendInput.clear();
          this.bindParams.imageIds = [];
          this.$refs.projectAcmCardBottom.refresh();
          this.$emit("sendSuccess", res.data);
          this.$kt.event.emit("projectAcm:sendPostComment", res.data);
        }).catch((err) => {
          uni.showToast({
            title: err.errMsg,
            icon: "none",
          });
        });
      }
    },
    cardDotClick(post){
      if(!this.$kt.userAccount.getIsLogin()){
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.$refs.communityPostOperatePopup.open(post);
    },
  },
}
</script>

<style lang="scss" scoped>
.box{
  box-sizing: border-box;
}
.bottom{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  z-index: 100;
  box-sizing: border-box;
}
</style>
