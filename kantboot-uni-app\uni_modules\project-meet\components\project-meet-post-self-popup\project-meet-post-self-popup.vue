<template>
  <view>
    <kt-popup
    ref="ktPopup">
      <view :class="clazz.box">
        <project-meet-post-self-panel
            :header-height="300"
        ></project-meet-post-self-panel>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      }
    };
  },
  mounted() {
  },
  methods: {
    open() {
      this.$refs.ktPopup.open();
    },
  },
}
</script>

<style lang="scss" scoped>
.box-mode-device-pc{
  position: fixed;
  width: 500px;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-radius: 20rpx;
  top:50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
