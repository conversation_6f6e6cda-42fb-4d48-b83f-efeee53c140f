import base64 from "./libs/base64";
import dataChange from "./libs/dataChange";
import file from "./libs/file";
import util from "./libs/util";
import router from "./libs/router";
import storage from "./libs/storage";
import event from "./libs/event";
import request from "./libs/request";
import image from "./libs/image";
import global from "./libs/global";
import date from "./libs/date";
import i18n from "./libs/i18n";
import userAccount from "./libs/userAccount";
import math from "./libs/math";
import websocket from "./libs/websocket";
import style from "./libs/style";
import pay from "@/uni_modules/kantboot/libs/pay";

export default {
    base64,
    dataChange,
    file,
    util,
    router,
    storage,
    event,
    request,
    image,
    date,
    global,
    i18n,
    userAccount,
    math,
    websocket,
    style,
    pay
}