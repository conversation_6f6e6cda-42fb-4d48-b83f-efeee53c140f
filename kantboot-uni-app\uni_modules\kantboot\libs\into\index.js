// 获取上一次加载的语言
import $i18n from "../i18n/index.js";
import $storage from "../storage/index.js";

// 判断是否加载
let checkLoad = (isSet = true) => {
    return new Promise(async (resolve, reject) => {
        setTimeout(() => {
            reject()
        }, 10000);
        let lastLoadingLanguage = $storage.get("lastLoadingLanguage");
        // 如果上一次加载的语言和当前语言一致
        if (lastLoadingLanguage === $i18n.getLanguageCode()) {
            // 获取上一次加载时间
            let lastLoadingTime = $storage.get("lastLoadingTime");
            // 如果小于两小时，则不加载
            if (lastLoadingTime !== null && new Date().getTime() - lastLoadingTime < 2 * 60 * 60 * 1000) {
                resolve();
            }
        }
        reject();
    })
}

let toSet = ()=>{
    $storage.set("lastLoadingLanguage", $i18n.getLanguageCode());
    $storage.set("lastLoadingTime", new Date().getTime());
}


export default {
    checkLoad,
    toSet
}