<template>
  <view>
    <kt-popup ref="ktPopup">
      <view :class="clazz.box">
        <view class="box-title">
          {{ $i18n.zhToGlobal("选择身高") }}</view>
        <view class="pop_class">
          <view
              class="class_item"
              v-for="(item, index) in heightArr"
              :key="index"
              :class="{
                'class_item-active': value === item.value
              }"
              @click="heightSelect(item)"
          >{{ item.value }}</view>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  props: {
    value: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      },
      heightArr: [
      ],
    };
  },
  created() {
    this.generateHeightArr();
  },
  methods: {
    // 生成weightArr
    generateHeightArr() {
      for (let i = 127; i <= 245; i++) {
        // let height = `4'2"(1.27m)`;
        // i代表厘米
        let feet = Math.floor(i / 30.48);
        let inches = Math.round((i % 30.48) / 2.54);
        let height = `${feet}'${inches}" (${parseInt(i / 100)}.${i % 100} m)`;
        this.heightArr.push({
          value: height,
        });
      }
    },

    open() {
      this.$refs.ktPopup.open();
    },
    heightSelect(item) {
      this.value = item.value;
      this.$refs.ktPopup.close();
      this.$emit('input', this.value);

    }
  },
};
</script>

<style lang="scss" scoped>
.box{
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  padding: 20rpx;
}
.box-title{
  font-size: 32rpx;
  color: #333333;
}
.pop_class {
  height: 600rpx;
  overflow: auto;
}
.class_item {
  padding: 30rpx 0 30rpx 30rpx;
  color: #999;
  font-size: 30rpx;
}
.class_item:active {
  background-color: #e5e5e5;
  color: white;
}

.class_item-active {
  background-color: #000000;
  color: #FFFFFF;
  font-weight: bold;
  border-radius: 10rpx;
}

.box-mode-device-pc{
  border-radius: 20rpx;
}
</style>
