<view hidden="{{!(show)}}" class="{{['data-v-56978ccd',clazz.box]}}"><view class="box-logo data-v-56978ccd"><image class="logo data-v-56978ccd" src="{{$root.g0}}" mode="aspectFit"></image></view><block wx:if="{{wechatLoginMethod==='loginByCode'}}"><kt-button class="button data-v-56978ccd vue-ref" bind:click="__e" vue-id="7c5a8dd9-1" data-ref="loginByCodeButton" data-event-opts="{{[['^click',[['loginByCode']]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><image class="button-icon data-v-56978ccd" src="{{$root.g1}}"></image>{{''+$root.g2+''}}<view class="button-into-tip data-v-56978ccd">{{''+"➠"+''}}</view></kt-button></block><block wx:if="{{wechatLoginMethod==='loginByPhone'}}"><view class="data-v-56978ccd"><kt-button data-custom-hidden="{{!(!agree)}}" class="button data-v-56978ccd vue-ref" bind:click="__e" vue-id="7c5a8dd9-2" data-ref="loginByPhoneButtonTips" data-event-opts="{{[['^click',[['tipAgree']]]]}}" bind:__l="__l" vue-slots="{{['default']}}">{{''+$root.g3+''}}<view class="button-into-tip data-v-56978ccd">{{''+"➠"+''}}</view></kt-button><kt-button data-custom-hidden="{{!(agree)}}" class="button data-v-56978ccd vue-ref" style="position:relative;" vue-id="7c5a8dd9-3" data-ref="loginByPhoneButton" bind:__l="__l" vue-slots="{{['default']}}"><view class="data-v-56978ccd"></view>{{''+$root.g4+''}}<view class="button-into-tip data-v-56978ccd">{{''+"➠"+''}}</view><button style="position:absolute;z-index:999;width:100%;height:100%;top:0;left:0;opacity:0;" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['loginByPhone',['$event']]]]]}}" bindgetphonenumber="__e" class="data-v-56978ccd"></button></kt-button></view></block><view style="height:20rpx;" class="data-v-56978ccd"></view><view style="text-align:center;" class="data-v-56978ccd"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="little-btn data-v-56978ccd" bindtap="__e">{{$root.g5}}</view></view></view>