<template>
  <view>
    <view
        style="margin-bottom: 40rpx"
        class="box-item"
        v-for="item in list">
      <view class="kt-text">{{$i18n.zhToGlobal("访问时间")}}{{":"}}{{$kt.date.format(item.gmtCreate,"yyyy-MM-dd hh:mm:ss")}}</view>
      <kt-user-info-card
      :user-info="item.userAccountOfVisitor"
      ></kt-user-info-card>
      <view style="height: 30rpx"></view>
    </view>
    <view
        v-if="!loading&&list.length===0">
      <view style="height: 100rpx"></view>
      <u-empty
          :text="$kt.i18n.zhToGlobal('暂无访问记录')"
      ></u-empty>
      <view style="height: 100rpx"></view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list:[],
      loading:false
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      this.$kt.request.post("/fp-community-web/userAccountVisit/getListSelf", {
      }).then(async (res) => {
        this.list = res.data;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.box-item{
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 20rpx;
  border: 1rpx solid #f0f0f0;
}

.kt-text{
  font-size: 24rpx;
  color: #999999;
  margin-top: 20rpx;
  text-align: right;
}
</style>
