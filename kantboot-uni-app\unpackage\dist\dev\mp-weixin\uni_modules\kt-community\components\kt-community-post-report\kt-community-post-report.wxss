@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.option-box.data-v-8cb61eaa {
  padding: 20rpx;
  border-radius: 10rpx;
  text-align: center;
}
.option-box .option-item.data-v-8cb61eaa {
  display: inline-block;
  margin: 1%;
  width: 30%;
  text-align: center;
  font-size: 30rpx;
  padding: 15rpx;
  color: #333333;
  border: 1px solid #E5E5E5;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  cursor: pointer;
}
.option-box .option-item-active.data-v-8cb61eaa {
  background-color: #F0F0F0;
  border: 1px solid #f0f0f0;
}
.textarea-box.data-v-8cb61eaa {
  border-radius: 10rpx;
}
.textarea-box .textarea.data-v-8cb61eaa {
  width: 100%;
  height: 200rpx;
  border: 1px solid #E5E5E5;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 30rpx;
  color: #333333;
  line-height: 50rpx;
  box-sizing: border-box;
}
