import $kt from "@/uni_modules/kantboot"

let result = {};

/**
 * 加载兴趣爱好
 */
result.loadInterests = async () => {
    let interest = [];
    let interestMap = {};
    // /project-make-friends-web/interest/getAll
    await $kt.request.post("/project-make-friends-web/interest/getAll").then(res => {
        interest = res.data;
        for (let i = 0; i < interest.length; i++) {
            let interestItem = interest[i];
            interestMap[interestItem.id] = interestItem;
        }
        // 放入缓存中
        $kt.storage.set("projectMakeFriendsInterest:all", interest);
        // 将map放入缓存中
        $kt.storage.set("projectMakeFriendsInterest:map", interestMap);
    }).catch(err => {
    });

    // 获取i18n
    let i18nMap = {};
    await $kt.i18n.getI18n({
        topKey: "ProjectMakeFriendsInterest",
        bottomKey: "name"
    }).then(res => {
        i18nMap = res.map;
        console.log(JSON.stringify(i18nMap),"i18nMap");
        console.log(res,"获取i18n");
        for (let i = 0; i < interest.length; i++) {
            let interestItem = interest[i];
            let interestName = i18nMap[interestItem.id+""].content;
            if (interestName !== undefined) {
                interestItem.name = interestName;
            }
            interestMap[interestItem.id] = interestItem;
        }
        // 放入缓存中
        $kt.storage.set("projectMakeFriendsInterest:all", interest);
        // 将map放入缓存中
        $kt.storage.set("projectMakeFriendsInterest:map", interestMap);
    }).catch(err => {
    });
}

/**
 * 获取所有兴趣爱好
 */
result.getInterests = () => {
    let interest = $kt.storage.get("projectMakeFriendsInterest:all");
    if (interest === null) {
        interest = [];
    }
    return interest;
}

/**
 * 根据id获取兴趣爱好名称
 */
result.getInterestNameById = (id) => {
    let interestMap = $kt.storage.get("projectMakeFriendsInterest:map");
    if (interestMap === null) {
        interestMap = {};
    }
    let interest = interestMap[id+""];
    if (interest === undefined) {
        return "";
    }
    // return interest.name;
    // 首字母大写
    return $kt.util.firstLetterUpper(interest.name);
}

/**
 * 获取所有个人特点
 */
result.getCharacteristics = () => {
    let personalCharacteristics = $kt.storage.get("projectMakeFriendsCharacteristic:all");
    if (personalCharacteristics === null) {
        personalCharacteristics = [];
    }
    return personalCharacteristics;
}

result.loadCharacteristics = async () => {
    let personalCharacteristics = [];
    let personalCharacteristicsMap = {};
    await $kt.request.post("/project-make-friends-web/characteristic/getAll").then(res => {
        personalCharacteristics = res.data;
        for (let i = 0; i < personalCharacteristics.length; i++) {
            let personalCharacteristicsItem = personalCharacteristics[i];
            personalCharacteristicsMap[personalCharacteristicsItem.id+""] = personalCharacteristicsItem;
        }
        // 放入缓存中
        $kt.storage.set("projectMakeFriendsCharacteristic:all", personalCharacteristics);
        // 将map放入缓存中
        $kt.storage.set("projectMakeFriendsCharacteristic:map", personalCharacteristicsMap);
    }).catch(err => {
    });
    await $kt.i18n.getI18n({
        topKey: "ProjectMakeFriendsCharacteristic",
        bottomKey: "name"
    }).then(res => {
        let i18nMap = res.map;
        for (let i = 0; i < personalCharacteristics.length; i++) {
            let personalCharacteristicsItem = personalCharacteristics[i];
            let personalCharacteristicsName = i18nMap[personalCharacteristicsItem.id+""];
            if (personalCharacteristicsName !== undefined) {
                personalCharacteristicsItem.name = personalCharacteristicsName.content;
            }
            personalCharacteristicsMap[personalCharacteristicsItem.id] = personalCharacteristicsItem;
            $kt.storage.set("projectMakeFriendsCharacteristic:all", personalCharacteristics);
            $kt.storage.set("projectMakeFriendsCharacteristic:map", personalCharacteristicsMap);
        }
    })

}

result.getCharacteristicNameById = (id) => {
    let map = $kt.storage.get("projectMakeFriendsCharacteristic:map");
    if (map === null) {
        map = {};
    }
    let item = map[id+""];
    if (item === undefined) {
        return "";
    }
    // return item.name;
    // 首字母大写
    return $kt.util.firstLetterUpper(item.name);
}

/**
 * 加载工作类型
 */
result.loadJobTypeAll = () => {
    let jobTypeAll = [];
    let jobTypeMap = {};
    // /project-make-friends-web/jobType/getAll
    return $kt.request.post("/project-make-friends-web/jobType/getAll").then(res => {
        jobTypeAll = res.data;
        for (let i = 0; i < jobTypeAll.length; i++) {
            let jobTypeItem = jobTypeAll[i];
            jobTypeMap[jobTypeItem.id+""] = jobTypeItem;
        }
        // 放入缓存中
        $kt.storage.set("projectMakeFriendsJobType:all", jobTypeAll);
        // 将map放入缓存中
        $kt.storage.set("projectMakeFriendsJobType:map", jobTypeMap);
    }).catch(err => {
    });
}

/**
 * 根据id获取工作类型名称
 */
result.getJobTypeNameById = (id) => {
    let jobTypeMap = $kt.storage.get("projectMakeFriendsJobType:map");
    if (jobTypeMap === null) {
        jobTypeMap = {};
    }
    let jobType = jobTypeMap[id+""]
    if (jobType === undefined) {
        return "";
    }
    return jobType.name;
}



export default result;