<template>
  <view
      :class="clazz.container"
  >
    <view
        v-if="!userAccount.id"
        class="user-info-card">
      <view style="display: inline-block;width: 130rpx;">
        <u-loading-icon mode="circle"
                        size="120rpx"
        ></u-loading-icon>
      </view>
      <view style="vertical-align: top;display: inline-block;width: calc(100% - 150rpx);margin-left: 20rpx;">
        <view>
          <view class="user-info-card-nickname">{{ $i18n.zhToGlobal("获取昵称中") }}</view>
        </view>
      </view>
    </view>
    <view
        v-if="userAccount.id"
        class="user-info-card">
      <view style="display: inline-block;width: 130rpx;">
        <kt-avatar
            v-if="userAccount.fileIdOfAvatar"
            size="120rpx"
            :src="$kt.file.visit(userAccount.fileIdOfAvatar)">
        </kt-avatar>
        <kt-avatar
            border="none"
            v-else-if="!userAccount.fileIdOfAvatar&&userAccount.srcOfAvatar"
            size="120rpx"
            :src="userAccount.srcOfAvatar">
        </kt-avatar>
        <kt-avatar
            v-else
            border="none"
            size="120rpx"
            :src="$kt.file.byPath('/image/logo.png')"
        ></kt-avatar>

      </view>
      <view style="vertical-align: top;display: inline-block;width: calc(100% - 150rpx);margin-left: 20rpx;">
        <view>
          <view class="user-info-card-nickname">
            <text
            v-if="userAccount.remark"
            >
              {{ userAccount.remark }}{{" - "}}
            </text>
            <text>{{ userAccount.nickname || $i18n.zhToGlobal("无昵称") }}</text>
          </view>

          <view
              v-if="userAccount.gmtBirthday&&!userAccount.isAgeSecret&&showAge"
              class="user-info-card-age">
            <view v-if="$i18n.getLanguageCode()==='zh_CN'||$i18n.getLanguageCode()==='zh_HK'||$i18n.getLanguageCode()==='zh_MO'||$i18n.getLanguageCode()==='zh_TW'">
              {{ $kt.date.getAge(userAccount.gmtBirthday) }}{{ $i18n.zhToGlobal("岁") }}
            </view>
            <view v-else>
              {{$i18n.zhToGlobal("年龄")}}{{":"}}{{ $kt.date.getAge(userAccount.gmtBirthday) }}
            </view>
          </view>

          <view
              v-if="!userAccount.isGenderSecret"
              class="user-info-card-gender">
            <image
                v-if="userAccount.genderCode === 'male'"
                class="user-info-card-gender-icon"
                :src="$kt.file.byPath('icon/male.svg')"></image>
            <image
                v-else
                class="user-info-card-gender-icon"
                :src="$kt.file.byPath('icon/female.svg')"></image>
          </view>

        </view>

        <view style="margin-top: 20rpx;">
          <!-- 个性签名 -->
          <view class="user-info-card-intro"
                style="width: calc(100% - 50rpx);"
          >{{ userAccount.introduction || $i18n.zhToGlobal("无问候语") }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props:{
    userInfo:{
      type: Object,
      default: null
    },
    userAccountId:{
      type: [String, Number],
      default: null
    },
      requestRemark: {
        type: Boolean,
        default: false
      },
    /**
     * 是否显示年龄
     */
    showAge: {
      type: Boolean,
      default: true
    }
  },
  data(){
    return {
      userAccount: null,
      clazz:{
        container: this.$kt.style.toggleClass("container"),
      }
    }
  },
  watch:{
    userAccountId(newVal){
      if(newVal){
        this.getById(newVal);
      }
    },
    userInfo:{
      handler(val) {
        this.userAccount = val;
        if(this.requestRemark && this.userAccount && this.userAccount.id){
          this.getRemark();
        }
      },
      immediate: true,
      deep: true
    }
  },
  created(){
    if(this.userAccountId){
      this.getById(this.userAccountId);
    }
    if(this.requestRemark){
      this.$kt.event.on("remarkChange",()=>{
        this.getRemark();
      });
    }
  },
  methods:{
    getRemark() {
      this.$request.post("/user-interrelation-web/interrelationRemark/getRemark", {
        data: {
          userAccountIdOfRemark: this.userAccount.id
        }
      }).then(res => {
        this.userAccount.remark = res.data.remark;
        try{
          // 重新渲染
          this.$forceUpdate();
        }catch (e) {
          
        }
      }).catch(err => {
      });
    },
    getById(userAccountId){
      this.$kt.userAccount.getById(userAccountId).then(res=>{
        this.userInfo = res;
      }).catch(err=>{

      })
    }
  }
}
</script>
<style lang="scss" scoped>
.user-info-card{
  min-width: 500rpx;
  width: 100%;
  text-align: left;
}
.user-info-card-nickname{
  display: inline-block;
  font-weight: bold;
  font-size: 36rpx;
  letter-spacing: 1px;
  // 不换行
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 200rpx);
}
.user-info-card-age{
  display: inline-block;
  font-size: 28rpx;
  // 绿色
  color: #07c160;
  // 透明绿
  background-color: rgba(7, 193, 96, 0.1);
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
}
.user-info-card-gender{
  display: inline-block;
  margin-left: 20rpx;
  vertical-align: top;
  margin-top: 5rpx;
  width: 30rpx;
  height: 30rpx;
  .user-info-card-gender-icon{
    width: 100%;
    height: 100%;
  }
}
.user-info-card-intro{
  //font-size: 28rpx;
  font-size: 30rpx;
  color: #999;
  letter-spacing: 1px;
  // 限制2行
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  max-height: 80rpx;
}
.container-mode-color-scheme-dark{
  .user-info-card{
    .user-info-card-nickname{
      color: #ccc;
    }
    .user-info-card-intro{
      color: #AAAAAA;
    }
  }
}
</style>