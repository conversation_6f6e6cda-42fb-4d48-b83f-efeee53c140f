<template>
  <view>
    <view
        class="header"
        id="headerInMemberTransfer">
      <kt-nav-bar :title="$i18n.zhToGlobal('会员转移')" :showBack="true"></kt-nav-bar>
    </view>

    <view class="content-box">
      <!-- 轮播图 -->
      <view class="carousel-box" v-if="swiperList.length > 0">
        <swiper class="swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500"
          :circular="true">
          <swiper-item v-for="(item, index) in swiperList" :key="index" @click="onCarouselClick(item)">
            <view class="swiper-item">
              <image :src="$kt.file.visit(item.fileIdOfImage)" mode="aspectFill" class="swiper-image"></image>
              <view class="swiper-title" v-if="item.title">{{ item.title }}</view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 搜索框 -->
      <view class="box-search">
        <view class="search-box">
          <u-input :placeholder="$i18n.zhToGlobal('输入ID或手机号搜索')" @confirm="searchUsers" v-model="searchText">
            <template slot="suffix">
              <button @tap="searchUsers" class="search-btn" :disabled="!searchText.trim()">搜索</button>
            </template>
          </u-input>
        </view>
      </view>

      <!-- 转移记录分组列表 -->
      <view class="box">
        <view v-if="groupList.length === 0 && !isLoading" class="empty-tip">
          {{ $i18n.zhToGlobal('暂无转移记录') }}
        </view>

        <!-- 分组卡片列表 -->
        <view class="box-item" v-for="item in groupList" :key="item.groupUserAccountId" @click="selectGroup(item)">
          <view class="box-item-card">
            <view class="group-card-header">
              <kt-user-info-card :user-info="getUserInfoFromGroup(item)"></kt-user-info-card>
              <view class="group-card-badge">
                <text class="badge-text">{{ item.transferCount }}{{ $i18n.zhToGlobal('条记录') }}</text>
              </view>
            </view>
            <view class="box-item-info">
              <view class="box-item-id">ID: {{ item.userAccountId }}</view>
              <view class="box-item-phone" v-if="item.phone">{{ item.phoneAreaCode }} {{ item.phone }}</view>
              <view class="box-item-transfer-time" v-if="item.latestTransferTime">
                {{ $i18n.zhToGlobal('最新转移时间') }}: {{ formatDate(item.latestTransferTime) }}
              </view>
            </view>
            <!-- 箭头图标 -->
            <view class="box-item-arrow">
              <text class="arrow-icon">></text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <u-loading-icon v-if="isLoading" mode="circle" size="50rpx"></u-loading-icon>
        <view v-if="isBottom && groupList.length > 0" style="text-align: center; color: #999999; font-size: 24rpx;">
          {{ $kt.i18n.zhToGlobal('没有更多了') }}
        </view>
        <view style="height: 50rpx;"></view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-btn-box">
      <view class="btn-item add-btn" @click="toAddTransfer">
        <text>{{ $i18n.zhToGlobal('新增转移') }}</text>
      </view>
      <view class="btn-item who-helped-btn" @click="toWhoHelped">
        <text>{{ $i18n.zhToGlobal('谁帮了我') }}</text>
      </view>
    </view>

    <!-- 转移同意弹窗 -->
    <project-make-friends-transfer-consent-popup ref="transferConsentPopup" @processed="onTransferProcessed">
    </project-make-friends-transfer-consent-popup>
  </view>
</template>

<script>
import ProjectMakeFriendsTransferConsentPopup from '@/uni_modules/project-make-friends/components/project-make-friends-transfer-popup/project-make-friends-transfer-consent-popup.vue'

export default {
  components: {
    ProjectMakeFriendsTransferConsentPopup
  },
  data() {
    return {
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      },
      searchText: '',
      groupList: [], // 分组列表数据
      pageNum: 1,
      pageSize: 10,
      isLoading: false,
      isBottom: false,
      // 轮播图数据
      swiperList: [
      ]
    }
  },
  onLoad() {
    this.getInitList();
    this.checkPendingTransfer();
    this.getCarouselData();
  },
  onReachBottom() {
    this.loadMore();
  },
  methods: {
    // 获取分组列表数据（合并初始加载和加载更多）
    getGroupListData(isLoadMore = false) {
      if (this.isLoading || (isLoadMore && this.isBottom)) return;

      this.isLoading = true;

      if (!isLoadMore) {
        this.pageNum = 1;
        this.isBottom = false;
      } else {
        this.pageNum++;
      }

      this.$request.post("/project-make-friends-web/userTransferRecord/getTransferGroupByNewInviter", {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        searchText: this.searchText
      }).then(res => {
        if (res.state === 2000) {
          const newData = res.data || [];

          if (!isLoadMore) {
            this.groupList = newData;
          } else {
            this.groupList = [...this.groupList, ...newData];
          }

          if (newData.length < this.pageSize) {
            this.isBottom = true;
          }
        } else {
          uni.showToast({
            title: res.msg || '获取分组列表失败',
            icon: 'none'
          });
        }
        this.isLoading = false;
      }).catch(err => {
        console.error(err);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
        this.isLoading = false;
      });
    },

    // 初始加载分组列表
    getInitList() {
      this.getGroupListData(false);
    },

    // 搜索用户
    searchUsers() {
      this.getGroupListData(false);
    },

    // 选择分组 - 修复参数传递问题
    selectGroup(group) {
      // 修复：使用 groupUserAccountId 作为参数传递
      uni.navigateTo({
        url: `/pages/project-make-friends-pages/member-transfer/transfer-detail-list?newInviterUserAccountId=${group.groupUserAccountId}&nickname=${encodeURIComponent(group.nickname || '')}`
      });
    },

    // 从分组数据构建用户信息对象
    getUserInfoFromGroup(group) {
      return {
        id: group.userAccountId,
        userAccountId: group.userAccountId,
        nickname: group.nickname,
        fileIdOfAvatar: group.fileIdOfAvatar,
        phone: group.phone,
        phoneAreaCode: group.phoneAreaCode
      };
    },

    // 新增转移
    toAddTransfer() {
      uni.navigateTo({
        url: '/pages/project-make-friends-pages/member-transfer/member-transfer-user'
      });
    },

    // 谁帮了我
    toWhoHelped() {
      uni.navigateTo({
        url: '/pages/project-make-friends-pages/member-transfer/who-helped'
      });
    },

    // 检查待同意的转移申请
    checkPendingTransfer() {
      this.$request.post("/project-make-friends-web/userTransferConsent/getPendingTransfer", {
        data: {}
      }).then(res => {
        if (res.state === 2000 && res.data) {
          // 有待同意的转移申请，显示弹窗
          this.$refs.transferConsentPopup.open(res.data);
        }
      }).catch(err => {
        console.error('获取待同意转移申请失败:', err);
      });
    },

    // 转移申请处理完成回调
    onTransferProcessed() {
      // 可以在这里刷新页面数据或显示提示
      this.getInitList();
    },

    // 获取轮播图数据
    getCarouselData() {
      this.$request.post("/fp-carousel-web/carousel/getByTypeCode", {
        data: {
          typeCode: "member" // 会员转移页面轮播图类型编码
        }
      }).then(res => {
        if (res.state === 2000 && res.data && res.data.length > 0) {
          this.swiperList = res.data.map(item => ({
            fileIdOfImage: item.fileIdOfImage,
            title: item.title,
            link: item.link,
            id: item.id
          }));
        }
      }).catch(err => {
        console.error('获取轮播图数据失败:', err);
        // 保持默认的轮播图数据，不影响页面正常显示
      });
    },

    // 轮播图点击事件
    onCarouselClick(item) {
      if (item.link) {
        // 如果有链接，可以跳转到相应页面
        if (item.link.startsWith('http')) {
          // 外部链接
          uni.navigateTo({
            url: `/pages/common/webview?url=${encodeURIComponent(item.link)}`
          });
        } else {
          // 内部页面跳转
          uni.navigateTo({
            url: item.link
          });
        }
      }
    },

    // 加载更多
    loadMore() {
      this.getGroupListData(true);
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '转移中',
        1: '转移成功',
        2: '转移失败',
        3: '已退回',
        4: '已弃掉'
      };
      return this.$i18n.zhToGlobal(statusMap[status] || '未知状态');
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        0: 'status-transferring',
        1: 'status-success',
        2: 'status-failed',
        3: 'status-rollback',
        4: 'status-abandoned'
      };
      return classMap[status] || 'status-unknown';
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.back {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: #F0F0F0;
  top: 0;
  left: 0;
  z-index: -1;
  overflow: hidden;
}

.header-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}

.content-box {
  padding-bottom: 120rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 轮播图样式 */
.swiper-box {
  padding: 20rpx;

  .swiper {
    height: 300rpx;
    border-radius: 12rpx;
    overflow: hidden;

    .swiper-image {
      width: 100%;
      height: 100%;
    }
  }
}

/* 搜索框样式 - 更新为与member-transfer-user.vue一致 */
.box-search {
  width: calc(100% - 40rpx);
  background-color: #F0F0F0;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin: 20rpx auto;
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  padding: 10rpx;
}

/* 搜索按钮样式 */
.search-btn {
  background-color: #000000;
  height: 30px;
  line-height: 30px;
  color: #ffffff;

  &[disabled] {
    background-color: #cccccc;
    color: #ffffff;
    opacity: 0.6;
  }
}

/* 轮播图样式 */
.carousel-box {
  width: calc(100% - 40rpx);
  margin: 0 auto 30rpx auto;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .swiper {
    width: 100%;
    height: 300rpx;

    .swiper-item {
      position: relative;
      width: 100%;
      height: 100%;

      .swiper-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .swiper-title {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
        color: #ffffff;
        padding: 20rpx;
        font-size: 28rpx;
        font-weight: 500;
      }
    }
  }
}

/* 主容器样式 - 参考member-transfer-user.vue */
.box {
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;

  .box-item {
    margin-bottom: 30rpx;

    .box-item-card {
      position: relative;
      padding: 20rpx;
      border-radius: 12rpx;
      background-color: #ffffff;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      border: 1rpx solid #eaeaea;

      .group-card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16rpx;

        .group-card-badge {
          background-color: #007AFF;
          border-radius: 20rpx;
          padding: 8rpx 16rpx;
          margin-left: 20rpx;
          flex-shrink: 0;

          .badge-text {
            color: #FFFFFF;
            font-size: 24rpx;
            font-weight: bold;
          }
        }
      }

      .box-item-info {
        font-size: 28rpx;
        color: #999999;
        margin-top: 16rpx;

        .box-item-id,
        .box-item-phone,
        .box-item-transfer-time {
          margin-bottom: 8rpx;
        }
      }

      .box-item-arrow {
        position: absolute;
        right: 20rpx;
        top: 50%;
        transform: translateY(-50%);

        .arrow-icon {
          font-size: 32rpx;
          color: #CCCCCC;
          font-weight: bold;
        }
      }

      .box-item-status {
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: bold;

        &.status-transferring {
          background-color: #FFF3CD;
          color: #856404;
        }

        &.status-success {
          background-color: #D4EDDA;
          color: #155724;
        }

        &.status-failed {
          background-color: #F8D7DA;
          color: #721C24;
        }

        &.status-rollback {
          background-color: #D1ECF1;
          color: #0C5460;
        }

        &.status-abandoned {
          background-color: #E2E3E5;
          color: #383D41;
        }

        &.status-unknown {
          background-color: #F8F9FA;
          color: #6C757D;
        }
      }
    }
  }
}

.box-item:active {
  opacity: .8;
}

.box-item-card:active {
  background-color: #f9f9f9;
}

/* 提示语样式 */
.empty-tip {
  text-align: center;
  color: #999999;
  font-size: 28rpx;
  margin: 100rpx 0;
}

.bottom-btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120rpx;
  display: flex;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 999;
  overflow: hidden;

  .btn-item {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;

    &:active {
      opacity: 0.8;
    }
  }

  .add-btn {
    background-color: #000000;
    color: #FFFFFF;
  }

  .who-helped-btn {
    background-color: #FFFFFF;
    color: #000000;
    border-top: 1rpx solid #EEEEEE;
  }
}

// 暗黑模式适配
.container-mode-color-scheme-dark {
  .back {
    background-color: #191919;
  }

  .search-box {
    background-color: #1F1F1F;
  }

  .carousel-box {
    box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.05);
  }

  .box {
    .box-item {
      .box-item-card {
        background-color: #1F1F1F;
        border: 1rpx solid #333333;

        &:active {
          background-color: #2A2A2A;
        }
      }
    }
  }

  .bottom-btn-box {
    background-color: #1F1F1F;
    box-shadow: 0 -2rpx 10rpx rgba(255, 255, 255, 0.05);

    .who-helped-btn {
      background-color: #1F1F1F;
      color: #AAAAAA;
      border-top: 1rpx solid #333333;
    }
  }
}

// PC端适配
.container-mode-device-pc {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;

  .header-box {
    width: calc(100% - 240px);
    left: 240px;
  }

  .content-box {
    margin-left: 240px;
    width: calc(100% - 240px);

    .box {
      max-width: 800rpx;
      margin: 0 auto;
    }
  }

  .bottom-btn-box {
    width: calc(100% - 240px);
    left: 240px;
  }
}
</style>