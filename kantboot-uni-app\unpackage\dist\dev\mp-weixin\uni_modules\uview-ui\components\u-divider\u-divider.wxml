<view data-event-opts="{{[['tap',[['click',['$event']]]]]}}" class="u-divider data-v-5257fd26" style="{{$root.s0}}" bindtap="__e"><u-line vue-id="03acad39-1" color="{{lineColor}}" customStyle="{{leftLineStyle}}" hairline="{{hairline}}" dashed="{{dashed}}" class="data-v-5257fd26" bind:__l="__l"></u-line><block wx:if="{{dot}}"><text class="u-divider__dot data-v-5257fd26">●</text></block><block wx:else><block wx:if="{{text}}"><text class="u-divider__text data-v-5257fd26" style="{{$root.s1}}">{{text}}</text></block></block><u-line vue-id="03acad39-2" color="{{lineColor}}" customStyle="{{rightLineStyle}}" hairline="{{hairline}}" dashed="{{dashed}}" class="data-v-5257fd26" bind:__l="__l"></u-line></view>