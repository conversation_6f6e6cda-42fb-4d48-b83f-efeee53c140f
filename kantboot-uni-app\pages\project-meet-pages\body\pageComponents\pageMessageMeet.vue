<script>


import ProjectMeetGiftCard from "@/pages/project-make-friends-pages/project-meet-gift-card/project-meet-gift-card.vue";

export default {
  name: "pageChat",
  components: {ProjectMeetGiftCard},
  data() {
    return {
      noticeText:"",
      noticeTextShow: true,
      // 组件高度
      height: 0,
      // 组件高度
      navBarHeight: 0,
      // 是否加载完成
      isLoad: false,
      scrollTop: 0,
      list: [],
      clazz: {
        container: this.$kt.style.toggleClass("container"),
        bg: this.$kt.style.toggleClass("bg")
      },
      dialogIdSelected:"",
      footerHeight: 0,
      dialog:{

      },
      userAccount:{
        genderCode:""
      },
      self: {
        isSubAccount: false
      },
      isCustomerService:false,
      // 目标翻译语言
      targetLanguage: {
        code:""
      },
    };
  },
  mounted() {
    this.self = this.$kt.userAccount.getSelf();
    // 获取导航栏高度
    // this.navBarHeight = this.$refs.navBar.getHeight();
    // 获取#navBarInMessage的高度
    this.$kt.event.on('changeTabbar', () => {
      this.noticeTextShow = false;
      setTimeout(() => {
        try {
          this.noticeTextShow=true;
          uni.createSelectorQuery()
              .in(this)
              .select("#navBarInMessage")
              .boundingClientRect((res) => {
                this.navBarHeight = res.height;
                console.log(res, "pageMessage");
              }).exec();
        } catch (e) {
          console.log(e, "pageMessage 错误的高度获取");
        }

      }, 50);
    });


    this.$kt.event.on("translate:balanceNotEnough",()=>{
      this.$refs.projectMeetPointsPopup.open(this.self.id);
    });


  },
  created() {
    // 监听登录成功事件
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.getInitList();
    });
    this.$kt.event.on("projectMeetChatDialogMessageSend", (dialogId)=>{
      this.cardClick({
        id: dialogId
      });
    });
    this.getNotify();
  },
  methods: {
    selectLanguage(res){
      this.targetLanguage = res;
      this.$refs.ktLanguageSelectPopup.close();
    },
    toUnTranslate(){
      this.targetLanguage = {
        code:""
      };
      this.$refs.ktLanguageSelectPopup.close();
    },
    getIsCustomerService(){
      this.$request.post("/functional-chat-web/dialog/isCustomerService",{
        data:{
          userAccountId:this.dialog.userAccountId
        }
      }).then((res)=>{
        this.isCustomerService = res.data;
      }).catch((res)=>{
        this.isCustomerService = false;
      })
    },
    getNotify(){
      this.$request.post("/project-meet-web/setting/getNotify").then(res => {
        this.noticeText = res.data;
      });
    },
    userCardClick(userAccount){
      if (this.$kt.style.detectDeviceType()==='pc') {
        this.$refs.usPopup.open(userAccount.id);
        // this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info");
      } else {
        this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info?userAccountId=" + userAccount.id);
      }
    },
    openGoldPopup(){
      this.$refs.projectMeetGoldTransferPopup.open(this.userAccount.id);
    },
    change() {
      // 获取navBarPostDetail的高度
      this.$nextTick(() => {
        uni.createSelectorQuery()
            .in(this)
            .select('#navBarInMessage')
            .boundingClientRect((res) => {
              this.navBarHeight = res.height;
              console.log(res, "pageMessage");
            }).exec();

        uni.createSelectorQuery()
            .in(this)
            .select("#projectMeetPageMessageFooter")
            .boundingClientRect((res) => {
              this.footerHeight = res.height;
              console.log(res, "pageMessage");
            }).exec();
      });


      setTimeout(()=>{
        // 获取navBarPostDetail的高度
        this.$nextTick(() => {
          uni.createSelectorQuery()
              .in(this)
              .select('#navBarInMessage')
              .boundingClientRect((res) => {
                this.navBarHeight = res.height;
                console.log(res, "pageMessage");
              }).exec();

          uni.createSelectorQuery()
              .in(this)
              .select("#projectMeetPageMessageFooter")
              .boundingClientRect((res) => {
                this.footerHeight = res.height;
                console.log(res, "pageMessage");
              }).exec();
        });
      },100);


      setTimeout(()=>{
        // 获取navBarPostDetail的高度
        this.$nextTick(() => {
          uni.createSelectorQuery()
              .in(this)
              .select('#navBarInMessage')
              .boundingClientRect((res) => {
                this.navBarHeight = res.height;
                console.log(res, "pageMessage");
              }).exec();

          uni.createSelectorQuery()
              .in(this)
              .select("#projectMeetPageMessageFooter")
              .boundingClientRect((res) => {
                this.footerHeight = res.height;
                console.log(res, "pageMessage");
              }).exec();
        });
      },500);

      setTimeout(()=>{
        // 获取navBarPostDetail的高度
        this.$nextTick(() => {
          uni.createSelectorQuery()
              .in(this)
              .select('#navBarInMessage')
              .boundingClientRect((res) => {
                this.navBarHeight = res.height;
                console.log(res, "pageMessage");
              }).exec();

          uni.createSelectorQuery()
              .in(this)
              .select("#projectMeetPageMessageFooter")
              .boundingClientRect((res) => {
                this.footerHeight = res.height;
                console.log(res, "pageMessage");
              }).exec();
        });
      },1000);


    },
    chooseGift() {
      this.$refs.projectMeetGiftPopup.open(this.userAccount.id);
    },
    init(){
      // projectMeetPageMessageFooter
      uni.createSelectorQuery()
          .in(this)
          .select("#projectMeetPageMessageFooter")
          .boundingClientRect((res) => {
            this.footerHeight = res.height;
            console.log(res, "pageMessage");
          }).exec();
    },
    send(param){
      try{
        let content = param.content;
        // 去除所有空格
        content = content.replace(/\n/g, "");
        param.content = content;
      }catch (e) {

      }
      if(this.targetLanguage.code){
        param.targetLanguageCode = this.targetLanguage.code;
      }

      this.$refs.sendInput.toLoading();
      // /fp-community-web/post/push
      this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf",{
        data:{
          items:[param],
          dialogId:this.dialogIdSelected
        }
      }).then((res)=>{
        this.$refs.sendInput.clear();
        this.$refs.sendInput.toNone();
      }).catch((res)=>{
        if(res.stateCode === 'balanceNotEnough'){
          setTimeout(()=>{
            this.$refs.projectMeetPointsPopup.open(this.self.id);
          },1000);

        }
        if(res.stateCode === 'onlyVipCanSendImage'||
            res.stateCode === 'onlyVipCanSendVideo'
        ){
          setTimeout(()=>{
            this.$refs.projectMeetVipPopup.open(this.self.id);
          },1000);
        }
        if(res.stateCode === 'goldNotEnough'){
          setTimeout(()=>{
            this.$refs.projectMeetGoldPopup.open(this.self.id);
          },1000);
        }
        this.$refs.sendInput.toNone();
        uni.showToast({
          title: res.errMsg,
          icon:"none"
        });
      })
    },
    cardClick(item) {
      if(this.$kt.style.detectDeviceType()==='pc'){
        this.dialogIdSelected = item.id;
        this.$nextTick(()=>{
          this.init();
        })
        return;
      }
      console.log(JSON.stringify(item)+"item.id")
      this.$kt.router.navTo('/pages/project-meet-pages/chat-dialog/chat-dialog?dialogId=' + item.id);
    },
    dialogLoad(dialog) {
      this.dialog = dialog;
      this.$kt.userAccount.getById(dialog.userAccountId).then((userAccount) => {
        this.userAccount = userAccount;
      });
      this.getIsCustomerService();
    },
  }
}
</script>

<template>
  <view :class="clazz.container">
    <view :class="clazz.bg">
      <image
          v-if="$kt.style.detectDeviceType()==='mobile'"
          class="bg-image"
          mode="aspectFill"
          :src="$kt.file.byPath('meet/bg.png')"
      ></image>
      <image
          v-if="$kt.style.detectDeviceType()==='mobile'"
          class="bg-image-2"
          mode="aspectFill"
          :src="$kt.file.byPath('meet/bg.png')"
      ></image>
      <image
          v-if="$kt.style.detectDeviceType()==='mobile'"
          class="bg-image-3"
          mode="aspectFill"
          :src="$kt.file.byPath('meet/bg.png')"
      ></image>

      <view class="bg-bg"></view>

    </view>
    <view id="navBarInMessage"
          class="box-header"
    >
      <kt-nav-bar
          :is-has-i18n="$kt.style.detectDeviceType()==='mobile'"
          ref="navBar"
          :background-color="'rgba(255,255,255,.3)'"
          :icon="$kt.file.byPath('tabbar/message-selected.svg')"
          :title="$i18n.zhToGlobal('消息')"></kt-nav-bar>
    </view>

    <view class="kt-chat-list-panel-box">
      <view
          style="padding: 0 20rpx">
        <u-notice-bar
            v-if="noticeTextShow&&noticeText"
            @close="noticeText=''"
            :icon="'-'"
            :text="noticeText"
            mode="closable"
            fontSize="28rpx"
        ></u-notice-bar>
      </view>
      <kt-chat-list-panel
          :selected="dialogIdSelected"
          :height="'calc(100vh - '+navBarHeight+'px - 20rpx - 50rpx - 20rpx)'"
          @cardClick="cardClick">
      </kt-chat-list-panel>
    </view>
    <view
        v-if="$kt.style.detectDeviceType()==='pc'"
        class="kt-chat-dialog-panel-box">
      <kt-chat-dialog-panel
          :has-read="self.isSubAccount"
          :copy="self.isSubAccount"
          @load="dialogLoad"
          @userCardClick="userCardClick"
          :height="'calc(100vh - '+navBarHeight+'px - '+footerHeight+'px - 20rpx - 50rpx - 20rpx)'"
          :dialog-id="dialogIdSelected">
        <template v-slot:messageItem="{item,message}">
          <view>
            <view v-if="item.type==='projectMeetGift'&&item.content">
              <project-meet-gift-card
                  :gift-id="item.content"
              ></project-meet-gift-card>
            </view>
            <view v-if="item.type==='projectMeetGoldTransfer'&&item.content">
              <project-meet-gold-transfer-card
                  :number="item.content"
              ></project-meet-gold-transfer-card>
            </view>
          </view>
        </template>
      </kt-chat-dialog-panel>
      <view
          v-if="dialog.id&&dialog.userAccountId&&!isCustomerService"
          class="charge-button"
          @click="$refs.projectMeetVgpPopup.open()"
      >

        <text v-if="userAccount.genderCode==='male'">{{$i18n.zhToGlobal('为他充值')}}</text>
        <text v-else>{{$i18n.zhToGlobal('为她充值')}}</text>
      </view>

      <view
          v-if="dialog.id&&dialog.userAccountId&&self.isSubAccount&&!isCustomerService"

      >
        <view
            @click="$refs.setRemarkPopup.open(dialog.userAccountId)"
            class="remark-button">{{$i18n.zhToGlobal('修改备注')}}</view>
      </view>

      <view id="projectMeetPageMessageFooter"
            class="footer">

        <kt-send-input
            :has-user-account="false"
            :has-voice="false"
            :has-gift="true&&!isCustomerService"
            @send="send"
            @change="change"
            @chooseGift="chooseGift()"
            mode="bar"
            ref="sendInput"
        >
          <template v-slot:operateExtra>
            <view style="display: inline-block" v-if="!isCustomerService&&!self.isSubAccount">
              <image
                  @click="openGoldPopup()"
                  class="input-icon"
                  :src="$kt.file.byPath('projectMeet/icon/gold.png')"
              ></image>
            </view>
            <view style="display: inline-block;margin-left: 30rpx;vertical-align: top;" v-if="!isCustomerService">
              <view
              class="kt-checkbox"
              @click="$refs.ktLanguageSelectPopup.open()"
              >
                <image
                :src="$kt.file.byPath('kantboot/icon/language.svg')"
                class="input-language-icon"
                ></image>
                <text
                v-if="!targetLanguage.code"
                >{{$i18n.zhToGlobal("不翻译")}}</text>
                <text
                v-else
                >
                {{targetLanguage.i18nName}}
                </text>
              </view>
            </view>

          </template>
        </kt-send-input>

      </view>
    </view>

<!--    <project-meet-gold-popup-->
<!--        v-if="dialog.id&&dialog.userAccountId"-->
<!--        :userAccountId="dialog.userAccountId"-->
<!--        ref="projectMeetGoldPopup"-->
<!--    ></project-meet-gold-popup>-->

    <kt-language-select-popup
        ref="ktLanguageSelectPopup"
        :reset="false"
        @select="selectLanguage"
    >
      <template
      v-slot:topLeft
      >
        <view
            @click="toUnTranslate()"
            class="top-left-btn">{{$i18n.enToGlobal("Cancel translation")}}</view>
      </template>
    </kt-language-select-popup>

    <project-meet-vgp-popup
    v-if="dialog.id&&dialog.userAccountId"
        :userAccountId="dialog.userAccountId"
        ref="projectMeetVgpPopup"
    ></project-meet-vgp-popup>

    <project-meet-gift-popup
        ref="projectMeetGiftPopup"
    ></project-meet-gift-popup>

    <project-meet-gold-popup
    ref="projectMeetGoldPopup"
    ></project-meet-gold-popup>


    <project-meet-points-popup
        :hasSelfNumber="false"
        ref="projectMeetPointsPopup"
        :user-account-id="self.id"
    ></project-meet-points-popup>

    <project-meet-vip-popup
        ref="projectMeetVipPopup"
    ></project-meet-vip-popup>

    <project-meet-gold-transfer-popup
    ref="projectMeetGoldTransferPopup"
    ></project-meet-gold-transfer-popup>

    <project-meet-user-info-popup
        ref="usPopup"
    ></project-meet-user-info-popup>
    <kt-set-remark-popup
        ref="setRemarkPopup"
    ></kt-set-remark-popup>

    <kt-no-login
        ref="noLogin"
    ></kt-no-login>

  </view>


</template>

<style scoped lang="scss">
.box {
  padding: 20rpx;
  box-sizing: border-box;

}

.bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  // 从白到黑的渐变，从上到下
  //background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}

.bg-mode-color-scheme-light {
  //background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
  background-color: #FFFFFF;
}

.bg-mode-color-scheme-dark {
  background-color: #191919;
}

.in-box {
  position: relative;
  height: 170rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .in-box-user-info-card {
    position: absolute;
    width: 100%;
    top: 15rpx;
    left: 0
  }
}

.container-mode-color-scheme-dark {
  .in-box {
    border-bottom: 1rpx solid #404a56;
  }
}

.container-mode-device-pc {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;

  .box {
    .in-box {
      position: relative;
      width: 100%;
      height: 160rpx;

      .in-box-user-info-card {
        position: absolute;
        width: 100%;
        top: 15rpx;
        left: -450rpx
      }
    }
  }

  .header-box {
    width: 100%;
  }
}
.container-mode-device-pc{
  .kt-chat-list-panel-box{
    position: relative;
    display: inline-block;
    width: 600rpx;
    background-color: #F3F3F3;
  }
  .kt-chat-dialog-panel-box{
    position: relative;
    display: inline-block;
    width: calc(100% - 600rpx);
    color: #000000;
    vertical-align: top;
    // 渐变，从白到#f0f0f0
    background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
    .charge-button{
      position: absolute;
      top:20rpx;
      right:0;
      z-index: 9999;
      color: #FFFFFF;
      font-size: 28rpx;
      background-color: #F6A496;
      padding: 10rpx 10rpx 10rpx 30rpx;
      border-radius: 30rpx 0 0 30rpx;
      cursor: pointer;
    }
    .remark-button{
      position: absolute;
      top:90rpx;
      right:0;
      z-index: 10000;
      color: #FFFFFF;
      font-size: 28rpx;
      background-color: rgba(0,0,0,.8);
      padding: 10rpx 10rpx 10rpx 30rpx;
      border-radius: 30rpx 0 0 30rpx;
    }
  }
}

.footer{
  background-color: #FFFFFF;
}

.bg {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  //background-color: #f9f9f9;
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
  opacity: .5;

  .bg-image {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    right: 0;
    z-index: -2;

  }

  .bg-image-2 {
    position: fixed;
    width: 100vw;
    height: 100vh;
    bottom: 25vh;
    right: 0;
    // 上下翻转
    transform: scaleY(-1);
    z-index: -2;

  }

  .bg-image-3 {
    position: fixed;
    width: 100vw;
    height: 100vh;
    bottom: 0;
    right: 0;
    z-index: -2;
    // 上下翻转
    transform: scaleY(-1);
  }

  .bg-bg {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    right: 0;
    z-index: -1;
    // 渐变，从上到下
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 100rpx, rgba(255, 255, 255, 1) 100%);
  }
}

.bg-mode-color-scheme-light {
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}

.bg-mode-color-scheme-dark {
  background: #191919;
}


.input-icon{
  width: 40rpx;
  height: 40rpx;
  margin-left: 30rpx;
  vertical-align: top;
  margin-top: -7rpx;
  cursor: pointer;
}

.input-icon:active{

}

.kt-checkbox{
  position: absolute;
  display: inline-block;
  text-align: center;
  border-radius: 25rpx;
  cursor: pointer;
  right:70rpx;
  bottom: 40rpx;
  font-size: 28rpx;
  border: 1rpx solid #f0f0f0;
  padding: 0 20rpx;
  .input-language-icon{
    width: 30rpx;
    height: 30rpx;
    vertical-align: middle;
    margin-right: 10rpx;
    margin-top: -10rpx;
  }
}

.top-left-btn{
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #cccccc;
  cursor: pointer;
}

.top-left-btn:active{
  background-color: #f0f0f0;
}
</style>
