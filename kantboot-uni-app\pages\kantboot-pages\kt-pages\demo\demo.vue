<template>

  <view
      class="container">
    <kt-nav-bar
        :title="$i18n.zhToGlobal('KANTBOOT组件')"
    ></kt-nav-bar>


    <view :class="clazz.boxBox">
      <!-- #ifndef MP-WEIXIN -->
      <kt-box class="kt-box" :title="$i18n.zhToGlobal('提示')">
        <kt-markdown :markdown="
          '- 以下所有组件都兼容暗黑模式、PC端、手机端、手表端' + '\n' +
          '- 若使用KANTBOOT后端、则以下所有组件，都兼容国际化' + '\n'
        "></kt-markdown>
      </kt-box>
      <!-- #endif -->

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('')+' 地区选择面板'">
        <kt-state-area-address-select-panel></kt-state-area-address-select-panel>
      </kt-box>

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('消息通知面板')+' kt-notify-list-panel'">
        <kt-notify-list-panel></kt-notify-list-panel>
      </kt-box>


      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('Meetov登录面板')+' project-meet-login-panel'">
        <project-meet-login-panel></project-meet-login-panel>
      </kt-box>

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('Meetov登录面板')+' project-meet-login-panel'">
        <project-meet-login-panel></project-meet-login-panel>
      </kt-box>

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('按钮')+' kt-button'">
        <!-- #ifndef MP-WEIXIN -->
        <kt-markdown :markdown="
          '- '+$i18n.zhToGlobal('效果显示默认3秒') + '\n\n' +
          '- '+$i18n.zhToGlobal('参数1：文字显示') + '\n ' +
          '- '+$i18n.zhToGlobal('参数2：效果显示事件（毫秒/单位）')
        "></kt-markdown>
        <!-- #endif -->


        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('无效果')">
          <view :class="clazz.ktButtonBox">
            <kt-button>{{ $i18n.zhToGlobal("点击") }}</kt-button>
          </view>
        </kt-box>

        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('等待效果')">
          <view :class="clazz.ktButtonBox">
            <kt-button
                ref="ktButtonOfLoading"
                @click="$refs.ktButtonOfLoading.loading()"
            >{{ $i18n.zhToGlobal("点击") }}
            </kt-button>
          </view>
        </kt-box>


        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('成功效果')">
          <view :class="clazz.ktButtonBox">
            <kt-button
                ref="ktButtonOfSuccess"
                @click="$refs.ktButtonOfSuccess.success($i18n.zhToGlobal('成功'))"
            >{{ $i18n.zhToGlobal("点击") }}
            </kt-button>
          </view>
        </kt-box>

        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('错误效果')">
          <view :class="clazz.ktButtonBox">
            <kt-button
                ref="ktButtonOfError"
                @click="$refs.ktButtonOfError.error($i18n.zhToGlobal('失败'))"
            >{{ $i18n.zhToGlobal("点击") }}
            </kt-button>
          </view>
        </kt-box>


      </kt-box>

      <kt-box
          :title="$i18n.zhToGlobal('头像')+' kt-avatar'"
          class="kt-box">
        <kt-box class="in-kt-box" style="text-align: center">
          <kt-avatar
              :file-id="658533007089669"
          ></kt-avatar>
          <kt-avatar
              :src="$kt.file.visit(658533007089669)"
          ></kt-avatar>
          <kt-avatar
              :file-id="658533007089669"
              size="200rpx"
          ></kt-avatar>
          <kt-avatar
              :file-id="658533007089669"
              :online="true"
          ></kt-avatar>
          <kt-avatar
              :file-id="658533007089669"
              border="5rpx solid #333"
          ></kt-avatar>
        </kt-box>
      </kt-box>

      <kt-box class="kt-box"
              :title="$i18n.zhToGlobal('语言选择面板')+' kt-language-select-panel'">
        <view>
          <kt-language-select-panel></kt-language-select-panel>
        </view>
      </kt-box>

      <kt-box class="kt-box" :title="$i18n.zhToGlobal('语言选择弹窗')+' kt-language-select-popup'">
        <view>
          <kt-language-select-popup ref="ktLanguageSelectPopup"></kt-language-select-popup>
          <view :class="clazz.ktButtonBox">
            <kt-button
                @click="$refs.ktLanguageSelectPopup.open()"
            >{{ $i18n.zhToGlobal("打开语言选择弹窗") }}
            </kt-button>
          </view>
        </view>
      </kt-box>


      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('轮播图')+' kt-carousel'">
        <!-- #ifndef MP-WEIXIN -->
        <kt-markdown :markdown="'' +
         '- '+$i18n.zhToGlobal('需配合KANTBOOT的后端使用') + '\n\n' +
         '- '+$i18n.zhToGlobal('通过管理后台设置对应typeCode的图片')"></kt-markdown>
        <!-- #endif -->

        <kt-box class="in-kt-box"
                :title="$i18n.zhToGlobal('默认高度')">
          <kt-carousel
              :typeCode="'ad'"
          ></kt-carousel>
        </kt-box>

        <view style="height: 30rpx"></view>

        <kt-box class="in-kt-box"
                :title="$i18n.zhToGlobal('自定义高度')">
          <kt-carousel
              :typeCode="'ad'"
              height="600rpx"
          ></kt-carousel>
        </kt-box>


      </kt-box>

      <kt-box class="kt-box" :title="$i18n.zhToGlobal('角色卡片')+' kt-character-card'">
        <!-- #ifndef MP-WEIXIN -->
        <kt-markdown
            :markdown="$i18n.zhToGlobal('> 很多项目，角色卡片都有特定需求，不一定对所在项目完全适合')"></kt-markdown>
        <!-- #endif -->
        <kt-character-card></kt-character-card>
      </kt-box>

      <!-- #ifndef MP-WEIXIN -->
      <kt-box class="kt-box" :title="'Markdown' +' kt-markdown'">
        <kt-markdown
            :markdown="'# 一级标题\n## 二级标题\n### 三级标题\n#### 四级标题\n##### 五级标题\n###### 六级标题\n'"></kt-markdown>
        <kt-markdown
            :markdown="'> 引用文字\n> 引用文字\n> 引用文字\n'"></kt-markdown>
        <kt-markdown
            :markdown="'- 无序列表\n- 无序列表\n- 无序列表\n'"></kt-markdown>
        <kt-markdown
            :markdown="'1. 有序列表\n2. 有序列表\n3. 有序列表\n'"></kt-markdown>
        <kt-markdown
            :markdown="'<div style=\'color:#999999\'>自定义HTML和CSS</div>'"></kt-markdown>
      </kt-box>
      <!-- #endif -->


      <kt-box class="kt-box" :title="$i18n.zhToGlobal('格式显示')+' kt-format'">
        <kt-format
            :data='[{
          type: "text",
          content: "测试001"
        }, {
          type: "image:id",
          content: "658887105785861"
        }, {
          type: "images:id",
          content: "[658887105785861,658887105785861]"
        },
        {
          type: "text",
          content: "测试001"
        },
        {
          type: "images:id",
          content: "[658887105785861,658887105785861]"
        },
        {
          type: "images:id",
          content: "[658887105785861,658887105785861,658887105785861]"
        },
        {
          type: "video:id",
          content: "668444281610245",
          ratio: "16 / 9"
        }]'
        ></kt-format>
      </kt-box>

      <!-- #ifndef MP-WEIXIN -->
      <kt-box class="kt-box" :title="$i18n.zhToGlobal('图片组件')+' kt-image'">
        <kt-markdown
            :markdown="'> '+$i18n.zhToGlobal('先使用模糊图，等待主图加载完成，模糊图自带高斯模糊效果')"></kt-markdown>
      </kt-box>
      <!-- #endif -->


      <kt-box class="kt-box" :title="$i18n.zhToGlobal('图片选择器')+' kt-image-select'">
        <!-- #ifndef MP-WEIXIN -->
        <kt-markdown :markdown="
        '- '+$i18n.zhToGlobal('需配合KANTBOOT的后端使用') + '\n\n' +
        '- '+$i18n.zhToGlobal('PC和手机网页端为按钮点击移动，APP端为拖拽移动')"></kt-markdown>
        <!-- #endif -->

        <kt-image-select
            file-group-code="fp"
        ></kt-image-select>
        <!-- #ifndef MP-WEIXIN -->
        <kt-markdown :markdown="
        '- '+$i18n.zhToGlobal('使用v-modal绑定数组') + '\n\n' +
        '- 属性1: file-group-code 文件组' + '\n\n'
        +'- 属性2: count 图片的可选择数量（小程序中，最大为9）' + '\n\n'
        "></kt-markdown>
        <!-- #endif -->

      </kt-box>

      <!-- #ifndef MP-WEIXIN -->
      <kt-box class="kt-box" :title="$i18n.zhToGlobal('键盘高度')+' kt-keyboard-size'">
        <kt-markdown markdown="> 让手机端的底部输入框可以不上移屏幕，更好的显示"></kt-markdown>
        <kt-keyboard-size></kt-keyboard-size>
      </kt-box>
      <!-- #endif -->

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('等待效果')+' kt-loading'">
        <kt-loading
            ref="ktLoading"
        ></kt-loading>
        <view :class="clazz.ktButtonBox">
          <kt-button
              @click="ktLoadingOpen"
          >{{ $i18n.zhToGlobal("查看等待效果") }}
          </kt-button>
        </view>
      </kt-box>


      <kt-box class="kt-box"
              :title="$i18n.zhToGlobal('提示弹窗')+' kt-modal'">
        <kt-modal
            ref="ktModal"
        ></kt-modal>
        <view :class="clazz.ktButtonBox">
          <kt-button
              @click="$refs.ktModal.open()">{{ $i18n.zhToGlobal("打开提示窗") }}
          </kt-button>
        </view>
      </kt-box>


      <kt-box class="kt-box"
              :title="$i18n.zhToGlobal('登录面板')+' kt-login-panel'">
        <!-- #ifndef MP-WEIXIN -->
        <kt-markdown :markdown="'- 小程序端，默认为一键获取手机号登录遮挡（可选择小程序ID登录）'"></kt-markdown>
        <!-- #endif -->
        <view :class="clazz.ktButtonBox">
          <kt-login-panel></kt-login-panel>
        </view>
        <!-- #ifndef MP-WEIXIN -->
        <kt-markdown :markdown="'- 不一定适用于所有项目'"></kt-markdown>
        <!-- #endif -->
      </kt-box>

      <kt-box class="kt-box"
              :title="$i18n.zhToGlobal('登录弹窗')+' kt-login-panel'">
        <kt-login-popup ref="ktLoginPopup"></kt-login-popup>
        <view :class="clazz.ktButtonBox">
          <kt-button
              @click="$refs.ktLoginPopup.open()"
          >{{ $i18n.zhToGlobal("打开登录弹窗") }}
          </kt-button>
        </view>
      </kt-box>

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('用户信息卡片组件')+'（kt-user-info-card）'">
        <kt-user-info-card
            :user-account-id="***************"
        ></kt-user-info-card>
      </kt-box>

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('用户关系面板')+'（kt-user-account-interrelation-panel）'">
        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('默认模式')">
          <view :class="clazz.ktButtonBox">

            <kt-user-account-interrelation-panel
                :user-account-id="***************"
            ></kt-user-account-interrelation-panel>

          </view>
        </kt-box>

        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('多选择模式')">
          <view :class="clazz.ktButtonBox">

            <kt-user-account-interrelation-panel
                many-select
                :user-account-id="***************"
            ></kt-user-account-interrelation-panel>
          </view>
        </kt-box>

        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('自定义文字')">
          <view :class="clazz.ktButtonBox">

            <kt-user-account-interrelation-panel
                :mutual-follow-text="$i18n.zhToGlobal('好友')"
                :follow-text="$i18n.zhToGlobal('关注')"
                :followed-text="$i18n.zhToGlobal('粉丝')"
                :user-account-id="***************"
            ></kt-user-account-interrelation-panel>
          </view>
        </kt-box>

        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('自定义用户卡片')">
          <view :class="clazz.ktButtonBox">

            <kt-user-account-interrelation-panel
                many-select
                custom-user-info-card
                :user-account-id="***************">
              <template v-slot:userInfoCard="{userAccount}">
                <view>
                  <view>{{ $i18n.zhToGlobal('昵称') }}:{{ userAccount.nickname }}</view>
                </view>
              </template>
            </kt-user-account-interrelation-panel>
          </view>
        </kt-box>

      </kt-box>

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('信息发送输入框')+' kt-send-input'">

        <kt-box class="in-kt-box"
                :title="$i18n.zhToGlobal('Bar模式')+
                '（'+$i18n.zhToGlobal('默认模式')+'）'">
          <view :class="clazz.ktButtonBox">

            <!-- start: 主要代码 -->
            <kt-send-input
                @send="send"
            ></kt-send-input>
            <!-- end: 主要代码 -->

            <kt-box class="in-kt-box">
              <kt-markdown markdown="> 对应JSON"></kt-markdown>
              <pre>{{ JSON.stringify(inputData) }}</pre>
            </kt-box>
          </view>
        </kt-box>

        <kt-box class="in-kt-box"
                :title="$i18n.zhToGlobal('Panel模式')">
          <view :class="clazz.ktButtonBox">

            <!-- start: 主要代码 -->
            <kt-send-input
                mode="panel"
                @send="send"
            ></kt-send-input>
            <!-- end: 主要代码 -->
          </view>
        </kt-box>

        <kt-box class="in-kt-box"
                :title="$i18n.zhToGlobal('自定义扩展')">
          <view :class="clazz.ktButtonBox">

            <!-- start: 主要代码 -->
            <kt-send-input
                @send="send"
                :has-user-account="false"
                :has-video="false"
            >
              <template v-slot:operateExtra>
                <view style="
                display: inline-block;
                font-size: 22rpx;
                background-color: rgba(0,0,0,.9);
                color: #f9f9f9;
                padding: 5rpx 20rpx 5rpx 20rpx;
                border-radius: 30rpx;
                box-sizing: border-box;
                vertical-align: top;">KANTBOOT
                </view>
              </template>
            </kt-send-input>
            <!-- end: 主要代码 -->
          </view>

          <view :class="clazz.ktButtonBox">

            <!-- start: 主要代码 -->
            <kt-send-input
                @send="send"
                :has-user-account="false"
                :has-video="false"
                :operate-extra-array="[{
                    iconSrc: $kt.file.byPath('kantboot/icon/language.svg'),
                    text: $i18n.zhToGlobal('翻译'),
                }]"
            >
            </kt-send-input>
            <!-- end: 主要代码 -->
          </view>


          <view :class="clazz.ktButtonBox">

            <!-- start: 主要代码 -->
            <kt-send-input
                mode="panel"
                @send="send"
                :has-user-account="false"
                :has-video="false"
                :operate-extra-array="[{
                    iconSrc: $kt.file.byPath('kantboot/icon/language.svg'),
                    text: $i18n.zhToGlobal('翻译'),
                }]"
            >
            </kt-send-input>
            <!-- end: 主要代码 -->
          </view>

        </kt-box>


      </kt-box>

      <kt-box class="kt-box"
              :title="$i18n.zhToGlobal('二维码')+'（kt-qrcode）'">
        <kt-box class="in-kt-box align-center"
                :title="$i18n.zhToGlobal('默认模式')">
          <kt-qrcode :content="'https://www.kantboot.com'"></kt-qrcode>
        </kt-box>

        <kt-box class="in-kt-box align-center"
                :title="$i18n.zhToGlobal('自定义大小')">
          <kt-qrcode
              size="300rpx"
              :content="'https://www.kantboot.com'"></kt-qrcode>
        </kt-box>

        <kt-box class="in-kt-box align-center"
                :title="$i18n.zhToGlobal('居中图片')">
          <kt-qrcode
              size="300rpx"
              :image-src="$kt.file.visit(673339735523333)"
              :content="'https://www.kantboot.com'"></kt-qrcode>
        </kt-box>

        <!-- #ifndef MP-WEIXIN -->
        <kt-box class="in-kt-box align-center"
                :title="$i18n.zhToGlobal('保存图片')">
          <view>
            <kt-qrcode
                ref="ktQrcode"
                size="300rpx"
                :image-src="$kt.file.visit(673339735523333)"
                :content="'https://www.kantboot.com'"></kt-qrcode>
          </view>
          <view>
            <kt-button
                @click="$refs.ktQrcode.saveImage()"
            >{{ $i18n.zhToGlobal('保存图片') }}
            </kt-button>
          </view>

        </kt-box>
        <!-- #endif -->

      </kt-box>

      <kt-box class="kt-box"
              :title="$i18n.zhToGlobal('邀请面板')+'（kt-invite-panel）'"
      >
        <kt-box class="in-kt-box"
                :title="$i18n.zhToGlobal('默认模式')"
        >
          <kt-invite-panel></kt-invite-panel>
        </kt-box>
        <kt-box class="in-kt-box"
                :title="$i18n.zhToGlobal('显示邀请码')"
        >
          <kt-invite-panel
              :has-direct-code="true"
          ></kt-invite-panel>
        </kt-box>
        <kt-box class="in-kt-box"
                :title="$i18n.zhToGlobal('自定义用户信息卡片')">
          <kt-invite-panel
              custom-user-info-card
          >
            <template v-slot:userInfoCard="{userAccount}">
              <view>
                <view>{{ $i18n.zhToGlobal('我的昵称') }}:{{ userAccount.nickname }}</view>
              </view>
            </template>
          </kt-invite-panel>
        </kt-box>
      </kt-box>

      <kt-box class="kt-box"
              :title="$i18n.zhToGlobal('设置面板')+'（kt-setting-panel）'">
        <kt-box class="in-kt-box"
                :title="$i18n.zhToGlobal('默认模式')">
          <kt-setting-panel
              :user-agreement-url="'https://www.kantboot.com'"
          ></kt-setting-panel>
        </kt-box>
        <kt-box class="in-kt-box"
                :title="$i18n.zhToGlobal('自定义模式')">
          <kt-setting-panel
              :user-agreement-url="'https://www.kantboot.com'"
              :privacy-agreement-url="'https://www.kantboot.com'"
              :has-app="false"
              :has-account="false"
              :has-user-agreement="false"
          ></kt-setting-panel>
        </kt-box>

      </kt-box>


      <kt-box class="kt-box" :title="$i18n.zhToGlobal('支付面板')+'（kt-pay-panel）'">
        <view :class="clazz.ktButtonBox">
          <kt-pay-panel></kt-pay-panel>
        </view>
      </kt-box>

      <kt-box class="kt-box" :title="$i18n.zhToGlobal('支付弹窗')+'（kt-pay-popup）'">
        <view :class="clazz.ktButtonBox">
          <kt-pay-popup
          ref="ktPayPopup"
          ></kt-pay-popup>
          <kt-button
          @click="$refs.ktPayPopup.open('100006')"
          >{{$i18n.zhToGlobal("前往支付")}}</kt-button>
        </view>
      </kt-box>

      <!-- #ifndef MP-WEIXIN -->
      <kt-box class="kt-box" :title="$i18n.zhToGlobal('结尾')">
        <kt-markdown markdown="> 其它效果不逐一显示，可查看对应README.md"></kt-markdown>
        <kt-markdown :markdown="
          '- 状态栏高度适配（kt-status-bar-height）' + '\n' +
          '- 弹出层（kt-popup）' + '\n'
        "></kt-markdown>
      </kt-box>
      <!-- #endif -->

    </view>


  </view>
</template>

<script>

export default {
  data() {
    return {
      clazz: {
        boxBox: this.$kt.style.toggleClass("box-box"),
        ktButtonBox: this.$kt.style.toggleClass("kt-button-box"),
      },
      inputData: {},
    };
  },
  methods: {
    ktLoadingOpen() {
      this.$refs.ktLoading.open();
      setTimeout(() => {
        this.$refs.ktLoading.close();
      }, 2000);
    },
    send(e) {
      this.inputData = e;
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  box-sizing: border-box;
  text-align: center;

  .box-box {
    display: inline-block;
    width: 100%;
    text-align: center;
    margin-left: 0rpx;
  }
}

.kt-box {
  text-align: left;
  border-radius: 10rpx;
  margin-bottom: 50rpx;
  box-shadow: 0 0 10rpx rgba(118, 118, 118, .3);

  .in-kt-box {
    border: 1rpx solid #eee;
    border-radius: 10rpx;
    margin-bottom: 30rpx;
  }
}

.box-box-mode-device-pc {
  max-width: 700px;
}

.kt-button-box-mode-device-pc {
  padding: 60rpx 300rpx 60rpx 300rpx;
  box-sizing: border-box;
}

.align-center {
  text-align: center;
}
</style>
