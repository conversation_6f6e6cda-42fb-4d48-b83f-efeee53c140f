<view class="{{['data-v-11f32d7e',clazz.container]}}"><view class="{{['data-v-11f32d7e',clazz.bg]}}"><block wx:if="{{$root.g0==='mobile'}}"><image class="bg-image data-v-11f32d7e" mode="aspectFill" src="{{$root.g1}}"></image></block><block wx:if="{{$root.g2==='mobile'}}"><image class="bg-image-2 data-v-11f32d7e" mode="aspectFill" src="{{$root.g3}}"></image></block><block wx:if="{{$root.g4==='mobile'}}"><image class="bg-image-3 data-v-11f32d7e" mode="aspectFill" src="{{$root.g5}}"></image></block><view class="bg-bg data-v-11f32d7e"></view></view><view class="box-header data-v-11f32d7e" id="navBarInMessage"><kt-nav-bar vue-id="2c730348-1" is-has-i18n="{{$root.g6==='mobile'}}" background-color="rgba(255,255,255,.3)" icon="{{$root.g7}}" title="{{$root.g8}}" data-ref="navBar" class="data-v-11f32d7e vue-ref" bind:__l="__l"></kt-nav-bar></view><view class="kt-chat-list-panel-box data-v-11f32d7e"><view style="padding:0 20rpx;" class="data-v-11f32d7e"><block wx:if="{{noticeTextShow&&noticeText}}"><u-notice-bar vue-id="2c730348-2" icon="-" text="{{noticeText}}" mode="closable" fontSize="28rpx" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" class="data-v-11f32d7e" bind:__l="__l"></u-notice-bar></block></view><kt-chat-list-panel vue-id="2c730348-3" selected="{{dialogIdSelected}}" height="{{'calc(100vh - '+navBarHeight+'px - 20rpx - 50rpx - 20rpx)'}}" data-event-opts="{{[['^cardClick',[['cardClick']]]]}}" bind:cardClick="__e" class="data-v-11f32d7e" bind:__l="__l"></kt-chat-list-panel></view><block wx:if="{{$root.g9==='pc'}}"><view class="kt-chat-dialog-panel-box data-v-11f32d7e"><kt-chat-dialog-panel generic:scoped-slots-messageItem="pageMessageMeet-kt-chat-dialog-panel-messageItem" data-vue-generic="scoped" vue-id="2c730348-4" has-read="{{self.isSubAccount}}" copy="{{self.isSubAccount}}" height="{{'calc(100vh - '+navBarHeight+'px - '+footerHeight+'px - 20rpx - 50rpx - 20rpx)'}}" dialog-id="{{dialogIdSelected}}" data-event-opts="{{[['^load',[['dialogLoad']]],['^userCardClick',[['userCardClick']]]]}}" bind:load="__e" bind:userCardClick="__e" class="data-v-11f32d7e" bind:__l="__l" vue-slots="{{['messageItem']}}"></kt-chat-dialog-panel><block wx:if="{{dialog.id&&dialog.userAccountId&&!isCustomerService}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="charge-button data-v-11f32d7e" bindtap="__e"><block wx:if="{{userAccount.genderCode==='male'}}"><text class="data-v-11f32d7e">{{$root.g10}}</text></block><block wx:else><text class="data-v-11f32d7e">{{$root.g11}}</text></block></view></block><block wx:if="{{dialog.id&&dialog.userAccountId&&self.isSubAccount&&!isCustomerService}}"><view class="data-v-11f32d7e"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="remark-button data-v-11f32d7e" bindtap="__e">{{$root.g12}}</view></view></block><view class="footer data-v-11f32d7e" id="projectMeetPageMessageFooter"><kt-send-input vue-id="2c730348-7" has-user-account="{{false}}" has-voice="{{false}}" has-gift="{{true&&!isCustomerService}}" mode="bar" data-ref="sendInput" data-event-opts="{{[['^send',[['send']]],['^change',[['change']]],['^chooseGift',[['chooseGift']]]]}}" bind:send="__e" bind:change="__e" bind:chooseGift="__e" class="data-v-11f32d7e vue-ref" bind:__l="__l" vue-slots="{{['operateExtra']}}"><view slot="operateExtra"><block wx:if="{{!isCustomerService&&!self.isSubAccount}}"><view style="display:inline-block;" class="data-v-11f32d7e"><image class="input-icon data-v-11f32d7e" src="{{$root.g13}}" data-event-opts="{{[['tap',[['openGoldPopup']]]]}}" bindtap="__e"></image></view></block><block wx:if="{{!isCustomerService}}"><view style="display:inline-block;margin-left:30rpx;vertical-align:top;" class="data-v-11f32d7e"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="kt-checkbox data-v-11f32d7e" bindtap="__e"><image class="input-language-icon data-v-11f32d7e" src="{{$root.g14}}"></image><block wx:if="{{!targetLanguage.code}}"><text class="data-v-11f32d7e">{{$root.g15}}</text></block><block wx:else><text class="data-v-11f32d7e">{{''+targetLanguage.i18nName+''}}</text></block></view></view></block></view></kt-send-input></view></view></block><kt-language-select-popup vue-id="2c730348-8" reset="{{false}}" data-ref="ktLanguageSelectPopup" data-event-opts="{{[['^select',[['selectLanguage']]]]}}" bind:select="__e" class="data-v-11f32d7e vue-ref" bind:__l="__l" vue-slots="{{['topLeft']}}"><view data-event-opts="{{[['tap',[['toUnTranslate']]]]}}" class="top-left-btn data-v-11f32d7e" bindtap="__e" slot="topLeft">{{$root.g16}}</view></kt-language-select-popup><block wx:if="{{dialog.id&&dialog.userAccountId}}"><project-meet-vgp-popup vue-id="2c730348-9" userAccountId="{{dialog.userAccountId}}" data-ref="projectMeetVgpPopup" class="data-v-11f32d7e vue-ref" bind:__l="__l"></project-meet-vgp-popup></block><project-meet-gift-popup vue-id="2c730348-10" data-ref="projectMeetGiftPopup" class="data-v-11f32d7e vue-ref" bind:__l="__l"></project-meet-gift-popup><project-meet-gold-popup vue-id="2c730348-11" data-ref="projectMeetGoldPopup" class="data-v-11f32d7e vue-ref" bind:__l="__l"></project-meet-gold-popup><project-meet-points-popup vue-id="2c730348-12" hasSelfNumber="{{false}}" user-account-id="{{self.id}}" data-ref="projectMeetPointsPopup" class="data-v-11f32d7e vue-ref" bind:__l="__l"></project-meet-points-popup><project-meet-vip-popup vue-id="2c730348-13" data-ref="projectMeetVipPopup" class="data-v-11f32d7e vue-ref" bind:__l="__l"></project-meet-vip-popup><project-meet-gold-transfer-popup vue-id="2c730348-14" data-ref="projectMeetGoldTransferPopup" class="data-v-11f32d7e vue-ref" bind:__l="__l"></project-meet-gold-transfer-popup><project-meet-user-info-popup vue-id="2c730348-15" data-ref="usPopup" class="data-v-11f32d7e vue-ref" bind:__l="__l"></project-meet-user-info-popup><kt-set-remark-popup vue-id="2c730348-16" data-ref="setRemarkPopup" class="data-v-11f32d7e vue-ref" bind:__l="__l"></kt-set-remark-popup><kt-no-login vue-id="2c730348-17" data-ref="noLogin" class="data-v-11f32d7e vue-ref" bind:__l="__l"></kt-no-login></view>