<template>

  <view
      class="container">
    <kt-nav-bar
        :title="$i18n.zhToGlobal('kt-link组件')"
    ></kt-nav-bar>


    <view :class="clazz.boxBox">

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('外链操作卡片')+' kt-link-operation-card'">
        <kt-link-operation-card>
        </kt-link-operation-card>
      </kt-box>

      <kt-box
        class="kt-box"
        :title="$i18n.zhToGlobal('外链资料卡片')+' kt-link-info-card'">
        <kt-link-info-card
        :link-id="'676345286795269'"
        ></kt-link-info-card>
      </kt-box>


    </view>


  </view>
</template>

<script>

export default {
  data() {
    return {
      clazz: {
        boxBox: this.$kt.style.toggleClass("box-box"),
        ktButtonBox: this.$kt.style.toggleClass("kt-button-box"),
      },
      inputData: {},
    };
  },
  methods: {

  },
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  box-sizing: border-box;
  text-align: center;

  .box-box {
    display: inline-block;
    width: 100%;
    text-align: center;
    margin-left: 0rpx;
  }
}

.kt-box {
  text-align: left;
  border-radius: 10rpx;
  margin-bottom: 50rpx;
  box-shadow: 0 0 10rpx rgba(118, 118, 118, .3);

  .in-kt-box {
    border: 1rpx solid #eee;
    border-radius: 10rpx;
    margin-bottom: 30rpx;
  }
}

.box-box-mode-device-pc {
  max-width: 700px;
}

.kt-button-box-mode-device-pc {
  padding: 60rpx 300rpx 60rpx 300rpx;
  box-sizing: border-box;
}

.align-center{
  text-align: center;
}
</style>
