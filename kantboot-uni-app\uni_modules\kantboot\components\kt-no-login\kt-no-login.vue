<template>
  <view>
    <view
        v-show="!isLogin"
        :class="clazz.box"
        :style="{
        zIndex: zIndex
      }"
    >
      <view :class="clazz.back"></view>
      <view class="in-box">

        <view class="icon-box">
          <image
              class="icon"
              :src="$kt.file.byPath('icon/plan.svg')"
          ></image>
        </view>
        <view style="height: 30rpx"></view>
        <kt-button
            @click="toLogin()">
          {{ $i18n.zhToGlobal("前往登录") }} {{ "➠" }}
        </kt-button>
        <view style="height: 30rpx"></view>

      </view>
      <view style="position: fixed;z-index: 701;width: 100%;left:0;text-align: center;bottom: 150rpx">
        <image
            @click="$kt.router.toSetting()"
            class="setting-icon"
            :src="$kt.file.byPath('icon/setting.svg')"></image>
      </view>

    </view>

    <kt-login-popup
        :wechat-login-method="wechatLoginMethod"
        ref="ktLoginPopup"
    ></kt-login-popup>

  </view>

</template>

<script>
export default {
  props:{
    wechatLoginMethod:{
      type: String,
      default: "loginByPhone"
    }
  },
  data() {
    return {
      isLogin: this.$kt.userAccount.getIsLogin(),
      userInfo: this.$kt.userAccount.getSelf(),
      clazz: {
        box: this.$kt.style.toggleClass("box"),
        back: this.$kt.style.toggleClass("back"),
      },
      zIndex:700
    };
  },
  created() {
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.userInfo = this.$kt.userAccount.getSelf();
    });
  },
  mounted() {
  },
  methods: {
    toLogin() {
      // this.$kt.router.toLogin();
      this.$refs.ktLoginPopup.open();
    },
  }
}
</script>

<style lang="scss" scoped>
.box {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}


.icon {
  width: 200rpx;
  height: 200rpx;
  // 左右晃动的动画
  animation: inNoLoginShake 2s infinite;
}

@keyframes inNoLoginShake {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(10deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(-10deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.in-box{
  position: fixed;
  top: calc(50% - 75rpx);
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  font-size: 30rpx;
  color: #000000;
  width: 100%;
  padding: 20rpx 60rpx 20rpx 60rpx;
  box-sizing: border-box;
  z-index: 701;
}

.box-mode-color-scheme-dark {
  .icon{
    // 颜色反转
    filter: invert(1);
  }
}

.box-mode-device-pc {
  .in-box{
    width: 400px;
  }
}

.back{
  z-index: 600;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
}

.back-mode-color-scheme-light {
  background-color: #FFFFFF;
}

.back-mode-color-scheme-dark {
  background-color: #191919;
}

.setting-icon{
  width: 50rpx;
  height: 50rpx;
  opacity: .7;
}

.setting-icon:active{
  transform: scale(0.9);
}

.box-mode-color-scheme-dark{
  .setting-icon{
    // 颜色反转
    filter: invert(1);
  }
}
</style>
