<template>
  <view class="panel-privileges-card-item">
    <view class="panel-privileges-card-item-icon">
      <image
          class="panel-privileges-card-item-icon-image"
          mode="aspectFit"
          :src="info.icon"
      ></image>
    </view>
    <view class="panel-privileges-card-item-text">
      {{info.text}}
    </view>
    <view class="panel-privileges-card-item-tip">
      {{info.tip}}
    </view>
  </view>
</template>

<script>
export default {
  props:{
    info: {
      type: Object,
      default: () => {
        return {
          icon: '',
          text: '',
          tip: ''
        }
      }
    }
  },
  data() {
    return {
    };
  }
}
</script>

<style lang="scss" scoped>
.panel-privileges-card-item {
  width: 33.3%;
  margin-bottom: 30rpx;
  .panel-privileges-card-item-icon {
    width: 100%;
    margin-right: 10rpx;
    text-align: center;
    .panel-privileges-card-item-icon-image {
      width: 100rpx;
      height: 100rpx;
    }
  }
  .panel-privileges-card-item-text {
    text-align: center;
    font-size: 28rpx;
    color: #000000;
  }
  .panel-privileges-card-item-tip {
    text-align: center;
    font-size: 24rpx;
    color: #999999;
  }
}
</style>
