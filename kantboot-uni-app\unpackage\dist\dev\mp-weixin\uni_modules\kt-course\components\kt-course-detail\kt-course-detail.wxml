<view class="data-v-fab12aaa"><block wx:if="{{loading}}"><view class="data-v-fab12aaa"><view style="height:20rpx;" class="data-v-fab12aaa"></view><u-loading-icon vue-id="5b73288a-1" mode="circle" size="{{50}}" class="data-v-fab12aaa" bind:__l="__l"></u-loading-icon></view></block><block wx:if="{{$root.g0}}"><view class="data-v-fab12aaa"><view class="box data-v-fab12aaa"><kt-format vue-id="5b73288a-2" data="{{[{type:'video:id',content:courseItemSelected.content}]}}" class="data-v-fab12aaa" bind:__l="__l"></kt-format></view><view class="box box-2 data-v-fab12aaa"><view class="box-title data-v-fab12aaa">{{''+course.title+''}}</view><view class="box-desc data-v-fab12aaa">{{''+$root.g1+": "+course.description+''}}</view></view><view class="box box-3 data-v-fab12aaa"><view class="scroll-view-x data-v-fab12aaa"><block wx:for="{{courseItems}}" wx:for-item="item" wx:for-index="index"><view data-event-opts="{{[['tap',[['selectCourseItem',['$0'],[[['courseItems','',index]]]]]]]}}" class="{{['view-item','data-v-fab12aaa',(item.id===courseItemSelected.id)?'view-item-selected':'']}}" bindtap="__e"><view class="item-index data-v-fab12aaa">{{''+"NO."+(index+1)+''}}</view><view class="item-title data-v-fab12aaa">{{''+item.title+''}}</view></view></block></view></view><block wx:if="{{courseItemSelected.fileIdOfCourseware}}"><view class="box button-box data-v-fab12aaa"><kt-button bind:click="__e" vue-id="5b73288a-3" data-ref="previewButton" data-event-opts="{{[['^click',[['preview',['$0'],['courseItemSelected.fileIdOfCourseware']]]]]}}" class="data-v-fab12aaa vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g2}}</kt-button></view></block></view></block><block wx:if="{{$root.g3}}"><view class="box-2 data-v-fab12aaa"><view style="height:100rpx;" class="data-v-fab12aaa"></view><u-empty vue-id="5b73288a-4" textSize="32rpx" text="{{$root.g4}}" mode="list" class="data-v-fab12aaa" bind:__l="__l"></u-empty></view></block></view>