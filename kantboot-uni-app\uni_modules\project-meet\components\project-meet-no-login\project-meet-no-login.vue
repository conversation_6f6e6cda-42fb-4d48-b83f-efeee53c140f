<template>
  <view>
    <view
        :class="clazz.box"
        v-show="!isLogin"
        :style="{
          zIndex: zIndex
        }"
    >
      <view
	  v-if="isInit"
          @click="toLogin()"
          class="to-login-btn">{{$i18n.zhToGlobal("登录")}}</view>
      <view>
        <image
            class="bg-img"
            mode="aspectFill"
            :src="$kt.file.byPath('backBig.png')"></image>
      </view>
      <view :class="clazz.back"></view>
      <view 
	  v-if="isInit"
	  class="in-box">
        <view class="in-box-title-title">
            <image
                class="in-box-title-title-icon"
              :src="$kt.file.byPath('projectMeet/icon/logo.png')"
            ></image>
          <view class="in-box-title-title-text">{{"MEETOV"}}</view>
        </view>
        <view style="height: 20rpx"></view>
        <view class="in-box-title">{{$i18n.zhToGlobal("让相遇成为爱的起点")}}</view>
        <view style="height: 40rpx"></view>
        <view>
          <view
          class="in-box-btn"
          @click="toRegister()"
          >{{$i18n.zhToGlobal("立即注册")}}</view>
        </view>

      </view>

      <image
          class="to-language-btn"
          @click="$refs.ktLanguageSelectPopup.open()"
          :src="$kt.file.byPath('kantboot/icon/language.svg')"></image>


      <!-- 联系我们 -->
      <view
	  v-if="isInit"
          class="contact-us">
        <text @click="toEmail('<EMAIL>')">
          {{$i18n.zhToGlobal("联系我们")}}{{": "}}{{"<EMAIL>"}}
        </text>
      </view>


    </view>

    <project-meet-login-popup
    ref="ktLoginPopup"
    ></project-meet-login-popup>

    <project-meet-register-popup
        @registerSuccess="registerSuccess"
    ref="ktRegisterPopup"
    ></project-meet-register-popup>


    <kt-language-select-popup
    ref="ktLanguageSelectPopup"
    ></kt-language-select-popup>

  </view>

</template>

<script>
export default {
  data() {
    return {
      isLogin: this.$kt.userAccount.getIsLogin(),
      userInfo: this.$kt.userAccount.getSelf(),
      clazz: {
        box: this.$kt.style.toggleClass("box"),
        back: this.$kt.style.toggleClass("back"),
      },
      zIndex:700,
	  isInit:false
    };
  },
  created() {
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.userInfo = this.$kt.userAccount.getSelf();
    });
    this.$kt.event.on("projectMeet:forgetPasswordPopupOpen",()=>{
      this.$refs.ktLoginPopup.close();
    });
	this.isInit = false;
	if(this.$kt.storage.get("isInit")){
		this.isInit = true;		
	}
	this.$kt.dataChange.checkDataChange("clientInit")
	    .then(async (res) => {
			if(res.isChange){
				uni.navigateTo({
					url:"/pages/pages-body/into/into"
				});
				this.$kt.dataChange.setDataChange("clientInit","---")
				this.isInit = true;
			}
			this.$kt.storage.set("isInit","1");
		});
  },
  mounted() {
  },
  methods: {
    registerSuccess(){
      this.$refs.ktLoginPopup.open();
    },
    toLogin() {
      // this.$kt.router.toLogin();
      this.$refs.ktLoginPopup.open();

      this.$request.post("/project-meet-web/userLog/add",{
        data:{
          typeCode: "login",
          operationCode: "toLogin",
          sceneCode: this.$kt.style.detectDeviceType(),
          operationText: "前往登录",
          levelCode: "info",
          levelText: "普通信息",
        }
      });
    },
    toRegister() {
      // this.$kt.router.toRegister();
      this.$refs.ktRegisterPopup.open();

      this.$request.post("/project-meet-web/userLog/add",{
        data:{
          typeCode: "register",
          operationCode: "toRegister",
          sceneCode: this.$kt.style.detectDeviceType(),
          operationText: "前往注册",
          levelCode: "info",
          levelText: "普通信息",
        }
      })
    },
    toEmail(email) {
      // js打开邮箱
      window.location.href = "mailto:" + email;
    },
  }
}
</script>

<style lang="scss" scoped>
.box {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}


.icon {
  width: 200rpx;
  height: 200rpx;
  // 左右晃动的动画
  animation: inNoLoginShake 2s infinite;
}

@keyframes inNoLoginShake {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(10deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(-10deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.in-box{
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  font-size: 30rpx;
  color: #000000;
  width: 100%;
  padding: 20rpx 60rpx 20rpx 60rpx;
  box-sizing: border-box;
  z-index: 701;

  .in-box-title{
    font-size: 50rpx;
    font-weight: bold;
    color: #FFFFFF;
    letter-spacing: 10rpx;
    text-shadow: 0 0 10rpx rgba(0,0,0,.3);
  }

  .in-box-btn{
    display: inline-block;
    background-color: rgba(0,0,0,.9);
    padding: 20rpx 60rpx 20rpx 60rpx;
    color: #FFFFFF;
    width: 300rpx;
    border-radius: 30rpx;
    border: 5rpx solid #FFFFFF;
    cursor: pointer;
  }
}

.box-mode-color-scheme-dark {
  .icon{
    // 颜色反转
    filter: invert(1);
  }
}


.box-mode-device-pc {
  .in-box{
    width: 600px;
  }
}

.back{
  z-index: 600;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  //background-color: #FFFFFF;
}

.back-mode-color-scheme-light {
  //background-color: rgba(255,255,255,.6);
}

.back-mode-color-scheme-dark {
  background-color: #191919;
}

.setting-icon{
  width: 50rpx;
  height: 50rpx;
  opacity: .7;
}

.setting-icon:active{
  transform: scale(0.9);
}

.box-mode-color-scheme-dark{
  .setting-icon{
    // 颜色反转
    filter: invert(1);
  }
}

.bg-img{
  position: fixed;
  width: calc(100vw + 100px);
  height: calc(100vh + 100px);
  top: -50px;
  left: -50px;
  z-index: -1;
  // 动画，高斯模糊，循环
  animation: bgImg 3s infinite;
}

@keyframes bgImg {
  0% {
    filter: blur(0px);
  }
  50% {
    filter: blur(10px);
  }
  100% {
    filter: blur(0px);
  }
}

.to-login-btn{
  position: fixed;
  top: 40rpx;
  right: 100rpx;
  z-index: 700;
  color: #FFFFFF;
  font-size: 32rpx;
  border: 3rpx solid #FFFFFF;
  padding: 5rpx 70rpx 5rpx 70rpx;
  border-radius: 20rpx;
  cursor: pointer;
}

.to-language-btn{
  position: fixed;
  bottom: 30rpx;
  right: 30rpx;
  width: 50rpx;
  height: 50rpx;
  z-index: 700;
  // 颜色反转
  filter: invert(1);
  cursor: pointer;
}

.in-box-title-title{
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #FFFFFF;
  font-weight: bold;
  letter-spacing: 10rpx;
  .in-box-title-title-icon{
    width: 80rpx;
    height: 80rpx;
    margin-right: 10rpx;
  }
}

.contact-us{
  position: fixed;
  bottom: 20rpx;
  display: inline-block;
  left: 50%;
  transform: translateX(-50%);
  color: #FFFFFF;
  font-size: 24rpx;
  text-align: center;
  width: 100%;
  cursor: pointer;
  z-index: 703;
}
</style>
