(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel"],{

/***/ 1112:
/*!************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel.vue ***!
  \************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _kt_chat_dialog_panel_vue_vue_type_template_id_485ae7a2_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./kt-chat-dialog-panel.vue?vue&type=template&id=485ae7a2&scoped=true& */ 1113);
/* harmony import */ var _kt_chat_dialog_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./kt-chat-dialog-panel.vue?vue&type=script&lang=js& */ 1115);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _kt_chat_dialog_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _kt_chat_dialog_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _kt_chat_dialog_panel_vue_vue_type_style_index_0_id_485ae7a2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./kt-chat-dialog-panel.vue?vue&type=style&index=0&id=485ae7a2&lang=scss&scoped=true& */ 1117);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _kt_chat_dialog_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _kt_chat_dialog_panel_vue_vue_type_template_id_485ae7a2_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _kt_chat_dialog_panel_vue_vue_type_template_id_485ae7a2_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "485ae7a2",
  null,
  false,
  _kt_chat_dialog_panel_vue_vue_type_template_id_485ae7a2_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1113:
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel.vue?vue&type=template&id=485ae7a2&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_template_id_485ae7a2_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kt-chat-dialog-panel.vue?vue&type=template&id=485ae7a2&scoped=true& */ 1114);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_template_id_485ae7a2_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_template_id_485ae7a2_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_template_id_485ae7a2_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_template_id_485ae7a2_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 1114:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel.vue?vue&type=template&id=485ae7a2&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    ktUserInfoCard: function () {
      return __webpack_require__.e(/*! import() | uni_modules/kantboot/components/kt-user-info-card/kt-user-info-card */ "uni_modules/kantboot/components/kt-user-info-card/kt-user-info-card").then(__webpack_require__.bind(null, /*! @/uni_modules/kantboot/components/kt-user-info-card/kt-user-info-card.vue */ 767))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = !_vm.list || _vm.list.length == 0 || !_vm.loadFinish
  var g1 = g0 ? _vm.$kt.file.byPath("icon/chat.svg") : null
  var a0 =
    _vm.dialog && _vm.dialog.userAccountId && _vm.isCustomerService
      ? {
          id: "-1",
          srcOfAvatar: _vm.$kt.file.byPath("kantboot/icon/customerService.png"),
          nickname: _vm.$i18n.zhToGlobal("官方客服"),
          introduction: _vm.$i18n.zhToGlobal("官方客服"),
        }
      : null
  _vm.$initSSP()
  var l0 = !_vm.loading
    ? _vm.__map(_vm.list, function (item1, index) {
        var $orig = _vm.__get_orig(item1)
        var g2 = _vm.$kt.userAccount.getSelf()
        var m0 = _vm.getPositionId(item1)
        if (_vm.$scope.data.scopedSlotsCompiler === "augmented") {
          _vm.$setSSP("messageItem", {
            item: _vm.item,
            message: _vm.message,
          })
        }
        return {
          $orig: $orig,
          g2: g2,
          m0: m0,
        }
      })
    : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        a0: a0,
        l0: l0,
      },
    }
  )
  _vm.$callSSP()
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 1115:
/*!*************************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kt-chat-dialog-panel.vue?vue&type=script&lang=js& */ 1116);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1116:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 41));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 43));
var _userAccount2 = _interopRequireDefault(__webpack_require__(/*! @/uni_modules/kantboot/libs/userAccount */ 58));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var CardOfUserAccount = function CardOfUserAccount() {
  __webpack_require__.e(/*! require.ensure | uni_modules/kantboot/components/kt-chat-dialog-panel/conponents/card-of-user-account */ "uni_modules/kantboot/components/kt-chat-dialog-panel/conponents/card-of-user-account").then((function () {
    return resolve(__webpack_require__(/*! ./conponents/card-of-user-account.vue */ 1957));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  computed: {
    userAccount: function userAccount() {
      return _userAccount2.default;
    }
  },
  components: {
    CardOfUserAccount: CardOfUserAccount
  },
  props: {
    height: {
      type: String,
      default: ''
    },
    dialogId: {
      type: Number,
      default: ''
    },
    isRead: {
      type: Boolean,
      default: false
    },
    hasRead: {
      type: Boolean,
      default: true
    },
    copy: {
      type: Boolean,
      default: true
    }
  },
  data: function data() {
    return {
      loading: false,
      refresherEnabled: true,
      scrollIntoViewIdFinal: '',
      scrollIntoViewId: 'scrollIntoViewId',
      uuid: null,
      isMoveToEnd: true,
      chatScrollViewHeight: 0,
      chatDialog: {
        id: ''
      },
      list: [],
      loadFinish: false,
      userInfo: {
        fileIdOfAvatar: ''
      },
      dialog: {},
      unreadList: [],
      unreadMap: {},
      maxId: 0,
      minId: 0,
      model: {},
      isBeforeLoading: false,
      // 每次list的长度
      listLength: 20,
      // isTolower,代表是否可以继续加载
      isTolower: true,
      beforePool: [],
      topHeaderHeight: 0,
      isCustomerService: false,
      isStart: false
    };
  },
  watch: {
    dialog: {
      handler: function handler(val) {
        try {
          // 重新渲染
          this.$forceUpdate();
        } catch (e) {}
      },
      immediate: true,
      deep: true
    },
    dialogId: {
      handler: function handler(val) {
        try {
          this.getInit();
          this.getById();
          try {
            // 重新渲染
            this.$forceUpdate();
          } catch (e) {}
        } catch (e) {}
      },
      immediate: true,
      deep: true
    },
    height: {
      handler: function handler(val) {
        try {
          this.getChatScrollViewHeight();
          // 重新渲染
          this.$forceUpdate();
        } catch (e) {}
      },
      immediate: true,
      deep: true
    }
  },
  created: function created() {
    var _this = this;
    this.isStart = true;
    // 生成UUID
    this.uuid = this.$kt.util.generateUUID();
    this.initScrollViewId();
    this.$kt.event.on("FunctionalChatDialogMessage.readDialog", function () {
      if (!_this.isStart) {
        return;
      }
      _this.getUnreadByDialogIdAndUserAccountId();
    });
    this.$kt.event.on("translate:FunctionalChatDialogMessageItem.translate.tooFrequent", function () {
      if (!_this.isStart) {
        return;
      }
      _this.getInit();
    });
    this.$kt.event.on('FunctionalChatDialogMessage:sendMessage', /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(data) {
        var i, _i, flag;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (_this.isStart) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return");
              case 2:
                if (!(data.dialogId + "" === _this.dialogId + "")) {
                  _context.next = 28;
                  break;
                }
                i = 0;
              case 4:
                if (!(i < _this.list.length)) {
                  _context.next = 12;
                  break;
                }
                if (!(_this.list[i].virtualId + "" === data.virtualId + "")) {
                  _context.next = 9;
                  break;
                }
                // 如果是虚拟消息，就替换掉
                _this.list[i] = data;
                try {
                  _this.read();
                  // 重新渲染
                  _this.$forceUpdate();
                } catch (e) {}
                return _context.abrupt("return");
              case 9:
                i++;
                _context.next = 4;
                break;
              case 12:
                setTimeout(function () {
                  _this.read();
                }, 1000);
                _i = 0;
              case 14:
                if (!(_i < _this.list.length)) {
                  _context.next = 22;
                  break;
                }
                // if(this.list[i].id&&this.list[i].id+"" === data.id+""){
                //   alert(123);
                //   this.list[i] = data;
                //   return;
                // }
                flag = false;
                if (_this.list[_i].virtualId && _this.list[_i].virtualId + "" === data.virtualId + "") {
                  // 如果是虚拟消息，就替换掉
                  _this.list[_i] = data;
                  try {
                    _this.read();
                    // 重新渲染
                    _this.$forceUpdate();
                  } catch (e) {}
                  flag = true;
                }
                if (!flag) {
                  _context.next = 19;
                  break;
                }
                return _context.abrupt("break", 22);
              case 19:
                _i++;
                _context.next = 14;
                break;
              case 22:
                _this.unreadMap[data.id + ""] = true;
                console.log("接收到了消息=" + JSON.stringify(data));

                // this.list.push(data);
                // 不包含ID的聊天，也就是说不保存在数据库中的数据，先显示出来
                if (!_this.isExistMessage(data) && !data.id) {
                  _this.list.push(_objectSpread(_objectSpread({}, data), {}, {
                    id: 0
                  }));
                }
                if (data.id) {
                  _this.read();
                  setTimeout(function () {
                    _this.read();
                  }, 1000);
                }
                // 保存在数据库中的数据
                if (!_this.isExistMessage(data) && data.id) {
                  _this.pushReal(data);
                  _this.getUnreadByDialogIdAndUserAccountId();
                  _this.read();
                  setTimeout(function () {
                    _this.read();
                  }, 1000);
                  setTimeout(function () {
                    _this.read();
                  }, 4000);
                }
                _this.moveToEndForce();
              case 28:
              case "end":
                return _context.stop();
            }
          }
        }, _callee);
      }));
      return function (_x) {
        return _ref.apply(this, arguments);
      };
    }());
    this.$kt.event.on('FunctionalChatDialogMessage.setDeletedStatus', /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2(data) {
        var i;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                if (_this.isStart) {
                  _context2.next = 2;
                  break;
                }
                return _context2.abrupt("return");
              case 2:
                if (!(data.dialogId + "" === _this.dialogId + "")) {
                  _context2.next = 13;
                  break;
                }
                i = 0;
              case 4:
                if (!(i < _this.list.length)) {
                  _context2.next = 13;
                  break;
                }
                if (!(_this.list[i].id + "" === data.id + "")) {
                  _context2.next = 10;
                  break;
                }
                if (!(data.deleted === true)) {
                  _context2.next = 10;
                  break;
                }
                _this.list[i].deleted = true;
                _this.list.splice(i, 1);
                return _context2.abrupt("break", 13);
              case 10:
                i++;
                _context2.next = 4;
                break;
              case 13:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }));
      return function (_x2) {
        return _ref2.apply(this, arguments);
      };
    }());
    this.$Kt.event.on('sendMessageSuccess', /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(data) {
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (_this.isStart) {
                  _context3.next = 2;
                  break;
                }
                return _context3.abrupt("return");
              case 2:
                _this.list.push(_objectSpread(_objectSpread({}, data), {}, {
                  id: 0
                }));
              case 3:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }));
      return function (_x3) {
        return _ref3.apply(this, arguments);
      };
    }());
    this.$kt.event.on("appShow", /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
      return _regenerator.default.wrap(function _callee4$(_context4) {
        while (1) {
          switch (_context4.prev = _context4.next) {
            case 0:
              if (_this.isStart) {
                _context4.next = 2;
                break;
              }
              return _context4.abrupt("return");
            case 2:
              _context4.next = 4;
              return _this.getAfter();
            case 4:
            case "end":
              return _context4.stop();
          }
        }
      }, _callee4);
    })));
  },
  // 监听页面销毁
  beforeDestroy: function beforeDestroy() {
    this.isStart = false;
  },
  mounted: function mounted() {
    this.getById();
  },
  methods: {
    getUnreadByDialogIdAndUserAccountId: function getUnreadByDialogIdAndUserAccountId() {
      var _this2 = this;
      // /functional-chat-web/dialogMessage/getUnreadByDialogIdAndUserAccountId
      this.$request.post("/functional-chat-web/dialogMessage/getUnreadByDialogIdAndUserAccountId", {
        data: {
          dialogId: this.dialogId,
          userAccountId: this.dialog.userAccountId
        }
      }).then(function (res) {
        _this2.unreadList = res.data;
        _this2.unreadMap = {};
        for (var i = 0; i < _this2.unreadList.length; i++) {
          _this2.unreadMap[_this2.unreadList[i].messageId + ""] = true;
        }
      }).catch(function (err) {
        uni.showToast({
          title: err.errMsg,
          icon: "none"
        });
      });
    },
    getIsCustomerService: function getIsCustomerService(userAccountId) {
      var _this3 = this;
      return this.$request.post("/functional-chat-web/dialog/isCustomerService", {
        data: {
          userAccountId: userAccountId
        }
      }).then(function (res) {
        _this3.isCustomerService = res.data;
      }).catch(function (res) {
        _this3.isCustomerService = false;
      });
    },
    userCardClick: function userCardClick(userAccountId) {
      this.$emit("userCardClick", userAccountId);
    },
    init: function init() {
      var _this4 = this;
      var topHeaderId = "#top_header_" + this.uuid;
      // 获取navBarPostDetail的高度
      this.$nextTick(function () {
        uni.createSelectorQuery().select(topHeaderId).boundingClientRect(function (res) {
          _this4.topHeaderHeight = res.height;
        }).exec();
      });
    },
    listToIds: function listToIds(list) {
      var ids = [];
      for (var i = 0; i < list.length; i++) {
        ids.push(list[i].id);
      }
      return ids;
    },
    // /functional-chat-web/dialogMessage/getRead
    read: function read() {
      if (!this.dialogId) {
        return;
      }
      this.$kt.request.post("/functional-chat-web/dialogMessage/readByDialogId", {
        data: {
          dialogId: this.dialogId
        }
      }).then(function (res) {}).catch(function (res) {});
    },
    getById: function getById() {
      var _this5 = this;
      if (!this.dialogId) {
        return;
      }
      this.loading = true;
      this.$kt.request.post("/functional-chat-web/dialog/getById", {
        data: {
          id: this.dialogId
        }
      }).then( /*#__PURE__*/function () {
        var _ref5 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5(res) {
          var split, userAccountId1, userAccountId2, userAccountId;
          return _regenerator.default.wrap(function _callee5$(_context5) {
            while (1) {
              switch (_context5.prev = _context5.next) {
                case 0:
                  _this5.dialog = res.data;
                  split = _this5.dialog.oneToOneIdentifier.split("&");
                  userAccountId1 = split[0].split(":")[1];
                  userAccountId2 = split[1].split(":")[1];
                  userAccountId = userAccountId1 + "" === _this5.$kt.userAccount.getSelf().id + "" ? userAccountId2 : userAccountId1;
                  _context5.next = 7;
                  return _this5.getIsCustomerService(userAccountId);
                case 7:
                  _this5.dialog.userAccountId = userAccountId;
                  _this5.$emit("load", _this5.dialog);
                  _this5.init();
                  setTimeout(function () {
                    _this5.init();
                  }, 100);
                  _this5.loading = false;
                  _this5.moveToEndForce();
                  _this5.getUnreadByDialogIdAndUserAccountId();
                case 14:
                case "end":
                  return _context5.stop();
              }
            }
          }, _callee5);
        }));
        return function (_x4) {
          return _ref5.apply(this, arguments);
        };
      }()).catch(function (res) {
        _this5.dialog = res.data;
        _this5.loading = false;
      });
    },
    onRefresherrefresh: function onRefresherrefresh() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                if (!_this6.isTolower) {
                  _this6.refresherEnabled = false;
                }
                setTimeout( /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
                  return _regenerator.default.wrap(function _callee6$(_context6) {
                    while (1) {
                      switch (_context6.prev = _context6.next) {
                        case 0:
                          _this6.refresherEnabled = false;
                          _context6.next = 3;
                          return _this6.getBefore();
                        case 3:
                          setTimeout(function () {
                            if (!_this6.isTolower) {
                              _this6.refresherEnabled = false;
                              return;
                            }
                            _this6.refresherEnabled = true;
                          }, 500);
                        case 4:
                        case "end":
                          return _context6.stop();
                      }
                    }
                  }, _callee6);
                })), 1000);
              case 2:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7);
      }))();
    },
    setScrollIntoViewId: function setScrollIntoViewId(id) {
      var _this7 = this;
      this.scrollIntoViewId = ""; // 清空之前的值
      this.$nextTick(function () {
        _this7.scrollIntoViewId = id; // 确保 DOM 渲染完成后再设置
      });
    },
    onScroll: function onScroll(res) {
      this.isMoveToEnd = false;
      this.$emit('scroll', res);
    },
    onScrolltolower: function onScrolltolower(res) {
      var _this8 = this;
      // this.$emit('scrolltolower', res);
      setTimeout(function () {
        _this8.isMoveToEnd = true;
        console.log(_this8.isMoveToEnd);
      }, 100);
    },
    onScrolltoupper: function onScrolltoupper(res) {
      // this.$emit('scrolltoupper', res);
      // this.getBefore();
    },
    // 移动到最后
    moveToEnd: function moveToEnd() {
      var _this9 = this;
      if (!this.isMoveToEnd) {
        return;
      }
      if (this.loading) {
        return;
      }
      this.scrollIntoViewId = "";
      setTimeout(function () {
        _this9.scrollIntoViewId = "" + _this9.scrollIntoViewIdFinal;
      }, 1);
      setTimeout(function () {
        _this9.scrollIntoViewId = "" + _this9.scrollIntoViewIdFinal;
      }, 100);
    },
    /**
     * 强制移到最底部
     */
    moveToEndForce: function moveToEndForce() {
      this.isMoveToEnd = true;
      this.moveToEnd();
    },
    initScrollViewId: function initScrollViewId() {
      this.uuid = this.$kt.util.generateUUID();
      this.scrollIntoViewIdFinal = 'scrollIntoView_' + this.uuid;
    },
    getPositionId: function getPositionId(item) {
      return "id_" + item.dialogId + "_" + item.id;
    },
    pushReal: function pushReal(data) {
      var virtualId = data.virtualId;
      // 查看有没有重复的虚拟键，有的话先删除
      for (var i = 0; i < this.list.length; i++) {
        if (this.list[i].virtualId == virtualId) {
          this.list.splice(i, 1);
          break;
        }
      }
      this.list.push(data);
    },
    // 往前加
    unshiftBefore: function unshiftBefore(data) {
      var virtualId = data.virtualId;
      // 查看有没有重复的虚拟键，有的话先删除
      for (var i = 0; i < this.list.length; i++) {
        if (this.list[i].virtualId === virtualId) {
          this.list.splice(i, 1);
          break;
        }
      }
      this.list.unshift(data);
    },
    isExistMessage: function isExistMessage(message) {
      for (var i = 0; i < this.list.length; i++) {
        if (this.list[i].id + "" === message.id + "") {
          console.log("消息已存在");
          return true;
        }
      }
      console.log("消息不存在");
      return false;
    },
    /**
     * 获取最大和最小ID
     */
    getMaxAndMinId: function getMaxAndMinId() {
      var maxId = 0;
      var minId = 0;
      for (var i = 0; i < this.list.length; i++) {
        if (this.list[i].id > maxId) {
          maxId = this.list[i].id;
        }
        if (minId === 0 || this.list[i].id < minId) {
          minId = this.list[i].id;
        }
      }
      this.maxId = minId;
      this.minId = maxId;
    },
    getInit: function getInit() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                if (_this10.dialogId) {
                  _context8.next = 2;
                  break;
                }
                return _context8.abrupt("return");
              case 2:
                _this10.loadFinish = false;
                // /functional-chat-web/dialogMessage/getList
                _context8.next = 5;
                return _this10.$request.post('/functional-chat-web/dialogMessage/getList', {
                  data: {
                    dialogId: _this10.dialogId
                  }
                }).then(function (res) {
                  _this10.list = [];
                  if (res.data.length === 0) {
                    _this10.loadFinish = true;
                    return;
                  }
                  // 倒序
                  for (var i = res.data.length - 1; i >= 0; i--) {
                    if (!_this10.isExistMessage(res.data[i])) {
                      _this10.list.push(res.data[i]);
                    }
                  }
                  _this10.read();
                  _this10.moveToEndForce();
                  setTimeout(function () {
                    _this10.loadFinish = true;
                  }, 100);
                  // 获取最大ID和最小ID
                  _this10.getMaxAndMinId();
                }).catch(function (err) {});
              case 5:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8);
      }))();
    },
    getAfter: function getAfter() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                _context9.next = 2;
                return _this11.$request.post('/functional-chat-web/dialogMessage/getList', {
                  data: {
                    dialogId: _this11.dialogId,
                    minId: _this11.minId
                  }
                }).then(function (res) {
                  console.log("获取到消息", JSON.stringify(res.data));
                  if (res.data.length === 0) {
                    return;
                  }
                  console.log("获取到消息", res.data.length);
                  // 倒序
                  for (var i = res.data.length - 1; i >= 0; i--) {
                    if (!_this11.isExistMessage(res.data[i])) {
                      _this11.pushReal(res.data[i]);
                    }
                  }
                  // 获取最大ID和最小ID
                  _this11.getMaxAndMinId();
                  try {
                    // 重新渲染
                    _this11.$forceUpdate();
                  } catch (e) {}
                  _this11.read();

                  // this.toBottom();
                }).catch(function (err) {});
              case 2:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9);
      }))();
    },
    getBefore: function getBefore() {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var data, i;
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                if (!(_this12.isTolower === false)) {
                  _context11.next = 2;
                  break;
                }
                return _context11.abrupt("return");
              case 2:
                if (!_this12.isBeforeLoading) {
                  _context11.next = 4;
                  break;
                }
                return _context11.abrupt("return");
              case 4:
                _this12.isBeforeLoading = true;
                // 如果beforePool中有数据，就直接取5条数据
                if (!(_this12.beforePool.length > 0)) {
                  _context11.next = 11;
                  break;
                }
                data = _this12.beforePool.splice(0, 5);
                for (i = 0; i < data.length; i++) {
                  _this12.unshiftBefore(data[i]);
                }
                // 获取最大ID和最小ID
                _this12.getMaxAndMinId();
                _this12.isBeforeLoading = false;
                return _context11.abrupt("return");
              case 11:
                _context11.next = 13;
                return _this12.$request.post('/functional-chat-web/dialogMessage/getList', {
                  data: {
                    dialogId: _this12.dialogId,
                    maxId: _this12.maxId
                  }
                }).then( /*#__PURE__*/function () {
                  var _ref7 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10(res) {
                    var firstId, _i2;
                    return _regenerator.default.wrap(function _callee10$(_context10) {
                      while (1) {
                        switch (_context10.prev = _context10.next) {
                          case 0:
                            // 获取当前第一个消息的定位ID
                            firstId = _this12.getPositionId(_this12.list[0]);
                            console.log("CHAT 第一个消息的定位ID", firstId);
                            console.log("获取到消息", JSON.stringify(res.data));
                            if (!(res.data.length < _this12.listLength)) {
                              _context10.next = 8;
                              break;
                            }
                            _this12.isTolower = false;
                            _this12.refresherEnabled = false;
                            _this12.isBeforeLoading = false;
                            return _context10.abrupt("return");
                          case 8:
                            // if (res.data.length === 0) {
                            //   this.isBeforeLoading = false;
                            //   return;
                            // }
                            console.log("获取到消息", res.data.length);
                            // 倒序
                            for (_i2 = 0; _i2 < res.data.length; _i2++) {
                              // if(!this.isExistMessage(res.data[i])) {
                              //   this.unshiftBefore(res.data[i]);
                              //   console.log("CHAT 获取到消息2",firstId);
                              // }
                              // 取出前5条数据，其余的放入beforePool中
                              if (_i2 < 5) {
                                _this12.unshiftBefore(res.data[_i2]);
                              } else {
                                _this12.beforePool.push(res.data[_i2]);
                              }
                            }
                            _this12.read();
                            // setTimeout(()=>{
                            //   // 获取res.data.length，如果大于三个，就定位到倒数第三个
                            //   if(res.data.length >= 3){
                            //     this.setScrollIntoViewId(res.data[res.data.length - 3]);
                            //   }
                            //   // 如果小于三个，就定位到第一个
                            //   if(res.data.length < 3){
                            //     this.setScrollIntoViewId(res.data[0]);
                            //   }
                            //
                            // },10)

                            // 获取最大ID和最小ID
                            _this12.getMaxAndMinId();
                            _this12.isBeforeLoading = false;
                          case 13:
                          case "end":
                            return _context10.stop();
                        }
                      }
                    }, _callee10);
                  }));
                  return function (_x5) {
                    return _ref7.apply(this, arguments);
                  };
                }()).catch(function (err) {});
              case 13:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11);
      }))();
    },
    // 获取聊天列表的高度
    getChatScrollViewHeight: function getChatScrollViewHeight() {}
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 1117:
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel.vue?vue&type=style&index=0&id=485ae7a2&lang=scss&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_style_index_0_id_485ae7a2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kt-chat-dialog-panel.vue?vue&type=style&index=0&id=485ae7a2&lang=scss&scoped=true& */ 1118);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_style_index_0_id_485ae7a2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_style_index_0_id_485ae7a2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_style_index_0_id_485ae7a2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_style_index_0_id_485ae7a2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_dialog_panel_vue_vue_type_style_index_0_id_485ae7a2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1118:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel.vue?vue&type=style&index=0&id=485ae7a2&lang=scss&scoped=true& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel-create-component',
    {
        'uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(1112))
        })
    },
    [['uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel-create-component']]
]);
