@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.header-box.data-v-2a3fa8c6 {
  text-align: center;
}
.header-box .header-item.data-v-2a3fa8c6 {
  width: 33.33%;
  font-size: 28rpx;
  display: inline-block;
  cursor: pointer;
}
.header-box .header-item-active.data-v-2a3fa8c6 {
  color: #000000;
  font-weight: bold;
}
.loading-icon-2.data-v-2a3fa8c6 {
  width: 100px;
  height: 100px;
}
.box.data-v-2a3fa8c6 {
  padding: 20rpx;
  box-sizing: border-box;
}
.input-box.data-v-2a3fa8c6 {
  position: relative;
  background-color: #f8f8f8;
  padding: 10rpx;
  border-radius: 10rpx;
}
.input-box .input-box-icon.data-v-2a3fa8c6 {
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  left: 20rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.input-box .input-box-input.data-v-2a3fa8c6 {
  display: inline-block;
  width: calc(100% - 100rpx);
  margin-left: 60rpx;
}
.input-box .input-box-icon-clear.data-v-2a3fa8c6 {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  right: 20rpx;
  top: 50%;
  opacity: 0.2;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 1;
}
.item-no-selected.data-v-2a3fa8c6 {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.scroll-view.data-v-2a3fa8c6 {
  overflow: scroll;
  /* 允许滚动 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
}
.scroll-view.data-v-2a3fa8c6::-webkit-scrollbar {
  display: none !important;
}
