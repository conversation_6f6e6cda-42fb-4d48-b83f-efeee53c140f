<view class="data-v-16bc6d1d"><scroll-view class="{{['data-v-16bc6d1d',clazz.userRecommendPanel]}}" style="{{'height:'+(height)+';'}}" scroll-y="{{true}}" scroll-with-animation="{{true}}" show-scrollbar="{{false}}" refresher-triggered="{{refresherTriggered}}" refresher-enabled="{{false}}" lower-threshold="{{$root.g0}}" data-event-opts="{{[['scrolltolower',[['getAfter',['$event']]]],['refresherrefresh',[['onRefresherrefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="meet-user-match-card-box data-v-16bc6d1d"><project-meet-user-match-card bind:toLogin="__e" vue-id="7e5c90ee-1" data-ref="userMatchCard" data-event-opts="{{[['^toLogin',[['toLogin']]]]}}" class="data-v-16bc6d1d vue-ref" bind:__l="__l"></project-meet-user-match-card></view><view class="user-list-box data-v-16bc6d1d"><block wx:for="{{userList}}" wx:for-item="item" wx:for-index="__i0__"><view class="{{['data-v-16bc6d1d',clazz.userBox]}}"><project-meet-user-info-out-card class="card-in-user-box data-v-16bc6d1d" vue-id="{{'7e5c90ee-2-'+__i0__}}" userInfo="{{item}}" data-event-opts="{{[['^userClick',[['toUserInfoPage',['$0'],[[['userList','',__i0__]]]]]]]}}" bind:userClick="__e" bind:__l="__l"></project-meet-user-info-out-card></view></block><block wx:if="{{alfterLoading}}"><u-loading-icon vue-id="7e5c90ee-3" mode="circle" size="50rpx" class="data-v-16bc6d1d" bind:__l="__l"></u-loading-icon></block><block wx:if="{{isBottom}}"><view style="text-align:center;color:#999999;font-size:24rpx;" class="data-v-16bc6d1d">{{''+$root.g1+''}}</view></block><view style="height:140rpx;" class="data-v-16bc6d1d"></view></view></scroll-view><project-meet-user-info-popup vue-id="7e5c90ee-4" data-ref="usPopup" class="data-v-16bc6d1d vue-ref" bind:__l="__l"></project-meet-user-info-popup></view>