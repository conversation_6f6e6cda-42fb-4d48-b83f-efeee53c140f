@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.bg.data-v-34741968 {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}
.bg-mode-color-scheme-light.data-v-34741968 {
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}
.bg-mode-color-scheme-dark.data-v-34741968 {
  background: #191919;
}
.header-box-1.data-v-34741968 {
  padding: 0 10rpx;
  box-sizing: border-box;
  white-space: nowrap;
}
.header-box-1 .header-item.data-v-34741968 {
  display: inline-block;
  font-size: 36rpx;
  letter-spacing: 3rpx;
  margin-right: 5rpx;
  padding: 10rpx 20rpx;
}
.header-box-1 .header-item-selected.data-v-34741968 {
  font-weight: bold;
}
.scroll-view.data-v-34741968 {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scroll-view.data-v-34741968::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}
.scroll-view.data-v-34741968::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0);
  border-radius: 0;
}
.data-v-34741968::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}
.second-box.data-v-34741968 {
  width: 100%;
}
.second-box .second-box-box.data-v-34741968 {
  width: calc(100% - 100rpx);
  white-space: nowrap;
  overflow-x: auto;
}
.second-box .second-box-box .second-box-item.data-v-34741968 {
  display: inline-block;
  font-size: 32rpx;
  letter-spacing: 3rpx;
  margin-right: 5rpx;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}
.second-box .second-box-box .second-box-item-selected.data-v-34741968 {
  font-weight: bold;
}
.container-mode-device-pc.data-v-34741968 {
  width: calc(100% - 240px - 400px);
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
}
.split.data-v-34741968 {
  width: 100%;
  height: 3rpx;
  background-color: #eee;
}
.container-mode-color-scheme-dark.data-v-34741968 {
  background-color: #191919;
}
.container-mode-color-scheme-dark .split.data-v-34741968 {
  background-color: #888888;
}
.container-mode-color-scheme-dark .header-box.data-v-34741968 {
  background-color: #191919;
}
.container-mode-color-scheme-dark .scroll-view.data-v-34741968 {
  background-color: #191919;
}
.container-mode-color-scheme-dark .second-icon.data-v-34741968 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.container-mode-color-scheme-dark .header-box-1.data-v-34741968 {
  background-color: #191919;
}
.container-mode-color-scheme-dark .header-box-1 .header-item.data-v-34741968 {
  color: #fff;
}
.container-mode-color-scheme-dark .second-box .second-box-box .second-box-item.data-v-34741968 {
  color: #fff;
}
.container-mode-color-scheme-dark .second-box .second-box-box .second-box-item-selected.data-v-34741968 {
  color: #fff;
  font-weight: bold;
}
.container-mode-device-pc.data-v-34741968 {
  position: relative;
  width: calc(100% + 20px);
  padding: 0;
  box-sizing: border-box;
}
.container-mode-device-pc .second-icon.data-v-34741968 {
  margin-right: 10px;
}
