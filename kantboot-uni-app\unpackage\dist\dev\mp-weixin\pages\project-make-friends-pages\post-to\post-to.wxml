<view class="{{['data-v-34741968',clazz.container]}}"><view class="{{['data-v-34741968',clazz.bg]}}"></view><view style="width:100%;top:0;left:0;" id="pageHomeHeader" class="data-v-34741968"><view class="data-v-34741968"><kt-nav-bar vue-id="5a0e118c-1" title="{{$root.g0}}" class="data-v-34741968" bind:__l="__l"></kt-nav-bar></view><view class="data-v-34741968"><view class="split data-v-34741968"></view><view class="second-box data-v-34741968" style="position:relative;padding:11rpx;"><view class="second-box-box data-v-34741968"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['second-box-item','data-v-34741968',(selected==='friend')?'second-box-item-selected':'']}}" bindtap="__e">{{$root.g1+''}}</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['second-box-item','data-v-34741968',(selected==='privateFriend')?'second-box-item-selected':'']}}" bindtap="__e">{{$root.g2+''}}</view></view><image class="second-icon data-v-34741968" style="width:40rpx;height:40rpx;position:absolute;top:50%;transform:translateY(-50%);right:50rpx;" src="{{$root.g3}}"></image></view></view></view><view class="data-v-34741968"><swiper style="{{'height:'+($root.m0?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px)')+';'}}" current-item-id="{{selected}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e" class="data-v-34741968"><swiper-item item-id="recommend" class="data-v-34741968"><project-make-friends-recommend-panel vue-id="5a0e118c-2" height="{{$root.m1?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'}}" class="data-v-34741968" bind:__l="__l"></project-make-friends-recommend-panel></swiper-item><swiper-item item-id="friend" class="data-v-34741968"><kt-community-post-panel vue-id="5a0e118c-3" height="{{$root.m2?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'}}" data-ref="communityPostPanel" data-event-opts="{{[['^cardClick',[['cardClick']]],['^cardDotClick',[['cardDotClick']]]]}}" bind:cardClick="__e" bind:cardDotClick="__e" class="data-v-34741968 vue-ref" bind:__l="__l"></kt-community-post-panel></swiper-item><swiper-item item-id="privateFriend" class="data-v-34741968"><kt-community-post-panel vue-id="5a0e118c-4" mode="mutualFollow" height="{{$root.m3?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'}}" data-ref="communityPostPanel" data-event-opts="{{[['^cardClick',[['cardClick']]],['^cardDotClick',[['cardDotClick']]]]}}" bind:cardClick="__e" bind:cardDotClick="__e" class="data-v-34741968 vue-ref" bind:__l="__l"></kt-community-post-panel></swiper-item></swiper><kt-community-post-operate-popup vue-id="5a0e118c-5" data-ref="communityPostOperatePopup" class="data-v-34741968 vue-ref" bind:__l="__l"></kt-community-post-operate-popup></view><community-post-popup vue-id="5a0e118c-6" data-ref="communityPostPopup" class="data-v-34741968 vue-ref" bind:__l="__l"></community-post-popup></view>