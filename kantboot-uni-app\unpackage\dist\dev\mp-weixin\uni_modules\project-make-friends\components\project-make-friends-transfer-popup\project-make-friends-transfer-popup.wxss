@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-68270962 {
  width: 100vw;
  height: 90vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;
}
.box .title.data-v-68270962 {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
  padding-top: 20rpx;
}
.box .box-search.data-v-68270962 {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.box .search-box.data-v-68270962 {
  padding: 0 10rpx;
}
.box .search-box .search-btn.data-v-68270962 {
  background-color: #000000;
  height: 60rpx;
  line-height: 60rpx;
  color: #ffffff;
  font-size: 28rpx;
  margin-left: 10rpx;
}
.box .search-box .search-btn[disabled].data-v-68270962 {
  background-color: #cccccc;
  color: #ffffff;
  opacity: 0.6;
}
.box .member-list.data-v-68270962 {
  flex: 1;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.box .member-list .loading-box.data-v-68270962 {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}
.box .member-list .empty-tip.data-v-68270962 {
  text-align: center;
  color: #999;
  padding: 60rpx 0;
  font-size: 28rpx;
}
.box .member-list .member-item.data-v-68270962 {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.box .member-list .member-item-selected.data-v-68270962 {
  background-color: 0;
}
.box .member-list .member-item .member-info.data-v-68270962 {
  display: flex;
  align-items: center;
}
.box .member-list .member-item .member-info checkbox.data-v-68270962 {
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  margin-right: 10rpx;
}
.box .member-list .member-item .member-info .member-avatar.data-v-68270962 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.box .member-list .member-item .member-info .member-details.data-v-68270962 {
  flex: 1;
}
.box .member-list .member-item .member-info .member-details .member-nickname.data-v-68270962 {
  font-size: 30rpx;
  margin-bottom: 6rpx;
}
.box .member-list .member-item .member-info .member-details .member-id.data-v-68270962,
.box .member-list .member-item .member-info .member-details .member-phone.data-v-68270962 {
  font-size: 26rpx;
  color: #666;
}
.box .member-list .load-more.data-v-68270962 {
  text-align: center;
  color: #000000;
  padding: 30rpx 0;
  font-size: 28rpx;
}
.box .member-list .no-more.data-v-68270962 {
  text-align: center;
  color: #999;
  padding: 30rpx 0;
  font-size: 28rpx;
}
.box .footer.data-v-68270962 {
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}
.box .footer .selected-count.data-v-68270962 {
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 20rpx;
}
.box .footer .buttons.data-v-68270962 {
  display: flex;
  justify-content: space-between;
}
.box .footer .buttons button.data-v-68270962 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
}
.box .footer .buttons button.cancel-btn.data-v-68270962 {
  background-color: #f5f5f5;
  color: #000000;
  margin-right: 20rpx;
}
.box .footer .buttons button.confirm-btn.data-v-68270962 {
  background-color: #000000;
  color: #fff;
}
.box .footer .buttons button.confirm-btn[disabled].data-v-68270962 {
  background-color: #cccccc;
}
.close-btn.data-v-68270962 {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
  z-index: 10;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
