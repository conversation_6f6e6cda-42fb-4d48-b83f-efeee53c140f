<template>
  <view>
    <kt-popup :overlayClose="true" ref="ktPopup" @close="close" :zIndex="999999999">
      <view class="box">
        <view class="close-btn" @click="closePop">×</view>
        <view class="title">{{ $i18n.zhToGlobal("选择会员") }}</view>

         <!-- 搜索框 -->
         <view class="box-search">
          <view class="search-box">
            <u-input :placeholder="$i18n.zhToGlobal('输入ID或手机号搜索')" @confirm="onSearchInput" v-model="searchText">
              <template slot="suffix">
                <button @tap="onSearchInput" class="search-btn" :disabled="!searchText.trim()">{{ $i18n.zhToGlobal('搜索') }}</button>
              </template>
            </u-input>
          </view>
        </view>

        <!-- 会员列表 -->
        <scroll-view scroll-y class="member-list" @scrolltolower="loadMore">
          <view v-if="loading" class="loading-box">
            <u-loading mode="circle" size="28"></u-loading>
          </view>

          <view v-else-if="memberList.length === 0" class="empty-tip">
            {{ $i18n.zhToGlobal('暂无会员数据') }}
          </view>

          <checkbox-group @change="onCheckboxChange">
            <view v-for="(item, index) in memberList" :key="item.id" class="member-item"
              :class="{ 'member-item-selected': selectedIds.includes(item.id) }">
              <view class="member-info">
                <checkbox :value="item.id.toString()" :checked="selectedIds.includes(item.id)" />
                <image class="member-avatar"
                  :src="item.fileIdOfAvatar ? $kt.file.visit(item.fileIdOfAvatar) : '/static/default-avatar.png'"
                  mode="aspectFill"></image>
                <view class="member-details">
                  <view class="member-nickname">{{ item.nickname || $i18n.zhToGlobal('用户') }}</view>
                  <view class="member-id">ID: {{ item.id }}</view>
                  <view class="member-phone" v-if="item.phone">{{ item.phoneAreaCode }} {{ item.phone }}</view>
                </view>
              </view>
            </view>
          </checkbox-group>

          <view v-if="hasMore && !loading" class="load-more" @click="loadMore">
            {{ $i18n.zhToGlobal('加载更多') }}
          </view>

          <view v-if="!hasMore && memberList.length > 0" class="no-more">
            {{ $i18n.zhToGlobal('没有更多数据了') }}
          </view>
        </scroll-view>

        <!-- 底部操作按钮 -->
        <view class="footer">
          <view class="selected-count" v-if="selectedIds.length > 0">
            {{ $i18n.zhToGlobal('已选择') }}: {{ selectedIds.length }}
          </view>
          <view class="buttons">
            <button class="confirm-btn" :disabled="selectedIds.length === 0" @click="confirmSelection">{{
              $i18n.zhToGlobal('确定') }}</button>
          </view>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchText: '',
      memberList: [],
      selectedIds: [],
      pageNum: 1,
      pageSize: 20,
      hasMore: true,
      loading: false,
      searchTimer: null,
      excludeIds: []
    };
  },
  methods: {
    open(ids) {
      this.excludeIds = ids || [];
      this.$refs.ktPopup.open();
      this.reset();
      this.loadMembers();
    },

    close() {
      this.reset();
    },

    closePop() {
      this.$refs.ktPopup.close();
    },

    reset() {
      this.searchText = '';
      this.memberList = [];
      this.selectedIds = [];
      this.pageNum = 1;
      this.hasMore = true;
    },

    onSearchInput(e) {
      // 防抖处理
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      this.searchTimer = setTimeout(() => {
        this.pageNum = 1;
        this.memberList = [];
        this.hasMore = true;
        this.loadMembers();
      }, 500);
    },

    clearSearch() {
      this.searchText = '';
      this.pageNum = 1;
      this.memberList = [];
      this.hasMore = true;
      this.loadMembers();
    },

    loadMembers() {
      if (this.loading || !this.hasMore) return;

      this.loading = true;
      this.$request.post("/project-make-friends-web/userTransferConsent/getMyMembers", {
        data: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          searchText: this.searchText,
          excludeIds: this.excludeIds
        }
      }).then(res => {
        this.loading = false;
        if (res.state === 2000) {
          const data = res.data || [];

          // 添加新数据到列表
          this.memberList = [...this.memberList, ...data];

          // 判断是否还有更多数据
          this.hasMore = data.length >= this.pageSize;

          // 页码加1
          if (this.hasMore) {
            this.pageNum++;
          }
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('获取会员列表失败'),
            icon: 'none'
          });
        }
      }).catch(err => {
        this.loading = false;
        console.error(err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
      });
    },

    loadMore() {
      this.loadMembers();
    },

    onCheckboxChange(e) {
      this.selectedIds = e.detail.value.map(id => parseInt(id));
    },

    confirmSelection() {
      if (this.selectedIds.length === 0) {
        return;
      }

      // 获取选中的会员数据
      const selectedMembers = this.memberList.filter(item => this.selectedIds.includes(item.id));

      // 触发确认事件
      this.$emit('confirm', selectedMembers);

      // 关闭弹窗
      this.closePop();
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 90vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
    padding-top: 20rpx;
  }

  .box-search {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 20rpx 0;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  }

  .search-box {
    padding: 0 10rpx;

    .search-btn {
      background-color: #000000;
      height: 60rpx;
      line-height: 60rpx;
      color: #ffffff;
      font-size: 28rpx;
      margin-left: 10rpx;

      &[disabled] {
        background-color: #cccccc;
        color: #ffffff;
        opacity: 0.6;
      }
    }
  }

  .member-list {
    flex: 1;
    overflow: hidden;
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .loading-box {
      display: flex;
      justify-content: center;
      padding: 40rpx 0;
    }

    .empty-tip {
      text-align: center;
      color: #999;
      padding: 60rpx 0;
      font-size: 28rpx;
    }

    .member-item {
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &-selected {
        background-color: 000000;
      }

      .member-info {
        display: flex;
        align-items: center;

        checkbox {
          transform: scale(0.8);
          margin-right: 10rpx;
        }

        .member-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .member-details {
          flex: 1;

          .member-nickname {
            font-size: 30rpx;
            margin-bottom: 6rpx;
          }

          .member-id,
          .member-phone {
            font-size: 26rpx;
            color: #666;
          }
        }
      }
    }

    .load-more {
      text-align: center;
      color: #000000;
      padding: 30rpx 0;
      font-size: 28rpx;
    }

    .no-more {
      text-align: center;
      color: #999;
      padding: 30rpx 0;
      font-size: 28rpx;
    }
  }

  .footer {
    padding: 20rpx 0;
    border-top: 1rpx solid #f0f0f0;
    margin-top: 20rpx;

    .selected-count {
      font-size: 28rpx;
      color: #000000;
      margin-bottom: 20rpx;
    }

    .buttons {
      display: flex;
      justify-content: space-between;

      button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 30rpx;
        border-radius: 40rpx;

        &.cancel-btn {
          background-color: #f5f5f5;
          color: #000000;
          margin-right: 20rpx;
        }

        &.confirm-btn {
          background-color: #000000;
          color: #fff;

          &[disabled] {
            background-color: #cccccc;
          }
        }
      }
    }
  }
}

.close-btn {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
  z-index: 10;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
</style>
