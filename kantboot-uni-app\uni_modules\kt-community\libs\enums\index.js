import $kt from '@/uni_modules/kantboot'

let result = {};

/**
 * @description 文章的可见类型
 */
let visibleTypeEnum = {
    public: $kt.i18n.zhToGlobal("公开"),
    mutualFollow: $kt.i18n.zhToGlobal("互相关注可见"),
    private: $kt.i18n.zhToGlobal("私密")
}


/**
 * @description 文章的可见范围
 */
let visibleScopeEnum = {
    all: $kt.i18n.zhToGlobal("所有的人可见"),
    part: $kt.i18n.zhToGlobal("部分的人可见"),
    not: $kt.i18n.zhToGlobal("部分的人不可见")
}

/**
 * 根据编码获取文章的可见类型
 */
result.getVisibleType = function (code) {
    try{
        return visibleTypeEnum[code] || $kt.i18n.zhToGlobal("未知类型")
    }catch (e) {
        console.error(e)
        return $kt.i18n.zhToGlobal("未知类型")
    }
}

/**
 * 根据编码获取文章的可见范围
 */
result.getVisibleScope = function (code) {
    try{
        return visibleScopeEnum[code] || $kt.i18n.zhToGlobal("未知范围")
    }catch (e) {
        console.error(e)
        return $kt.i18n.zhToGlobal("未知范围")
    }
}

/**
 * 获取所有的文章的可见类型
 */
result.getVisibleTypeList = function () {
    let list = []
    for (let key in visibleTypeEnum) {
        list.push({
            code: key,
            name: visibleTypeEnum[key]
        })
    }
    return list
}

/**
 * 获取所有的文章的可见范围
 */
result.getVisibleScopeList = function () {
    let list = []
    for (let key in visibleScopeEnum) {
        list.push({
            code: key,
            name: visibleScopeEnum[key]
        })
    }
    return list
}

export default result
