@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.bg.data-v-006f6cc7 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-color: #f0f0f0;
}
.footer.data-v-006f6cc7 {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 9999;
  background-color: #FFFFFF;
}
.header.data-v-006f6cc7 {
  background-color: #FFFFFF;
}
.panel-box.data-v-006f6cc7 {
  position: relative;
}
.panel-box .charge-button.data-v-006f6cc7 {
  position: absolute;
  top: 20rpx;
  right: 0;
  z-index: 10000;
  color: #FFFFFF;
  font-size: 28rpx;
  background-color: #F6A496;
  padding: 10rpx 10rpx 10rpx 30rpx;
  border-radius: 30rpx 0 0 30rpx;
}
.panel-box .remark-button.data-v-006f6cc7 {
  position: absolute;
  top: 90rpx;
  right: 0;
  z-index: 10000;
  color: #FFFFFF;
  font-size: 28rpx;
  background-color: rgba(0, 0, 0, 0.8);
  padding: 10rpx 10rpx 10rpx 30rpx;
  border-radius: 30rpx 0 0 30rpx;
}
.charge-button.data-v-006f6cc7:active {
  opacity: 0.8;
}
.input-icon.data-v-006f6cc7 {
  width: 40rpx;
  height: 40rpx;
  margin-left: 30rpx;
  vertical-align: top;
  margin-top: -7rpx;
}
.kt-checkbox.data-v-006f6cc7 {
  position: absolute;
  display: inline-block;
  text-align: center;
  border-radius: 25rpx;
  cursor: pointer;
  right: 20rpx;
  bottom: 35rpx;
  font-size: 28rpx;
  border: 1rpx solid #f0f0f0;
  padding: 0 20rpx;
  max-width: 160rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}
.kt-checkbox .input-language-icon.data-v-006f6cc7 {
  width: 30rpx;
  height: 30rpx;
  vertical-align: middle;
  margin-right: 10rpx;
  margin-top: -10rpx;
}
.top-left-btn.data-v-006f6cc7 {
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #cccccc;
  cursor: pointer;
}
.top-left-btn.data-v-006f6cc7:active {
  background-color: #f0f0f0;
}
