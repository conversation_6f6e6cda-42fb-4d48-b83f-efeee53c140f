<template>
  <view>
    <view v-if="loading">
      <view style="height: 20rpx"></view>
      <u-loading-icon
          mode="circle"
          :size="50"
      ></u-loading-icon>
    </view>
<view v-if="!loading&&courseItems&&courseItems.length>0">
  <view class="box">
    <kt-format
        :data="[{
        type: 'video:id',
        content:courseItemSelected.content
      }]"
    ></kt-format>
  </view>
  <view class="box box-2">
    <view class="box-title">
      {{ course.title }}
    </view>
    <view class="box-desc">
      {{ $i18n.zhToGlobal("课程简介") }}{{ ": " }}{{ course.description }}
    </view>

  </view>
  <view class="box box-3">
    <view class="scroll-view-x">
      <view
          v-for="(item,index) in courseItems"
          class="view-item"
          :class="{
              'view-item-selected': item.id === courseItemSelected.id
            }"
          @click="selectCourseItem(item)"
      >
        <view class="item-index">
          {{ "NO." }}{{ index + 1 }}
        </view>
        <view class="item-title">
          {{ item.title }}
        </view>
      </view>
    </view>
  </view>
  <view
      v-if="courseItemSelected.fileIdOfCourseware"
      class="box button-box">
    <kt-button
        ref="previewButton"
        @click="preview(courseItemSelected.fileIdOfCourseware)"
    >{{$i18n.zhToGlobal("预览课件")}}</kt-button>
  </view>

</view>
    <view
        class="box-2"
        v-if="!loading&&(!courseItems||courseItems.length===0)">
      <view style="height: 100rpx"></view>
      <u-empty
          textSize="32rpx"
          :text="$kt.i18n.zhToGlobal('课件为空')"
          mode="list">
      </u-empty>
    </view>

  </view>
</template>

<script>
export default {
  props: {
    courseId: {
      type: String || Number,
      default: '666962793648133'
    }
  },
  data() {
    return {
      loading: false,
      course: null,
      courseItems: [],
      courseItemSelected: null,
    };
  }
  ,
  async mounted() {
    this.loading = true;
    await this.getCourse();
    await this.getCourseItems();
    this.loading = false;
  },
  methods: {
    getCourse() {
      return this.$kt.request.get('/fp-course-web/course/getById', {
        data: {id: this.courseId}
      }).then(res => {
        this.course = res.data;
      })
    },
    getCourseItems() {
      return this.$kt.request.get('/fp-course-web/courseItem/getByCourseId', {
        data: {courseId: this.courseId}
      }).then(res => {
        let courseItems = res.data;
        // 根据sort排序
        courseItems.sort((a, b) => {
          return a.sort - b.sort;
        });
        this.courseItems = courseItems;
        this.courseItemSelected = this.courseItems[0];
      })
    },
    selectCourseItem(item) {
      this.courseItemSelected = item;
    },
    preview(fileId) {
      // #ifdef H5
      this.$kt.router.toWebview(this.$kt.file.visit(fileId));
      // #endif

      // #ifdef MP-WEIXIN
      this.$refs.previewButton.loading(null,99999);
      uni.downloadFile({
        url: this.$kt.file.visit(fileId),
        success: (res) => {
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              showMenu: true, //显示右上角三个点，支持手动保存到本地
              success: (res) => {
                this.$refs.previewButton.success(this.$i18n.zhToGlobal('预览成功'),1000);
              },
              fail: (err) => {
                this.$refs.previewButton.error(this.$i18n.zhToGlobal('预览失败'));
              }
            })
          }
        },
        fail: (err) => {
          this.$refs.previewButton.error(this.$i18n.zhToGlobal('预览失败'));
        }
      });
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
}

.video {
  width: 100%;
  height: 400rpx;
}

.scroll-view-x {
  width: 100%;
  // 超出不换行，滚动
  overflow-x: auto;
  white-space: nowrap;

  .view-item {
    display: inline-block;
    width: 270rpx;
    background-color: #f6f7f8;
    padding: 20rpx;
    border-radius: 20rpx;
    color: #666666;
    margin-right: 20rpx;
    // 超出不换行，省略号
    text-overflow: ellipsis;

    .item-index {
      font-size: 26rpx;
    }

    .item-title {
      font-size: 28rpx;
    }
  }

  .view-item-selected {
    box-sizing: border-box;
    color: #303133;
    font-weight: bold;
  }
}

.scroll-view-x::-webkit-scrollbar {
  display: none;
}

.box-2 {
  height: 150rpx;
  padding: 0 10rpx 0 10rpx;

  .box-tag {
    display: inline-block;
    color: #666666;
    font-size: 28rpx;
    vertical-align: top;
    margin-top: 2rpx;
    margin-right: 10rpx;
    background-color: #f6f7f8;
    padding: 0 10rpx;
    border-radius: 20rpx;
  }

  .box-title {
    color: #18191c;
    font-size: 32rpx;
    font-weight: bold;
  }

  .box-desc {
    margin-top: 10rpx;
    color: #666666;
    font-size: 28rpx;
  }
}

.box-3{
  padding: 0 20rpx 0 20rpx;
  box-sizing: border-box;
}

.button-box{
  padding: 90rpx;
  box-sizing: border-box;
}
</style>
