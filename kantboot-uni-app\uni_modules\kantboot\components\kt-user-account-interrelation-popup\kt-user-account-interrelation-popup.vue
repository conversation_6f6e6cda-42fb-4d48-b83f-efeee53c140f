<template>
  <view>
    <kt-popup
        :height="height"
        @close="close"
        ref="ktPopup">
      <view :class="clazz.box">
        <view style="height: 20rpx"></view>
        <view class="title">
          {{$i18n.zhToGlobal("用户卡片")}}
        </view>
        <view style="height: 20rpx"></view>
        <view
            v-if="isLogin"
            class="panel-box">
          <kt-user-account-interrelation-panel
              ref="userAccountInterrelationPanel"
              :many-select="manySelect"
              :id-selected-list-prop="idSelectedList"
              :user-account-id="userAccountSelf.id"
              :height="'calc('+height+' - '+'100rpx)'"
              :init-code="initCode"
              @select="select"
          ></kt-user-account-interrelation-panel>
        </view>
        <view
            v-if="manySelect"
            class="bottom-btn-box">
          <kt-button @click="close()">{{$i18n.zhToGlobal("确定选中")}}</kt-button>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
import userAccount from "@/uni_modules/kantboot/libs/userAccount";

export default {
  computed: {
    userAccount() {
      return userAccount
    }
  },
  props: {
    height: {
      type: String,
      default: "calc(100vh - 300rpx)"
    },
    // 是否选择多个
    manySelect: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return {
      popupShow:false,
      idSelectedList:[],
      clazz:{
        box: this.$kt.style.toggleClass("box")
      },
      isLogin:false,
      userAccountSelf: false,
      initCode:""
    }
  },
  mounted() {
    this.isLogin = this.$kt.userAccount.getIsLogin();
    this.userAccountSelf = this.$kt.userAccount.getSelf();
  },
  methods: {
    setIdSelectedList(idSelectedList){
      this.idSelectedList = idSelectedList;
    },
    select(userAccount){
      this.$emit("select",userAccount);
      if(!this.manySelect){
        this.close();
      }
    },
    selectMany(idSelectedList){
      this.$emit("selectMany",idSelectedList);
      console.log("idSelectedList", idSelectedList);
    },
    open(initCode){
      if(initCode){
        this.initCode = initCode;
      }
      this.popupShow = true;
      this.$refs.ktPopup.open();
    },
    close(){
      this.popupShow = false;
      this.$refs.ktPopup.close();
      this.$emit("selectMany", this.$refs.userAccountInterrelationPanel.idSelectedList);
    },
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 20rpx;
  box-sizing: border-box;
  .panel-box{
    height: calc(100vh - 350rpx);
  }

  .title {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 20rpx;
    text-align: center;
  }
}

.bottom-btn-box{
  padding: 20rpx;
}

.box-mode-device-pc{
  border-radius: 20rpx;
  .panel-box{
    height: 500px;
  }
}
</style>