<template>
  <view>
    <kt-nav-bar
        :is-has-i18n="false"
        :title="$i18n.zhToGlobal('个人二维码')"
    ></kt-nav-bar>
    <view :class="clazz.bg"></view>
    <view class="fixed-box">
      <kt-user-info-card
          :user-account-id="$kt.userAccount.getSelf().id"
      ></kt-user-info-card>
      <kt-qrcode
          size="500rpx"
          :content="jsonStringify(content)"
      ></kt-qrcode>
      <view class="tips">
        {{$i18n.zhToGlobal('请使用本软件的扫一扫，不能使用微信的扫一扫')}}
      </view>
      <view class="tips">
        {{$i18n.zhToGlobal('扫描本码是本软件内部互相加好友使用')}}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        bg: this.$kt.style.toggleClass("bg"),
      },
      content:{
        // 操作编码
        operateCode: 'user-qrcode',
        // 用户账号id
        userAccountId: this.$kt.userAccount.getSelf().id
      }
    };
  },
  mounted() {
    this.content.userAccountId = this.$kt.userAccount.getSelf().id;
  },
  methods: {
    jsonStringify(obj) {
      return JSON.stringify(obj);
    }
  },
}
</script>

<style lang="scss" scoped>
.bg{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  //background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}

.fixed-box {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .tips {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
    text-align: center;
  }
}
</style>
