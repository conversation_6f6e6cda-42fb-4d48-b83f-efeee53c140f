<script>
import aiCenterCoursePanel from "@/pages/project-acm-pages/body/components/ai-center-course-panel.vue";

export default {
  name: "pageAIAcm",
  components: {aiCenterCoursePanel},
  data() {
    return {
      // 组件高度
      height: 0,
      // 组件高度
      navBarHeight: 0,
      // 是否加载完成
      isLoad: false,
      scrollTop: 0,
      clazz:{
        container: this.$kt.style.toggleClass("container"),
      }
    };
  },
  mounted() {

  },
  methods: {
    scroll(res){
      this.scrollTop = this.$kt.util.pxToRpx(res.target.scrollTop);
    },
    cardClick(res){
      if(!this.$kt.userAccount.getIsLogin()){
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.$kt.router.navTo("/pages/project-acm-pages/course-detail/course-detail",{
        courseId: res.id
      });
    }
  }
}
</script>

<template>
  <view :class="clazz.container">
    <view class="bg"></view>
    <view
        class="header-box"
        id="navBarInHomeCourse">
      <project-acm-nav-bar
          :is-has-i18n="false"
          ref="navBar"
          :icon="$kt.file.byPath('tabbar/ai-selected.svg')"
          :title="$i18n.zhToGlobal('AI中心')"></project-acm-nav-bar>
    </view>


    <scroll-view
        :style="'height: calc(100vh - '+navBarHeight+'px - 20rpx)'"
        scroll-y
        @scroll="scroll"
    >
      <view class="box">
        <!-- 广告位 -->
        <kt-carousel
            type-code="adOfAiPage"
        ></kt-carousel>
        <view style="height: 20rpx"></view>
        <ai-center-course-panel
            @cardClick="cardClick"
            :header-fixed="scrollTop>400"
            :header-top="'calc('+navBarHeight+'px + 10rpx)'"
        ></ai-center-course-panel>
        <view style="height: 100rpx"></view>
      </view>
    </scroll-view>

    <kt-login-popup
        ref="ktLoginPopup"
    ></kt-login-popup>

  </view>


</template>

<style scoped lang="scss">
.box {
  padding: 20rpx;
  box-sizing: border-box;
}
.bg{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  // 从白到黑的渐变，从上到下
  background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}

.container-mode-device-pc {
  position: relative;
  width: calc(100% - 240px - 400px);
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
  .header-box{
    width: calc(100% - 240px - 400px);
    left: 240px;
  }
}

.container-mode-color-scheme-dark{
  .bg{
    background: #191919;
  }
}
</style>