@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-087987cf {
  padding-bottom: 20rpx;
  margin-bottom: 30rpx;
  cursor: pointer;
}
.box.data-v-087987cf:active {
  -webkit-transform: scale(0.97);
          transform: scale(0.97);
}
.loading-box.data-v-087987cf {
  text-align: center;
}
.loading-image.data-v-087987cf {
  margin-top: 30rpx;
  height: 100rpx;
  width: 100rpx;
  opacity: 0.7;
}
.loading-text.data-v-087987cf {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999999;
  letter-spacing: 2rpx;
}
.box-selected.data-v-087987cf {
  position: relative;
  border: 5rpx solid #333333;
  padding: 30rpx 10rpx 30rpx 10rpx;
  border-radius: 20rpx;
}
.box-selected .box-selected-icon.data-v-087987cf {
  position: absolute;
  top: 0;
  right: -2rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 0 16rpx 0 0;
}
