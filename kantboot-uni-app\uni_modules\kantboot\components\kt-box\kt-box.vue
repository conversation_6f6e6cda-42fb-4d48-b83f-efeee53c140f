<template>
  <view class="box"
    :style="{
      padding,
      backgroundColor,
    }">
    <view v-if="title" class="box-title">
      <view class="box-title-text">
        {{title}}
      </view>
    </view>
    <slot></slot>
  </view>
</template>

<script>
import ktUtil from "@/uni_modules/kantboot/libs/util"

export default {
  props: {
    padding: {
      type: [String, Number],
      default: "30rpx"
    },
    backgroundColor: {
      type: String,
      default: ""
    },
    title:{
      type: String,
      default: ""
    }
  },
  data() {
    return {
      ktUtil,
    };
  },
  methods: {
    loading() {

    },
  }
}
</script>

<style lang="scss" scoped>
.box{
  box-sizing: border-box;
}

.box-title{
  padding: 20rpx 0 20rpx 0;
  font-size: 32rpx;
  color: #333333;
  text-align: left;
  font-weight: bold;
}
</style>
