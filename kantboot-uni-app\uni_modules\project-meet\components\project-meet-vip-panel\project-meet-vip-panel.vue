<template>
  <view>
	  <scroll-view :class="clazz.userRecommendPanel" :scroll-y="true"
	               :style="{height}">
    <view :class="clazz.panel">
      <view class="panel-header">
        <kt-user-info-card
          :user-account-id="userAccountId"
        ></kt-user-info-card>
      </view>
      <view class="panel-card-section">
        <view
          @click="selectSection('vip')"
          :class="{
            'panel-card-section-item-selected': section === 'vip',
          }"
          class="panel-card-section-item"
        >
          {{ "VIP" }}
        </view>
        <view
          @click="selectSection('svip')"
          :class="{
            'panel-card-section-item-selected': section === 'svip',
          }"
          class="panel-card-section-item"
        >
          {{ "SVIP" }}
        </view>
      </view>
      <swiper
        :current-item-id="section"
        @change="swiperChange"
        style="height: 300rpx"
      >
        <swiper-item item-id="vip">
          <view class="card-list">
            <project-meet-vip-card
              v-for="(item, index) in list"
              @click="select(item)"
              v-if="item.type === 'vip'"
              :selected="item.id + '' === selected + ''"
              class="card"
              :info="item"
            >
            </project-meet-vip-card>
          </view>
        </swiper-item>
        <swiper-item item-id="svip">
          <view class="card-list">
            <project-meet-vip-card
              v-for="(item, index) in list"
              @click="select(item)"
              v-if="item.type+'' === 'svip'"
              :selected="item.id + '' === selected + ''"
              class="card"
              :info="item"
            >
            </project-meet-vip-card>
          </view>
        </swiper-item>
      </swiper>

      <view class="panel-privileges">
        <kt-button ref="toPayBtn" class="pay-btn" @click="createOrder()"
          >{{ $i18n.zhToGlobal("立即开通") }}
        </kt-button>
      </view>

      <view class="panel-privileges">
        <view class="panel-privileges-box" v-show="section === 'vip'">
          <view class="panel-privileges-box-title">
            {{ $i18n.zhToGlobal("VIP特权") }}
          </view>
          <view class="panel-privileges-card">
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v0.png'),
                text: $i18n.zhToGlobal('解锁私聊'),
                tip: $i18n.zhToGlobal('无限制'),
              }"
            ></panel-privileges-card-item>
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v1.png'),
                text: $i18n.zhToGlobal('查看访问'),
                tip: $i18n.zhToGlobal('查看访客记录'),
              }"
            ></panel-privileges-card-item>

            <!-- 发布评论 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v2.png'),
                text: $i18n.zhToGlobal('发布评论'),
                tip: $i18n.zhToGlobal('随时发布评论'),
              }"
            ></panel-privileges-card-item>

            <!-- 发布动态 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v3.png'),
                text: $i18n.zhToGlobal('发布动态'),
                tip: $i18n.zhToGlobal('随时发布动态'),
              }"
            ></panel-privileges-card-item>

            <!-- 一键搭讪 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v4.png'),
                text: $i18n.zhToGlobal('一键搭讪'),
                tip:
                  $i18n.zhToGlobal('每日次数') +
                  ': ' +
                  settingData.vipDailyAccostTimes,
              }"
            ></panel-privileges-card-item>

            <!-- 查看谁关注了你 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v5.png'),
                text: $i18n.zhToGlobal('查看谁关注了你'),
                tip: $i18n.zhToGlobal('查看粉丝信息'),
              }"
            ></panel-privileges-card-item>

            <!-- 点赞 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v6.png'),
                text: $i18n.zhToGlobal('点赞'),
                tip: $i18n.zhToGlobal('随时点赞动态'),
              }"
            ></panel-privileges-card-item>

            <!-- 字符 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v7.png'),
                text: $i18n.zhToGlobal('积分'),
                tip:
                  $i18n.zhToGlobal('每日额度') +
                  ': ' +
                  settingData.vipDailyFreePoints,
              }"
            ></panel-privileges-card-item>

            <!-- 客服 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v8.png'),
                text: $i18n.zhToGlobal('客服'),
                tip: $i18n.zhToGlobal('拥有24小时专属客服'),
              }"
            ></panel-privileges-card-item>

            <!-- 推荐 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v9.png'),
                text: $i18n.zhToGlobal('推荐'),
                tip: $i18n.zhToGlobal('将您的资料推荐至少20名女士'),
              }"
            ></panel-privileges-card-item>

            <!-- 量身打造 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v10.png'),
                text: $i18n.zhToGlobal('量身打造'),
                tip: $i18n.zhToGlobal('使用精准算法，为您推荐更合适的异性'),
              }"
            ></panel-privileges-card-item>
          </view>
        </view>
        <view class="panel-privileges-box" v-show="section === 'svip'">
          <view class="panel-privileges-box-title">
            {{ $i18n.zhToGlobal("SVIP特权") }}
          </view>
          <view class="panel-privileges-card">
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v0.png'),
                text: $i18n.zhToGlobal('解锁私聊'),
                tip: $i18n.zhToGlobal('无限制'),
              }"
            ></panel-privileges-card-item>
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v1.png'),
                text: $i18n.zhToGlobal('查看访问'),
                tip: $i18n.zhToGlobal('查看访客记录'),
              }"
            ></panel-privileges-card-item>

            <!-- 发布评论 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v2.png'),
                text: $i18n.zhToGlobal('发布评论'),
                tip: $i18n.zhToGlobal('随时发布评论'),
              }"
            ></panel-privileges-card-item>

            <!-- 发布动态 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v3.png'),
                text: $i18n.zhToGlobal('发布动态'),
                tip: $i18n.zhToGlobal('随时发布动态'),
              }"
            ></panel-privileges-card-item>

            <!-- 一键搭讪 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v4.png'),
                text: $i18n.zhToGlobal('一键搭讪'),
                tip:
                  $i18n.zhToGlobal('每日次数') +
                  ': ' +
                  settingData.svipDailyAccostTimes,
              }"
            ></panel-privileges-card-item>

            <!-- 查看谁关注了你 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v5.png'),
                text: $i18n.zhToGlobal('查看谁关注了你'),
                tip: $i18n.zhToGlobal('查看粉丝信息'),
              }"
            ></panel-privileges-card-item>

            <!-- 点赞 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v6.png'),
                text: $i18n.zhToGlobal('点赞'),
                tip: $i18n.zhToGlobal('随时点赞动态'),
              }"
            ></panel-privileges-card-item>

            <!-- 字符 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v7.png'),
                text: $i18n.zhToGlobal('积分'),
                tip:
                  $i18n.zhToGlobal('每日额度') +
                  ': ' +
                  settingData.svipDailyFreePoints,
              }"
            ></panel-privileges-card-item>

            <!-- 客服 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v8.png'),
                text: $i18n.zhToGlobal('客服'),
                tip: $i18n.zhToGlobal('拥有24小时专属客服'),
              }"
            ></panel-privileges-card-item>

            <!-- 推荐 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v9.png'),
                text: $i18n.zhToGlobal('推荐'),
                tip: $i18n.zhToGlobal('将您的资料推荐至少20名女士'),
              }"
            ></panel-privileges-card-item>

            <!-- 量身打造 -->
            <panel-privileges-card-item
              :info="{
                icon: $kt.file.byPath('projectMeet/icon/vip/v10.png'),
                text: $i18n.zhToGlobal('量身打造'),
                tip: $i18n.zhToGlobal('使用精准算法，为您推荐更合适的异性'),
              }"
            ></panel-privileges-card-item>
          </view>
        </view>
      </view>
	  <view style="height: 100rpx;"></view>
    </view>
	</scroll-view>
    <kt-pay-popup
        @paySuccess="paySuccess()"
        ref="payPopup"></kt-pay-popup>
  </view>
</template>

<script>
import PanelPrivilegesCardItem from "./components/panel-privileges-card-item.vue";
import math from "../../../kantboot/libs/math/index";

export default {
  components: { PanelPrivilegesCardItem },
  props: {
    userAccountId:{
      type: [String, Number],
      default: ""
    },
    userInfo: {
      type: Object,
      default: () => {
        return "";
      },
    },
    height: {
      type: String,
      default: "100vh"
    },
  },
  data() {
    return {
      clazz: {
        panel: this.$kt.style.toggleClass("panel"),
      },
      section: "vip",
      selected: "1",
      vipCardList: [],
      svipCardList: [],
      list:[],
      settingData: {},
    };
  },
  created() {
    // this.initList();
    this.init();
  },
  methods: {
    paySuccess(){
      this.$kt.event.emit("projectMeet:vipPaySuccess");
    },
    init() {
      this.initList();
      this.getSetting();
    },
    async getSetting() {
      await this.$request
        .post("/project-meet-web/vip/getSetting")
        .then((res) => {
          console.log(res.data);
          this.settingData = res.data;
        });
    },
    async initList() {
      this.vipCardList = [];
      this.svipCardList = [];
      this.list = [];
      this.$request.post("/project-meet-web/vip/getAll").then((res) => {
        this.list=res.data;
        // 按照价格排序
        this.list.sort((a, b) => {
          return a.price - b.price;
        });

        for  (let i = 0; i < this.list.length; i++) {
          let item = this.list[i];
          let price = this.$kt.math.div(item.price, item.days)+"";
          price = price.substring(0, price.indexOf(".") + 2);
          item.tip = this.$i18n.zhToGlobal("每日仅需")+" $"+price;

          // vipCardList
          if (item.type === "vip") {
            this.vipCardList.push(item);
          } else if (item.type === "svip") {
            this.svipCardList.push(item);
          }

        }
        try{
          this.selectSection("svip");
        }catch (e) {

        }
      });
    },
    createOrder() {
      this.$refs.toPayBtn.loading("正在创建订单");
      // /project-meet-web/vip/generateOrder
      this.$kt.request.post("/project-meet-web/vip/generateOrder",{
        data:{
          id: this.selected,
          userAccountIdOfGetter: this.userAccountId,
        }
      }).then((res)=>{
        this.$refs.payPopup.open(res.data);
      });
      // this.$refs.payPopup.open("100006");
    },
    select(card) {
      this.selected = card.id;
    },
    selectSection(e) {
      this.section = e;
      // 选择第一个
      if (this.section === "vip") {
        this.selected = this.vipCardList[1].id;
      } else {
        this.selected = this.svipCardList[1].id;
      }
    },
    swiperChange(e) {
      // this.section = e.detail.currentItemId;
      this.selectSection(e.detail.currentItemId);
    },
  },
};
</script>

<style lang="scss" scoped>
.card-list {
  text-align: center;

  .card {
    display: inline-block;
    margin: 10rpx;
  }
}

// 滚动条样式
.card-list::-webkit-scrollbar {
  display: none;
}

.panel {
  .panel-header {
    padding: 20rpx;
  }
}

.panel-card-section {
  padding: 20rpx 30rpx 20rpx 30rpx;
  text-align: left;


  .panel-card-section-item {
    display: inline-block;
    margin-right: 30rpx;

  }

  .panel-card-section-item-selected {
    font-weight: bold;
    color: #000000;
  }
}

.panel-privileges {
  padding: 20rpx 30rpx 20rpx 30rpx;

  .panel-privileges-box {
    border-radius: 10rpx;

    .panel-privileges-box-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #000000;
    }
  }
}

.panel-privileges-card {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20rpx 0;
}

.panel-mode-device-pc {
  .panel-card-section {
    margin-left: 130rpx;
    .panel-card-section-item {
      cursor: pointer;
    }
    //
    //margin-left: -100rpx;
    //margin-right: 130rpx;
  }
  .card-list {
    display: flex;
    justify-content: space-evenly;
  }
  .panel-header {
    display: flex;
    justify-content: center;
  }
  .panel-privileges {
    text-align: center;
    .pay-btn {
      display: inline-block;
      width: 300px;
    }
  }
}
</style>
