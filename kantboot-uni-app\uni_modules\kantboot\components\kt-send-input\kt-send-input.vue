<script>

import OperateBar from "@/uni_modules/kantboot/components/kt-send-input/components/operate-bar.vue";
import OperatePanel from "@/uni_modules/kantboot/components/kt-send-input/components/operate-panel.vue";

export default {
  name: "chat-input-box",
  components: {OperateBar,OperatePanel},
  props: {
    mode: {
      type: String,
      default: "bar"
    },
    placeholder: {
      type: String,
      default: ""
    },
    /**
     * 是否有语音发送
     */
    hasVoice: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有图片发送
     */
    hasImage: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有视频发送
     */
    hasVideo: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有用户关系发送
     */
    hasUserAccount: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有礼物发送
     */
    hasGift: {
      type: Boolean,
      default: false
    },
    operateExtraArray: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      panelOpen: false,
      bindParam: {
        userAccountId: "",
        fileIdsOfImages: [],
        fileIdsOfVideos: [],
        text: "",
      },
      loading: false,
      loadingFile: false,
      requestParam: {
        dialogId: "",
        items: []
      },
    };
  },
  methods: {
    keyupEnter() {
      if(this.$kt.style.detectDeviceType() === "pc") {
        // 如果是PC端，按下回车键发送消息
        if (this.bindParam.text) {
          this.sendMessage();
        }
      }
    },
    toLoading(){
      this.loading = true;
    },
    toNone(){
      this.loading = false;
      this.bindParam.text = "-";
      this.change();
      setTimeout(()=>{
        this.bindParam.text = "";
        this.change();
      },10);
    },
    panelOpenChange(){
      this.panelOpen = !this.panelOpen;
      this.change();
    },
    clear() {
      this.bindParam = {
        userAccountId: "",
        fileIdsOfImages: [],
        fileIdsOfVideos: [],
        text: "",
      };
      this.requestParam = {
        dialogId: "",
        items: []
      };
    },
    isPC(){
      return this.$kt.style.detectDeviceType() === "pc";
    },
    inputFocus(){
      this.panelOpen = false;
      this.change();
    },
    userAccountSelect(userAccount) {
      this.bindParam.userAccountId = userAccount.id;
      this.$emit("send", {
        type: "userAccount:id",
        content: userAccount.id + "",
      });
    },
    chooseVideo() {
      uni.chooseVideo({
        sourceType: ['album', 'camera'],
        success: async (res) => {
          const tempFilePath = res.tempFilePath;
          console.log(tempFilePath, "====");
          this.loadingFile = true;
          this.$request.uploadFile({
            data: {
              file: tempFilePath,
              groupCode: "fp",
            },
            stateSuccess: (res1) => {
              this.loadingFile = false;
              this.bindParam.fileIdsOfVideos.push(res1.data.id);
              this.$emit("send", {
                type: "video:id",
                content: res1.data.id + "",
              });
              resolve(res1.data.id);
            },
            stateFail: (res1) => {
              this.loadingFile = false;
              reject(res1.data.id);
            }
          });

        }
      });
    },
    chooseImage() {
      uni.chooseImage({
        count: 9,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: async (res) => {
          // 获取图片的临时路径
          const tempFilePaths = res.tempFilePaths;
          for (let i = 0; i < tempFilePaths.length; i++) {
            let path = tempFilePaths[i];
            this.loadingFile = true;
            await new Promise((resolve, reject) => {
              this.$request.uploadFile({
                data: {
                  file: path,
                  groupCode: "fp",
                },
                stateSuccess: (res1) => {
                  this.loadingFile = false;
                  this.bindParam.fileIdsOfImages.push(res1.data.id);
                  this.$emit("send", {
                    type: "image:id",
                    content: res1.data.id + "",
                  });
                  resolve(res1.data.id);
                },
                stateError: (res1) => {
                  this.loadingFile = false;
                  reject(res1.data.id);
                }
              });
            });
          }

        },
      });
    },
    toSummary() {
      this.$emit("toSummary");
    },
    change() {
      this.$emit("change");
    },
    input($event) {
      this.bindParam.value = $event.detail.text;
      this.change();
    },
    chooseGift() {
      this.$emit("chooseGift");
    },
    async sendMessage() {
      this.requestParam.dialogId = this.dialogId;
      this.requestParam.items = [];
      if (this.bindParam.fileIdsOfImages
          && this.bindParam.fileIdsOfImages.length > 0) {
        this.requestParam.items.push({
          type: "image:id",
          content: this.bindParam.fileIdsOfImages,
        });
      }
      if (this.bindParam.text) {
        this.requestParam.items.push({
          type: "text",
          content: this.bindParam.text,
        });
      }
      if (this.bindParam.userAccountId) {
        this.requestParam.items.push({
          type: "userAccount:id",
          content: this.bindParam.userAccountId,
        });
      }
      if (this.bindParam.fileIdsOfVideos
          && this.bindParam.fileIdsOfVideos.length > 0) {
        this.requestParam.items.push({
          type: "video:id",
          content: this.bindParam.fileIdsOfVideos,
        });
      }
      this.$emit("send", {
        type: "text",
        content: this.bindParam.text,
      });

    },
  },
}
</script>

<template>
  <view style="width:100%;position: relative">

    <view
        style="display: inline-block;width: calc(100% - 100rpx)"
        class="chat-footer-input-container">
          <textarea
              :adjust-position="false"
              auto-height
              :disabled="loading"
              v-model="bindParam.text"
              @keyup.enter="keyupEnter"
              @input="input"
              @focus="inputFocus"
              :placeholder="placeholder||$i18n.zhToGlobal('输入内容')"
              maxlength="99999"
              class="chat-footer-input"/>
    </view>
    <view class="chat-footer-send-container btn"
          v-if="mode==='bar'"
          style="display: inline-block;width: 100rpx;
            text-align: center;position: relative">
      <image
          v-if="loading||loadingFile"
          class="icon icon-loading"
          :src="$kt.file.byPath('kantboot/icon/plan.svg')">
      </image>
      <image
          @click="sendMessage()"
          v-else-if="bindParam.text"
          class="icon"
          :src="$kt.file.byPath('kantboot/icon/plan.svg')"
      ></image>
      <image
          v-else-if="!bindParam.text"
          class="icon"
          style="opacity: .5"
          :src="$kt.file.byPath('kantboot/icon/plan.svg')"
      ></image>
    </view>

    <view class="chat-footer-send-container btn"
          v-if="mode==='panel'"
          :class="{
            'add-btn': !bindParam.text,
          }"
          style="display: inline-block;width: 100rpx;
            text-align: center;position: relative">
      <image
          v-if="loading||loadingFile"
          class="icon icon-loading"
          :src="$kt.file.byPath('kantboot/icon/plan.svg')">
      </image>
      <image
          @click="sendMessage()"
          v-else-if="bindParam.text"
          class="icon"
          :src="$kt.file.byPath('kantboot/icon/plan.svg')"
      ></image>
      <image
          v-else-if="!bindParam.text"
          class="icon add-icon"
          @click="panelOpenChange"
          :style="{
            opacity: panelOpen ? .5 : 1,
          }"
          :src="$kt.file.byPath('kantboot/icon/add.svg')"
      ></image>
    </view>

    <operate-bar
        v-if="mode === 'bar'"
        :has-gift="hasGift"
        :has-voice="hasVoice"
        :has-image="hasImage"
        :has-video="hasVideo"
        :has-user-account="hasUserAccount"
        @chooseGift="chooseGift"
        @chooseImage="chooseImage"
        @chooseVideo="chooseVideo"
        @openUserAccountSelect="$refs.userAccountInterrelationPopup.open()"
        :operate-extra-array="operateExtraArray"
    >
      <template v-slot:operateExtra>
        <slot name="operateExtra"></slot>
      </template>
    </operate-bar>

    <operate-panel
        v-if="mode === 'panel'&&panelOpen"
        :has-gift="hasGift"
        :has-voice="hasVoice"
        :has-image="hasImage"
        :has-video="hasVideo"
        :has-user-account="hasUserAccount"
        @chooseGift="chooseGift"
        @chooseImage="chooseImage"
        @chooseVideo="chooseVideo"
        @openUserAccountSelect="$refs.userAccountInterrelationPopup.open()"
        :operate-extra-array="operateExtraArray"
    >
      <template v-slot:operateExtra>
        <slot name="operateExtra"></slot>
      </template>
    </operate-panel>


    <view style="height: 20rpx"></view>
    <kt-user-account-interrelation-popup
        @select="userAccountSelect"
        :many-select="false"
        :height="isPC()?'530px':'calc(100vh - 200rpx)'"
        ref="userAccountInterrelationPopup"
    ></kt-user-account-interrelation-popup>

    <kt-keyboard-size></kt-keyboard-size>
  </view>
</template>

<style scoped lang="scss">

.chat-footer-input-container {
  padding: 20rpx;
  box-sizing: border-box;
}

.chat-footer-input {
  width: 100%;
  height: 100%;
  background-color: #f8f8f8;
  padding: 10rpx;
  border-radius: 10rpx;
  max-height: 200rpx;
  // 超出可拖动
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

// 让textarea 高度自适应
.chat-footer-input {
  height: 100%;
  width: 100%;
  padding: 15rpx;
}

.btn{
  cursor: pointer;
}

.btn:active {
  opacity: .8;
}



.chat-footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  //background-color: #FFFFFF;
  padding-top: 40rpx;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .5) 10%, rgba(255, 255, 255, 1) 30%, rgba(255, 255, 255, 1) 100%);

}

.icon {
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  left: 50%;
  transform: translate(-50%, calc(-100% - 10rpx));
}

.icon-loading {
  // 颜色反转
  //filter: invert(1);
  // 动画
  animation: loading-icon-ani-in-page-chat 1s linear infinite;
}

@keyframes loading-icon-ani-in-page-chat {
  0% {
    transform: translate(-50%, calc(-100% - 10rpx)) scale(1);
  }
  50% {
    transform: translate(-50%, calc(-100% - 10rpx)) scale(.3);
  }
  100% {
    transform: translate(-50%, calc(-100% - 10rpx)) scale(1);
  }
}

.add-icon{
  padding: 0 10rpx 0 10rpx;
  border-radius: 30rpx;
  width: 47rpx;
  height: 47rpx;
  border: 2px solid #333333;
}

</style>