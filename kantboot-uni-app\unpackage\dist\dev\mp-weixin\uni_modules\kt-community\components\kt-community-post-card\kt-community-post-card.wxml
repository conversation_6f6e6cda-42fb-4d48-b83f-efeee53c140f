<view class="{{['data-v-0d73e0ab',clazz.card]}}" style="{{'border-radius:'+(borderRadius)+';'+('background:'+(background?background:null)+';')}}"><view hidden="{{!(isLoading)}}" class="data-v-0d73e0ab"><view style="height:100rpx;" class="data-v-0d73e0ab"></view><block wx:if="{{isLoading}}"><u-loading-icon vue-id="bdee948a-1" mode="circle" size="{{50}}" class="data-v-0d73e0ab" bind:__l="__l"></u-loading-icon></block></view><view hidden="{{!(!isLoading)}}" class="data-v-0d73e0ab"><view hidden="{{!(showAuditStatus)}}" class="data-v-0d73e0ab"><view class="{{['dot','data-v-0d73e0ab',(postData.auditStatus==='auditing')?'dot-audit-auditing':'',(postData.auditStatus==='pass')?'dot-audit-pass':'',(postData.auditStatus==='fail')?'dot-audit-fail':'']}}"></view><view class="dot-text data-v-0d73e0ab"><block wx:if="{{postData.auditStatus==='auditing'}}"><view class="dot-text-auditing data-v-0d73e0ab">{{$root.g0}}</view></block><block wx:if="{{postData.auditStatus==='pass'}}"><view class="dot-text-pass data-v-0d73e0ab">{{$root.g1}}</view></block><block wx:if="{{postData.auditStatus==='fail'}}"><view class="dot-text-fail data-v-0d73e0ab">{{$root.g2}}</view></block></view><view class="dot-fail-reason data-v-0d73e0ab"><block wx:if="{{postData.auditStatus==='fail'}}"><view class="dot-text-fail data-v-0d73e0ab">{{$root.g3+"："+postData.auditFailReason}}</view></block></view></view><view class="in-user-card data-v-0d73e0ab"><view data-event-opts="{{[['tap',[['userClick']]]]}}" class="in-user-card-avatar-box data-v-0d73e0ab" catchtap="__e"><block wx:if="{{userAccountOfUploader.fileIdOfAvatar}}"><image class="in-user-card-avatar data-v-0d73e0ab" src="{{$root.g4}}" mode="aspectFit"></image></block><block wx:else><image class="in-user-card-avatar data-v-0d73e0ab" src="{{$root.g5}}" mode="aspectFit"></image></block></view><view class="in-user-card-info-box data-v-0d73e0ab"><view class="in-user-card-info-box-nickname data-v-0d73e0ab">{{userAccountOfUploader.nickname+''}}<block wx:if="{{isSelf}}"><view class="tag data-v-0d73e0ab">{{''+$root.g6+''}}</view></block><block wx:if="{{userAccountOfUploader.isInnerStaff}}"><view class="tag data-v-0d73e0ab">{{''+$root.g7+''}}</view></block><block wx:if="{{false}}"><view class="f-btn data-v-0d73e0ab">{{"+"+" "+$root.g8}}</view></block><block wx:if="{{hasDot}}"><image class="card-three-dot data-v-0d73e0ab" src="{{$root.g9}}" data-event-opts="{{[['tap',[['dotClick']]]]}}" catchtap="__e"></image></block></view><view class="in-user-card-info-box-time data-v-0d73e0ab">{{$root.g10+''}}</view></view></view><view class="in-card data-v-0d73e0ab"><kt-format vue-id="bdee948a-2" data="{{postData.items}}" data-event-opts="{{[['^postUserCardClick',[['postUserCardClick']]],['^postCardClick',[['postCardClick']]]]}}" bind:postUserCardClick="__e" bind:postCardClick="__e" class="data-v-0d73e0ab" bind:__l="__l"></kt-format></view><block wx:if="{{hasBottomOperation}}"><view class="in-footer-card data-v-0d73e0ab"><block wx:if="{{!postData.isForbidLike}}"><view data-event-opts="{{[['tap',[['toLike']]]]}}" class="in-footer-card-item data-v-0d73e0ab" catchtap="__e"><block wx:if="{{relationshipSelf.isLike}}"><image class="in-footer-card-item-icon in-footer-card-item-icon-active data-v-0d73e0ab" src="{{$root.g11}}"></image></block><block wx:else><image class="in-footer-card-item-icon data-v-0d73e0ab" src="{{$root.g12}}"></image></block><view class="in-footer-card-item-number data-v-0d73e0ab">{{''+$root.m0.likeCount+''}}</view></view></block><block wx:if="{{!postData.isForbidComment}}"><view class="in-footer-card-item data-v-0d73e0ab"><image class="in-footer-card-item-icon data-v-0d73e0ab" src="{{$root.g13}}"></image><view class="in-footer-card-item-number data-v-0d73e0ab">{{''+$root.m1.commentCount+''}}</view></view></block><block wx:if="{{!postData.isForbidCollect&&isForbidCollect}}"><view data-event-opts="{{[['tap',[['toCollect']]]]}}" class="in-footer-card-item data-v-0d73e0ab" catchtap="__e"><block wx:if="{{relationshipSelf.isCollect}}"><image class="in-footer-card-item-icon in-footer-card-item-icon-active data-v-0d73e0ab" src="{{$root.g14}}"></image></block><block wx:else><image class="in-footer-card-item-icon data-v-0d73e0ab" src="{{$root.g15}}"></image></block><view class="in-footer-card-item-number data-v-0d73e0ab">{{''+$root.m2.collectCount+''}}</view></view></block><block wx:if="{{$root.m3}}"><view data-event-opts="{{[['tap',[['toForward']]]]}}" class="in-footer-card-item data-v-0d73e0ab" catchtap="__e"><image class="in-footer-card-item-icon data-v-0d73e0ab" src="{{$root.g16}}"></image></view></block></view></block><view class="in-footer-card data-v-0d73e0ab"><slot name="bottom"></slot><scoped-slots-bottom postId="{{postData.id}}" post="{{postData}}" class="scoped-ref" bind:__l="__l"></scoped-slots-bottom></view></view><kt-community-post-share-popup vue-id="bdee948a-3" data-ref="communityPostSharePopup" class="data-v-0d73e0ab vue-ref" bind:__l="__l"></kt-community-post-share-popup></view>