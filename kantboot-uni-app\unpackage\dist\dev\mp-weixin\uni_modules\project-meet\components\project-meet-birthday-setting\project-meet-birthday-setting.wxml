<view class="data-v-748d9b0a"><view class="data-v-748d9b0a"><view class="input-box data-v-748d9b0a"><view class="input-item data-v-748d9b0a"><input class="picker data-v-748d9b0a" maxlength="2" type="number" placeholder="MM" data-event-opts="{{[['input',[['__set_model',['$0','month','$event',[]],['dateParams']],['convertToTimestamp',['$event']]]]]}}" value="{{dateParams.month}}" bindinput="__e"/></view><view class="input-split data-v-748d9b0a">/</view><view class="input-item data-v-748d9b0a"><input class="picker data-v-748d9b0a" maxlength="2" type="number" placeholder="DD" data-event-opts="{{[['input',[['__set_model',['$0','day','$event',[]],['dateParams']],['convertToTimestamp',['$event']]]]]}}" value="{{dateParams.day}}" bindinput="__e"/></view><view class="input-split data-v-748d9b0a">/</view><view class="input-item data-v-748d9b0a"><input class="picker data-v-748d9b0a" maxlength="4" type="number" placeholder="YYYY" data-event-opts="{{[['input',[['__set_model',['$0','year','$event',[]],['dateParams']],['convertToTimestamp',['$event']]]]]}}" value="{{dateParams.year}}" bindinput="__e"/></view></view></view></view>