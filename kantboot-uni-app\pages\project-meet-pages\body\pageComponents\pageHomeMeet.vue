<template>
  <view :class="clazz.container">
    <view :class="clazz.bg">
      <image
          v-if="$kt.style.detectDeviceType()==='mobile'"
          class="bg-image"
          mode="aspectFill"
          :src="$kt.file.byPath('meet/bg.png')"
      ></image>
      <image
          v-if="$kt.style.detectDeviceType()==='mobile'"
          class="bg-image-2"
          mode="aspectFill"
          :src="$kt.file.byPath('meet/bg.png')"
      ></image>
      <image
          v-if="$kt.style.detectDeviceType()==='mobile'"
          class="bg-image-3"
          mode="aspectFill"
          :src="$kt.file.byPath('meet/bg.png')"
      ></image>

      <view class="bg-bg"></view>

    </view>

    <view
        class="box-view"
        :class="{
            'box-view-no-login':  !this.isLogin
          }"
    >
      <view id="pageHomeHeader" style="width: 100%;top:0;left:0;">

        <view>
          <view>
            <kt-status-bar-height
                :background-color="'rgba(0,0,0,0)'"
            ></kt-status-bar-height>

          </view>
          <view>


            <view
                class="second-box" style="position: relative;padding: 11rpx;">
              <view
                  v-if="!isLogin"
                  class="second-box-box">
                <view class="second-box-item">
                  <view
                      @click="toLogin()"
                      class="login-btn">
                    {{ $i18n.zhToGlobal('登录') }}
                  </view>

                  {{ $i18n.zhToGlobal('登录后发现更多功能') }}
                </view>
              </view>
              <view
                  v-if="isLogin"
                  class="second-box-box">
                <view class="second-box-item"
                      :class="{
            'second-box-item-selected': selected === 'recommend'
                }"
                      @click="selected = 'recommend'"
                >{{ $i18n.zhToGlobal('发现') }}
                  <view
                      v-if="selected === 'recommend'"
                      class="second-box-line"></view>
                </view>
                <view class="second-box-item"
                      :class="{
                    'second-box-item-selected': selected === 'vip'
                  }"
                      @click="selected = 'vip'"
                >{{ "VIP/SVIP" }}
                  <view
                      v-if="selected === 'vip'"
                      class="second-box-line"></view>
                </view>
                <view class="second-box-item"
                      :class="{
            'second-box-item-selected': selected === 'gold'}"
                      @click="selected = 'gold'"
                >{{ $i18n.zhToGlobal('金币') }}
                  <view
                      v-if="selected === 'gold'"
                      class="second-box-line"></view>
                </view>
                <view class="second-box-item"
                      :class="{
            'second-box-item-selected': selected === 'points'}"
                      @click="selected = 'points'"
                >{{ $i18n.zhToGlobal('积分') }}
                  <view
                      v-if="selected === 'points'"
                      class="second-box-line"></view>

                </view>

              </view>

              <!-- 搜索图标 -->
              <image
                  v-if="$kt.style.detectDeviceType()==='mobile'&&selected === 'recommend'"
                  style="width: 40rpx;height: 40rpx;position: absolute;top:50%;transform: translateY(-50%);right: 50rpx;"
                  @click="$refs.projectMeetUserAccountSearchPopup.open()"
                  class="second-icon"
                  :src="$kt.file.byPath('icon/search.svg')"
              ></image>

              <view
              v-if="$kt.style.detectDeviceType()==='pc'&&selected === 'recommend'"
              >
                <view
                    class="box-search-box"
                >
                  <image
                      class="second-icon"
                  :src="$kt.file.byPath('icon/search.svg')"
                  ></image>
                  <input
                      v-model="keyword"
                      @input="keyword = $event.detail.value"
                      :placeholder="$i18n.zhToGlobal('搜索用户')"
                  ></input>

                </view>
              </view>

            </view>
          </view>
        </view>

        <view style="height: 20rpx"></view>

      </view>
      <view
      >
        <!-- <UserRecommendPanel></UserRecommendPanel> -->
        <project-meet-user-recommend-panel
            @toLogin="toLogin()"
            :keyword="keyword"
            ref="userRecommendPanel"
            v-show="selected === 'recommend'"
            :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px - 50px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'"
        ></project-meet-user-recommend-panel>

        <template v-if="isLogin&&self.id">
          <project-meet-vip-panel
              :user-account-id="self.id"
              v-show="selected === 'vip'"
              :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px - 50px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'"
          ></project-meet-vip-panel>

          <project-meet-gold-panel
              :user-account-id="self.id"
              v-show="selected === 'gold'"
              :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px - 50px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'"
          ></project-meet-gold-panel>

          <project-meet-points-panel
              :user-account-id="self.id"
              v-show="selected === 'points'"
              :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px - 50px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'"
          ></project-meet-points-panel>
        </template>

      </view>
    </view>

    <project-meet-user-account-search-popup
    ref="projectMeetUserAccountSearchPopup"
    ></project-meet-user-account-search-popup>

<!--    <kt-login-popup-->
<!--        ref="ktLoginPopup"></kt-login-popup>-->

<!--    <project-meet-login-popup-->
<!--    ref="ktLoginPopup"-->
<!--    ></project-meet-login-popup>-->

  </view>
</template>

<script>


export default {
  data() {
    return {
      navHeight: 48,
      menuButtonWidth: 0,
      pageHomeHeaderHeight: 0,
      clazz: {
        container: this.$kt.style.toggleClass("container"),
        bg: this.$kt.style.toggleClass("bg"),
      },
      selected: 'recommend',
      isLogin: false,
      self: {},
      keyword:""
    }
  },
  created() {
    this.isLogin = this.$kt.userAccount.getIsLogin();
    this.self = this.$kt.userAccount.getSelf();
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.self = this.$kt.userAccount.getSelf();
    });

  },
  mounted() {

    // #ifdef MP-WEIXIN
    this.navHeight = uni.getSystemInfoSync().system.indexOf("ios") != -1 ? 44 : 48;
    // 获取胶囊信息
    this.menuButtonWidth = wx.getMenuButtonBoundingClientRect().width;
    console.log(this.menuButtonWidth, "获取胶囊信息");
    // #endif
    // 获取pageHomeHeader的高度
    this.$nextTick(() => {
      // uniapp的获取元素高度的方法
      uni.createSelectorQuery().in(this).select('#pageHomeHeader').boundingClientRect((rect) => {
        console.log(rect, "获取pageHomeHeader的高度")
        this.pageHomeHeaderHeight = rect.height;
      }).exec()
    });
    this.$kt.event.on('changeTabbar', () => {
      this.selected = 'recommend';

      console.log("获取#pageHomeHeader的高度");
      this.$nextTick(() => {
        uni.createSelectorQuery()
            .in(this)
            .select("#pageHomeHeader")
            .boundingClientRect((res) => {
              console.log(res, "pageHomeHeader");
              this.pageHomeHeaderHeight = res.height;
            })
            .exec();
      });
    });
  },

  methods: {
    toLogin() {
      this.$refs.ktLoginPopup.open();
    },
    isY() {
      // 转换为大写
      let deviceType = this.$kt.style.detectDeviceType().toUpperCase();
      return deviceType === 'PC' ||
          deviceType === 'TV';

    }
  }
}
</script>

<style lang="scss" scoped>
.bg {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  //background-color: #f9f9f9;
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);

  .bg-image {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    right: 0;
    z-index: -2;

  }

  .bg-image-2 {
    position: fixed;
    width: 100vw;
    height: 100vh;
    bottom: 25vh;
    right: 0;
    // 上下翻转
    transform: scaleY(-1);
    z-index: -2;

  }

  .bg-image-3 {
    position: fixed;
    width: 100vw;
    height: 100vh;
    bottom: 0;
    right: 0;
    z-index: -2;
    // 上下翻转
    transform: scaleY(-1);
  }

  .bg-bg {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    right: 0;
    z-index: -1;
    // 渐变，从上到下
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 100rpx, rgba(255, 255, 255, 1) 100%);
  }
}

.bg-mode-color-scheme-light {
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}

.bg-mode-color-scheme-dark {
  background: #191919;
}

.header-box-1 {
  padding: 0 10rpx;
  box-sizing: border-box;
  // 不换行
  white-space: nowrap;

  .header-item {
    display: inline-block;
    font-size: 36rpx;
    letter-spacing: 3rpx;
    margin-right: 5rpx;
    padding: 10rpx 20rpx;
  }

  .header-item-selected {
    font-weight: bold;
  }
}

.scroll-view {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);

  // 超出滚动
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;
  // 隐藏滚动条
  &::-webkit-scrollbar {
    width: 0;
    height: 1rpx;
    display: none;
  }

  // 滚动
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

}

// 不显示滚动条
::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}

.second-box {
  width: 100%;

  .second-box-box {
    width: calc(100% - 100rpx);
    // 不换行
    white-space: nowrap;
    // 超出滚动
    overflow-x: auto;

    .second-box-item {
      position: relative;
      display: inline-block;
      font-size: 32rpx;
      letter-spacing: 3rpx;
      margin-right: 5rpx;
      padding: 10rpx 20rpx;
      border-radius: 10rpx;

      .login-btn {
        border: 2rpx solid #FFFFFF;
        box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);
        cursor: pointer;
        color: #ffffff;
        background-color: #5A7EF6;
        display: inline-block;
        padding: 10rpx 20rpx 10rpx 20rpx;
        border-radius: 30rpx;
        font-size: 24rpx;
        margin-right: 10rpx;
      }

      .second-box-line {
        position: absolute;
        width: 40rpx;
        height: 7rpx;
        background-color: #000000;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        border-radius: 20rpx;
      }
    }

    .second-box-item-selected {
      font-weight: bold;
    }
  }
}

.login-btn:active {
  transform: scale(.95);
}

//.box-view-no-login{
//  // 高斯模糊
//  filter: blur(10px);
//  // 动画
//  animation: fadeIn 2s infinite;
//}
//@keyframes fadeIn {
//  0% {
//    filter: blur(10px);
//  }
//  50%{
//    filter: blur(5px);
//  }
//  100% {
//    filter: blur(10px);
//
//  }
//}

.container-mode-device-pc {
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
}

.split {
  width: 100%;
  height: 3rpx;
  background-color: #eee;
}

.container-mode-color-scheme-dark {
  background-color: #191919;

  .split {
    background-color: #888888;
  }

  .header-box {
    background-color: #191919;
  }

  .scroll-view {
    background-color: #191919;
  }

  .second-icon {
    // 颜色反转
    filter: invert(1);
  }

  .header-box-1 {
    background-color: #191919;

    .header-item {
      color: #fff;
    }
  }

  .second-box {
    .second-box-box {
      .second-box-item {
        color: #fff;
      }


      .second-box-item-selected {
        color: #fff;
        font-weight: bold;
      }
    }
  }
}

.container-mode-device-pc {
  position: relative;
  width: calc(100% + 20px);
  padding: 0;
  box-sizing: border-box;

  .second-box-item {
    cursor: pointer;
  }

  .second-icon {
    margin-right: 10px;
  }
}

.box-search-box{
  position: absolute;
  top:20rpx;
  right:100rpx;
  width: 180px;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  background-color: #f0f0f0;
  padding-left: 60rpx;
  box-sizing: border-box;
  .second-icon {
    position: absolute;
    top: 50%;
    left: 20rpx;
    transform: translateY(-50%);
    width: 30rpx;
    height: 30rpx;
  }
}
</style>