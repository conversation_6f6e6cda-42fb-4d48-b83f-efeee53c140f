<template>
  <view class="box">
    <view>
      <kt-login-panel
      ></kt-login-panel>
    </view>
  </view>
</template>

<script>
import KtKeyboardSize from "@/uni_modules/kantboot/components/kt-keyboard-size/kt-keyboard-size.vue";
export default {
  components: {
    KtKeyboardSize,
  },
  data() {
    return {};
  }
}
</script>

<style lang="scss" scoped>
.box{
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
}

.title{
  font-size: 30rpx;
  text-align: center;
  //color: rgba(0,110,109,1);
  color: #000;
  line-height: 40rpx;
  font-weight: bold;
  margin: 10rpx;
}

.other-box{
  margin-top: 20rpx;
  width: 100%;
  text-align: center;
  .other-title{
    font-size: 24rpx;
    color: #666666;
  }
  .other-icon-box{
    display: inline-block;
    width: 100%;
    text-align: center;
    margin-top: 20rpx;
    .icon{
      width: 80rpx;
      height: 80rpx;
    }
  }
}

.logo-box{
  text-align: center;
  width: 100%;
}

.logo{
  display: inline-block;
  width: 200rpx;
  height: 100rpx;
  margin: 0 auto;
}
</style>
