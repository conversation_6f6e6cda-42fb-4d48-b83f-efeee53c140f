@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content.data-v-551d181e {
  position: relative;
  display: inline-block;
  border-radius: 50%;
}
.t-head.data-v-551d181e {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 55%;
  top: 0;
  left: 0;
}
.radius.data-v-551d181e {
  position: absolute;
  background-color: black;
  width: 20rpx;
  height: 20rpx;
  border: 5rpx solid white;
  border-radius: 50%;
  bottom: 0rpx;
  right: 0rpx;
}
.radius-line.data-v-551d181e {
  background-color: #08f175;
}
.radius-no-line.data-v-551d181e {
  background-color: #9b9c9c;
}
.loading-img.data-v-551d181e {
  opacity: 0.9;
  -webkit-animation: loading-data-v-551d181e 1s linear infinite;
          animation: loading-data-v-551d181e 1s linear infinite;
}
@-webkit-keyframes loading-data-v-551d181e {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes loading-data-v-551d181e {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
