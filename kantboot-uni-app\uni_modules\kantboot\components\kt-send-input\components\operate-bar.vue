<template>
  <view class="bar">

    <view
        v-if="hasVoice"
        class="bar-item">
      <image
          @click="voice"
          class="panel-icon"
          :src="$kt.file.byPath('kantboot/icon/voice.svg')">
      </image>
    </view>

    <view
        v-if="hasImage"
        class="bar-item">
      <image
        @click="chooseImage"
        class="panel-icon"
        :src="$kt.file.byPath('kantboot/icon/image.svg')">
      </image>
    </view>

    <view
        v-if="hasVideo"
        class="bar-item">
      <image
        @click="chooseVideo()"
        class="panel-icon"
        :src="$kt.file.byPath('kantboot/icon/video.svg')">
      </image>
    </view>

    <view
        v-if="hasUserAccount"
        class="bar-item">
      <image
        @click="openUserAccountSelect()"
        class="panel-icon"
        :src="$kt.file.byPath('kantboot/icon/interrelation.svg')">
      </image>
    </view>

    <view
        v-if="hasGift"
        class="bar-item">
      <image
        @click="chooseGift()"
        class="panel-icon"
        :src="$kt.file.byPath('kantboot/icon/gift.svg')">
      </image>
    </view>


    <view
        v-for="(item, index) in operateExtraArray"
        class="bar-item">
      <image
          @click="$emit(item.emit)"
          class="panel-icon"
          :src="item.iconSrc">
      </image>
    </view>

    <slot name="operateExtra"></slot>


  </view>
</template>

<script>
export default {
  props:{
    /**
     * 是否有语音发送
     */
    hasVoice: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有图片发送
     */
    hasImage: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有视频发送
     */
    hasVideo: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有用户关系发送
     */
    hasUserAccount: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有礼物发送
     */
    hasGift: {
      type: Boolean,
      default: true
    },
    operateExtraArray: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  methods: {
    voice() {
      this.$emit('voice');
    },
    chooseImage() {
      this.$emit('chooseImage');
    },
    chooseVideo() {
      this.$emit('chooseVideo');
    },
    chooseGift() {
      this.$emit('chooseGift');
    },
    toChange() {
      this.$emit('toChange');
    },
    openUserAccountSelect() {
      this.$emit('openUserAccountSelect');
    }
  },
}
</script>

<style lang="scss" scoped>
.bar {
  box-sizing: border-box;
  color: #666666;
  height: 60rpx;
  // 超出不换行，可以拖动
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
}

// 不显示滚动条
.bar::-webkit-scrollbar {
  display: none;
}

.bar-item {
  display: inline-block;
  width: calc(100% / 5);
  height: 100%;
  text-align: center;
}

.panel-item:active{
  opacity: .5;
}

.panel-icon {
  width: 40rpx;
  height: 40rpx;
  cursor: pointer;
}
</style>
