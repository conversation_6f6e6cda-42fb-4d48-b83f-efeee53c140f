<template>
  <view>
    <view>
      <view v-if="!customUserInfoCard">
        <kt-user-info-card
            :user-info="userAccount"
        ></kt-user-info-card>
      </view>
      <view v-else>
        <slot name="userInfoCard"
              :user-account="userAccount"
        ></slot>
      </view>
      <view style="text-align: center">
        <kt-qrcode
            v-if="!isPc()"
            size="400rpx"
            content="https://www.kantboot.com"
            :image-src="$kt.file.visit(userAccount.fileIdOfAvatar)"
        ></kt-qrcode>
        <kt-qrcode
            v-else
            content="https://www.kantboot.com"
            :image-src="$kt.file.visit(userAccount.fileIdOfAvatar)">
        </kt-qrcode>
      </view>
      <view
          v-if="hasDirectCode&&userAccount.directCode"
          style="text-align: center;font-size: 26rpx;color:#333333">
        <text style="vertical-align: top">
          {{ $i18n.zhToGlobal("邀请码") }}{{ ": " }}{{ userAccount.directCode }}
        </text>
        <image
            class="copy-icon"
            @click="toCopy(userAccount.directCode)"
            :src="$kt.file.byPath('kantboot/icon/copy.svg')"></image>
      </view>
      <view style="height: 40rpx"></view>
      <view>
        <!-- 邀请按钮 --->
        <!-- #ifdef MP-WEIXIN -->
        <kt-button
            v-if="userAccount.id"
            open-type="share">
          {{ $i18n.zhToGlobal("点击邀请") }}
        </kt-button>
        <!-- #endif -->
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    /**
     * 是否含有邀请码
     */
    hasDirectCode: {
      type: Boolean,
      default: false
    },
    /**
     * 是否自定义用户信息卡片
     */
    customUserInfoCard: {
      type: Boolean,
      default: false
    },
    intoPath: {
      type: String,
      default: '/pages/pages-body/into/into'
    },
  },
  data() {
    return {
      userAccount: {}
    };
  },
  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.$kt.userAccount.getSelf().nickname,
      path: this.intoPath + '?userAccountIdOfInviter=' + this.userAccount.id,
    }
  },
  onShareAppMessage() {
    return {
      title: this.$kt.userAccount.getSelf().nickname,
      path: this.intoPath + '?userAccountIdOfInviter=' + this.userAccount.id,
    }
  },
  async created() {
    this.userAccount = this.$kt.userAccount.getSelf();
    if (!this.userAccount.directCode) {
      // 如果没有直属码，则初始化
      await this.$request.post("/user-account-web/userAccountInvite/initDirectCodeSelf");
      await this.$kt.userAccount.requestSelf();
      this.userAccount = this.$kt.userAccount.getSelf();
    }
  },
  methods: {

    isPc() {
      return this.$kt.style.detectDeviceType() === "pc";
    },
    toCopy(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: $i18n.zhToGlobal("复制成功"),
            icon: 'none'
          })
        },
        fail: (err) => {
          uni.showToast({
            title: $i18n.zhToGlobal("复制失败"),
            icon: 'none'
          })
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.copy-icon {
  width: 30rpx;
  height: 30rpx;
}

.copy-icon:active {
  transform: scale(0.8);
}
</style>
