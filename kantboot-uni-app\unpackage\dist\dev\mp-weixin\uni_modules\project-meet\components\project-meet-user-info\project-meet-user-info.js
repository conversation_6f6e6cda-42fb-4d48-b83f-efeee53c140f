(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info"],{

/***/ 1105:
/*!********************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info.vue ***!
  \********************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _project_meet_user_info_vue_vue_type_template_id_0dd96055___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./project-meet-user-info.vue?vue&type=template&id=0dd96055& */ 1106);
/* harmony import */ var _project_meet_user_info_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./project-meet-user-info.vue?vue&type=script&lang=js& */ 1108);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _project_meet_user_info_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _project_meet_user_info_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _project_meet_user_info_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./project-meet-user-info.vue?vue&type=style&index=0&lang=css& */ 1110);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _project_meet_user_info_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _project_meet_user_info_vue_vue_type_template_id_0dd96055___WEBPACK_IMPORTED_MODULE_0__["render"],
  _project_meet_user_info_vue_vue_type_template_id_0dd96055___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _project_meet_user_info_vue_vue_type_template_id_0dd96055___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1106:
/*!***************************************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info.vue?vue&type=template&id=0dd96055& ***!
  \***************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_template_id_0dd96055___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project-meet-user-info.vue?vue&type=template&id=0dd96055& */ 1107);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_template_id_0dd96055___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_template_id_0dd96055___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_template_id_0dd96055___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_template_id_0dd96055___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 1107:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info.vue?vue&type=template&id=0dd96055& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    projectMeetVgpPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/project-meet/components/project-meet-vgp-popup/project-meet-vgp-popup */ "uni_modules/project-meet/components/project-meet-vgp-popup/project-meet-vgp-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/project-meet/components/project-meet-vgp-popup/project-meet-vgp-popup.vue */ 1126))
    },
    ktSetRemarkPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/kantboot/components/kt-set-remark-popup/kt-set-remark-popup */ "uni_modules/kantboot/components/kt-set-remark-popup/kt-set-remark-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/kantboot/components/kt-set-remark-popup/kt-set-remark-popup.vue */ 1168))
    },
    ktUserPostPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/kantboot/components/kt-user-post-popup/kt-user-post-popup */ "uni_modules/kantboot/components/kt-user-post-popup/kt-user-post-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/kantboot/components/kt-user-post-popup/kt-user-post-popup.vue */ 1950))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.userAccount.fileIdOfAvatar
    ? _vm.$kt.file.visit(_vm.userAccount.fileIdOfAvatar)
    : null
  var g1 = !_vm.userAccount.fileIdOfAvatar
    ? _vm.$kt.file.byPath("image/logo.png")
    : null
  var l0 = _vm.__map(_vm.userAccount.fileIdsOfPhotos, function (item, __i0__) {
    var $orig = _vm.__get_orig(item)
    var g2 = _vm.$kt.file.visit(item)
    return {
      $orig: $orig,
      g2: g2,
    }
  })
  var m0 = _vm.isOnline(_vm.userAccount) && _vm.self.isSubAccount
  var g3 = m0 ? _vm.$i18n.zhToGlobal("在线") : null
  var m1 = m0 ? _vm.getOnlineTime(_vm.userAccount) : null
  var g4 = m0 ? _vm.$i18n.zhToGlobal("分钟") : null
  var m2 = !_vm.isOnline(_vm.userAccount) && _vm.self.isSubAccount
  var g5 = m2 ? _vm.$i18n.zhToGlobal("离线") : null
  var m3 = m2 ? _vm.getOfflineTime(_vm.userAccount) : null
  var g6 = m2 ? _vm.$i18n.zhToGlobal("分钟") : null
  var g7 =
    _vm.userAccount.genderCode === "male"
      ? _vm.$kt.date.getAge(_vm.userAccount.gmtBirthday)
      : null
  var g8 =
    _vm.userAccount.genderCode === "femele"
      ? _vm.$kt.date.getAge(_vm.userAccount.gmtBirthday)
      : null
  var g9 =
    _vm.userAccount.genderCode === "male" ? _vm.$i18n.zhToGlobal("备注") : null
  var g10 =
    _vm.userAccount.genderCode === "male" && !_vm.remark
      ? _vm.$i18n.zhToGlobal("设置备注")
      : null
  var g11 = _vm.userAccount.countryCn
    ? _vm.$i18n.zhToGlobal(_vm.userAccount.countryCn)
    : null
  var g12 = _vm.$i18n.zhToGlobal("兴趣爱好")
  var g13 =
    !_vm.userAccount.interestsIds || _vm.userAccount.interestsIds.length === 0
  var g14 = g13 ? _vm.$i18n.zhToGlobal("无") : null
  var g15 =
    _vm.userAccount.interestsIds && _vm.userAccount.interestsIds.length > 0
  var g16 = _vm.$i18n.zhToGlobal("期待的关系")
  var g17 =
    !_vm.userAccount.expectedRelationshipIds ||
    _vm.userAccount.expectedRelationshipIds.length === 0
  var g18 = g17 ? _vm.$i18n.zhToGlobal("无") : null
  var g19 =
    _vm.userAccount.expectedRelationshipIds &&
    _vm.userAccount.expectedRelationshipIds.length > 0
  var l1 = _vm.__map(
    _vm.userAccount.expectedRelationshipIds,
    function (expectedRelationship, __i2__) {
      var $orig = _vm.__get_orig(expectedRelationship)
      var g20 =
        g19 && !_vm.expectedRelationshipMap[expectedRelationship + ""]
          ? _vm.$i18n.zhToGlobal("未知")
          : null
      return {
        $orig: $orig,
        g20: g20,
      }
    }
  )
  var g21 = _vm.$i18n.zhToGlobal("自我介绍")
  var g22 = !_vm.userAccount.introduction ? _vm.$i18n.zhToGlobal("无") : null
  var g23 = _vm.$i18n.zhToGlobal("动态")
  var g24 = _vm.$kt.file.byPath("icon/arrowRight.svg")
  var g25 =
    _vm.userAccount.genderCode === "male"
      ? _vm.$i18n.zhToGlobal("为他升级")
      : null
  var g26 =
    _vm.userAccount.genderCode === "female"
      ? _vm.$i18n.zhToGlobal("为她升级")
      : null
  var g27 = _vm.$i18n.zhToGlobal("私聊")
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      return _vm.$refs.projectMeetVgpPopup.open()
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        l0: l0,
        m0: m0,
        g3: g3,
        m1: m1,
        g4: g4,
        m2: m2,
        g5: g5,
        m3: m3,
        g6: g6,
        g7: g7,
        g8: g8,
        g9: g9,
        g10: g10,
        g11: g11,
        g12: g12,
        g13: g13,
        g14: g14,
        g15: g15,
        g16: g16,
        g17: g17,
        g18: g18,
        g19: g19,
        l1: l1,
        g21: g21,
        g22: g22,
        g23: g23,
        g24: g24,
        g25: g25,
        g26: g26,
        g27: g27,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 1108:
/*!*********************************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project-meet-user-info.vue?vue&type=script&lang=js& */ 1109);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1109:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 41));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 43));
var _userAccount2 = _interopRequireDefault(__webpack_require__(/*! @/uni_modules/kantboot/libs/userAccount */ 58));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  computed: {
    userAccount: function userAccount() {
      return _userAccount2.default;
    }
  },
  props: {
    bottomFixed: {
      type: Boolean,
      default: false
    }
  },
  data: function data() {
    return {
      clazz: {
        box: this.$kt.style.toggleClass("box")
      },
      userAccountId: "",
      userAccount: {
        // 头像
        fileIdOfAvatar: "",
        interestsIds: [],
        fileIdsOfPhotos: [],
        expectedRelationshipIds: [],
        // 时间戳
        gmtVipExpire: null,
        gmtSvipExpire: null
      },
      interests: [],
      interestMap: {},
      expectedRelationships: [],
      expectedRelationshipMap: {},
      isSvip: false,
      isVip: false,
      remark: "",
      self: {
        isSubAccount: false
      }
    };
  },
  created: function created() {
    this.getInterests();
    this.getExpectedRelationships();
  },
  mounted: function mounted() {
    this.self = this.$kt.userAccount.getSelf();
  },
  methods: {
    isOnline: function isOnline(userInfo) {
      try {
        return userInfo.onlineShow.isOnlineOfShow;
      } catch (e) {
        return false;
      }
    },
    // 计算在线时间，当前时间的时间戳-userInfo.onlineShow.gmtLastOnlineOfShow
    getOnlineTime: function getOnlineTime(userInfo) {
      try {
        var dateNow = new Date().getTime();
        var dateLong = dateNow - userInfo.onlineShow.gmtLastOnlineOfShow;
        // 计算多少分钟
        var minutes = Math.floor(dateLong / (1000 * 60));
        return minutes;
      } catch (e) {
        return 0;
      }
    },
    // 计算离线时间
    getOfflineTime: function getOfflineTime(userInfo) {
      // 当前时间的时间戳-userInfo.onlineShow.gmtLastOfflineOfShow

      try {
        var dateNow = new Date().getTime();
        var dateLong = dateNow - userInfo.onlineShow.gmtLastOfflineOfShow;
        // 计算多少分钟
        var minutes = Math.floor(dateLong / (1000 * 60));
        return minutes;
      } catch (e) {
        return 0;
      }
    },
    toUserPost: function toUserPost() {
      if (this.$kt.style.detectDeviceType() === 'pc') {
        this.$refs.ktUserPostPopup.open(this.userAccountId);
        return;
      }
      this.$kt.router.navTo("/pages/project-meet-pages/user-post/user-post", {
        userAccountId: this.userAccountId
      });
    },
    getRemark: function getRemark() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _this.$request.post("/user-interrelation-web/interrelationRemark/getRemark", {
                  data: {
                    userAccountIdOfRemark: _this.userAccountId
                  }
                }).then(function (res) {
                  _this.remark = res.data.remark;
                }).catch(function (err) {});
              case 1:
              case "end":
                return _context.stop();
            }
          }
        }, _callee);
      }))();
    },
    openSetRemarkPopup: function openSetRemarkPopup() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                if (_this2.userAccountId) {
                  _context2.next = 3;
                  break;
                }
                uni.showToast({
                  title: _this2.$i18n.zhToGlobal("用户加载未完成"),
                  icon: "none"
                });
                return _context2.abrupt("return");
              case 3:
                _context2.next = 5;
                return _this2.$refs.ktSetRemarkPopup.open(_this2.userAccountId);
              case 5:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    toPrivateChat: function toPrivateChat() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var dialogId;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (_this3.userAccountId) {
                  _context3.next = 3;
                  break;
                }
                uni.showToast({
                  title: _this3.$i18n.zhToGlobal("用户加载未完成"),
                  icon: "none"
                });
                return _context3.abrupt("return");
              case 3:
                if (!(_this3.$kt.style.detectDeviceType() === 'pc')) {
                  _context3.next = 11;
                  break;
                }
                // this.$kt.router.navTo("/pages/project-meet-pages/body/body", {
                //   tabbarSelected:"messageMeet"
                // });
                uni.showLoading({
                  // title: this.$i18n.zhToGlobal("正在加载"),
                  title: ""
                });
                dialogId = 0;
                _context3.next = 8;
                return _this3.$request.post("/functional-chat-web/dialog/getOneToOneByUserAccountId", {
                  data: {
                    userAccountId: _this3.userAccountId
                  }
                }).then(function (res) {
                  dialogId = res.data.id;
                }).catch(function (err) {
                  _this3.$kt.toast.error(err);
                });
              case 8:
                // /functional-chat-web/dialogMessage/sendMessageBySelf
                // await this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf", {
                //   data: {
                //     items: [{
                //       content: "Let's see what sparks we can create!",
                //       type: "text",
                //     }],
                //     dialogId,
                //   }
                // }).catch(err => {
                //   this.$kt.toast.error(err);
                // });

                _this3.$kt.event.emit("projectMeetChatDialogMessageSend", dialogId);
                uni.hideLoading();
                return _context3.abrupt("return");
              case 11:
                _this3.$kt.router.navTo("/pages/project-meet-pages/chat-dialog/chat-dialog", {
                  userAccountId: _this3.userAccountId
                });
              case 12:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    visit: function visit(userAccountId) {
      // /fp-community-web/userAccountVisit/visit
      this.$kt.request.post("/fp-community-web/userAccountVisit/visit", {
        data: {
          userAccountId: userAccountId
        }
      }).then(function (res) {
        // 访问成功
      }).catch(function (err) {});
    },
    setUserAccountId: function setUserAccountId(userAccountId) {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _this4.visit(userAccountId);
                _this4.userAccountId = userAccountId;
                _context4.next = 4;
                return _this4.$kt.userAccount.requestById(_this4.userAccountId).then(function (res) {
                  _this4.userAccount = res;
                  // 计算是否是svip
                  // 获取当前时间
                  var currentTime = new Date().getTime();
                  _this4.isSvip = _this4.userAccount.gmtSvipExpire && _this4.userAccount.gmtSvipExpire > currentTime;
                  _this4.ktPopup = _this4.userAccount.gmtVipExpire && _this4.userAccount.gmtVipExpire > currentTime;
                });
              case 4:
                if (!_this4.userAccount.fileIdsOfPhotos) {
                  _this4.userAccount.fileIdsOfPhotos = [];
                }
                _this4.getRemark();
              case 6:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    // /project-meet-web/interests/getAll
    getInterests: function getInterests() {
      var _this5 = this;
      this.$kt.request.get("/project-meet-web/interests/getAll").then(function (res) {
        _this5.interests = res.data;
        for (var i = 0; i < _this5.interests.length; i++) {
          var interest = _this5.interests[i];
          _this5.interestMap[interest.id] = interest;
        }
      }).catch(function (err) {
        console.error("获取兴趣失败", err);
      });
    },
    // /project-meet-web/expectedRelationship/getAll
    getExpectedRelationships: function getExpectedRelationships() {
      var _this6 = this;
      this.$kt.request.get("/project-meet-web/expectedRelationship/getAll").then(function (res) {
        _this6.expectedRelationships = res.data;
        for (var i = 0; i < _this6.expectedRelationships.length; i++) {
          var expectedRelationship = _this6.expectedRelationships[i];
          _this6.expectedRelationshipMap[expectedRelationship.id + ""] = expectedRelationship;
        }
      }).catch(function (err) {
        console.error("获取期待的关系失败", err);
      });
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 1110:
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info.vue?vue&type=style&index=0&lang=css& ***!
  \*****************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project-meet-user-info.vue?vue&type=style&index=0&lang=css& */ 1111);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_project_meet_user_info_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1111:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info.vue?vue&type=style&index=0&lang=css& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info-create-component',
    {
        'uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(1105))
        })
    },
    [['uni_modules/project-meet/components/project-meet-user-info/project-meet-user-info-create-component']]
]);
