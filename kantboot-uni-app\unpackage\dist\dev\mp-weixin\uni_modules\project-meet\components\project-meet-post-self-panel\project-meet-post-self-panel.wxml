<view class="data-v-07724e95"><kt-community-post-panel vue-id="324ff5de-1" height="{{'calc(100vh - '+headerHeight+'px)'}}" is-forbid-forward="{{false}}" isForbidCollect="{{false}}" is-self="{{true}}" show-audit-status="{{true}}" data-event-opts="{{[['^cardClick',[['cardClick']]],['^cardDotClick',[['cardDotClick']]]]}}" bind:cardClick="__e" bind:cardDotClick="__e" class="data-v-07724e95" bind:__l="__l"></kt-community-post-panel><kt-community-post-operate-popup vue-id="324ff5de-2" remove-text="{{$root.g0}}" report-text="{{$root.g1}}" has-permission-setting="{{false}}" data-ref="communityPostOperatePopup" class="data-v-07724e95 vue-ref" bind:__l="__l"></kt-community-post-operate-popup></view>