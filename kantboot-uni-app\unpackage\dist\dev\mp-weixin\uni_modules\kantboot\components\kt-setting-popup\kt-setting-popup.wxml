<view class="data-v-3edcdb82"><kt-popup vue-id="c681d478-1" data-ref="ktPopup" class="data-v-3edcdb82 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-3edcdb82"><view class="box-title data-v-3edcdb82">{{''+$root.g0+''}}</view><view class="in-box data-v-3edcdb82"><scroll-view class="in-box-user-info-card data-v-3edcdb82" style="{{('height: 500px;')}}" scroll-y="{{true}}"><kt-setting-panel vue-id="{{('c681d478-2')+','+('c681d478-1')}}" has-account="{{hasAccount}}" has-phone="{{hasPhone}}" has-email="{{hasEmail}}" no-has-old-password="{{noHasOldPassword}}" has-change-password="{{hasChangePassword}}" has-data-manage="{{hasDataManage}}" has-app="{{hasApp}}" has-language="{{hasLanguage}}" has-color-mode="{{hasColorMode}}" has-about="{{hasAbout}}" has-user-agreement="{{hasUserAgreement}}" has-privacy-agreement="{{hasPrivacyAgreement}}" user-agreement-url="{{userAgreementUrl}}" privacy-agreement-url="{{privacyAgreementUrl}}" class="data-v-3edcdb82" bind:__l="__l"></kt-setting-panel></scroll-view></view></view></kt-popup></view>