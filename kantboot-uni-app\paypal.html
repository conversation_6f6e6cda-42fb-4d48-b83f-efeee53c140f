<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PayPal JS SDK Standard Integration</title>
</head>
<body>
<div class="box">
    <div id="paypal-button-container"></div>
    <p id="result-message"></p>
</div>

<script
        src="https://www.paypal.com/sdk/js?client-id=AReDI9uOa3JY6Cxvaq5_UPblcakDUROZ5xj4t1TU36BANuwIgf28jqm9sCQneC09M117l6RoxFEywBW6&components=buttons"
        data-sdk-integration-source="developer-studio">
</script>

<script>
    // 获取url参数
    function getUrlParams(url) {
        const paramsRegex = /[?&]+([^=&]+)=([^&]*)/gi;
        const params = {};
        let match;
        while (match = paramsRegex.exec(url)) {
            params[match[1]] = match[2];
        }
        return params;
    };

    let urlParams = getUrlParams(window.location.href);

    let orderNumber = urlParams.orderNumber;


</script>

<script>


    const paypalButtons = window.paypal.Buttons({
        style: {
            shape: "rect",
            layout: "vertical",
            color: "gold",
            label: "paypal",
        },
        message: {
            amount: 100,
        },
        async createOrder() {
            return orderNumber
        },
        async onApprove(data, actions) {
            // 返回上一页
            window.history.back();
        },


    });
    paypalButtons.render("#paypal-button-container");


    // Example function to show a result to the user. Your site's UI library can be used instead.
    function resultMessage(message) {
        const container = document.querySelector("#result-message");
        container.innerHTML = message;
    }
</script>
<style>
    .box{
        position: fixed;
        width: 300px;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
    }
</style>
</body>
</html>
