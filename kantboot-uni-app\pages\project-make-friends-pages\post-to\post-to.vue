<template>
  <view :class="clazz.container">
    <view :class="clazz.bg"></view>

    <view id="pageHomeHeader" style="width: 100%;top:0;left:0;">

      <view>
        <kt-nav-bar
        :title="$i18n.zhToGlobal('朋友圈')"
        ></kt-nav-bar>
      </view>
      <view>
        <view
            class="split"
        ></view>

        <view class="second-box" style="position: relative;padding: 11rpx;">
          <view class="second-box-box">
            <view class="second-box-item"
                  :class="{
            'second-box-item-selected': selected === 'friend'
          }"
                  @click="selected = 'friend'"
            >{{ $i18n.zhToGlobal('公开朋友圈') }}
            </view>
            <view class="second-box-item"
                  :class="{
            'second-box-item-selected': selected === 'privateFriend'
          }"
                  @click="selected = 'privateFriend'"
            >{{ $i18n.zhToGlobal('好友朋友圈') }}
            </view>
          </view>
          <image
              style="width: 40rpx;height: 40rpx;position: absolute;top:50%;transform: translateY(-50%);right: 50rpx;"
              class="second-icon"
              :src="$kt.file.byPath('icon/search.svg')"
          ></image>

        </view>
      </view>
    </view>
    <view>
      <swiper
          :style="{
        height: isY()?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'
      }"
          @change="swiperChange"
          :current-item-id="selected"
      >
        <swiper-item item-id="recommend">

          <project-make-friends-recommend-panel
              :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'">
          </project-make-friends-recommend-panel>

        </swiper-item>
        <swiper-item
            item-id="friend">
          <kt-community-post-panel
              @cardClick="cardClick"
              @cardDotClick="cardDotClick"
              ref="communityPostPanel"
              :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'">
          </kt-community-post-panel>
        </swiper-item>
        <swiper-item
            item-id="privateFriend">
          <kt-community-post-panel
              mode="mutualFollow"
              @cardClick="cardClick"
              @cardDotClick="cardDotClick"
              ref="communityPostPanel"
              :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px)'">
          </kt-community-post-panel>
        </swiper-item>
      </swiper>


      <kt-community-post-operate-popup
          ref="communityPostOperatePopup">
      </kt-community-post-operate-popup>

      <!--      <community-post-panel-->
      <!--          v-show="selected === 'friend'"-->
      <!--          :height="isY()?'calc(100vh - '+pageHomeHeaderHeight+'px)':'calc(100vh - '+pageHomeHeaderHeight+'px - 100rpx)'"-->
      <!--          :style="{-->
      <!--        'margin-top': isY()?'730rpx':'50rpx'-->
      <!--      }"-->
      <!--          class="community-post-panel"-->
      <!--      ></community-post-panel>-->
    </view>

    <community-post-popup ref="communityPostPopup">
    </community-post-popup>

  </view>
</template>

<script>


export default {
  data() {
    return {
      navHeight: 48,
      menuButtonWidth: 0,
      pageHomeHeaderHeight: 0,
      clazz: {
        container: this.$kt.style.toggleClass("container"),
        bg: this.$kt.style.toggleClass("bg"),
      },
      selected: 'friend',
    }
  },
  onLoad(options) {
    if (options.selected) {
      this.selected = options.selected;
    }
  },
  mounted() {
    // #ifdef MP-WEIXIN
    this.navHeight = uni.getSystemInfoSync().system.indexOf("ios") != -1 ? 44 : 48;
    // 获取胶囊信息
    this.menuButtonWidth = wx.getMenuButtonBoundingClientRect().width;
    console.log(this.menuButtonWidth, "获取胶囊信息");
    // #endif
    // 获取pageHomeHeader的高度
    this.$nextTick(() => {
      // uniapp的获取元素高度的方法
      uni.createSelectorQuery().in(this).select('#pageHomeHeader').boundingClientRect((rect) => {
        console.log(rect, "获取pageHomeHeader的高度")
        this.pageHomeHeaderHeight = rect.height;
      }).exec()
    });
    this.$kt.event.on('changeTabbar', () => {
      console.log("获取#pageHomeHeader的高度");
      this.$nextTick(() => {
        uni.createSelectorQuery()
            .in(this)
            .select("#pageHomeHeader")
            .boundingClientRect((res) => {
              console.log(res, "pageHomeHeader");
              this.pageHomeHeaderHeight = res.height;
            })
            .exec();
      });
    });
  },

  methods: {
    cardDotClick(post){
      console.log(post,"post");
      if(!this.$kt.userAccount.getIsLogin()){
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.$refs.communityPostOperatePopup.open(post);
    },
    cardClick(post){
      this.$kt.router.navTo("/pages/project-make-friends-pages/post-detail/post-detail",{
        postId: post.id
      });
    },
    toPostPopup(post) {
      this.$refs.communityPostPopup.open(post);
    },
    swiperChange(e){
      this.selected = e.target.currentItemId;
    },
    isY() {
      // 转换为大写
      let deviceType = this.$kt.style.detectDeviceType().toUpperCase();
      return deviceType === 'PC' ||
          deviceType === 'TV';

    }
  }
}
</script>

<style lang="scss" scoped>
.bg {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  //background-color: #f9f9f9;
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}

.bg-mode-color-scheme-light {
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}

.bg-mode-color-scheme-dark {
  background: #191919;
}

.header-box-1 {
  padding: 0 10rpx;
  box-sizing: border-box;
  // 不换行
  white-space: nowrap;

  .header-item {
    display: inline-block;
    font-size: 36rpx;
    letter-spacing: 3rpx;
    margin-right: 5rpx;
    padding: 10rpx 20rpx;
  }

  .header-item-selected {
    font-weight: bold;
  }
}

.scroll-view {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);

  // 超出滚动
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;
  // 隐藏滚动条
  &::-webkit-scrollbar {
    width: 0;
    height: 1rpx;
    display: none;
  }

  // 滚动
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

}

// 不显示滚动条
::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}

.second-box {
  width: 100%;

  .second-box-box {
    width: calc(100% - 100rpx);
    // 不换行
    white-space: nowrap;
    // 超出滚动
    overflow-x: auto;

    .second-box-item {
      display: inline-block;
      font-size: 32rpx;
      letter-spacing: 3rpx;
      margin-right: 5rpx;
      padding: 10rpx 20rpx;
      border-radius: 10rpx;
    }

    .second-box-item-selected {
      font-weight: bold;
    }
  }
}

.container-mode-device-pc {
  width: calc(100% - 240px - 400px);
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
}

.split {
  width: 100%;
  height: 3rpx;
  background-color: #eee;
}

.container-mode-color-scheme-dark {
  background-color: #191919;

  .split{
    background-color: #888888;
  }

  .header-box {
    background-color: #191919;
  }

  .scroll-view {
    background-color: #191919;
  }

  .second-icon {
    // 颜色反转
    filter: invert(1);
  }

  .header-box-1 {
    background-color: #191919;

    .header-item {
      color: #fff;
    }
  }

  .second-box {
    .second-box-box {
      .second-box-item {
        color: #fff;
      }

      .second-box-item-selected {
        color: #fff;
        font-weight: bold;
      }
    }
  }
}

.container-mode-device-pc {
  position: relative;
  width: calc(100% + 20px);
  padding: 0;
  box-sizing: border-box;

  .second-icon {
    margin-right: 10px;
  }
}
</style>