{"usingComponents": {"kt-box": "/uni_modules/kantboot/components/kt-box/kt-box", "kt-image-select": "/uni_modules/kantboot/components/kt-image-select/kt-image-select", "kt-avatar": "/uni_modules/kantboot/components/kt-avatar/kt-avatar", "project-meet-birthday-setting": "/uni_modules/project-meet/components/project-meet-birthday-setting/project-meet-birthday-setting", "kt-menu": "/uni_modules/kantboot/components/kt-menu/kt-menu", "kt-button": "/uni_modules/kantboot/components/kt-button/kt-button", "project-meet-weight-popup": "/uni_modules/project-meet/components/project-meet-weight-popup/project-meet-weight-popup", "project-meet-height-popup": "/uni_modules/project-meet/components/project-meet-height-popup/project-meet-height-popup", "project-meet-country-popup": "/uni_modules/project-meet/components/project-meet-country-popup/project-meet-country-popup"}, "component": true}