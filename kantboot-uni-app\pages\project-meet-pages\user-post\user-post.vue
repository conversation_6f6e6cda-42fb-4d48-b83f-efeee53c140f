<template>
  <view>
    <kt-nav-bar
    :title="$i18n.zhToGlobal('动态')"
    ></kt-nav-bar>
    <view>
      <u-loading-icon
      v-if="loading"
      mode="circle"
      size="70"
      ></u-loading-icon>
      <!-- 空空如也 -->
      <view v-if="list.length === 0&&!loading"
            class="no-data"
      >
        <view>
          <image
              class="no-data-image"
              :src="$kt.file.byPath('icon/book.svg')"
          ></image>
        </view>
        <view
            class="no-data-text"
        >{{ $i18n.zhToGlobal("空空如也") }}
        </view>

      </view>

      <view v-for="item in list"
            @click="cardClick(item.id)"
      >
        <kt-community-post-card
            :post="item"
            :is-forbid-forward="false"
            @userClick="userClick"
            :has-dot="false"
        ></kt-community-post-card>
        <kt-community-post-operate-popup
            ref="communityPostOperatePopup">
        </kt-community-post-operate-popup>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list:[],
      userAccountId: null,
      loading:false
    };
  },
  onLoad(option) {
    this.userAccountId = option.userAccountId;
    // 页面加载时可以执行一些初始化操作
    this.getListByUserAccountIdOfUploader();
  },
  methods: {
    userClick(userAccount) {
      this.$kt.router.navTo('/pages/project-meet-pages/user-info/user-info', {'userAccountId': userAccount.id});
    },
    cardClick(id){
      this.$kt.router.navTo('/pages/project-meet-pages/post-detail/post-detail', {'postId': id});
    },
    getListByUserAccountIdOfUploader() {
      this.loading = true;
      this.$request.post("/fp-community-web/post/getListByUserAccountIdOfUploader", {
        data: {
          userAccountId: this.userAccountId,
        },
      }).then((res) => {
        this.list = res.data;
        this.loading = false;
      }).catch((err) => {
        this.loading = false;
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.no-data {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 200rpx;

  .no-data-image {
    width: 100rpx;
    height: 100rpx;
    opacity: .6;
  }
}

</style>
