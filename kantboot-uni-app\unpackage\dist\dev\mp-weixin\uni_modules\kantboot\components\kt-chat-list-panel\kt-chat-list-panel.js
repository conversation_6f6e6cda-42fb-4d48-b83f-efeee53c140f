(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel"],{

/***/ 1824:
/*!********************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel.vue ***!
  \********************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _kt_chat_list_panel_vue_vue_type_template_id_7d622d22_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./kt-chat-list-panel.vue?vue&type=template&id=7d622d22&scoped=true& */ 1825);
/* harmony import */ var _kt_chat_list_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./kt-chat-list-panel.vue?vue&type=script&lang=js& */ 1827);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _kt_chat_list_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _kt_chat_list_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _kt_chat_list_panel_vue_vue_type_style_index_0_id_7d622d22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./kt-chat-list-panel.vue?vue&type=style&index=0&id=7d622d22&lang=scss&scoped=true& */ 1829);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _kt_chat_list_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _kt_chat_list_panel_vue_vue_type_template_id_7d622d22_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _kt_chat_list_panel_vue_vue_type_template_id_7d622d22_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "7d622d22",
  null,
  false,
  _kt_chat_list_panel_vue_vue_type_template_id_7d622d22_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1825:
/*!***************************************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel.vue?vue&type=template&id=7d622d22&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_template_id_7d622d22_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kt-chat-list-panel.vue?vue&type=template&id=7d622d22&scoped=true& */ 1826);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_template_id_7d622d22_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_template_id_7d622d22_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_template_id_7d622d22_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_template_id_7d622d22_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 1826:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel.vue?vue&type=template&id=7d622d22&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    ktUserInfoCard: function () {
      return __webpack_require__.e(/*! import() | uni_modules/kantboot/components/kt-user-info-card/kt-user-info-card */ "uni_modules/kantboot/components/kt-user-info-card/kt-user-info-card").then(__webpack_require__.bind(null, /*! @/uni_modules/kantboot/components/kt-user-info-card/kt-user-info-card.vue */ 767))
    },
    ktNotifyListPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/kantboot/components/kt-notify-list-popup/kt-notify-list-popup */ "uni_modules/kantboot/components/kt-notify-list-popup/kt-notify-list-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/kantboot/components/kt-notify-list-popup/kt-notify-list-popup.vue */ 2250))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var a0 = {
    id: "-1",
    srcOfAvatar: _vm.$kt.file.byPath("kantboot/icon/customerService.svg"),
    nickname: _vm.$i18n.zhToGlobal("系统通知"),
    isGenderSecret: true,
    introduction: _vm.$i18n.zhToGlobal("系统通知"),
  }
  var a1 = {
    id: "0",
    srcOfAvatar: _vm.$kt.file.byPath("kantboot/icon/customerService.png"),
    nickname: _vm.$i18n.zhToGlobal("客服"),
    isGenderSecret: true,
    introduction: _vm.$i18n.zhToGlobal("客服"),
  }
  var m0 = _vm.getUnreadCount(_vm.dialogOfCustomerService.id)
  var m1 = m0 > 0 ? _vm.getUnreadCount(_vm.dialogOfCustomerService.id) : null
  var l0 = _vm.__map(_vm.list, function (item, index) {
    var $orig = _vm.__get_orig(item)
    var m2 =
      item.id !== _vm.dialogOfCustomerService.id
        ? _vm.getUnreadCount(item.id)
        : null
    var m3 =
      item.id !== _vm.dialogOfCustomerService.id && m2 > 0
        ? _vm.getUnreadCount(item.id)
        : null
    return {
      $orig: $orig,
      m2: m2,
      m3: m3,
    }
  })
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      return _vm.$refs.notifyListPopup.open()
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        a0: a0,
        a1: a1,
        m0: m0,
        m1: m1,
        l0: l0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 1827:
/*!*********************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kt-chat-list-panel.vue?vue&type=script&lang=js& */ 1828);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1828:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 41));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 43));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  props: {
    height: {
      type: String,
      default: "100vh"
    },
    selected: {
      type: String | Number,
      default: ""
    }
  },
  watch: {
    remarkMap: {
      handler: function handler(val) {
        this.toRemark();
      },
      immediate: true
    },
    list: {
      handler: function handler(val) {
        this.toRemark();
      },
      immediate: true,
      deep: true
    }
  },
  data: function data() {
    return {
      // 组件高度
      navBarHeight: 0,
      // 是否加载完成
      isLoad: false,
      scrollTop: 0,
      list: [],
      dialogOfCustomerService: {
        id: "0"
      },
      unreadMap: {},
      notifyUnreadCount: 0,
      // 备注map
      remarkMap: {}
    };
  },
  mounted: function mounted() {},
  created: function created() {
    var _this = this;
    // 获取列表数据
    this.getInitList();
    // 监听登录成功事件
    this.$kt.event.on("login:success", function () {
      _this.isLogin = true;
      _this.getInitList();
    });

    // 接收到消息，将lastMessage更新到列表中
    this.$kt.event.on('FunctionalChatDialogMessage:sendMessage', function (lastMessage) {
      console.log(lastMessage, "pageMessage收到新消息");
      if (lastMessage && lastMessage.dialogId) {
        _this.updateMessageInList(lastMessage);
        _this.toRemark();
      }
    });

    // "FunctionalNotify:relationship"
    this.$kt.event.on("FunctionalNotify:handleRelationship", function (res) {
      if (res.unreadCount) {
        _this.notifyUnreadCount = res.unreadCount;
      } else {
        _this.notifyUnreadCount = 0;
      }
    });

    // FunctionalChatDialogMessage.setDeletedStatus
    this.$kt.event.on("FunctionalChatDialogMessage.setDeletedStatus", function (res) {
      // 重新刷新
      _this.getInitList();
    });
    this.$kt.event.on("FunctionalChatDialogMessage.handleRelationship", function (res) {
      var dialogRelationship = res.dialogRelationship;
      if (dialogRelationship && dialogRelationship.dialogId) {
        _this.unreadMap[dialogRelationship.dialogId + ""] = dialogRelationship.unreadCount;
      }
      try {
        _this.$forceUpdate();
      } catch (e) {}
    });
    this.$kt.event.on("remarkChange", function () {
      _this.getRemarkSelf();
    });
    this.getByCustomerService();
    this.getRelationshipBySelf();
  },
  methods: {
    getRelationshipBySelf: function getRelationshipBySelf() {
      var _this2 = this;
      this.$kt.request.post("/functional-notify-web/notify/getRelationshipBySelf", {
        data: {}
      }).then(function (res) {
        console.log(res, "getRelationshipBySelf");
        _this2.notifyUnreadCount = res.data.unreadCount;
      }).catch(function (err) {
        console.error("获取通知关系失败", err);
      });
    },
    // /user-interrelation-web/interrelationRemark/getSelf
    getRemarkSelf: function getRemarkSelf() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                return _context.abrupt("return", _this3.$kt.request.post("/user-interrelation-web/interrelationRemark/getBySelf", {
                  data: {}
                }).then(function (res) {
                  // this.remarkMap = res.data;
                  _this3.remarkMap = {};
                  for (var i = 0; i < res.data.length; i++) {
                    var item = res.data[i];
                    _this3.remarkMap[item.userAccountIdOfRemark + ""] = item.remark;
                  }
                  _this3.toRemark();
                }).catch(function (err) {
                  console.error("获取备注失败", err);
                }));
              case 1:
              case "end":
                return _context.stop();
            }
          }
        }, _callee);
      }))();
    },
    toRemark: function toRemark() {
      for (var i = 0; i < this.list.length; i++) {
        var item = this.list[i];
        if (this.remarkMap[item.userAccount.id] && item.userAccount) {
          item.userAccount.remark = this.remarkMap[item.userAccount.id + ""];
          console.log(item.userAccount, "item.userAccount");
        } else {}
      }
      try {
        this.$forceUpdate();
      } catch (e) {}
    },
    listToIds: function listToIds(list) {
      var ids = [];
      for (var i = 0; i < list.length; i++) {
        ids.push(list[i].id);
      }
      return ids;
    },
    getUnreadCountSelfByDialogIds: function getUnreadCountSelfByDialogIds(dialogIds) {
      var _this4 = this;
      this.$kt.request.post("/functional-chat-web/relationship/getUnreadCountSelfByDialogIds", {
        data: {
          dialogIds: dialogIds
        }
      }).then( /*#__PURE__*/function () {
        var _ref = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2(res) {
          var i, item;
          return _regenerator.default.wrap(function _callee2$(_context2) {
            while (1) {
              switch (_context2.prev = _context2.next) {
                case 0:
                  console.log(res, "getUnreadCountSelfByDialogIds");
                  _this4.unreadMap = {};
                  if (!(res.data == null)) {
                    _context2.next = 4;
                    break;
                  }
                  return _context2.abrupt("return");
                case 4:
                  for (i = 0; i < res.data.length; i++) {
                    item = res.data[i];
                    _this4.unreadMap[item.dialogId] = item.unreadCount;
                  }
                case 5:
                case "end":
                  return _context2.stop();
              }
            }
          }, _callee2);
        }));
        return function (_x) {
          return _ref.apply(this, arguments);
        };
      }()).catch(function (err) {
        console.error("获取未读消息数量失败", err);
      });
    },
    getUnreadCount: function getUnreadCount(dialogId) {
      var count = this.unreadMap[dialogId + ""];
      if (!count) {
        return 0;
      }
      return count;
    },
    getByCustomerService: function getByCustomerService() {
      var _this5 = this;
      // /functional-chat-web/dialog/getByCustomerService
      this.$kt.request.post("/functional-chat-web/dialog/getByCustomerService", {
        data: {}
      }).then( /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(res) {
          return _regenerator.default.wrap(function _callee3$(_context3) {
            while (1) {
              switch (_context3.prev = _context3.next) {
                case 0:
                  _this5.dialogOfCustomerService = res.data;
                case 1:
                case "end":
                  return _context3.stop();
              }
            }
          }, _callee3);
        }));
        return function (_x2) {
          return _ref2.apply(this, arguments);
        };
      }()).catch(function (err) {});
    },
    cardClick: function cardClick(item) {
      // this.$kt.router.navTo('/pages/chat/chat?dialogId='+item.id);
      this.$emit("cardClick", item);
    },
    scroll: function scroll(res) {
      this.scrollTop = this.$kt.util.pxToRpx(res.target.scrollTop);
    },
    /**
     * 更新消息列表中的消息或添加新对话
     * @param messageData 新消息数据
     */
    updateMessageInList: function updateMessageInList(messageData) {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var dialogId, lastMessage, existingIndex, updatedItem, updatedDialog;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                dialogId = messageData.dialogId;
                lastMessage = messageData || {}; // 查找是否已存在此对话
                existingIndex = _this6.list.findIndex(function (item) {
                  return item.id === dialogId;
                });
                if (!(existingIndex >= 0)) {
                  _context5.next = 13;
                  break;
                }
                // 对话已存在，更新最后一条消息
                updatedItem = _objectSpread(_objectSpread({}, _this6.list[existingIndex]), {}, {
                  lastMessage: lastMessage,
                  lastMessageJsonStr: JSON.stringify(lastMessage)
                }); // 更新对话信息
                _context5.next = 8;
                return _this6.updateItem(updatedItem);
              case 8:
                updatedDialog = _context5.sent;
                // 删除旧位置
                _this6.list.splice(existingIndex, 1);
                // 添加到列表顶部
                _this6.list.unshift(updatedDialog);
                _context5.next = 14;
                break;
              case 13:
                // 对话不存在，获取对话信息并添加到列表
                _this6.$kt.request.post("/functional-chat-web/dialog/getById", {
                  data: {
                    id: dialogId
                  }
                }).then( /*#__PURE__*/function () {
                  var _ref3 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4(res) {
                    var newDialog, _updatedDialog;
                    return _regenerator.default.wrap(function _callee4$(_context4) {
                      while (1) {
                        switch (_context4.prev = _context4.next) {
                          case 0:
                            if (!res.data) {
                              _context4.next = 7;
                              break;
                            }
                            newDialog = _objectSpread(_objectSpread({}, res.data), {}, {
                              lastMessage: lastMessage,
                              lastMessageJsonStr: JSON.stringify(lastMessage)
                            }); // 完善对话信息
                            _context4.next = 4;
                            return _this6.updateItem(newDialog);
                          case 4:
                            _updatedDialog = _context4.sent;
                            // 添加到列表顶部
                            _this6.list.unshift(_updatedDialog);
                            _this6.toRemark();
                          case 7:
                          case "end":
                            return _context4.stop();
                        }
                      }
                    }, _callee4);
                  }));
                  return function (_x3) {
                    return _ref3.apply(this, arguments);
                  };
                }());
              case 14:
                _context5.next = 19;
                break;
              case 16:
                _context5.prev = 16;
                _context5.t0 = _context5["catch"](0);
                console.error("更新消息列表失败", _context5.t0);
              case 19:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 16]]);
      }))();
    },
    /**
     * 修改item信息
     */
    updateItem: function updateItem(item) {
      var _this7 = this;
      return new Promise(function (resolve, reject) {
        var oneToOneIdentifierSplit = item.oneToOneIdentifier.split("&");
        var userAccountId = oneToOneIdentifierSplit[0].split(":")[1];
        if (userAccountId + "" === _this7.$kt.userAccount.getSelf().id + "") {
          userAccountId = oneToOneIdentifierSplit[1].split(":")[1];
        }
        _this7.$kt.userAccount.getById(userAccountId).then(function (res) {
          console.log(res, "userAccount");
          var lastMessage = {};
          if (item.lastMessage) {
            lastMessage = item.lastMessage;
          } else {
            // 最后一条消息
            lastMessage = JSON.parse(item.lastMessageJsonStr || "{}");
            console.log(lastMessage, "lastMessage");
          }
          res.remark = _this7.remarkMap[userAccountId + ""] || "";
          res.introduction = "";
          try {
            var type = lastMessage.items[0].type;
            var content = lastMessage.items[0].content;
            if (type === "text") {
              res.introduction = content;
            } else if (type.startsWith("image")) {
              res.introduction = "[" + _this7.$i18n.zhToGlobal("图片") + "]";
            } else if (type.startsWith("userAccount")) {
              res.introduction = "[" + _this7.$i18n.zhToGlobal("用户名片") + "]";
            } else if (type.startsWith("video")) {
              res.introduction = "[" + _this7.$i18n.zhToGlobal("视频") + "]";
            } else if (type.startsWith("audio")) {
              res.introduction = "[" + _this7.$i18n.zhToGlobal("音频") + "]";
            } else if (type.startsWith("projectMeetGift")) {
              res.introduction = "[" + _this7.$i18n.zhToGlobal("礼物") + "]";
            } else if (type.startsWith("projectMeetGold")) {
              res.introduction = "[" + _this7.$i18n.zhToGlobal("金币") + "]";
            } else {
              res.introduction = "[" + _this7.$kt.date.toReadable(lastMessage.gmtCreate) + "]";
            }
          } catch (err) {
            reject(err);
          }
          item = _objectSpread(_objectSpread({}, item), {}, {
            userAccount: res,
            lastMessageJsonStr: lastMessage
          });
          resolve(item);
        });
      }).catch(function (err) {
        reject(item);
      });
    },
    getInitList: function getInitList() {
      var _this8 = this;
      // /functional-chat-web/dialog/getBySelf
      this.$kt.request.post("/functional-chat-web/dialog/getBySelf", {
        data: {}
      }).then( /*#__PURE__*/function () {
        var _ref4 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6(res) {
          var list, i, item;
          return _regenerator.default.wrap(function _callee6$(_context6) {
            while (1) {
              switch (_context6.prev = _context6.next) {
                case 0:
                  _this8.list = [];
                  list = res.data;
                  console.log(list, "userAccount");
                  i = 0;
                case 4:
                  if (!(i < list.length)) {
                    _context6.next = 15;
                    break;
                  }
                  item = list[i];
                  if (item.oneToOneIdentifier) {
                    _context6.next = 8;
                    break;
                  }
                  return _context6.abrupt("continue", 12);
                case 8:
                  _context6.next = 10;
                  return _this8.updateItem(item).then(function (res) {
                    _this8.list.push(res);
                  });
                case 10:
                  _this8.getUnreadCountSelfByDialogIds(_this8.listToIds(_this8.list));
                  _this8.getRemarkSelf();
                case 12:
                  i++;
                  _context6.next = 4;
                  break;
                case 15:
                case "end":
                  return _context6.stop();
              }
            }
          }, _callee6);
        }));
        return function (_x4) {
          return _ref4.apply(this, arguments);
        };
      }()).catch(function (err) {});
    }
  }
};
exports.default = _default;

/***/ }),

/***/ 1829:
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel.vue?vue&type=style&index=0&id=7d622d22&lang=scss&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_style_index_0_id_7d622d22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kt-chat-list-panel.vue?vue&type=style&index=0&id=7d622d22&lang=scss&scoped=true& */ 1830);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_style_index_0_id_7d622d22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_style_index_0_id_7d622d22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_style_index_0_id_7d622d22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_style_index_0_id_7d622d22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_kt_chat_list_panel_vue_vue_type_style_index_0_id_7d622d22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1830:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel.vue?vue&type=style&index=0&id=7d622d22&lang=scss&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel-create-component',
    {
        'uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(1824))
        })
    },
    [['uni_modules/kantboot/components/kt-chat-list-panel/kt-chat-list-panel-create-component']]
]);
