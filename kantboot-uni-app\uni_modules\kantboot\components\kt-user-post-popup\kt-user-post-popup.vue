<template>
  <view>
    <kt-popup ref="ktPopup">
      <view
      :class="clazz.box"
      >
        <view class="in-box">
          <kt-user-post-panel
              v-if="userAccountId"
              :user-account-id="userAccountId">
          </kt-user-post-panel>

        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box")
      },
      userAccountId: "",
    };
  },
  methods: {
    open(userAccountId) {
      this.userAccountId = userAccountId;
      this.$refs.ktPopup.open();
    },
    close() {
      this.$refs.ktPopup.close();
    }
  }

}
</script>

<style lang="scss" scoped>
.box {
  padding: 20rpx 40rpx 20rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
}

.in-box{
  height: calc(100vh - 300rpx);
  overflow-y: auto;
}

.box-mode-device-pc{
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 20rpx;
  padding: 40rpx;
  width: 600px;
  .in-box{
  }
}

</style>
