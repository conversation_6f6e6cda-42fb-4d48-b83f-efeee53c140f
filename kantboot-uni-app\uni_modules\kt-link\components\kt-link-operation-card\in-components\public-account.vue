<template>
  <kt-box class="form-item"
          :title="$i18n.zhToGlobal('公众号信息')">
    <kt-box class="in-form-item">
      <kt-box
          :title="$i18n.zhToGlobal('公众号头像')"
          class="form-item">
        <kt-image-select
            v-model="fileIdsOfAvatar"
            :count="1"
            file-group-code="fp"
        ></kt-image-select>
        <view
            v-if="showParams"
            class="form-item-param">{{"params.fileIdOfAvatar"}}</view>

      </kt-box>
      <kt-box
          :title="$i18n.zhToGlobal('公众号名称')"
          class="form-item">
        <view class="form-item-input">
          <input
              v-model="params.name"
              type="text"
              placeholder="输入公众号名称"
              class="input"/>
        </view>
        <view
            v-if="showParams"
            class="form-item-param">{{params.name}}</view>
      </kt-box>

      <kt-box
          :title="$i18n.zhToGlobal('公众号描述')"
          class="form-item">
        <view class="form-item-input">
          <input
              v-model="params.description"
              type="text"
              placeholder="输入公众号描述"
              class="input"/>
        </view>
        <view
            v-if="showParams"
            class="form-item-param">{{params.description}}</view>
      </kt-box>

      <kt-box
          :title="$i18n.zhToGlobal('公众号二维码')"
          class="form-item">
        <kt-image-select
            v-model="fileIdsOfQrcode"
            :count="1"
            file-group-code="fp"
        ></kt-image-select>
        <view
            v-if="showParams"
            class="form-item-param">{{"params.fileIdOfQrcode"}}</view>
      </kt-box>
    </kt-box>
  </kt-box>
</template>

<script>
export default {
  props: {
    showParams: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileIdsOfAvatar: [],
      fileIdsOfQrcode: [],
      params: {
        fileIdOfAvatar: '',
        name: '',
        description: '',
        fileIdOfQrcode: ''
      }
    };
  },
  watch: {
    fileIdsOfAvatar: {
      handler(val) {
        if (val.length === 0) {
          this.params.fileIdOfAvatar = '';
          return;
        }
        this.params.fileIdOfAvatar = val[0];
        this.changeParams();
      },
      immediate: true,
      deep: true
    },
    fileIdsOfQrcode: {
      handler(val) {
        if (val.length === 0) {
          this.params.fileIdOfQrcode = '';
          return;
        }
        this.params.fileIdOfQrcode = val[0];
        this.changeParams();
      },
      immediate: true,
      deep: true
    },
    params: {
      handler(val) {
        this.changeParams();
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.changeParams();
  },
  methods: {
    changeParams() {
      this.$emit('changeParams', this.params);
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../css/kt-link-operation-card.scss";
</style>
