<template>
  <view>
    <kt-popup
        :overlayClose="false"
    ref="ktPopup"
    @close="close"
    :zIndex="999999999"
    >

      <view class="box">
        <view class="close-btn" @click="closePop">×</view>
        <view class="title">{{$i18n.zhToGlobal("初始化信息")}}</view>

        <scroll-view
            v-if="isLogin&&self.id"
        :scroll-y="true"
        style="height: calc(100vh - 500rpx)"
        >


        <view class="in-box">
          <view class="in-box-input">
            <image
                class="in-box-input-icon"
              :src="$kt.file.byPath('icon/phone.svg')">
            </image>
            <input
                class="in-box-input-input"
                length="11"
                v-model="requestParams.phone"
                @input="requestParams.phone = $event.detail.value"
                :placeholder="$i18n.zhToGlobal('输入手机号')"
            ></input>
          </view>
        </view>

        <view class="in-box">
          <view class="in-box-input">
            <image
                class="in-box-input-icon"
                :src="$kt.file.byPath('icon/key.svg')"></image>
            <input
                class="in-box-input-input"
                type="password"
                v-model="requestParams.password"
                @input="requestParams.password = $event.detail.value"
                v-if="!passwordShow"
                :placeholder="$i18n.zhToGlobal('输入密码')"></input>
            <input
                class="in-box-input-input"
                type="text"
                v-model="requestParams.password"
                @input="requestParams.password = $event.detail.value"
                v-if="passwordShow"
                :placeholder="$i18n.zhToGlobal('输入密码')"></input>
            <image
                class="in-box-input-icon"
                v-if="!passwordShow"
                :src="$kt.file.byPath('icon/eye.svg')"></image>
            <image
                class="in-box-input-icon"
                v-if="passwordShow"
                :src="$kt.file.byPath('icon/eyeOff.svg')"></image>
            <view
                class="in-box-input-show-change"
                @click="passwordShowChange()">
            </view>
          </view>
        </view>

        <view class="in-box">
          <view class="in-box-input">
            <image
                class="in-box-input-icon"
                :src="$kt.file.byPath('icon/key.svg')"></image>
            <input
                class="in-box-input-input"
                type="password"
                v-model="passwordConfirm"
                @input="passwordConfirm = $event.detail.value"
                v-if="!passwordConfirmShow"
                :placeholder="$i18n.zhToGlobal('再次输入密码')"></input>
            <input
                class="in-box-input-input"
                type="text"
                v-model="passwordConfirm"
                @input="passwordConfirm = $event.detail.value"
                v-if="passwordConfirmShow"
                :placeholder="$i18n.zhToGlobal('再次输入密码')"></input>
            <image
                class="in-box-input-icon"
                v-if="!passwordConfirmShow"
                :src="$kt.file.byPath('icon/eye.svg')"></image>
            <image
                class="in-box-input-icon"
                v-if="passwordConfirmShow"
                :src="$kt.file.byPath('icon/eyeOff.svg')"></image>
            <view
            class="in-box-input-show-change"
            @click="passwordConfirmShowChange()"
            ></view>
          </view>
          <view style="height: 50rpx"></view>
          <view class="common-info-text">{{$i18n.zhToGlobal("基本信息")}}{{" "}}{{"("}}{{$i18n.zhToGlobal("必填")}}{{")"}}</view>
          <project-make-friends-user-info-panel
              :user-account-id="self.id"
              :show-relation="false"
              :show-user-post="false"
          ></project-make-friends-user-info-panel>
        </view>
        </scroll-view>


        <view style="height: 30rpx"></view>

        <kt-button
            ref="ktButton"
        @click="toBind()">
          {{$i18n.zhToGlobal("确定绑定")}}
        </kt-button>
      </view>

    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      passwordShow: false,
      passwordConfirmShow: false,
      requestParams: {
        phoneAreaCode: '86',
        phone: '',
        password: '',
      },
      passwordConfirm: '',
      self: {},
      isLogin: false,
    };
  },
  mounted() {
    // 获取是否在登录状态
    let isLogin = this.$kt.userAccount.getIsLogin();
    this.isLogin = isLogin;
    if (isLogin) {
      // 检查是否已经绑定手机号
      let self = this.$kt.userAccount.getSelf();
      this.self = self;
      if (self.phone && self.phone.length > 0) {
        this.$refs.ktPopup.close();
        this.$emit("phone:bindSuccess");
        this.$kt.event.emit("phone:bindSuccess", self);
        return;
      }
      this.$refs.ktPopup.open();
      return;
    }
    this.$kt.event.on("login:success", () => {
      let self = this.$kt.userAccount.getSelf();
      this.self=self;
      this.isLogin = true;

      console.log("登录成功，检查手机号绑定状态", self);
      if (self.phone && self.phone.length > 0) {
        this.$refs.ktPopup.close();
        this.$emit("phone:bindSuccess");
        this.$kt.event.emit("phone:bindSuccess", self);
        return;
      }

      this.$refs.ktPopup.open();
    });

  },
  methods: {
    passwordShowChange(){
      this.passwordShow = !this.passwordShow;
      try {
        this.$forceUpdate();
      }catch (e){

      }
    },
    passwordConfirmShowChange(){
      this.passwordConfirmShow = !this.passwordConfirmShow;
      try {
        this.$forceUpdate();
      }catch (e){
      }
    },
    closePop() {
      uni.showModal({
        title: this.$i18n.zhToGlobal('提示'),
        content: this.$i18n.zhToGlobal('确定要取消登录并返回首页吗？退出后需要重新登录才能使用完整功能。'),
        confirmText: this.$i18n.zhToGlobal('确定'),
        cancelText: this.$i18n.zhToGlobal('取消'),
        success: (res) => {
          if (res.confirm) {
            this.$refs.ktPopup.close();
            // 登出逻辑
            this.$kt.userAccount.setIsLogin(false);
            this.$kt.storage.remove("token");
            uni.reLaunch({
              url: this.$kt.router.config.intoPath
            });
          }
        }
      });
    },
    async toBind(){
      if (this.requestParams.phone.length !== 11) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("手机号格式不正确"));
        return;
      }
      if (this.requestParams.password.length < 6) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("密码长度不能小于6位"));
        return;
      }
      if (this.requestParams.password !== this.passwordConfirm) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("两次输入的密码不一致"));
        return;
      }
      this.$refs.ktButton.loading(null, 999999);
      await this.$kt.userAccount.requestSelf();
      this.self = this.$kt.userAccount.getSelf();
      // 如果昵称没填写
      if (!this.self.nickname || this.self.nickname.length < 1) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未设置昵称"));
        return;
      }
      if (!this.self.fileIdOfAvatar) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未设置头像"));
        return;
      }
      if(!this.self.gmtBirthday){
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未设置生日"));
        return;
      }
      // 如果个人签名
      if (!this.self.introduction || this.self.introduction.length < 1) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未设置个人签名"));
        return;
      }
      // interests
      if (!this.self.interests || this.self.interests.length < 1) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未设置兴趣爱好"));
        return;
      }
      // characteristics
      if (!this.self.characteristics || this.self.characteristics.length < 1) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未设置个人特点"));
        return;
      }

      if(this.self.characteristics.length<3){
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("个人特点至少需要3个"));
        return;
      }

      // personalIntroduction
      if (!this.self.personalIntroduction || this.self.personalIntroduction.length < 1) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未设置个人简介"));
        return;
      }


      this.$request.post("/project-make-friends-web/userAccount/bindPhoneAndPassword",{
        data: this.requestParams,
      }).then(res => {
        this.$refs.ktButton.success(res.msg);
        this.$refs.ktPopup.close();
        this.$kt.userAccount.requestSelf();
        this.$emit("phone:bindSuccess");
        this.$kt.event.emit("phone:bindSuccess", this.requestParams);
      }).catch(err => {
        this.$refs.ktButton.error(err.errMsg);
      })

    },
  },
}
</script>

<style lang="scss" scoped>
.box{
  .title{
    font-size: 32rpx;
    color: #333333;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
  padding: 50rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  .in-box{
    padding: 20rpx;
    .in-box-input{
      position: relative;
      background-color: #f0f0f0;
      padding: 30rpx;
      border-radius: 20rpx;
      .in-box-input-icon{
        width: 40rpx;
        height: 40rpx;
        display: inline-block;
        vertical-align: middle;
      }
      .in-box-input-input{
        position: relative;
        vertical-align: top;
        display: inline-block;
        letter-spacing: 2rpx;
        margin-left: 10rpx;
        width: calc(100% - 100rpx);
        border: none;
        font-size: 28rpx;
        color: #333333;
        z-index:2;
      }
      .in-box-input-show-change{
        position: absolute;
        right: 0;
        top: 0;
        width: 100rpx;
        height: 100%;
        opacity: 0.1;
        z-index: 99999;
      }
    }
  }
}
.common-info-text{
  font-size: 28rpx;
  color: #666666;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  text-align: center;
}
</style>

<style scoped>
.close-btn {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #666;
  z-index: 10;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  user-select: none;
  outline: none;
}
.close-btn:hover,
.close-btn:active {
  background: #f5f5f5;
  color: #666;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transform: none;
}
</style>
