<view class="data-v-fc9ed52a"><view class="box data-v-fc9ed52a"><view class="data-v-fc9ed52a"><textarea class="box-textarea data-v-fc9ed52a" placeholder="{{$root.g0}}" data-event-opts="{{[['input',[['__set_model',['$0','text','$event',[]],['requestParam']]]]]}}" value="{{requestParam.text}}" bindinput="__e"></textarea></view><view style="height:30rpx;" class="data-v-fc9ed52a"></view><kt-image-select vue-id="973ace0a-1" file-group-code="fp" data-event-opts="{{[['^input',[['e0']]]]}}" bind:input="__e" class="data-v-fc9ed52a" bind:__l="__l"></kt-image-select><view style="height:30rpx;" class="data-v-fc9ed52a"></view><view class="box-bottom data-v-fc9ed52a"><kt-button vue-id="973ace0a-2" border-radius="20rpx" is-open-box-shadow="{{false}}" data-ref="pushButton" data-event-opts="{{[['^click',[['push']]]]}}" bind:click="__e" class="data-v-fc9ed52a vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{''+$root.g1+''}}</kt-button><block wx:if="{{isShowPermissionSetting}}"><kt-community-post-permission-setting-box generic:scoped-slots-userInfoCardOfPartVisible="kt-community-post-push-kt-community-post-permission-setting-box-userInfoCardOfPartVisible" data-vue-generic="scoped" generic:scoped-slots-userInfoCardOfNotVisible="kt-community-post-push-kt-community-post-permission-setting-box-userInfoCardOfNotVisible" bind:openPartVisible="__e" bind:openNotVisible="__e" bind:input="__e" vue-id="973ace0a-3" data-ref="fpCommunityPostPermissionSettingBox" value="{{requestParam.permission}}" data-event-opts="{{[['^openPartVisible',[['openPartVisible']]],['^openNotVisible',[['openNotVisible']]],['^input',[['__set_model',['$0','permission','$event',[]],['requestParam']]]]]}}" class="data-v-fc9ed52a vue-ref" bind:__l="__l" vue-slots="{{['userInfoCardOfNotVisible','userInfoCardOfPartVisible']}}"></kt-community-post-permission-setting-box></block></view></view></view>