<view class="data-v-006f6cc7"><view class="header data-v-006f6cc7" id="projectMeetPagesHeader"><kt-nav-bar vue-id="0d7ead1f-1" title="{{title}}" class="data-v-006f6cc7" bind:__l="__l"></kt-nav-bar></view><view class="bg data-v-006f6cc7"></view><view class="panel-box data-v-006f6cc7"><block wx:if="{{ifa}}"><kt-chat-dialog-panel generic:scoped-slots-messageItem="chat-dialog-kt-chat-dialog-panel-messageItem" data-vue-generic="scoped" vue-id="0d7ead1f-2" has-read="{{self.isSubAccount}}" copy="{{self.isSubAccount}}" height="{{'calc(100vh - '+headerHeight+'px - '+footerHeight+'px)'}}" dialog-id="{{dialogId}}" data-event-opts="{{[['^userCardClick',[['userCardClick']]],['^load',[['dialogLoad']]]]}}" bind:userCardClick="__e" bind:load="__e" class="data-v-006f6cc7" bind:__l="__l" vue-slots="{{['messageItem']}}"></kt-chat-dialog-panel></block><block wx:if="{{dialog.id&&dialog.userAccountId&&!isCustomerService}}"><view class="data-v-006f6cc7"><block wx:if="{{userAccount.genderCode==='female'}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="charge-button data-v-006f6cc7" bindtap="__e">{{$root.g0+''}}</view></block><block wx:if="{{userAccount.genderCode==='male'}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="charge-button data-v-006f6cc7" bindtap="__e">{{''+$root.g1+''}}</view></block></view></block><block wx:if="{{dialog.id&&dialog.userAccountId&&self.isSubAccount&&!isCustomerService}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="remark-button data-v-006f6cc7" bindtap="__e">{{$root.g2}}</view></block></view><view class="footer data-v-006f6cc7" id="projectMeetPageFooter"><kt-send-input vue-id="0d7ead1f-5" has-user-account="{{false}}" has-voice="{{false}}" has-gift="{{true&&!isCustomerService}}" mode="bar" data-ref="sendInput" data-event-opts="{{[['^change',[['change']]],['^send',[['send']]],['^chooseGift',[['chooseGift']]]]}}" bind:change="__e" bind:send="__e" bind:chooseGift="__e" class="data-v-006f6cc7 vue-ref" bind:__l="__l" vue-slots="{{['operateExtra']}}"><view style="display:inline-block;" class="data-v-006f6cc7" slot="operateExtra" wx:if="{{!isCustomerService}}"><block wx:if="{{!self.isSubAccount}}"><image class="input-icon data-v-006f6cc7" src="{{$root.g3}}" data-event-opts="{{[['tap',[['openGoldPopup']]]]}}" bindtap="__e"></image></block><view style="display:inline-block;margin-left:30rpx;vertical-align:top;" class="data-v-006f6cc7"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="kt-checkbox data-v-006f6cc7" bindtap="__e"><image class="input-language-icon data-v-006f6cc7" src="{{$root.g4}}"></image><block wx:if="{{!targetLanguage.code}}"><text class="data-v-006f6cc7">{{$root.g5}}</text></block><block wx:else><text class="data-v-006f6cc7">{{''+targetLanguage.i18nName+''}}</text></block></view></view></view></kt-send-input></view><block wx:if="{{dialog.id&&dialog.userAccountId}}"><project-meet-vgp-popup vue-id="0d7ead1f-6" userAccountId="{{dialog.userAccountId}}" data-ref="projectMeetVgpPopup" class="data-v-006f6cc7 vue-ref" bind:__l="__l"></project-meet-vgp-popup></block><project-meet-gift-popup vue-id="0d7ead1f-7" data-ref="projectMeetGiftPopup" class="data-v-006f6cc7 vue-ref" bind:__l="__l"></project-meet-gift-popup><project-meet-gold-transfer-popup vue-id="0d7ead1f-8" data-ref="projectMeetGoldTransferPopup" class="data-v-006f6cc7 vue-ref" bind:__l="__l"></project-meet-gold-transfer-popup><project-meet-user-info-popup vue-id="0d7ead1f-9" data-ref="usPopup" class="data-v-006f6cc7 vue-ref" bind:__l="__l"></project-meet-user-info-popup><project-meet-points-popup vue-id="0d7ead1f-10" hasSelfNumber="{{false}}" user-account-id="{{self.id}}" data-ref="projectMeetPointsPopup" class="data-v-006f6cc7 vue-ref" bind:__l="__l"></project-meet-points-popup><project-meet-vip-popup vue-id="0d7ead1f-11" data-ref="projectMeetVipPopup" class="data-v-006f6cc7 vue-ref" bind:__l="__l"></project-meet-vip-popup><project-meet-gold-popup vue-id="0d7ead1f-12" data-ref="projectMeetGoldPopup" class="data-v-006f6cc7 vue-ref" bind:__l="__l"></project-meet-gold-popup><kt-set-remark-popup vue-id="0d7ead1f-13" data-ref="setRemarkPopup" class="data-v-006f6cc7 vue-ref" bind:__l="__l"></kt-set-remark-popup><kt-language-select-popup vue-id="0d7ead1f-14" reset="{{false}}" data-ref="ktLanguageSelectPopup" data-event-opts="{{[['^select',[['selectLanguage']]]]}}" bind:select="__e" class="data-v-006f6cc7 vue-ref" bind:__l="__l" vue-slots="{{['topLeft']}}"><view data-event-opts="{{[['tap',[['toUnTranslate']]]]}}" class="top-left-btn data-v-006f6cc7" bindtap="__e" slot="topLeft">{{$root.g6}}</view></kt-language-select-popup></view>