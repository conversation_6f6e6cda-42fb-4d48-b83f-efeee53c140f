<template>
  <view>
    <view class="container">
      <view class="box">
        <user-info-card-edit
            @click="$refs.userInfoPopup.open(userAccount)"
            style="position: absolute;right: 10px;" v-if="isSelf"></user-info-card-edit>
        <kt-user-info-card
            :userInfo="userAccount"></kt-user-info-card>
        <view style="height: 30rpx;"></view>
        <view class="box-data"
        v-if="showRelation"
        >
          <view
              @click="toInterrelation('mutualFollow')"
              class="box-data-item">
            <view class="box-data-item-num">
              {{getInterrelation(userAccount).mutualFollowCount || '0'}}
            </view>
            <view class="box-data-item-text">{{ $i18n.zhToGlobal("好友数") }}</view>
          </view>
          <view
              @click="toInterrelation('follow')"
              class="box-data-item">
            <view class="box-data-item-num">
              {{getInterrelation(userAccount).followCount || '0'}}
            </view>
            <view class="box-data-item-text">{{ $i18n.zhToGlobal("关注的人") }}</view>
          </view>

          <view
              @click="toInterrelation('followed')"
              class="box-data-item">
            <view class="box-data-item-num">
              {{getInterrelation(userAccount).followedCount || '0'}}
            </view>
            <view class="box-data-item-text">{{ $i18n.zhToGlobal("粉丝数") }}</view>
          </view>
        </view>


        <view
            v-if="!isSelf&&loadFinish"
            style="height: 30rpx;"></view>
        <view
            v-if="!isSelf&&loadFinish"
            class="box-btn-box">
          <view
              @click="requestFollow()"
              v-if="!isFollow"
              class="box-btn box-fllow-btn">
            {{ $i18n.zhToGlobal("关注") }}
          </view>
          <view
              v-else
              @click="$refs.unFollowPopup.open(userAccount)"
              class="box-btn box-fllow-btn-sel">{{ $i18n.zhToGlobal("已关注") }}{{" "}}▼</view>
          <view
              style="float: right;"
              @click="toChat()"
              class="box-btn">{{ $i18n.zhToGlobal("私信") }}</view>
        </view>

      </view>

      <view
          v-if="showUserPost"
          class="box" style="padding-top:0;padding-bottom: 0;">
        <kt-menu
            v-if="isSelf"
            @click="toPostSelf()"
            :title="$i18n.zhToGlobal('我的朋友圈')"
            :is-right="true"
            :icon="$kt.file.byPath('icon/dynamics.svg')"
        ></kt-menu>
        <kt-menu
            v-else
            @click="toUserPost(userAccountId)"
            :title="$i18n.zhToGlobal('朋友圈')"
            :is-right="true"
            :icon="$kt.file.byPath('icon/dynamics.svg')"
        ></kt-menu>

      </view>



      <view
          class="box">
        <view class="box-title" style="position: relative;display: block;">
          {{ $i18n.zhToGlobal("是否为素食主义者11") }}
          <view
              v-if="!isSelf"
              style="position: absolute;right: 10rpx;top:0;color: #666;">{{
              userAccount.vegetarian ? $i18n.zhToGlobal('是') : $i18n.zhToGlobal('否') }}</view>

          <view
              v-if="isSelf&&loadFinish"
              style="position: absolute;right: 98rpx;top:0;color: #666;">{{
              userAccount.vegetarian ? $i18n.zhToGlobal('是') : $i18n.zhToGlobal('否') }}</view>
          <view
              v-if="isSelf&&loadFinish"
              style="position: absolute;right: 0rpx;top:0;">
            <u-switch activeColor="#000000" :size="40" v-model="userAccount.vegetarian"></u-switch>
          </view>

          <view
              v-if="isSelf&&loadFinish"
              @click="openChangeVegetarian()"
              style="position: absolute;right: 0rpx;top:0;width: 100rpx;height: 100%;background-color: rgba(0,0,0,0);">
          </view>

        </view>

      </view>

      <view
          v-if="userAccount.vegetarian"
          class="box">

        <view class="box-title">{{ $i18n.zhToGlobal("素食时长") }}</view>
        <view class="box-data box-data-2">
          <view class="tag tag-v"
                v-if="$i18n.getLanguageCode() === 'zh_CN' || $i18n.getLanguageCode() === 'zh_HK' || $i18n.getLanguageCode() === 'zh_MO' || $i18n.getLanguageCode() === 'zh_TW'">
            {{ "素食" + $kt.math.sub(currentYear, userAccount.vegetarianStartYear) + "年" }}
          </view>
          <view class="tag tag-v" v-else>{{ $i18n.zhToGlobal("素食年份") }}{{ ": " }}<view
              style="display: inline-block;font-weight: bold;margin-left: 8rpx;">{{
              $kt.math.sub(currentYear, userAccount.vegetarianStartYear) }}</view>
          </view>
          <user-info-card-edit @click="openVegetarianPicker()"
                               style="display: inline-block;margin-left: 10rpx;" v-if="isSelf"></user-info-card-edit>

        </view>
      </view>


      <view
          v-if="isSelf"
          class="box">
        <view class="box-title" style="position: relative;">
          {{ $i18n.zhToGlobal("出生时间") }}

        </view>
        <view class="box-data box-data-2">
          <view style="position: relative;">


            <view
                v-if="userAccount.gmtBirthday"
                class="tag tag-v">
              <view
                  style="display: inline-block;margin-right: 10rpx;"
                  v-if="$i18n.getLanguageCode() === 'zh_CN' || $i18n.getLanguageCode() === 'zh_HK' || $i18n.getLanguageCode() === 'zh_MO' || $i18n.getLanguageCode() === 'zh_TW'"
              >{{ $i18n.zhToGlobal("公历") }}{{":"}}</view>

              <view
                  style="display: inline-block;"
                  v-if="isBithdayDateShow">
                {{ $kt.date.format(userAccount.gmtBirthday, 'yyyy-MM-DD hh:mm') }}
              </view>
              <view
                  style="display: inline-block;"
                  v-else>
                {{ "******" }}
              </view>

            </view>
            <view
                v-else
                class="tag tag-v">
              {{ $i18n.zhToGlobal("未设置") }}
            </view>
            <user-info-card-edit @click="$refs.birthDatePicker.open(userAccount)"
                                 style="display: inline-block;margin-left: 10rpx;" v-if="isSelf"></user-info-card-edit>
          </view>
          <view
              v-if="$i18n.getLanguageCode() === 'zh_CN' || $i18n.getLanguageCode() === 'zh_HK' || $i18n.getLanguageCode() === 'zh_MO' || $i18n.getLanguageCode() === 'zh_TW'"

              style="position: relative;">
            <view
                v-if="userAccount.gmtBirthday"
                class="tag tag-v">
              <view
                  style="display: inline-block;margin-right: 10rpx;"
                  v-if="$i18n.getLanguageCode() === 'zh_CN' || $i18n.getLanguageCode() === 'zh_HK' || $i18n.getLanguageCode() === 'zh_MO' || $i18n.getLanguageCode() === 'zh_TW'"
              >{{ $i18n.zhToGlobal("农历") }}{{":"}}</view>

              <view
                  style="display: inline-block;"
                  v-if="isBithdayDateShow">
                {{ $kt.date.format(userAccount.gmtBirthday, 'yyyy-MM-DD hh:mm') }}
              </view>
              <view
                  style="display: inline-block;"
                  v-else>
                {{ "******" }}
              </view>

            </view>
            <view
                v-else
                class="tag tag-v">
              {{ $i18n.zhToGlobal("未设置") }}
            </view>
            <!-- <user-info-card-edit @click="$refs.birthDatePicker.open(userAccount)"
            style="display: inline-block;margin-left: 10rpx;" v-if="isSelf"></user-info-card-edit>				 -->
          </view>
        </view>

        <image
            v-if="!isBithdayDateShow"
            @click="isBithdayDateShow = !isBithdayDateShow"
            style="position: absolute;right: 20rpx;bottom: 20rpx;"
            :src="$kt.file.byPath('icon/eyeOff.svg')"
            mode="widthFix"
            class="icon-img"
        ></image>
        <image
            v-else
            @click="isBithdayDateShow = !isBithdayDateShow"

            style="position: absolute;right: 20rpx;bottom: 20rpx;"
            :src="$kt.file.byPath('icon/eye.svg')"
            mode="widthFix"
            class="icon-img"
        ></image>

      </view>

      <view
          v-if="isSelf"
          class="box">
        <view class="box-title" style="position: relative;">
          {{ $i18n.zhToGlobal("年龄") }}
          <view style="position: absolute;right: 98rpx;top:0;color: #666;">{{
              userAccount.isAgeSecret ? $i18n.zhToGlobal('保密状态') : $i18n.zhToGlobal('非保密状态') }}</view>
          <view style="position: absolute;right: 0rpx;top:0;">
            <u-switch activeColor="#000000" :size="40" v-model="userAccount.isAgeSecret"></u-switch>
          </view>
          <view @click="openChangeAgeSecret()"
                style="position: absolute;right: 0rpx;top:0;width: 100rpx;height: 100%;background-color: rgba(0,0,0,0);">
          </view>
        </view>
        <view class="box-data box-data-2">
          <view
              v-if="userAccount.gmtBirthday"
              class="tag tag-v">
            <view style="display: inline-block;" v-if="isAgeDateShow">
              {{ $kt.date.getAge(userAccount.gmtBirthday) }}
            </view>
            <view v-else>
              {{ "***" }}
            </view>
          </view>
          <view
              v-else
              class="tag tag-v">
            {{ $i18n.zhToGlobal("未设置") }}
          </view>

        </view>
        <image
            v-if="!isAgeDateShow"
            @click="isAgeDateShow = !isAgeDateShow"
            style="position: absolute;right: 20rpx;bottom: 20rpx;"
            :src="$kt.file.byPath('icon/eyeOff.svg')"
            mode="widthFix"
            class="icon-img"
        ></image>
        <image
            v-else
            @click="isAgeDateShow = !isAgeDateShow"

            style="position: absolute;right: 20rpx;bottom: 20rpx;"
            :src="$kt.file.byPath('icon/eye.svg')"
            mode="widthFix"
            class="icon-img"
        ></image>


      </view>

      <view class="box">
        <view class="box-title" style="position: relative;">{{ $i18n.zhToGlobal("职业") }}
          <user-info-card-edit
              @click="$refs.jobTypePopup.open(userAccount)"
              style="position: absolute;right: 10px;" v-if="isSelf"></user-info-card-edit>
        </view>
        <view class="box-data box-data-2" style="color: #666;">
          {{ getJobTypeNameById(userAccount.jobTypeId) || $i18n.zhToGlobal("职业") }}
        </view>
      </view>

      <view class="box">
        <view class="box-title">{{ $i18n.zhToGlobal("兴趣爱好") }}</view>
        <view class="box-data box-data-2">
          <view
              v-for="(item,index) in userAccount.interests"
              class="tag tag-v">
            {{ makeFriends.getInterestNameById(item.interestId) }}
          </view>
          <view class="tag tag-v tag-btn"
                @click="openInterestPopup()"
                style="background-color: #f0f0f0;position: relative;width: 50rpx;padding: 0;"
                v-if="isSelf">
            <image
                style="width: 30rpx;height: 30rpx;opacity: .7;position: absolute;top:50%;left:50%;transform: translate(-50%,-50%);"
                :src="$kt.file.byPath('icon/add.svg')"></image>
          </view>
          <view
              v-if="!userAccount.interests||userAccount.interests.length === 0"
              class="box-data box-data-2" style="color: #666;">
            {{$i18n.zhToGlobal("未填写兴趣爱好") }}
          </view>

        </view>
      </view>

      <view class="box">
        <view class="box-title">{{ $i18n.zhToGlobal("个人特点") }}</view>
        <view class="box-data box-data-2">
          <view
              v-for="(item,index) in userAccount.characteristics"
              class="tag tag-v">
            {{ makeFriends.getCharacteristicNameById(item.characteristicId) }}
          </view>
          <view class="tag tag-v tag-btn"
                @click="openCharacteristicPopup()"
                style="background-color: #f0f0f0;position: relative;width: 50rpx;padding: 0;"
                v-if="isSelf">
            <image
                style="width: 30rpx;height: 30rpx;opacity: .7;position: absolute;top:50%;left:50%;transform: translate(-50%,-50%);"
                :src="$kt.file.byPath('icon/add.svg')"></image>
          </view>
          <view
              v-if="!userAccount.characteristics||userAccount.characteristics.length === 0"
              class="box-data box-data-2" style="color: #666;">
            {{$i18n.zhToGlobal("未填写个人特点") }}
          </view>

        </view>
      </view>

      <view class="box">
        <view class="box-title" style="position: relative;">{{ $i18n.zhToGlobal("个人简介") }}
          <user-info-card-edit
              @click="$refs.personalIntroductionPopup.open(userAccount)"
              style="position: absolute;right: 10px;" v-if="isSelf"></user-info-card-edit>
        </view>
        <view class="box-data box-data-2" style="color: #666;">
          {{ userAccount.personalIntroduction || $i18n.zhToGlobal("无个人简介") }}
        </view>
      </view>

      <view class="box">
        <view class="box-title" style="position: relative;">
          {{ $i18n.zhToGlobal("常住地") }}
          <user-info-card-edit
              @click="openLivePlacePopup"
              style="position: absolute; right: 10px;"
              v-if="isSelf"
          ></user-info-card-edit>
        </view>
        <view class="box-data box-data-2" style="color: #666;">
          <view v-if="!livePlace">
            {{ $i18n.zhToGlobal("未设置") }}
          </view>
          <view v-else>
            <view class="selector-item">
              <text v-if="livePlace && livePlace.parent && livePlace.parent.parent">
                {{ livePlace.parent.parent.name }} /
              </text>
              <text v-if="livePlace && livePlace.parent">
                {{ livePlace.parent.name }} /
              </text>
              <text v-if="livePlace && livePlace.name">
                {{ livePlace.name }}
              </text>
              <button @click="openLivePlacePopup" style="margin-left: 10px;">
                {{ $i18n.zhToGlobal("选择") }}
              </button>
            </view>
            <button @click="saveLivePlace" style="margin-top: 10px;">
              {{ $i18n.zhToGlobal("保存常住地") }}
            </button>
          </view>
        </view>
      </view>

      <view class="box">
        <view class="box-title" style="position: relative;">{{ $i18n.zhToGlobal("出生地") }}
          <user-info-card-edit
              @click="$refs.birthStateAreaAddressSelectPopup.open()"
              style="position: absolute;right: 10px;" v-if="isSelf"></user-info-card-edit>
        </view>
        <view class="box-data box-data-2" style="color: #666;">
          <view
              v-if="!userAccount.birthStateAreaAddress"
          >
            {{$i18n.zhToGlobal("未设置")}}
          </view>
          <view>
            <text
                v-if="userAccount.birthStateAreaAddress.parent&&userAccount.birthStateAreaAddress.parent.parent"
            >
              {{userAccount.birthStateAreaAddress.parent.parent.name}}{{"/"}}
            </text>
            <text
                v-if="userAccount.birthStateAreaAddress.parent"
            >
              {{userAccount.birthStateAreaAddress.parent.name}}{{"/"}}
            </text>
            <text v-if="userAccount.birthStateAreaAddress.name">
              {{userAccount.birthStateAreaAddress.name}}
            </text>
          </view>
        </view>
      </view>

      <view class="box">
        <view class="box-title" style="position: relative;">{{ $i18n.zhToGlobal("成长地") }}
          <user-info-card-edit
              @click="$refs.birthStateAreaAddressSelectPopup.open()"
              style="position: absolute;right: 10px;" v-if="isSelf"></user-info-card-edit>
        </view>
        <view class="box-data box-data-2" style="color: #666;">
          <view
              v-if="!userAccount.growUpPlaces"
          >
            {{$i18n.zhToGlobal("未设置")}}
          </view>
          <view>
            <text
                v-if="userAccount.growUpPlaces.parent&&userAccount.growUpPlaces.parent.parent"
            >
              {{userAccount.growUpPlaces.parent.parent.name}}{{"/"}}
            </text>
            <text
                v-if="userAccount.growUpPlaces.parent"
            >
              {{userAccount.growUpPlaces.parent.name}}{{"/"}}
            </text>
            <text v-if="userAccount.growUpPlaces.name">
              {{userAccount.growUpPlaces.name}}
            </text>
          </view>
        </view>
      </view>


      <view class="box">
        <view class="box-title" style="position: relative;">{{ $i18n.zhToGlobal("向往地") }}
          <user-info-card-edit
              @click="$refs.birthStateAreaAddressSelectPopup.open()"
              style="position: absolute;right: 10px;" v-if="isSelf"></user-info-card-edit>
        </view>
        <view class="box-data box-data-2" style="color: #666;">
          <view
              v-if="!userAccount.dreamPlaces"
          >
            {{$i18n.zhToGlobal("未设置")}}
          </view>
          <view>
            <text
                v-if="userAccount.dreamPlaces.parent&&userAccount.dreamPlaces.parent.parent"
            >
              {{userAccount.dreamPlaces.parent.parent.name}}{{"/"}}
            </text>
            <text
                v-if="userAccount.dreamPlaces.parent"
            >
              {{userAccount.dreamPlaces.parent.name}}{{"/"}}
            </text>
            <text v-if="userAccount.dreamPlaces.name">
              {{userAccount.dreamPlaces.name}}
            </text>
          </view>
        </view>
      </view>

      <view class="box">
        <view class="box-title" style="position: relative;">{{ $i18n.zhToGlobal("父亲出生地") }}
          <user-info-card-edit
              @click="$refs.birthStateAreaAddressSelectPopup.open()"
              style="position: absolute;right: 10px;" v-if="isSelf"></user-info-card-edit>
        </view>
        <view class="box-data box-data-2" style="color: #666;">
          <view
              v-if="!userAccount.fatherHometownStateAreaAddress"
          >
            {{$i18n.zhToGlobal("未设置")}}
          </view>
          <view>
            <text
                v-if="userAccount.fatherHometownStateAreaAddress.parent&&userAccount.fatherHometownStateAreaAddress.parent.parent"
            >
              {{userAccount.fatherHometownStateAreaAddress.parent.parent.name}}{{"/"}}
            </text>
            <text
                v-if="userAccount.fatherHometownStateAreaAddress.parent"
            >
              {{userAccount.fatherHometownStateAreaAddress.parent.name}}{{"/"}}
            </text>
            <text v-if="userAccount.fatherHometownStateAreaAddress.name">
              {{userAccount.fatherHometownStateAreaAddress.name}}
            </text>
          </view>
        </view>
      </view>


    </view>

    <kt-state-area-address-select-popup
    ref="birthStateAreaAddressSelectPopup"
    @select="birthStateAreaAddressSelect"
    ></kt-state-area-address-select-popup>
    <kt-state-area-address-select-popup
        ref="livePlacesSelectPopup"
        @select="handleLivePlaceSelect"
    ></kt-state-area-address-select-popup>

    <u-modal :show="isChangeVegetarian" :showCancelButton="true" :title="$i18n.zhToGlobal('提示')"
             :content="$i18n.zhToGlobal('是否修改素食主义者状态')" :confirm-text="$i18n.zhToGlobal('确定')"
             :cancel-text="$i18n.zhToGlobal('取消')" :confirm-color="'#000000'" @cancel="isChangeVegetarian = false"
             :closeOnClickOverlay="true" @confirm="changeVegetarian"></u-modal>

    <u-modal :show="isChangeAgeSecret" :showCancelButton="true" :title="$i18n.zhToGlobal('提示')"
             :content="$i18n.zhToGlobal('是否修改出生时间保密状态')" :confirm-text="$i18n.zhToGlobal('确定')"
             :cancel-text="$i18n.zhToGlobal('取消')" :confirm-color="'#000000'" @cancel="isChangeAgeSecret = false"
             :closeOnClickOverlay="true" @confirm="changeAgeSecret"></u-modal>

    <VegetarianPicker @select="vegetarianYearSelect" ref="vegetarianPicker"></VegetarianPicker>
    <InterestPopup ref="interestPopup"></InterestPopup>
    <CharacteristicPopup ref="characteristicPopup"></CharacteristicPopup>
    <UserInfoPopup ref="userInfoPopup"></UserInfoPopup>
    <job-type-popup ref="jobTypePopup"></job-type-popup>
    <BirthDatePicker @confirm="birthDateConfirm" ref="birthDatePicker"></BirthDatePicker>
    <PersonalIntroductionPopup ref="personalIntroductionPopup"></PersonalIntroductionPopup>
    <UnFollowPopup @unFollow="unFollow" ref="unFollowPopup"></UnFollowPopup>
    <kt-login-popup
        wechatLoginMethod="loginByCode"
        @close="loginPopupCloseHandle" ref="ktLoginPopup">
    </kt-login-popup>
  </view>
</template>

<script>
import BirthDatePicker from './components/birth-date-picker.vue';
import CharacteristicPopup from './components/characteristic-popup.vue';
import InterestPopup from './components/interest-popup.vue';
import PersonalIntroductionPopup from './components/personal-introduction-popup.vue';
import UnFollowPopup from './components/un-follow-popup.vue';
import userInfoCardEdit from './components/user-info-card-edit.vue';
import UserInfoPopup from './components/user-info-popup.vue';
import VegetarianPicker from './components/vegetarian-picker.vue';
import makeFriends from '@/uni_modules/project-make-friends/libs/load';
import JobTypePopup
  from "./components/job-type-popup.vue";

export default {
  props:{
    userAccountId:  {
      type: String | Number,
      default: ''
    },
    // 显示关系
    showRelation:{
      type: Boolean,
      default: true
    },
    // 显示朋友圈跳转
    showUserPost:{
      type: Boolean,
      default: true
    }
  },
  components: {
    JobTypePopup,
    BirthDatePicker,
    UserInfoPopup,
    userInfoCardEdit,
    VegetarianPicker,
    InterestPopup,
    PersonalIntroductionPopup,
    UnFollowPopup,
    CharacteristicPopup
  },
  data() {
    return {
      loadFinish: false,
      isAgeDateShow: false,
      isBithdayDateShow: false,
      makeFriends,
      // 是否修改素食
      isChangeVegetarian: false,
      // 是否修改出生时间保密
      isChangeAgeSecret: false,

      pageTitle: this.$i18n.zhToGlobal('个人资料'),
      userAccount: {},
      // 是否是自己
      isSelf: false,
      selfUserAccount: {},
      // 当前年份
      currentYear: new Date().getFullYear(),
      isFollow: false,
      isLogin: false,
      theme:"",
      // 是否准备关注
      isFollowing: false,
      // 是否准备聊天
      isChatting: false,
      interrelation:{
        followCount: 0,
        followedCount: 0,
        mutualFollowCount: 0
      },
      jobList: [],
      jobMap: {},
    }
  },
  async onLoad(options) {

    // this.$refs.userInfoPopup.open(this.userAccount);
  },
  mounted() {
    this.init();
    this.initLivePlaceSelectors();
    this.initGrowUpPlace();

    this.$kt.event.on("userAccount:getSelf", () => {
      console.log("userAccount:getSelf");
      if(this.isSelf){
        this.userAccount = this.$kt.userAccount.getSelf();
      }
    });
    this.$kt.event.on("login:success", () => {
      this.init();
      if(this.isFollowing){
        this.requestFollow();
        return;
      }
      if(this.isChatting){
        setTimeout(()=>{
          this.toChat();
        },300);
        return;
      }
    });
    this.getJobList();

  },
  methods: {
    birthStateAreaAddressSelect(item) {
      this.userAccount.birthStateAreaAddressCode = item.fullCode;
      this.userAccount.birthStateAreaAddress = item;
      // /project-make-friends-web/userAccount/setBirthStateAreaAddressCode
      this.$request.post('/project-make-friends-web/userAccount/setBirthStateAreaAddressCode', {data: {birthStateAreaAddressCode: item.fullCode}}).then(res => {
        this.$kt.userAccount.requestSelf();
      }).catch(err => {
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        });
      });
    },

    // 打开选择弹窗
    openGrowUpPlacePopup() {
      console.log('Opening grow up place popup');
      this.$refs.growUpPlacesSelectPopup.open();
    },
    // 处理成长地选择
    handleGrowUpPlaceSelect(selectedPlace) {
      console.log('Selected grow up place:', JSON.stringify(selectedPlace));
      if (!selectedPlace || !selectedPlace.fullCode) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('请选择有效的成长地'),
          icon: 'none'
        });
        return;
      }
      this.growUpPlace = selectedPlace;
      this.saveGrowUpPlace(this.growUpPlace);
      this.$refs.growUpPlacesSelectPopup.close();
    },
    saveGrowUpPlace() {
      if (!this.growUpPlace || !this.growUpPlace.fullCode) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('请先选择成长地'),
          icon: 'none'
        });
        return;
      }

      const addressCode = this.growUpPlace.fullCode;
      console.log('Selected grow up place address code:', addressCode);

      this.$request.post('/project-make-friends-web/userAccount/setGrowUpPlace', {
        data: { growUpPlaceStateAreaAddressCode: addressCode }
      }).then(res => {
        this.userAccount.growUpPlaces = [{ stateAreaAddress: this.growUpPlace }];
        this.$kt.userAccount.requestSelf();
        uni.showToast({
          title: this.$i18n.zhToGlobal('保存成功'),
          icon: 'success'
        });
      }).catch(err => {
        console.error('设置成长地失败:', err);
        uni.showToast({
          title: err.errMsg || this.$i18n.zhToGlobal('设置失败，请重试'),
          icon: 'none'
        });
      });
    },
    initGrowUpPlace() {
      if (this.userAccount.growUpPlaces && this.userAccount.growUpPlaces.length > 0) {
        this.growUpPlace = this.userAccount.growUpPlaces[0].stateAreaAddress; // 取第一个成长地的地址
      } else {
        this.growUpPlace = null;
      }
    },
    // 初始化常住地选择框
    initLivePlaceSelectors() {
      if (this.userAccount.livePlaces && this.userAccount.livePlaces.length > 0) {
        // 如果已有常住地，初始化选择框
        this.livePlace = this.userAccount.livePlaces[0]; // 取第一个常住地
      } else {
        // 默认一个空选择框
        this.livePlace = null;
      }
    },
    saveLivePlace() {
      if (!this.livePlace || !this.livePlace.fullCode) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('请先选择常住地'),
          icon: 'none'
        });
        return;
      }

      const addressCode = this.livePlace.fullCode;
      console.log('Selected address code:', addressCode);

      this.$request.post('/project-make-friends-web/userAccount/setLivePlace', {
        data: {liveStateAreaAddressCode: addressCode}
      }).then(res => {
        this.userAccount.livePlaces = [this.livePlace];
        this.$kt.userAccount.requestSelf();
        uni.showToast({
          title: this.$i18n.zhToGlobal('保存成功'),
          icon: 'success'
        });
      }).catch(err => {
        console.error('设置常住地失败:', err);
        uni.showToast({
          title: err.errMsg || this.$i18n.zhToGlobal('设置失败，请重试'),
          icon: 'none'
        });
      });
    },
    // 打开选择弹窗
    openLivePlacePopup(index) {
      console.log('Opening live place popup');
      this.$refs.livePlacesSelectPopup.open();
    },
    // 处理常住地选择
    handleLivePlaceSelect(selectedPlace) {
      console.log('Selected place:', JSON.stringify(selectedPlace));
      if (!selectedPlace || !selectedPlace.fullCode) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('请选择有效的常住地'),
          icon: 'none'
        });
        return;
      }
      this.livePlace = selectedPlace;
      this.saveLivePlace(this.livePlace);
      this.$refs.livePlacesSelectPopup.close();
    },
    getJobList() {
      // /project-make-friends-web/admin/jobType/getAllParentIsNull
      this.$request.get('/project-make-friends-web/admin/jobType/getAll').then(res => {
        this.jobList = res.data;
        this.jobMap = {};
        for(let i=0;i<this.jobList.length;i++){
          this.jobMap[this.jobList[i].id+""] = this.jobList[i];
        }
      }).catch(err => {
      })
    },
    getJobTypeNameById(id) {
      if(!id){
        return "";
      }
      if(this.jobMap[id+""]){
        return this.jobMap[id+""].name;
      }
      return "";
    },
    toUserPost(userAccountId){
      this.$kt.router.navTo('/pages/project-make-friends-pages/user-post/user-post?userAccountId='+userAccountId);
    },
    toPostSelf(){
      this.$kt.router.navTo('/pages/project-make-friends-pages/post-self/post-self');
    },
    /**
     * 前往用户关系
     */
    toInterrelation(code){
      this.$kt.router.navTo('/pages/project-make-friends-pages/user-account-interrelation/user-account-interrelation?code='+code+"&userAccountId="+this.userAccountId);
    },
    toChat(){
      this.isChatting = true;
      if(!this.isLogin){
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.isChatting = false;
      if (this.userAccountId+"" === this.selfUserAccount.id+"") {
        uni.showToast({
          title: this.$i18n.zhToGlobal('不能私信自己'),
          icon: 'none'
        });
        return;
      }
      this.$kt.router.navTo("/pages/project-make-friends-pages/chat-dialog/chat-dialog", {
        userAccountId: this.userAccountId
      });
    },
    loginPopupCloseHandle() {
      setTimeout(()=>{
        this.isLogin = this.$kt.userAccount.getIsLogin();
        if(!this.isLogin){
          this.isFollowing = false;
        }
      },3000);
    },
    async init(){
      this.theme = uni.getSystemInfoSync().theme;
      this.isSelf = false;
      this.isFollow = false;
      this.isLogin = this.$kt.userAccount.getIsLogin();
      console.log(this.$kt.userAccount.getIsLogin(),"isLogin");
      if(this.$kt.userAccount.getIsLogin()){
        this.selfUserAccount = this.$kt.userAccount.getSelf();
        if (this.userAccountId+"" === this.selfUserAccount.id+"") {
          this.isSelf = true;
          this.userAccount = this.selfUserAccount;
        }
      }

      this.loadFinish = false;
      if (this.isSelf) {
        this.pageTitle = this.$i18n.zhToGlobal('我的资料');
        this.userAccount = this.selfUserAccount;
        await this.getInterrelationSelf();
      } else {
        this.pageTitle = this.$i18n.zhToGlobal('个人资料');
        await this.getUserAccountById();
        await this.getInterrelationByUserAccountId();
        await this.getInterrelationIsFollow();
      }
      this.loadFinish = true;
    },
    unFollow(){
      this.isFollow = false;
      this.getInterrelationByUserAccountId();
      this.getInterrelationIsFollow();
    },
    // /user-interrelation-web/interrelation/isFollow
    async getInterrelationIsFollow() {
      if(!this.isLogin){
        return;
      }
      await this.$request.post('/user-interrelation-web/interrelation/isFollow', {
        data: { userAccountId: this.userAccountId }
      }).then(res => {
        this.isFollow = res.data;
      }).catch(err => {
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        });
      });
    },
    // /user-interrelation-web/interrelation/getByUserAccountId
    async getInterrelationByUserAccountId() {
      await this.$request.post('/user-interrelation-web/interrelation/getByUserAccountId', {
        data: { userAccountId: this.userAccountId }
      }).then(res => {
        this.interrelation = res.data;
      }).catch(err => {
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        });
      });
    },
    // /user-interrelation-web/interrelation/getSelf
    async getInterrelationSelf() {
      await this.$request.post('/user-interrelation-web/interrelation/getBySelf').then(res => {
        this.interrelation = res.data;
      }).catch(err => {
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        });
      });
    },

    requestFollow() {
      this.isFollowing = true;
      if(!this.isLogin){
        this.$refs.ktLoginPopup.open();
        return;
      }
      if (this.userAccountId == this.selfUserAccount.id) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('不能关注自己'),
          icon: 'none'
        });
        return;
      }
      this.isFollow = true;

      this.$request.post('/user-interrelation-web/interrelation/follow', {
        data: { userAccountId: this.userAccountId }
      }).then(res => {
        this.getInterrelationByUserAccountId();
        this.getInterrelationIsFollow();
        this.isFollowing = false;
        // this.$kt.userAccount.requestSelf();
      }).catch(err => {
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        });
      });
    },
    getInterrelation(userAccount){
      let interrelation = {
        followCount: 0,
        followedCount: 0,
        mutualFollowCount: 0

      }
      if(this.interrelation){
        interrelation = this.interrelation;
        if(!interrelation.mutualFollowCount) {
          interrelation.mutualFollowCount = 0;
        }
        if(!interrelation.followCount) {
          interrelation.followCount = 0;
        }
        if(!interrelation.followedCount) {
          interrelation.followedCount = 0;
        }
      }
      return interrelation;
    },
    // /user-account-web/userAccount/getById
    async getUserAccountById() {

      await this.$kt.userAccount.getById(this.userAccountId).then(res => {
        this.userAccount = res;
      }).catch(err => {
      });
      await this.$kt.userAccount.requestById(this.userAccountId).then(res => {
        this.userAccount = res;
      }).catch(err => {
      });

    },
    openChangeAgeSecret(){
      this.isChangeAgeSecret = true;
    },
    changeAgeSecret(){
      this.userAccount.isAgeSecret = !this.userAccount.isAgeSecret;
      // /project-make-friends-web/userAccount/setAgeSecret
      this.$request.post('/project-make-friends-web/userAccount/setIsAgeSecret', {
        data: { isAgeSecret: this.userAccount.isAgeSecret }
      }).then(res => {
        this.$kt.userAccount.requestSelf();
      })
      this.isChangeAgeSecret = false;
    },
    birthDateConfirm(value){
    },
    /**
     * 打开兴趣
     */
    openInterestPopup(){
      this.$refs.interestPopup.open(this.userAccount.interests);
    },
    /**
     * 打开个人特点
     */
    openCharacteristicPopup(){
      this.$refs.characteristicPopup.open(this.userAccount.characteristics);
    },
    vegetarianYearSelect(year) {
      console.log(year, "---");
      this.$request.post('/project-make-friends-web/userAccount/setVegetarianStartYear', {
        data: { yearNumber: year }
      }).then(res => {
        this.$kt.userAccount.requestSelf();
      }).catch(err => {
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        });
      });
    },
    openChangeVegetarian() {
      this.isChangeVegetarian = true;
    },
    changeVegetarian() {
      this.userAccount.vegetarian = !this.userAccount.vegetarian;
      // /project-make-friends-web/userAccount/setVegetarian
      this.$request.post('/project-make-friends-web/userAccount/setVegetarian', {
        data: { vegetarian: this.userAccount.vegetarian }
      }).then(res => {
        this.$kt.userAccount.requestSelf();
      });
      this.isChangeVegetarian = false;
    },
    openVegetarianPicker() {
      console.log("openVegetarianPicker");
      this.$refs.vegetarianPicker.open();
    }

  }
}
</script>

<style lang="scss" scoped>
.icon-img{
  width: 40rpx;
  height: 40rpx;
}
.back {
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  background-color: #f5f5f5;
}


// 暗黑模式
@media (prefers-color-scheme: dark) {
  .back {
    background-color: #000000;
  }
}

.container {
  //padding: 30rpx;
  box-sizing: border-box;
}

.box {
  position: relative;
  background-color: #ffffff;
  border-radius: 30rpx;
  padding: 30rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;

  .box-title {
    font-size: 30rpx;
    font-weight: bold;
  }
}


.box-data {
  text-align: center;
  margin-top: 10rpx;
  padding: 0 30rpx;

  .box-data-item {
    display: inline-block;
    width: 33%;
    vertical-align: top;

    .box-data-item-num {
      font-size: 30rpx;
      font-weight: bold;
    }

    .box-data-item-text {
      font-size: 24rpx;
      color: #999999;
    }
  }

}

.box-data-2 {
  text-align: left;
  margin-top: 10rpx;
  padding: 10rpx 0 10rpx 0rpx;

  .tag-v {
    background-color: rgba(255, 244, 240, 1);
    display: inline-block;
    vertical-align: top;
    margin-right: 20rpx;
    height: 50rpx;
    line-height: 50rpx;
    margin-bottom: 20rpx;
    padding: 0rpx 20rpx 0rpx 20rpx;
    border-radius: 10rpx;
    font-size: 28rpx;
    color: rgba(245, 172, 54, 1);

  }
}

.tag-btn:active{
  opacity: .7;
}

.box-btn-box {
  text-align: center;
  margin-top: 10rpx;

  .box-btn {
    display: inline-block;
    width: 45%;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #f5f5f5;
    border-radius: 20rpx;
    font-size: 28rpx;
    color: #333333;
    text-align: center;
    margin-right: 10rpx;
  }

  .box-fllow-btn{
    // background-color: #333333;
    background-color: $kt-color-primary;
    color: #ffffff;
  }
}

.box-btn:active{
  opacity: .7;
}

</style>
