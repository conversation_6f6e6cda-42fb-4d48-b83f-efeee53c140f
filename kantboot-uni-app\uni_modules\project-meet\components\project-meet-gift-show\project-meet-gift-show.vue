<template>
  　　<view>
      <view v-if="fileId">
        <project-meet-gift-show-in
        :file-id="fileId"
        @close="fileId = ''"
        ></project-meet-gift-show-in>
      </view>
  　　</view>
</template>
<script>
import ProjectMeetGiftShowIn from './project-meet-gift-show-in.vue'

import SVGA from 'svgaplayerweb'
export default {
  name: "project-meet-gift-show",
  components: {
    ProjectMeetGiftShowIn
  },
  data() {
    return {
      fileId: "",
    }
  },
  mounted() {
    this.$refs.ktPopup.open();
  },
  methods: {
    open(fileId) {
      this.fileId = fileId;
      this.$refs.ktPopup.open();
    },
  }
};
</script>
<style>
</style>