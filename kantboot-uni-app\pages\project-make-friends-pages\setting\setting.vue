<template>
  <view>
    <project-acm-nav-bar
        :is-has-i18n="false"
        :title="$i18n.zhToGlobal('设置')"
    ></project-acm-nav-bar>
    <view :class="clazz.bg"></view>
    <kt-setting-panel
        :privacy-agreement-url="'https://www.kantboot.com'"
        :user-agreement-url="'https://www.baidu.com'"
    ></kt-setting-panel>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        bg: this.$kt.style.toggleClass("bg"),
      },
    };
  }
}
</script>

<style lang="scss" scoped>
.bg{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}
</style>
