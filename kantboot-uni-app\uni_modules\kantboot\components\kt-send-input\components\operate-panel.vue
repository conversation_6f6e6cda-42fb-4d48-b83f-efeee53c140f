<template>
  <view :class="clazz.panel">

    <view
        v-if="hasVoice"
        @click="voice"
        class="panel-item">
      <image
          class="panel-icon"
          :src="$kt.file.byPath('kantboot/icon/voice.svg')">
      </image>
      <view>
        <kt-one-line-text
            width="80rpx"
            font-size="24rpx"
        :text="$i18n.zhToGlobal('语音')"
        ></kt-one-line-text>
      </view>
    </view>

    <view
        v-if="hasImage"
        @click="chooseImage"
        class="panel-item">
      <image
        class="panel-icon"
        :src="$kt.file.byPath('kantboot/icon/image.svg')">
      </image>
      <view>
        <kt-one-line-text
            width="80rpx"
            font-size="24rpx"
            :text="$i18n.zhToGlobal('相册')"
        ></kt-one-line-text>
      </view>
    </view>

    <view
        v-if="hasVideo"
        @click="chooseVideo()"
        class="panel-item">
      <image
        class="panel-icon"
        :src="$kt.file.byPath('kantboot/icon/video.svg')">
      </image>
      <view>
        <kt-one-line-text
            width="80rpx"
            font-size="24rpx"
            :text="$i18n.zhToGlobal('视频')"
        ></kt-one-line-text>
      </view>
    </view>

    <view
        v-if="hasUserAccount"
        @click="openUserAccountSelect()"
        class="panel-item">
      <image
        class="panel-icon"
        :src="$kt.file.byPath('kantboot/icon/interrelation.svg')">
      </image>
      <view>
        <kt-one-line-text
            width="80rpx"
            font-size="24rpx"
            :text="$i18n.zhToGlobal('用户卡片')"
        ></kt-one-line-text>
      </view>
    </view>

    <view
        v-if="hasGift"
        @click="toChange"
        class="panel-item">
      <image
        class="panel-icon"
        :src="$kt.file.byPath('kantboot/icon/gift.svg')">
      </image>
      <view>
        <kt-one-line-text
            width="80rpx"
            font-size="24rpx"
            :text="$i18n.zhToGlobal('礼物')"
        ></kt-one-line-text>
      </view>
    </view>

    <view
        v-for="(item, index) in operateExtraArray"
        @click="$emit(item.emit)"
        class="panel-item">
      <image
          class="panel-icon"
          :src="item.iconSrc">
      </image>
      <view>
        <kt-one-line-text
            width="80rpx"
            font-size="24rpx"
            :text="item.text"
        ></kt-one-line-text>
      </view>
    </view>

    <slot name="operateExtra"></slot>



  </view>
</template>

<script>
export default {
  props:{
    /**
     * 是否有语音发送
     */
    hasVoice: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有图片发送
     */
    hasImage: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有视频发送
     */
    hasVideo: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有用户关系发送
     */
    hasUserAccount: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有礼物发送
     */
    hasGift: {
      type: Boolean,
      default: true
    },
    operateExtraArray: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      clazz:{
        panel: this.$kt.style.toggleClass("panel"),
      }
    };
  },
  methods: {
    voice() {
      this.$emit('voice');
    },
    chooseImage() {
      this.$emit('chooseImage');
    },
    chooseVideo() {
      this.$emit('chooseVideo');
    },
    toChange() {
      this.$emit('toChange');
    },
    openUserAccountSelect() {
      this.$emit('openUserAccountSelect');
    }
  },
}
</script>

<style lang="scss" scoped>
.panel {
  box-sizing: border-box;
  color: #666666;
  text-align: center;
  background-color: #fafafa;
  width: 100%;
  display: flex;
  // 超出换行
  flex-wrap: wrap;
  // 对齐方式
  justify-content: left;
}

.panel-item {
  width: calc(100% / 8 - 4rpx);
  height: 80rpx;
  display: inline-block;
  background-color: #FFFFFF;
  margin: 20rpx;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  cursor: pointer;
}

.panel-item:active{
  opacity: .8;
}

.panel-icon {
  width: 45rpx;
  height: 45rpx;
}

.panel-mode-device-pc{
  .panel-item{
    width: 80rpx;
  }
}
</style>
