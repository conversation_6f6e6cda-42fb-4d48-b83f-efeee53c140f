@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.btn-view.data-v-21db9f90 {
  position: relative;
}
.btn-view .box-shadow-view.data-v-21db9f90 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #333333;
  box-shadow: 0 20rpx 30rpx #333333;
  opacity: 0.2;
}
.btn-view .background-view.data-v-21db9f90 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #333333;
}
.btn-view .bg-color-view.data-v-21db9f90 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #333333;
}
.btn-view .black-common-bg-color-view.data-v-21db9f90 {
  background-color: #333333;
}
.btn-view .red-common-bg-color-view.data-v-21db9f90 {
  background-color: #dd524d;
}
.btn-view .yellow-common-bg-color-view.data-v-21db9f90 {
  background-color: #ff9900;
}
.btn-view .blue-common-bg-color-view.data-v-21db9f90 {
  background-color: #3091f2;
}
.btn-view .error-bg-color-view.data-v-21db9f90 {
  background-color: #dd524d;
  opacity: 0;
}
.loading-status-body-view .box-shadow-view.data-v-21db9f90 {
  box-shadow: 0 0rpx 0rpx #333333;
  -webkit-animation-name: box-shadow-view-hide-animation-data-v-21db9f90;
          animation-name: box-shadow-view-hide-animation-data-v-21db9f90;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
}
.error-status-body-view .box-shadow-view.data-v-21db9f90 {
  box-shadow: 0 0rpx 0rpx #333333;
  -webkit-animation-name: box-shadow-view-hide-animation-data-v-21db9f90;
          animation-name: box-shadow-view-hide-animation-data-v-21db9f90;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
}
.error-status-body-view .error-bg-color-view.data-v-21db9f90 {
  background-color: #dd524d;
  opacity: 1;
  -webkit-animation-name: error-bg-color-view-animation-data-v-21db9f90;
          animation-name: error-bg-color-view-animation-data-v-21db9f90;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
}
@-webkit-keyframes error-bg-color-view-animation-data-v-21db9f90 {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@keyframes error-bg-color-view-animation-data-v-21db9f90 {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
.none-status-body-view:active .box-shadow-view.data-v-21db9f90 {
  box-shadow: 0 15rpx 30rpx #333333;
}
.error-to-none-status-body-view:active .box-shadow-view.data-v-21db9f90 {
  box-shadow: 0 15rpx 30rpx #333333;
}
.to-none-status-body-view:active .box-shadow-view.data-v-21db9f90 {
  box-shadow: 0 15rpx 30rpx #333333;
}
.error-to-none-status-body-view .box-shadow-view.data-v-21db9f90 {
  -webkit-animation-name: box-shadow-view-show-animation-data-v-21db9f90;
          animation-name: box-shadow-view-show-animation-data-v-21db9f90;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
}
.error-to-none-status-body-view .box-shadow-view.data-v-21db9f90 {
  box-shadow: 0 15rpx 30rpx #333333;
}
.error-to-none-status-body-view .error-bg-color-view.data-v-21db9f90 {
  background-color: #dd524d;
  opacity: 0;
  -webkit-animation-name: error-bg-color-view-in-error-to-none-status-body-view-animation-data-v-21db9f90;
          animation-name: error-bg-color-view-in-error-to-none-status-body-view-animation-data-v-21db9f90;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
}
@-webkit-keyframes error-bg-color-view-in-error-to-none-status-body-view-animation-data-v-21db9f90 {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@keyframes error-bg-color-view-in-error-to-none-status-body-view-animation-data-v-21db9f90 {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
.btn.data-v-21db9f90 {
  position: relative;
  color: #fff;
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  font-size: 35rpx;
  text-align: center;
  line-height: 40rpx;
  background-color: rgba(0, 0, 0, 0);
}
.btn .img-in-btn.data-v-21db9f90 {
  position: relative;
  width: 35rpx;
  height: 35rpx;
  vertical-align: top;
  margin-left: 10rpx;
}
.loading-btn .img-in-btn.data-v-21db9f90 {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 50rpx;
  height: 50rpx;
  -webkit-animation-name: in-loading-btn-image-animation-data-v-21db9f90;
          animation-name: in-loading-btn-image-animation-data-v-21db9f90;
  -webkit-animation-duration: 3s;
          animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
}
@-webkit-keyframes in-loading-btn-image-animation-data-v-21db9f90 {
0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
            transform: translate(-50%, -50%) rotate(0deg);
}
100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
            transform: translate(-50%, -50%) rotate(360deg);
}
}
@keyframes in-loading-btn-image-animation-data-v-21db9f90 {
0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
            transform: translate(-50%, -50%) rotate(0deg);
}
100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
            transform: translate(-50%, -50%) rotate(360deg);
}
}
.success-status-body-view .box-shadow-view.data-v-21db9f90 {
  box-shadow: 0 0rpx 0rpx #333333;
  -webkit-animation-name: box-shadow-view-hide-animation-data-v-21db9f90;
          animation-name: box-shadow-view-hide-animation-data-v-21db9f90;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
}
.to-none-status-body-view .box-shadow-view.data-v-21db9f90 {
  -webkit-animation-name: box-shadow-view-show-animation-data-v-21db9f90;
          animation-name: box-shadow-view-show-animation-data-v-21db9f90;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
}
.to-none-status-body-view .box-shadow-view.data-v-21db9f90 {
  box-shadow: 0 15rpx 30rpx #333333;
}
.btn.data-v-21db9f90:active {
  opacity: 0.8;
}
@-webkit-keyframes box-shadow-view-show-animation-data-v-21db9f90 {
0% {
    box-shadow: 0 0rpx 0rpx #333333;
}
100% {
    box-shadow: 0 20rpx 30rpx #333333;
}
}
@keyframes box-shadow-view-show-animation-data-v-21db9f90 {
0% {
    box-shadow: 0 0rpx 0rpx #333333;
}
100% {
    box-shadow: 0 20rpx 30rpx #333333;
}
}
@-webkit-keyframes box-shadow-view-hide-animation-data-v-21db9f90 {
0% {
    box-shadow: 0 20rpx 30rpx #333333;
}
100% {
    box-shadow: 0 0rpx 0rpx #333333;
}
}
@keyframes box-shadow-view-hide-animation-data-v-21db9f90 {
0% {
    box-shadow: 0 20rpx 30rpx #333333;
}
100% {
    box-shadow: 0 0rpx 0rpx #333333;
}
}
