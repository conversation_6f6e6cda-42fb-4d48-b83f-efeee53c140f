<template>
  <view>
    <kt-popup ref="ktPopup">
      <view class="meet-album-box">
        <project-meet-album :isHasToBack="false"></project-meet-album>
      </view>
    </kt-popup>
  </view>
</template>

<script>
import projectMeetAlbum from "../project-meet-album/project-meet-album.vue";
export default {
  components: { projectMeetAlbum },
  data() {
    return {};
  },
  methods: {
    open() {
      this.$refs.ktPopup.open();
    },
  },
};
</script>

<style lang="scss" scoped>
.meet-album-box {
  background-color: white;
  padding: 70rpx;
  border-radius: 40rpx;
}
</style>
