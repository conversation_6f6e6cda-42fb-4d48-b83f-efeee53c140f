@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.gender.data-v-d50184d6 {
  display: flex;
  align-items: center;
}
.gender .gender-item.data-v-d50184d6 {
  text-align: center;
  margin-right: 40rpx;
  opacity: 0.5;
}
.gender .gender-item .gender-item-img.data-v-d50184d6 {
  width: 200rpx;
  height: 200rpx;
}
.gender .gender-item .gender-item-text.data-v-d50184d6 {
  font-size: 30rpx;
}
.gender .gender-item-active.data-v-d50184d6 {
  opacity: 1;
}
.input.data-v-d50184d6 {
  width: 100%;
  height: 80rpx;
  border-radius: 10rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
  box-sizing: border-box;
}
.textarea.data-v-d50184d6 {
  width: 100%;
  height: 200rpx;
  border-radius: 10rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
  box-sizing: border-box;
}
.a-box-mode-device-pc .gender .gender-item.data-v-d50184d6 {
  cursor: pointer;
}
.select .select-item.data-v-d50184d6 {
  display: inline-block;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
  cursor: pointer;
}
.select .select-item-active.data-v-d50184d6 {
  background-color: #333333;
  color: #ffffff;
}
