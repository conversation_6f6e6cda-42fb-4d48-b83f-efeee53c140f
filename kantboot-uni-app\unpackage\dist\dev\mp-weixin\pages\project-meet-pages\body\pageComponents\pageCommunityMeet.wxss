@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.header-box-1.data-v-23cefd60 {
  padding: 0 10rpx;
  box-sizing: border-box;
  white-space: nowrap;
}
.header-box-1 .header-item.data-v-23cefd60 {
  display: inline-block;
  font-size: 36rpx;
  letter-spacing: 3rpx;
  margin-right: 5rpx;
  padding: 10rpx 20rpx;
}
.header-box-1 .header-item-selected.data-v-23cefd60 {
  font-weight: bold;
}
.scroll-view.data-v-23cefd60 {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scroll-view.data-v-23cefd60::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}
.scroll-view.data-v-23cefd60::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0);
  border-radius: 0;
}
.data-v-23cefd60::-webkit-scrollbar {
  width: 0;
  height: 1rpx;
  display: none;
}
.second-box.data-v-23cefd60 {
  width: 100%;
}
.second-box .second-box-box.data-v-23cefd60 {
  width: calc(100% - 100rpx);
  white-space: nowrap;
  overflow-x: auto;
}
.second-box .second-box-box .second-box-item.data-v-23cefd60 {
  display: inline-block;
  font-size: 32rpx;
  letter-spacing: 3rpx;
  margin-right: 5rpx;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}
.second-box .second-box-box .second-box-item-selected.data-v-23cefd60 {
  font-weight: bold;
}
.container-mode-device-pc.data-v-23cefd60 {
  width: calc(100% - 240px - 400px);
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
}
.container-mode-device-pc .community-post-box.data-v-23cefd60 {
  width: 700px;
  margin-left: 150px;
}
.container-mode-device-pc .second-box-item.data-v-23cefd60 {
  cursor: pointer;
}
.split.data-v-23cefd60 {
  width: 100%;
  height: 3rpx;
  background-color: #eee;
}
.container-mode-color-scheme-dark.data-v-23cefd60 {
  background-color: #191919;
}
.container-mode-color-scheme-dark .split.data-v-23cefd60 {
  background-color: #888888;
}
.container-mode-color-scheme-dark .header-box.data-v-23cefd60 {
  background-color: #191919;
}
.container-mode-color-scheme-dark .scroll-view.data-v-23cefd60 {
  background-color: #191919;
}
.container-mode-color-scheme-dark .second-icon.data-v-23cefd60 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.container-mode-color-scheme-dark .header-box-1.data-v-23cefd60 {
  background-color: #191919;
}
.container-mode-color-scheme-dark .header-box-1 .header-item.data-v-23cefd60 {
  color: #fff;
}
.container-mode-color-scheme-dark .second-box .second-box-box .second-box-item.data-v-23cefd60 {
  color: #fff;
}
.container-mode-color-scheme-dark .second-box .second-box-box .second-box-item-selected.data-v-23cefd60 {
  color: #fff;
  font-weight: bold;
}
.container-mode-device-pc.data-v-23cefd60 {
  position: relative;
  width: calc(100% + 20px);
  padding: 0;
  box-sizing: border-box;
}
.container-mode-device-pc .second-icon.data-v-23cefd60 {
  margin-right: 10px;
}
.bg.data-v-23cefd60 {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}
.bg .bg-image.data-v-23cefd60 {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  right: 0;
  z-index: -2;
}
.bg .bg-image-2.data-v-23cefd60 {
  position: fixed;
  width: 100vw;
  height: 100vh;
  bottom: 25vh;
  right: 0;
  -webkit-transform: scaleY(-1);
          transform: scaleY(-1);
  z-index: -2;
}
.bg .bg-image-3.data-v-23cefd60 {
  position: fixed;
  width: 100vw;
  height: 100vh;
  bottom: 0;
  right: 0;
  z-index: -2;
  -webkit-transform: scaleY(-1);
          transform: scaleY(-1);
}
.bg .bg-bg.data-v-23cefd60 {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  right: 0;
  z-index: -1;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 100rpx, white 100%);
}
.bg-mode-color-scheme-light.data-v-23cefd60 {
  background: linear-gradient(180deg, #ffffff 100rpx, #f9f9f9 100%);
}
.bg-mode-color-scheme-dark.data-v-23cefd60 {
  background: #191919;
}
.push-box.data-v-23cefd60 {
  position: fixed;
  bottom: 180rpx;
  right: 20rpx;
  font-size: 60rpx;
  color: #ffffff;
  width: 70rpx;
  height: 70rpx;
  text-align: center;
  line-height: 70rpx;
  border-radius: 50%;
  background: linear-gradient(180deg, #1383ff 0%, #e7e1ff 100%);
  text-shadow: 0 0 5rpx rgba(0, 0, 0, 0.2);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.push-box.data-v-23cefd60:active {
  -webkit-transform: scale(0.96);
          transform: scale(0.96);
}
