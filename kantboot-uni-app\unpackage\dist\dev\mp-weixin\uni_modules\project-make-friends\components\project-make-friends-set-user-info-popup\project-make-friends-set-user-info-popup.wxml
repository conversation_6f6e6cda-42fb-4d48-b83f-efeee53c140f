<view class="data-v-486714e6"><kt-popup vue-id="7707ee40-1" data-ref="ktPopup" class="data-v-486714e6 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-486714e6"><scroll-view style="height:calc(100vh - 300rpx);" scroll-y="{{true}}" class="data-v-486714e6"><project-make-friends-user-info-panel vue-id="{{('7707ee40-2')+','+('7707ee40-1')}}" user-account-id="{{self.id}}" class="data-v-486714e6" bind:__l="__l"></project-make-friends-user-info-panel></scroll-view></view></kt-popup></view>