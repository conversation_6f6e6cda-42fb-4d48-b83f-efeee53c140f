<template>
  <view>
    <view :class="clazz.box">
      <view v-if="!paySuccess">

        <view>
          <!-- 支付金额 -->
          <view
              v-if="result.id"
              class="pay-result">
            <view class="pay-result-amount">
              <text
                  class="pay-result-amount-symbol">
                {{ currencyMap[result.currency].symbol }}</text>
              <text class="pay-result-amount-value">
                {{ result.amount }}</text>
            </view>
            <view class="pay-result-currency">
              <text>{{ currencyMap[result.currency].name }}</text>
            </view>
          </view>
        </view>


        <view v-if="callingPay&&!payChecking">
          <view
              class="loading-box"
          >
            <view>
              <image
                  class="loading-image"
                  :src="$kt.file.byPath('kantboot/icon/loading.svg')"></image>
            </view>
            <view
                class="loading-text"
            >{{$kt.i18n.zhToGlobal("调起支付中")}}</view>
          </view>
        </view>

        <view v-if="!callingPay&&!payChecking">
          <view
              v-if="isHasPayMethod('wechat')"
              @click="wechatPay()"
              class="btn wechat-pay-btn">
            <view class="btn-icon">
              <image
                  mode="aspectFit"
                  class="btn-icon-image"
                  :src="$kt.file.byPath('kantboot/icon/wechat.svg')"></image>
            </view>
            <view class="btn-text">{{ $kt.i18n.zhToGlobal("微信支付") }}</view>
          </view>

          <view
              v-if="isHasPayMethod('alipay')"
              class="btn alipay-pay-btn">
            <view class="btn-icon">
              <image
                  mode="aspectFit"
                  class="btn-icon-image"
                  :src="$kt.file.byPath('kantboot/icon/alipay.svg')"></image>
            </view>
            <view class="btn-text">{{ $kt.i18n.zhToGlobal("支付宝支付") }}</view>
          </view>


          <!-- 立即支付 -->
          <view v-if="isHasPayMethod('paypal')"
                class="btn paypal-pay-btn" @click="paypalPay()">
            <view class="btn-icon">
              <image
                  mode="aspectFit"
                  class="btn-icon-image"
                  :src="$kt.file.byPath('kantboot/icon/paypal.svg')"></image>
            </view>
            <view class="btn-text">{{ $kt.i18n.zhToGlobal("前往支付") }}</view>
          </view>

        </view>

        <view v-if="payChecking">
          <view
              class="loading-box"
          >
            <view>
              <image
                  class="loading-image"
                  :src="$kt.file.byPath('kantboot/icon/loading.svg')"></image>
            </view>
            <view
                class="loading-text"
            >{{$kt.i18n.zhToGlobal("支付校验中")}}</view>
          </view>
        </view>

      </view>
      <view v-if="paySuccess">
        <view class="success-box">
          <view>
            <image
                class="success-logo"
                :src="$kt.file.byPath('kantboot/icon/success.svg')"
            ></image>
          </view>
          <view class="success-text">
            {{ $kt.i18n.zhToGlobal("支付成功") }}
          </view>
        </view>
      </view>


    </view>
  </view>
</template>

<script>
export default {
  props: {
    payMethods: {
      type: Array,
      default: () => {
        return ["wechat","alipay"];
      }
    },
  },
  data() {
    return {
      clazz: {
        box: this.$kt.style.toggleClass("box"),
      },
      params: {
        orderId: "",
      },
      result: {
        orderId: "",
        amount: 0,
        currency: "CNY",
      },
      currencyMap:{
        "CNY":{
          "name": this.$kt.i18n.zhToGlobal("人民币"),
          "symbol": "¥",
        },
        "USD":{
          "name": this.$kt.i18n.zhToGlobal("美元"),
          "symbol": "$",
        },
        "EUR":{
          "name": this.$kt.i18n.zhToGlobal("欧元"),
          "symbol": "€",
        },
        "HKD":{
          "name": this.$kt.i18n.zhToGlobal("港币"),
          "symbol": "HK$",
        },
        "GBP":{
          "name": this.$kt.i18n.zhToGlobal("英镑"),
          "symbol": "£",
        },
        "JPY":{
          "name": this.$kt.i18n.zhToGlobal("日元"),
          "symbol": "¥",
        },
      },
      // 调起支付中
      callingPay: false,
      // 校验支付中
      payChecking: false,
      paySuccess: false,
	  errMsg:""
    };
  },
  methods: {
    getOrder(orderId){
      this.result = {
        orderId: "",
        amount: "",
        currency: "CNY",
      };
      // /functional-pay-web/order/getById
      this.$kt.request.post("/functional-pay-web/order/getById",{
        data:{id: orderId}
      }).then((res)=>{
        this.result = res.data;
        this.paySuccess = false;
        if(this.payMethods.length===1
            && this.payMethods[0]==="paypal"
        ){
          this.paypalPay();
        }
      });
    },
    wechatPay(){
      this.callingPay = true;
      // #ifdef MP-WEIXIN
      wx.login({
        success :(res)=> {
          if (res.code) {
            this.$kt.request.post("/ai-wechat-pay/order/getPayResultApplet",
                {data: {
                    code: res.code,
                    orderId: this.result.id}})
                .then((res1) => {
                  setTimeout(()=>{
                    this.callingPay = false;
                  }, 500);
                  //调用微信官方支付接口弹出付款界面
                  uni.requestPayment({
                    ...res1.data,
                    success (res2) {
                      if (res2.errMsg == "requestPayment:ok"){
                        console.log('支付成功', res2)
                      }else{
                        console.log('支付失败')
                      }
                    },
                    fail (res2) {
                      console.log('支付失败', res2)
                    }
                  })

                }).catch((err1) => {
              this.callingPay = false;
              console.log(err1);
              uni.showToast({
                title: err1.errMsg,
                icon: "none",
              });
            });
          } else {
            console.log('登录失败！' + res.errMsg)
          }
        }
      });
      // #endif
    },
    paypalPay(){
      this.callingPay = true;
      // 生成paypal的订单
      this.$kt.request.post("/api-paypal-web/order/createOrderNumber",{
        data:{
          payOrderId:this.result.id
        }
      }).then((res)=>{

        // 调起paypal支付
        this.$kt.pay.toPaypal({
          "orderNumber":res.data
        });
        this.paypalVerifyPaySuccess();
        this.$emit("calling");
        // 调起支付完成后，关闭支付面板
        this.callingPay = false;
      }).catch((err)=>{
        this.callingPay = false;
      });


    },
// /api-paypal-web/order/verifyPaySuccess
    /**
     * 检测paypal支付是否成功
     */
    async paypalVerifyPaySuccess(){
      this.payChecking = true;
	  // let errNumber = 0;
      let flag = false;
      for(let i = 0; i < 500; i++){
        if(flag){
          this.payChecking = false;
          return;
        }
        await new Promise((resolve, reject)=>{
          setTimeout(()=>{
            this.$kt.request.post("/api-paypal-web/order/verifyPaySuccess",{
              data:{
                payOrderId: this.result.id
              }
            }).then((res)=>{
              if(res.data){
                this.paySuccess = true;
                setTimeout(()=>{
                  this.paySuccess = false;
                }, 3000);
                this.$emit("paySuccess");
                flag = true;
                resolve();
              }else{
                resolve();
              }
            }).catch((err)=>{
			 //  errNumber++;
    //           if(errNumber>3){
				// console.log(err);
				// uni.showToast({
				// 	title: err.errMsg,
				// 	icon: "none",
				// });
				// this.$emit("close");				  
			 //  }
              reject(err);
            });
          },3000);
        });
      }
    },
    toPay(methodCode){
    },
    isHasPayMethod(methodCode) {
      // 获取当前平台
      let platform = uni.getSystemInfoSync().uniPlatform;
      console.log("当前平台", platform);
      if(platform === "mp-weixin") {
        return methodCode === "wechat";
      }
      return this.payMethods.includes(methodCode);
    },
  },
}
</script>

<style lang="scss" scoped>

.pay-result{
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
  .pay-result-amount{
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000000;
    .pay-result-amount-symbol{
      font-size: 30rpx;
      font-weight: bold;
      margin-right: 10rpx;
    }
    .pay-result-amount-value{
      font-size: 50rpx;
      font-weight: bold;
    }
  }
  .pay-result-currency{
    position: absolute;
    top: 0;
    right: 0;
    font-size: 24rpx;
    color: #999999;
    background-color: #F0F0F0;
    padding: 2rpx 10rpx 2rpx 10rpx;
    border-radius: 20rpx;
  }

}

.box {
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;

  .btn {
    position: relative;
    width: 100%;
    height: 80rpx;
    font-size: 28rpx;
    text-align: center;
    line-height: 80rpx;
    border-radius: 10rpx;
    margin-bottom: 20rpx;

    .btn-icon {
      position: absolute;
      left: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 60rpx;
      height: 60rpx;

      .btn-icon-image {
        width: 100%;
        height: 100%;
        // 颜色反转
        filter: invert(1);
      }
    }

    .btn-text {
      display: inline-block;
      margin-left: 20rpx;
      color: #FFFFFF;
    }

  }

  .wechat-pay-btn {
    background-color: #1AAD19;
  }

  .alipay-pay-btn {
    // 支付宝蓝
    background-color: #1678FF;
  }

  .paypal-pay-btn{
    background-color: #000;
    .btn-icon-image {
      width: 100%;
      height: 100%;
    }
  }
}

.box-mode-device-pc {
  cursor: pointer;
}

.loading-box{
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F0F0F0;
  border-radius: 10rpx;
  margin-bottom: 20rpx;

  .loading-image {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
    opacity: .6;
    // 颜色反转
    filter: invert(1);
    // 动画
    animation: loading-image-animation 1s linear infinite;
    @keyframes loading-image-animation {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  }

  .loading-text {
    font-size: 28rpx;
    color: #999999;
  }
}

.success-box{
  text-align: center;
}

.success-logo{
  width: 200rpx;
  height: 200rpx;
  // 颜色反转
  filter: invert(1);
  // 动画
  animation: success-logo-animation 1s linear;
}

.success-text{
  margin-top: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
}

@keyframes success-logo-animation {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(360deg);
  }
}


</style>
