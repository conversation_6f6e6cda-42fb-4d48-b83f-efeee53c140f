<template>

  <view
      class="container">
    <kt-nav-bar
        :title="$i18n.zhToGlobal('课程组件')"
    ></kt-nav-bar>

    <view class="box-box">
      <kt-box class="kt-box"
              :title="$i18n.zhToGlobal('课程卡片')+'（kt-course-card）'"
      >
        <kt-course-card
            :info="{
                    title: '一起来学习吧',
                    description: '这是一个课程卡片',
                    fileIdOfCover: '666896986198021',
                    isLineThroughPrice: false,
            }"
        ></kt-course-card>
      </kt-box>
      <kt-box class="kt-box"
              :title="$i18n.zhToGlobal('课程面板')+'（kt-course-panel）'">
        <kt-course-panel></kt-course-panel>
      </kt-box>

      <kt-box class="kt-box"
              :title="$i18n.zhToGlobal('课程详情')+'（kt-course-detail）'">
        <kt-course-detail></kt-course-detail>
      </kt-box>



    </view>

  </view>
</template>

<script>

export default {
  data() {
    return {};
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  box-sizing: border-box;
  text-align: center;

  .box-box {
    display: inline-block;
    width: 100%;
    max-width: 700px;
  }
}

.kt-box {
  text-align: left;
  border-radius: 10rpx;
  margin-bottom: 50rpx;
  box-shadow: 0 0 10rpx rgba(118, 118, 118, .3);

  .in-kt-box {
    border: 1rpx solid #eee;
    border-radius: 10rpx;
    margin-bottom: 30rpx;
  }
}
</style>
