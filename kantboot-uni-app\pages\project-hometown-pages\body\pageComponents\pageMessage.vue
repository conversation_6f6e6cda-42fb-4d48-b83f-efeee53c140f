<script>


export default {
  name: "pageChat",
  data() {
    return {
      // 组件高度
      height: 0,
      // 组件高度
      navBarHeight: 0,
      // 是否加载完成
      isLoad: false,
      scrollTop: 0,
      list: [],
      clazz: {
        container: this.$kt.style.toggleClass("container"),
        bg: this.$kt.style.toggleClass("bg")
      },
      dialogIdSelected:{},
      footerHeight: 0,
    };
  },
  mounted() {
    // 获取导航栏高度
    // this.navBarHeight = this.$refs.navBar.getHeight();
    // 获取#navBarInMessage的高度
    this.$kt.event.on('changeTabbar', () => {
      setTimeout(() => {
        try {
          uni.createSelectorQuery()
              .in(this)
              .select("#navBarInMessage")
              .boundingClientRect((res) => {
                this.navBarHeight = res.height;
                console.log(res, "pageMessage");
              }).exec();
        } catch (e) {
          console.log(e, "pageMessage 错误的高度获取");
        }

      }, 50);
    })

  },
  created() {
    // 监听登录成功事件
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.getInitList();
    });
  },
  methods: {
    init(){
      // projectMakeFriendsPageMessageFooter
      uni.createSelectorQuery()
          .in(this)
          .select("#projectMakeFriendsPageMessageFooter")
          .boundingClientRect((res) => {
            this.footerHeight = res.height;
            console.log(res, "pageMessage");
          }).exec();
    },
    send(param){
      this.$refs.sendInput.toLoading();
      // /fp-community-web/post/push
      this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf",{
        data:{
          items:[param],
          dialogId:this.dialogIdSelected
        }
      }).then((res)=>{
        this.$refs.sendInput.clear();
        this.$refs.sendInput.toNone();
      }).catch((res)=>{

      })
    },
    cardClick(item) {
      if(this.$kt.style.detectDeviceType()==='pc'){
        this.dialogIdSelected = item.id;
        this.$nextTick(()=>{
          this.init();
        })
        return;
      }
      console.log(JSON.stringify(item)+"item.id")
      this.$kt.router.navTo('/pages/project-make-friends-pages/chat-dialog/chat-dialog?dialogId=' + item.id);
    }
  }
}
</script>

<template>
  <view :class="clazz.container">
    <view :class="clazz.bg"></view>
    <view id="navBarInMessage"
          class="box-header"
    >
      <kt-nav-bar
          ref="navBar"
          :icon="$kt.file.byPath('tabbar/message-selected.svg')"
          :title="$i18n.zhToGlobal('消息')"></kt-nav-bar>
    </view>

    <view class="kt-chat-list-panel-box">
      <kt-chat-list-panel
          :height="'calc(100vh - '+navBarHeight+'px - 20rpx - 50rpx)'"
          @cardClick="cardClick">
      </kt-chat-list-panel>
    </view>
    <view
        v-if="$kt.style.detectDeviceType()==='pc'"
        class="kt-chat-dialog-panel-box">
      <kt-chat-dialog-panel
          :height="'calc(100vh - '+navBarHeight+'px - '+footerHeight+'px - 20rpx - 50rpx)'"
          :dialog-id="dialogIdSelected">
      </kt-chat-dialog-panel>
      <view id="projectMakeFriendsPageMessageFooter"
            class="footer">

        <kt-send-input
            :has-voice="false"
            @change="init"
            @send="send"
            mode="panel"
            ref="sendInput"
        >

        </kt-send-input>

      </view>
    </view>



    <kt-no-login
        wechat-login-method="loginByCode"
        ref="noLogin"
    ></kt-no-login>

  </view>


</template>

<style scoped lang="scss">
.box {
  padding: 20rpx;
  box-sizing: border-box;

}

.bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  // 从白到黑的渐变，从上到下
  //background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}

.bg-mode-color-scheme-light {
  //background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
  background-color: #FFFFFF;
}

.bg-mode-color-scheme-dark {
  background-color: #191919;
}

.in-box {
  position: relative;
  height: 170rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .in-box-user-info-card {
    position: absolute;
    width: 100%;
    top: 15rpx;
    left: 0
  }
}

.container-mode-color-scheme-dark {
  .in-box {
    border-bottom: 1rpx solid #404a56;
  }
}

.container-mode-device-pc {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;

  .box {
    .in-box {
      position: relative;
      width: 100%;
      height: 160rpx;

      .in-box-user-info-card {
        position: absolute;
        width: 100%;
        top: 15rpx;
        left: -450rpx
      }
    }
  }

  .header-box {
    width: 100%;
  }
}
.container-mode-device-pc{
  .kt-chat-list-panel-box{
    display: inline-block;
    width: 600rpx;
  }
  .kt-chat-dialog-panel-box{
    display: inline-block;
    width: calc(100% - 600rpx);
    color: #000000;
    vertical-align: top;
    // 渐变，从白到#f0f0f0
    background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
  }
}

.footer{
  background-color: #FFFFFF;
}
</style>
