<view style="width:100%;position:relative;" class="data-v-eddcde14"><view class="chat-footer-input-container data-v-eddcde14" style="display:inline-block;width:calc(100% - 100rpx);"><textarea class="chat-footer-input data-v-eddcde14" adjust-position="{{false}}" auto-height="{{true}}" disabled="{{loading}}" placeholder="{{$root.g0}}" maxlength="99999" data-event-opts="{{[['keyup',[['keyupEnter',['$event']]]],['input',[['__set_model',['$0','text','$event',[]],['bindParam']],['input',['$event']]]],['focus',[['inputFocus',['$event']]]]]}}" value="{{bindParam.text}}" bindkeyup="__e" bindinput="__e" bindfocus="__e"></textarea></view><block wx:if="{{mode==='bar'}}"><view class="chat-footer-send-container btn data-v-eddcde14" style="display:inline-block;width:100rpx;text-align:center;position:relative;"><block wx:if="{{loading||loadingFile}}"><image class="icon icon-loading data-v-eddcde14" src="{{$root.g1}}"></image></block><block wx:else><block wx:if="{{bindParam.text}}"><image class="icon data-v-eddcde14" src="{{$root.g2}}" data-event-opts="{{[['tap',[['sendMessage']]]]}}" bindtap="__e"></image></block><block wx:else><block wx:if="{{!bindParam.text}}"><image class="icon data-v-eddcde14" style="opacity:.5;" src="{{$root.g3}}"></image></block></block></block></view></block><block wx:if="{{mode==='panel'}}"><view class="{{['chat-footer-send-container','btn','data-v-eddcde14',(!bindParam.text)?'add-btn':'']}}" style="display:inline-block;width:100rpx;text-align:center;position:relative;"><block wx:if="{{loading||loadingFile}}"><image class="icon icon-loading data-v-eddcde14" src="{{$root.g4}}"></image></block><block wx:else><block wx:if="{{bindParam.text}}"><image class="icon data-v-eddcde14" src="{{$root.g5}}" data-event-opts="{{[['tap',[['sendMessage']]]]}}" bindtap="__e"></image></block><block wx:else><block wx:if="{{!bindParam.text}}"><image class="icon add-icon data-v-eddcde14" style="{{'opacity:'+(panelOpen?.5:1)+';'}}" src="{{$root.g6}}" data-event-opts="{{[['tap',[['panelOpenChange',['$event']]]]]}}" bindtap="__e"></image></block></block></block></view></block><block wx:if="{{mode==='bar'}}"><operate-bar vue-id="cdbde260-1" has-gift="{{hasGift}}" has-voice="{{hasVoice}}" has-image="{{hasImage}}" has-video="{{hasVideo}}" has-user-account="{{hasUserAccount}}" operate-extra-array="{{operateExtraArray}}" data-event-opts="{{[['^chooseGift',[['chooseGift']]],['^chooseImage',[['chooseImage']]],['^chooseVideo',[['chooseVideo']]],['^openUserAccountSelect',[['e0']]]]}}" bind:chooseGift="__e" bind:chooseImage="__e" bind:chooseVideo="__e" bind:openUserAccountSelect="__e" class="data-v-eddcde14" bind:__l="__l" vue-slots="{{['operateExtra']}}"><slot name="operateExtra" slot="operateExtra"></slot></operate-bar></block><block wx:if="{{mode==='panel'&&panelOpen}}"><operate-panel vue-id="cdbde260-2" has-gift="{{hasGift}}" has-voice="{{hasVoice}}" has-image="{{hasImage}}" has-video="{{hasVideo}}" has-user-account="{{hasUserAccount}}" operate-extra-array="{{operateExtraArray}}" data-event-opts="{{[['^chooseGift',[['chooseGift']]],['^chooseImage',[['chooseImage']]],['^chooseVideo',[['chooseVideo']]],['^openUserAccountSelect',[['e1']]]]}}" bind:chooseGift="__e" bind:chooseImage="__e" bind:chooseVideo="__e" bind:openUserAccountSelect="__e" class="data-v-eddcde14" bind:__l="__l" vue-slots="{{['operateExtra']}}"><slot name="operateExtra" slot="operateExtra"></slot></operate-panel></block><view style="height:20rpx;" class="data-v-eddcde14"></view><kt-user-account-interrelation-popup vue-id="cdbde260-3" many-select="{{false}}" height="{{$root.m0?'530px':'calc(100vh - 200rpx)'}}" data-ref="userAccountInterrelationPopup" data-event-opts="{{[['^select',[['userAccountSelect']]]]}}" bind:select="__e" class="data-v-eddcde14 vue-ref" bind:__l="__l"></kt-user-account-interrelation-popup><kt-keyboard-size vue-id="cdbde260-4" class="data-v-eddcde14" bind:__l="__l"></kt-keyboard-size></view>