<view class="data-v-087987cf"><block wx:if="{{loading}}"><u-loading-icon vue-id="06b7f317-1" mode="circle" size="{{50}}" class="data-v-087987cf" bind:__l="__l"></u-loading-icon></block><block wx:if="{{$root.g0}}"><view class="loading-box data-v-087987cf"><image class="loading-image data-v-087987cf" src="{{$root.g1}}"></image><view class="loading-text data-v-087987cf">{{$root.g2}}</view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['select',['$0'],[[['list','',__i0__,'userAccountOfFollowed']]]]]]]}}" class="{{['box','data-v-087987cf',(item.m0)?'box-selected':'']}}" bindtap="__e"><block wx:if="{{item.m1}}"><image class="box-selected-icon data-v-087987cf" src="{{item.g3}}"></image></block><block wx:if="{{!customUserInfoCard}}"><kt-user-info-card vue-id="{{'06b7f317-2-'+__i0__}}" user-info="{{item.$orig.userAccountOfFollowed}}" class="data-v-087987cf" bind:__l="__l"></kt-user-info-card></block><block wx:else><slot name="userInfoCard"></slot><scoped-slots-userInfoCard userAccount="{{item.$orig.userAccountOfFollowed}}" class="scoped-ref" bind:__l="__l"></scoped-slots-userInfoCard></block></view></block></view>