<scroll-view class="user-recommend-panel data-v-86c3a76a" style="{{'height:'+(height)+';'}}" scroll-y="true" s="{{true}}" croll-with-animation="true" show-scrollbar="{{false}}" refresher-triggered="{{refresherTriggered}}" refresher-enabled="{{refresherEnabled}}" lower-threshold="{{$root.g0}}" data-event-opts="{{[['scrolltolower',[['getAfter',['$event']]]],['refresherrefresh',[['onRefresherrefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="user-list-box data-v-86c3a76a"><block wx:for="{{userList}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['toUserInfoPage',['$0'],[[['userList','',__i0__]]]]]]]}}" class="{{['data-v-86c3a76a',clazz.userBox]}}" bindtap="__e"><project-hometown-user-info-out-card vue-id="{{'51b043ca-1-'+__i0__}}" userInfo="{{item}}" class="data-v-86c3a76a" bind:__l="__l"></project-hometown-user-info-out-card></view></block><block wx:if="{{alfterLoading}}"><u-loading-icon vue-id="51b043ca-2" mode="circle" size="50rpx" class="data-v-86c3a76a" bind:__l="__l"></u-loading-icon></block><block wx:if="{{isBottom}}"><view style="text-align:center;color:#999999;font-size:24rpx;" class="data-v-86c3a76a">{{''+$root.g1+''}}</view></block><view style="height:50rpx;" class="data-v-86c3a76a"></view></view></scroll-view>