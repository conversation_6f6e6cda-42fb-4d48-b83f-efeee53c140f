<view><kt-popup class="vue-ref" vue-id="6d5a611e-1" data-ref="ktPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{[clazz.box]}}"><view class="box-title">{{''+$root.g0+''}}</view><view style="height:20rpx;"></view><view class="box-search-input"><input type="text" placeholder="{{$root.g1}}" data-event-opts="{{[['input',[['__set_model',['','keyword','$event',[]]],['input',['$event']]]],['blur',[['inputBlur',['$event']]]]]}}" value="{{keyword}}" bindinput="__e" bindblur="__e"/></view><view><view style="height:100rpx;"></view><block wx:if="{{loading}}"><u-loading-icon vue-id="{{('6d5a611e-2')+','+('6d5a611e-1')}}" mode="circle" bind:__l="__l"></u-loading-icon></block><block wx:else><block wx:if="{{$root.g2}}"><u-empty vue-id="{{('6d5a611e-3')+','+('6d5a611e-1')}}" content="{{$root.g3}}" bind:__l="__l"></u-empty></block><block wx:else><view class="user-card-box"><block wx:for="{{userAccountList}}" wx:for-item="userAccount" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toUserInfoPage',['$0'],[[['userAccountList','',index]]]]]]]}}" class="kt-user-info-card-box" bindtap="__e"><kt-user-info-card vue-id="{{('6d5a611e-4-'+index)+','+('6d5a611e-1')}}" user-info="{{userAccount}}" bind:__l="__l"></kt-user-info-card></view></block></view></block></block><view style="height:100rpx;"></view></view></view></kt-popup><project-meet-user-info-popup class="vue-ref" vue-id="6d5a611e-5" data-ref="usPopup" bind:__l="__l"></project-meet-user-info-popup></view>