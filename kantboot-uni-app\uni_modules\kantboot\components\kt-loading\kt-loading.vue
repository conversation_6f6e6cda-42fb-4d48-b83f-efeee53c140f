<template>
  <view
      v-show="loading"
      :style="{
        zIndex
      }"
      class="loading-box">
    <image
        mode="widthFix"
        class="loading-icon"
        :src="src"></image>

  </view>
</template>

<script>
import file from "../../libs/file"

export default {
  props: {
    src: {
      type: String,
      default: file.byPath('kantboot/icon/loading.svg')
    },
    zIndex: {
      type: Number,
      default: 999
    }
  },
  data() {
    return {
		loading:false
	};
  },
  methods:{
	  open(){
		  this.loading = true;
	  },
	  close(){
	
      this.loading = false;
	  }
  }
}
</script>

<style lang="scss" scoped>
.loading-box{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #FFFFFF;
}
.loading-icon{
  animation: loading-icon-ani-in-kt-loading 2s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  width: 200rpx;
  height: 200rpx;
  // 颜色反转
  filter: invert(1);
}


@keyframes loading-icon-ani-in-kt-loading {
  0% {
    // opacity: .5;
    transform: translate(-50%,-50%) rotate(0deg);
  }
  50% {
    // opacity: 1;
    transform: translate(-50%,-50%) rotate(360deg);
  }
  100% {
    // opacity: .5;
    transform: translate(-50%,-50%) rotate(720deg);
  }
}

</style>
