@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-3f15af75 {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.icon.data-v-3f15af75 {
  width: 200rpx;
  height: 200rpx;
  -webkit-animation: inNoLoginShake-data-v-3f15af75 2s infinite;
          animation: inNoLoginShake-data-v-3f15af75 2s infinite;
}
@-webkit-keyframes inNoLoginShake-data-v-3f15af75 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
25% {
    -webkit-transform: rotate(10deg);
            transform: rotate(10deg);
}
50% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
75% {
    -webkit-transform: rotate(-10deg);
            transform: rotate(-10deg);
}
100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
}
@keyframes inNoLoginShake-data-v-3f15af75 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
25% {
    -webkit-transform: rotate(10deg);
            transform: rotate(10deg);
}
50% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
75% {
    -webkit-transform: rotate(-10deg);
            transform: rotate(-10deg);
}
100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
}
.in-box.data-v-3f15af75 {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  text-align: center;
  font-size: 30rpx;
  color: #000000;
  width: 100%;
  padding: 20rpx 60rpx 20rpx 60rpx;
  box-sizing: border-box;
  z-index: 701;
}
.in-box .in-box-title.data-v-3f15af75 {
  font-size: 50rpx;
  font-weight: bold;
  color: #FFFFFF;
  letter-spacing: 10rpx;
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.3);
}
.in-box .in-box-btn.data-v-3f15af75 {
  display: inline-block;
  background-color: rgba(0, 0, 0, 0.9);
  padding: 20rpx 60rpx 20rpx 60rpx;
  color: #FFFFFF;
  width: 300rpx;
  border-radius: 30rpx;
  border: 5rpx solid #FFFFFF;
  cursor: pointer;
}
.box-mode-color-scheme-dark .icon.data-v-3f15af75 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.box-mode-device-pc .in-box.data-v-3f15af75 {
  width: 600px;
}
.back.data-v-3f15af75 {
  z-index: 600;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.back-mode-color-scheme-dark.data-v-3f15af75 {
  background-color: #191919;
}
.setting-icon.data-v-3f15af75 {
  width: 50rpx;
  height: 50rpx;
  opacity: 0.7;
}
.setting-icon.data-v-3f15af75:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.box-mode-color-scheme-dark .setting-icon.data-v-3f15af75 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
.bg-img.data-v-3f15af75 {
  position: fixed;
  width: calc(100vw + 100px);
  height: calc(100vh + 100px);
  top: -50px;
  left: -50px;
  z-index: -1;
  -webkit-animation: bgImg-data-v-3f15af75 3s infinite;
          animation: bgImg-data-v-3f15af75 3s infinite;
}
@-webkit-keyframes bgImg-data-v-3f15af75 {
0% {
    -webkit-filter: blur(0px);
            filter: blur(0px);
}
50% {
    -webkit-filter: blur(10px);
            filter: blur(10px);
}
100% {
    -webkit-filter: blur(0px);
            filter: blur(0px);
}
}
@keyframes bgImg-data-v-3f15af75 {
0% {
    -webkit-filter: blur(0px);
            filter: blur(0px);
}
50% {
    -webkit-filter: blur(10px);
            filter: blur(10px);
}
100% {
    -webkit-filter: blur(0px);
            filter: blur(0px);
}
}
.to-login-btn.data-v-3f15af75 {
  position: fixed;
  top: 40rpx;
  right: 100rpx;
  z-index: 700;
  color: #FFFFFF;
  font-size: 32rpx;
  border: 3rpx solid #FFFFFF;
  padding: 5rpx 70rpx 5rpx 70rpx;
  border-radius: 20rpx;
  cursor: pointer;
}
.to-language-btn.data-v-3f15af75 {
  position: fixed;
  bottom: 30rpx;
  right: 30rpx;
  width: 50rpx;
  height: 50rpx;
  z-index: 700;
  -webkit-filter: invert(1);
          filter: invert(1);
  cursor: pointer;
}
.in-box-title-title.data-v-3f15af75 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #FFFFFF;
  font-weight: bold;
  letter-spacing: 10rpx;
}
.in-box-title-title .in-box-title-title-icon.data-v-3f15af75 {
  width: 80rpx;
  height: 80rpx;
  margin-right: 10rpx;
}
.contact-us.data-v-3f15af75 {
  position: fixed;
  bottom: 20rpx;
  display: inline-block;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  color: #FFFFFF;
  font-size: 24rpx;
  text-align: center;
  width: 100%;
  cursor: pointer;
  z-index: 703;
}
