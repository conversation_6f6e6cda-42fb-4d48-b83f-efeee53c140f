import $event from "@/uni_modules/kantboot/libs/event"

let result = {}

/**
 * 发送帖子状态改变的事件
 * @param eventParams 事件参数
 */
result.emitRelationshipSelfChange = (eventParams)=>{
    $event.emit("communityPostCard:relationshipSelf:change", {
        postId: eventParams.postId,
        post: eventParams.post,
        relationshipSelf: eventParams.relationshipSelf
    });
}

/**
 * 监听帖子状态改变的事件
 * @param callback 回调函数
 */
result.onRelationshipSelfChange = (callback) => {
    $event.on("communityPostCard:relationshipSelf:change", (eventParams) => {
        callback(eventParams);
    });
}

/**
 * 帖子发布的事件
 */
result.emitPostPush = (eventParams) => {
    $event.emit("fpCommunityPost:push", {
        postId: eventParams.postId,
        post: eventParams.post
    });
}

/**
 * 监听帖子发布的事件
 */
result.onPostPush = (callback) => {
    $event.on("fpCommunityPost:push", (eventParams) => {
        callback(eventParams);
    });
}

/**
 * 发送帖子删除的事件
 */
result.emitPostRemove = (eventParams) => {
    $event.emit("fpCommunityPost:remove", {
        postId: eventParams.postId,
        post: eventParams.post
    });
}

/**
 * 监听帖子删除的事件
 */
result.onPostRemove = (callback) => {
    $event.on("fpCommunityPost:remove", (eventParams) => {
        callback(eventParams);
    });
}

export default result;