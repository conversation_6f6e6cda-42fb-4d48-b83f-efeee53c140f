@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.card.data-v-d5d2ba6a {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  background-color: #FFFFFF;
}
.cover.data-v-d5d2ba6a {
  width: 100%;
  height: 200rpx;
}
.content.data-v-d5d2ba6a {
  padding: 15rpx;
}
.title.data-v-d5d2ba6a {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.description.data-v-d5d2ba6a {
  font-size: 24rpx;
  color: #666;
  height: 80rpx;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.price.data-v-d5d2ba6a {
  text-align: right;
  font-size: 28rpx;
  font-weight: bold;
  color: #FF0000;
}
.price .price-old.data-v-d5d2ba6a {
  text-decoration: line-through;
  opacity: 0.5;
}
.card-mode-color-scheme-dark.data-v-d5d2ba6a {
  background-color: rgba(0, 0, 0, 0);
}
.card-mode-color-scheme-dark .content.data-v-d5d2ba6a {
  background-color: #212932;
}
.card-mode-color-scheme-dark .content .title.data-v-d5d2ba6a {
  color: #e9e9e9;
}
.card-mode-color-scheme-dark .content .description.data-v-d5d2ba6a {
  color: #b9b9b9;
}
.card-mode-color-scheme-dark .content .price.data-v-d5d2ba6a {
  color: #fef0f0;
}
.card-mode-color-scheme-dark .content .price .price-old.data-v-d5d2ba6a {
  color: #b9b9b9;
  opacity: 1;
}
