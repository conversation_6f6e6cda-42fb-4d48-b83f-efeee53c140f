@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-df5e4216 {
  bottom: 0;
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
}
.box .box-title.data-v-df5e4216 {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: center;
  font-weight: bold;
}
.menu-box.data-v-df5e4216 {
  padding: 20rpx;
  box-sizing: border-box;
}
.menu-box .menu-item.data-v-df5e4216 {
  padding: 20rpx;
  border: 3px solid #F0F0F0;
  text-align: center;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  color: #666666;
}
.menu-box .menu-item-selected.data-v-df5e4216 {
  border: 3px solid #333333;
  background-color: #333333;
  color: #FFFFFF;
}
.scroll-view.data-v-df5e4216 {
  padding: 30rpx;
  box-sizing: border-box;
}
.user-info-card-box-box.data-v-df5e4216 {
  padding: 0 20rpx 0 20rpx;
  box-sizing: border-box;
}
.user-info-card-box.data-v-df5e4216 {
  border: 3px solid #F0F0F0;
  padding: 10rpx;
  border-radius: 20rpx;
}
.add-btn-box.data-v-df5e4216 {
  text-align: center;
}
.add-btn-box .add-btn.data-v-df5e4216 {
  color: #999999;
  font-size: 28rpx;
}
.sub-title.data-v-df5e4216 {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}
.no-data.data-v-df5e4216 {
  text-align: center;
  font-size: 90rpx;
  font-weight: lighter;
  color: #999999;
  height: 200rpx;
  line-height: 200rpx;
  background-color: #F0F0F0;
  border-radius: 20rpx;
}
.no-data.data-v-df5e4216:active {
  -webkit-transform: scale(0.97);
          transform: scale(0.97);
}
.user-info-card-box-tb.data-v-df5e4216 {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border-radius: 55%;
  top: 20rpx;
  right: 40rpx;
}
.user-info-card-box-remove.data-v-df5e4216 {
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  opacity: 0.8;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  -webkit-filter: invert(16%) sepia(100%) saturate(1000%) hue-rotate(300deg);
          filter: invert(16%) sepia(100%) saturate(1000%) hue-rotate(300deg);
  /* 调整色相到红色 */
}
.box-mode-device-pc.data-v-df5e4216 {
  position: fixed;
  border-radius: 20rpx;
  overflow: hidden;
  width: 400px;
  left: 50%;
  bottom: 50%;
  -webkit-transform: translate(-50%, 50%);
          transform: translate(-50%, 50%);
}
