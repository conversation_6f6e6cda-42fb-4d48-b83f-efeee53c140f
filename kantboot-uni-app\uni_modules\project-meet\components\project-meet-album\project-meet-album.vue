<template>
  <view>
    <kt-image-select :fileGroupCode="'fp'" v-model="imgList"></kt-image-select>
    <view style="margin-top: 40rpx">
      <kt-button 
	 ref="submitBtn" 
	  @click="subimt()">
        {{ $i18n.zhToGlobal("确认上传") }}
      </kt-button>
    </view>
  </view>
</template>

<script>
export default {
	props:{
		isHasToBack:{
			type: Boolean,
			default: true
		}
	},
  data() {
    return {
		imgList: [],
		self:{
			fileIdsOfPhotos:[]
		}
	};
  },
  created(){
	this.init();
	this.$kt.event.on("login:success",()=>{
		this.init();
	});
  },
  methods: {
	async init(){
		await this.$kt.userAccount.requestSelf();
		this.self = this.$kt.userAccount.getSelf();
		if(!this.self.fileIdsOfPhotos){
			this.self.fileIdsOfPhotos = [];
		}
		this.imgList = this.self.fileIdsOfPhotos;
	},
	async subimt() {
		this.$refs.submitBtn.loading();
		 await this.$request.post('/project-meet-web/userAccount/setFileIdsOfPhotos', {
			data:{
				fileIdsOfPhotos: this.imgList
			}
		 }).then((res)=>{
		 }).catch((err)=>{
			if(err.errMsg){
				this.$refs.submitBtn.error(err.errMsg);
			}
		});
		if(this.isHasToBack){
			setTimeout(()=>{
				this.$kt.router.navBack();
			},500);
    }
		
	}
  },
};
</script>

<style>
</style>
