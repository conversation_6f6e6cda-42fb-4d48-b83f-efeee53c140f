<view class="data-v-5f686c3c"><kt-popup bind:close="__e" bind:confirm="__e" vue-id="e4b4c76c-1" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" class="data-v-5f686c3c vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-5f686c3c',clazz.popup]}}"><view class="box data-v-5f686c3c"><view class="box-title data-v-5f686c3c">{{$root.g0}}</view><view class="box-tags data-v-5f686c3c"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['expectedRelationshipPush',['$0'],[[['expectedRelationshipList','',__i0__,'id']]]]]]]}}" class="{{['tag','data-v-5f686c3c',(item.m0)?'tag-selected':'']}}" bindtap="__e">{{expectedRelationshipMap[item.$orig.id]}}</view></block></view><view style="height:30rpx;" class="data-v-5f686c3c"></view><view class="data-v-5f686c3c"><kt-button bind:click="__e" vue-id="{{('e4b4c76c-2')+','+('e4b4c76c-1')}}" data-ref="ktButton" data-event-opts="{{[['^click',[['confirm']]]]}}" class="data-v-5f686c3c vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g1}}</kt-button></view><view style="height:30rpx;" class="data-v-5f686c3c"></view></view></view></kt-popup></view>