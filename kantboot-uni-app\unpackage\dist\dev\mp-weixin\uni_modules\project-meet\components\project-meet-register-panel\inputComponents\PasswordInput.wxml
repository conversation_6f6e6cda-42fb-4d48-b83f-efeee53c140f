<view class="{{['data-v-017a2e7a',clazz.loginInputBox]}}"><view class="icon-box data-v-017a2e7a"><view class="icon data-v-017a2e7a"><image class="icon-img data-v-017a2e7a" src="{{$root.g0}}" mode="widthFix"></image></view></view><view class="input-box data-v-017a2e7a" style="width:calc(100% - 180rpx);"><input class="input data-v-017a2e7a" adjust-position="{{false}}" placeholder="{{$root.g1}}" type="{{passwordType}}" data-event-opts="{{[['blur',[['addUserLog',['$event']]]],['input',[['changePassword',['$event']]]]]}}" bindblur="__e" bindinput="__e"/></view><block wx:if="{{passwordType==='password'}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="icon-box data-v-017a2e7a" bindtap="__e"><view class="icon data-v-017a2e7a"><view style="font-size:20rpx;" class="data-v-017a2e7a"><image class="icon-img data-v-017a2e7a" src="{{$root.g2}}" mode="widthFix"></image></view></view></view></block><block wx:if="{{passwordType==='text'}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="icon-box data-v-017a2e7a" bindtap="__e"><view class="icon data-v-017a2e7a"><view style="font-size:20rpx;" class="data-v-017a2e7a"><image class="icon-img data-v-017a2e7a" src="{{$root.g3}}" mode="widthFix"></image></view></view></view></block></view>