<template>
  <view :class="clazz.box">
    <view class="open-box">

      <view class="emoji-item"
            v-for="(emoji, index) in emojisStrList"
            :key="index">{{ emoji }}
      </view>

    </view>
    <view
        :class="clazz.back"></view>
    <view
        class="logo-box">
      <image class="img" :src="$kt.file.byPath('image/logo.svg')" mode="widthFix"></image>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      cursor: 0,
      content: '',
      // emojis: "国家电网人才托举系统公司连续17年获评中央企业业绩考核A级企业，2016-2018年蝉联《财富》世界500强第2位、中国500强企业第1位，是全球最大的公用事业企业。 2018年12月，世界品牌实验室编制的《2018世界品牌500强》揭晓，国家电网有限公司排名第30。 2019年7月22日，《财富》世界500强位列第5位。 [3]2019年9月1日，2019中国战略性新兴产业领军企业100强榜单在济南发布，国家电网有限公司排名第20位；2019中国服务业企业500强榜单在济南发布，国家电网有限公司排名第1位。 [4-5]2019年12月18日，人民日报发布中国品牌发展指数100榜单，国家电网排名第8位。 2020年4月，入选国务院国资委“科改示范企业”名单。2024年8月5日，《财富》世界500强排行榜发布第3位。",
      emojis:"Sado蜡Candle烛MasoSwitchDomSubPuppyMaster主Le maître人",
      emojisStrList: "",
      clazz:{
        box: this.$kt.style.toggleClass("box"),
        back: this.$kt.style.toggleClass("back"),
      }
    }
  },

  mounted() {
    setTimeout(()=>{
      this.toText()
    },10);
  },
  methods: {
    async toText(){
      this.emojisStrList = this.emojis+this.emojis+this.emojis+this.emojis;
    }
  },
  watch: {}
}
</script>

<style lang="scss" scoped>
.box {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #f2f2f2;
  padding: 20rpx;
  box-sizing: border-box;
  z-index:-1
}

.textarea {
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  font-size: 32rpx;
  background-color: #FFFFFF;
  border-radius: 10px;
}

.open-box {
  position: relative;
  text-align: center;
  box-sizing: border-box;
  color: rgba(0, 0, 0, .8);
  //color:rgba(255,255,255,.2);
  z-index:2;

  .emoji-item {
    display: inline-block;
    width: 50rpx;
    height: 50rpx;
    text-align: center;
    font-size: 30rpx;
    font-weight: lighter;
    // animation: emoji-item 1s infinite;
  }

  @keyframes emoji-item {
    0% {
      transform: translateY(0) rotate(0deg);
    }
    50% {
      transform: translateY(10rpx) rotate(45deg);
    }
    100% {
      transform: translateY(0) rotate(0deg);
    }
  }

  .emoji-item:active {
    transform: scale(.9);
    background-color: #f0f0f0;
    border-radius: 10rpx;
  }
}

.back {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255,255,255,.9);
  z-index: 2;
}


.logo-box{
  position: fixed;
  top: calc(50% - 150rpx - 300rpx - 100rpx);
  left:0;
  z-index: 2;
  opacity: 1;
  width: 100%;
  padding: 60rpx;
  box-sizing: border-box;
  text-align: center;

  .img{
    width: 500rpx;
    height: 500rpx;
    //background-color: #f0f0f0;
    border-radius: 55%;
    // 渐变 透明 不透明 透明
    //background: linear-gradient(90deg, rgba(240,240,240,0), rgba(240,240,240,1), rgba(240,240,240,0));
  }

  .text{
    font-size: 40rpx;
    text-align: left;
    margin-top: -50rpx;
  }

}


.box-mode-color-scheme-light {
  background-color: #fff;
  color: #000;
  .type-box-btn-selected {
    color: #000000;
  }
  .type-box-line {
    background-color: #000000;
  }
}

.box-mode-color-scheme-dark {
  color: #fff;
  .type-box-btn {
    color: #fff;
    .type-box-line {
      background-color: #fff;
    }
  }
  .type-box-btn-selected {
    color: #fff;
  }
}

.back-mode-color-scheme-light {
  background-color: #fff;
  color: #000;
}

.back-mode-color-scheme-dark {
  background-color: rgba(0,0,0,.9);
  color: #fff;
  .type-box-btn {
    color: #fff;
    .type-box-line {
      background-color: #fff;
    }
  }
  .type-box-btn-selected {
    color: #fff;
  }
}

</style>
