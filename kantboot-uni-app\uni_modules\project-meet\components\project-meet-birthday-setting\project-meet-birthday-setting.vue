<template>
  <view>
	  <view>
      <view class="input-box">
        <view class="input-item">
          <input
              class="picker"
              maxlength="2"
              type="number"
              @input="convertToTimestamp"
              v-model="dateParams.month"
              placeholder="MM"></input>
        </view>
        <view class="input-split">{{"/"}}</view>
        <view class="input-item">
          <input
              class="picker"
              maxlength="2"
              type="number"
              @input="convertToTimestamp"
              v-model="dateParams.day"
              placeholder="DD"></input>
        </view>
        <view class="input-split">{{"/"}}</view>
        <view class="input-item">
          <input
              class="picker"
              maxlength="4"
              type="number"
              @input="convertToTimestamp"
              v-model="dateParams.year"
              placeholder="YYYY"></input>
        </view>

      </view>


    </view>
  </view>
</template>

<script>
export default {
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
  },
  data() {
    return {
      single:0,
      show: false,
      selectedDate: '',
      timestamp: null,
      dateParams:{
        month: "",
        day: "",
        year: "",
      }
    };
  },
  watch: {
    value(newValue) {
      if (newValue&&!this.dateParams.month&&!this.dateParams.day&&!this.dateParams.year) {
        this.timestamp = newValue;
        const date = new Date(newValue);
        this.dateParams.month = date.getMonth() + 1;
        this.dateParams.day = date.getDate();
        this.dateParams.year = date.getFullYear();
      }
    }

  },
  methods: {
    // 转换为时间戳
    convertToTimestamp() {
      // 将value转换为时间戳
      const { month, day, year } = this.dateParams;
      if (month && day && year) {
        const dateStr = `${year}-${month}-${day}`;
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          this.timestamp = date.getTime();
          this.selectedDate = dateStr;
        } else {
          this.timestamp = null;
          this.selectedDate = '';
        }
      } else {
        this.timestamp = null;
        this.selectedDate = '';
      }
      // 判断月份是否正确
      if (month && (month < 1 || month > 12)) {
        uni.showToast({
          title: '月份不正确',
          icon: 'none',
        });
        this.dateParams.month = '';
        this.timestamp = null;
        return;
      }
      // 判断是否日期31天
      if (day) {
        if(day < 1 || day > 31) {
          uni.showToast({
            title: '日期不正确',
            icon: 'none',
          });
          this.dateParams.day = '';
          this.timestamp = null;
          return;
        }
      }

      if(day && month){
        // 2月份不能超过29天，不做闰年判断
        if (month == 2 && day > 29) {
          uni.showToast({
            title: '2月份不能超过29天',
            icon: 'none',
          });
          this.dateParams.day = '';
          this.timestamp = null;
          return;
        }
        // 4,6,9,11月不能超过30天
        if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
          uni.showToast({
            title: '该月份不能超过30天',
            icon: 'none',
          });
          this.dateParams.day = '';
          this.timestamp = null;
          return;
        }
      }

      // 判断日期是否正确，年份输入完成的时候
      if (year && day && year.length === 4) {
        const date = new Date(year, month - 1, day);
        if (date.getFullYear() !== parseInt(year) || date.getMonth() + 1 !== parseInt(month) || date.getDate() !== parseInt(day)) {
          uni.showToast({
            title: '日期不正确',
            icon: 'none',
          });
          this.dateParams.day = '';
          this.timestamp = null;
          return;
        }
      }

      this.value = this.timestamp;
      this.$emit('input', this.timestamp);

    },
    onDateChange(e) {
      this.selectedDate = e.detail.value;
      this.timestamp = new Date(e.detail.value).getTime();
    },
    confirm() {
      if (this.timestamp) {
        this.$emit('confirm', this.timestamp);
      } else {
        uni.showToast({
          title: '请选择日期',
          icon: 'none',
        });
      }
      this.close();
    },
    open() {
      this.$refs.ktPopup.open();
    },
    close() {
      this.show = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.input-box {
  .input-item{
    display: inline-block;
    border: 1rpx solid #F0F0F0;
    width: 120rpx;
    box-sizing: border-box;
    padding: 10rpx;
    text-align: center;
  }
  .input-split {
    vertical-align: top;
    display: inline-block;
    width: 20rpx;
    font-size: 40rpx;
    text-align: center;
    line-height: 30rpx;
    color: #999999;
    margin-top: 15rpx;
    margin-left: 10rpx;
    margin-right: 10rpx;
  }
}
</style>