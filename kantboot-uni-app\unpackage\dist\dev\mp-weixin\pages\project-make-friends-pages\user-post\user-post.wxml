<view class="data-v-4e0a3a40"><kt-nav-bar vue-id="733d8ec6-1" title="{{$root.g0}}" class="data-v-4e0a3a40" bind:__l="__l"></kt-nav-bar><view class="data-v-4e0a3a40"><block wx:if="{{loading}}"><u-loading-icon vue-id="733d8ec6-2" mode="circle" size="70" class="data-v-4e0a3a40" bind:__l="__l"></u-loading-icon></block><block wx:if="{{$root.g1}}"><view class="no-data data-v-4e0a3a40"><view class="data-v-4e0a3a40"><image class="no-data-image data-v-4e0a3a40" src="{{$root.g2}}"></image></view><view class="no-data-text data-v-4e0a3a40">{{$root.g3+''}}</view></view></block><block wx:for="{{list}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['cardClick',['$0'],[[['list','',__i0__,'id']]]]]]]}}" bindtap="__e" class="data-v-4e0a3a40"><kt-community-post-card vue-id="{{'733d8ec6-3-'+__i0__}}" post="{{item}}" is-forbid-forward="{{false}}" has-dot="{{false}}" data-event-opts="{{[['^userClick',[['userClick']]]]}}" bind:userClick="__e" class="data-v-4e0a3a40" bind:__l="__l"></kt-community-post-card><kt-community-post-operate-popup vue-id="{{'733d8ec6-4-'+__i0__}}" data-ref="communityPostOperatePopup" class="data-v-4e0a3a40 vue-ref-in-for" bind:__l="__l"></kt-community-post-operate-popup></view></block></view></view>