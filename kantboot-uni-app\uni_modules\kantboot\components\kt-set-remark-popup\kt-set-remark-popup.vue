<template>
  <view>
    <kt-popup
        ref="ktPopup"
    >
      <view :class="clazz.box">
        <view>
            <view class="title">{{$i18n.zhToGlobal("设置备注")}}</view>
            <view>
              <view class="remark-input-box">
                <input
                    type="text"
                    v-model="bodyData.remark"
                    @input="bodyData.remark = $event.detail.value"
                    :placeholder="$i18n.zhToGlobal('备注')"
                    class="remark-input"
                />
              </view>
            </view>
            <view style="height: 50rpx"></view>
            <view>
              <kt-button
              ref="ktButton"
              @click="confirm()"
              >{{$i18n.zhToGlobal("确定")}}</kt-button>
            </view>
          <view style="height: 50rpx"></view>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box")
      },
      userAccountIdOfRemark:"",
      bodyData:{
        remark:""
      }
    };
  },
  methods: {
    open(userAccountIdOfRemark) {
      this.userAccountIdOfRemark = userAccountIdOfRemark;
      this.$refs.ktPopup.open();
      this.getRemark();
    },
    close() {
      this.$refs.ktPopup.close();
    },
    confirm() {
      this.$refs.ktButton.loading();
      this.setRemark();
      // this.$request.post("/user-interrelation-web/interrelationRemark/setRemark", {
      //   data: {
      //     userAccountIdOfRemark: this.userAccountIdOfRemark,
      //     remark: this.bodyData.remark
      //   }
      // }).then(res => {
      //   this.$refs.ktButton.success(res.msg);
      //   this.$refs.ktPopup.close();
      //   this.$emit("confirm");
      // }).catch(err => {
      //   this.$refs.ktButton.success(err.errMsg);
      //   this.$refs.ktPopup.close();
      // });
   },
    setRemark() {
      this.$request.post("/user-interrelation-web/interrelationRemark/setRemark", {
        data: {
          userAccountIdOfRemark: this.userAccountIdOfRemark,
          remark: this.bodyData.remark
        }
      }).then(res => {
        this.$refs.ktButton.success(res.msg);
        this.$emit("confirm");
        this.$kt.event.emit("remarkChange");
        setTimeout(()=>{
          this.$refs.ktPopup.close();
        },100);
      }).catch(err => {
        this.$refs.ktButton.error(err.errMsg);
      });
    },
    // /user-interrelation-web/interrelationRemark/getRemark
    async getRemark() {
      this.$refs.ktButton.loading();
      this.$request.post("/user-interrelation-web/interrelationRemark/getRemark", {
        data: {
          userAccountIdOfRemark: this.userAccountIdOfRemark
        }
      }).then(res => {
        this.bodyData = res.data;
        this.$refs.ktButton.success(res.msg);
      }).catch(err => {
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.box{
  background-color: #FFFFFF;
  box-sizing: border-box;
  padding: 30rpx;
  border-radius: 20rpx 20rpx 0 0;

  .title{
    font-size: 32rpx;
    color: #333333;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .remark-input-box{
    width: 100%;
    height: 80rpx;
    border-radius: 10rpx;
    background-color: #F5F5F5;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    box-sizing: border-box;

    .remark-input {
      width: 100%;
      height: 100%;
      font-size: 28rpx;
      color: #333333;
      background-color: transparent;
      border: none;
      outline: none;
    }
  }

}

.box-mode-device-pc{
  border-radius: 20rpx;
}
</style>
