<template>
  <view>
    <into-back></into-back>
    <view class="center-box">
      <view class="img">
        <kt-image
            class="img-img"
            border-radius="20rpx"
            mode="widthFix"
            :src="$kt.file.byPath('image/logo.png')"></kt-image>
      </view>
    </view>

  </view>
</template>

<script>
import IntoBack from "./components/IntoBack.vue";
import $kt from "@/uni_modules/kantboot"

export default {
  components: {
    IntoBack
  },
  data() {
    return {
      isHasBack: false,
      isToTo: false
    };
  },
  onLoad(options) {
  },
  async created() {
    // 获取该页面所有参数
    let pages = getCurrentPages();
    // 获取当前页面
    let currentPage = pages[pages.length - 1];
    let options = currentPage.options;
    setTimeout(() => {
      this.toTo(options)
    }, 20000)
    // await requestOfInto.request();
    // this.toTo(options);

    this.$kt.dataChange.checkDataChange("clientInit")
        .then(async (res) => {
          if(res.isChange){
            await $kt.i18n.loadingSupportLanguage();
            await $kt.i18n.loadingLocalized();
            await $kt.i18n.loadLanguagePackage("appFront", "en");
            await $kt.i18n.loadLanguagePackage("appFront", "zh_CN");
            await $kt.i18n.loadLanguagePackage("appFront");
            await $kt.userAccount.requestSelf();
            this.$kt.event.emit("init:clientInit");
            return;
          }
          this.$kt.event.emit("init:clientInit");
        }).catch((err)=>{
      this.$kt.event.emit("init:clientInit");
    });

    this.$kt.event.on("init:clientInit",()=>{
      this.toTo(options)
    })


  },
  onShow() {
    uni.$emit("intoPage", true);
  },
  methods: {
    toTo(options) {
      if (this.isToTo) {
        return;
      }
      this.isToTo = true;
      // 判断是否参数是否有重定向
      if (options?.redirect) {
        // 跳转到指定页面
        uni.redirectTo({
          url: options.redirect
        })
      } else {
        // 获取所有页面
        let pages = getCurrentPages();
        let url = $kt.router.config.indexPath;
        // 判断是否有上一级页面
        if (pages.length > 1) {
          // 获取上一个页面
          url = "/"+pages[pages.length - 2].route;
        }

        // 跳转到指定页面
        uni.reLaunch({
          url
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.box {
  position: fixed;
  left: 0;
  bottom: 0;
  font-size: 30rpx;
  z-index: 999;
  color: #000000;
  width: 100%;
  text-align: center;
  padding: 20rpx 60rpx 20rpx 60rpx;
  box-sizing: border-box;

  .progress-box {
    width: 80%;
    height: 20rpx;
    //color: #FFFFFF;
    color: #000000;
    left: 0;
    background-color: #f2f2f2;
    //background-color: rgba(0, 0, 0, .8);
    border: 1px solid #FFFFFF;
    border-radius: 12rpx;
    margin-top: 10rpx;

    .progress {
      width: 0%;
      height: 100%;
      background-color: #000000;
      //background-color: rgba(255, 255, 255, .8);
      border-radius: 10rpx;
    }
  }
}

.progress-text {
  //color: #FFFFFF;
  color: #000000;
  font-weight: bold;
}

.center-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;

  .img {
    width: 400rpx;
    height: 400rpx;
    // 动画
    animation: into-rotate 2s linear infinite;
  }
}

@keyframes into-rotate {
  0% {
    opacity: 1;
  }
  25% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  75% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

.progress-text-2 {
  color: rgba(255, 255, 255, .7)
}


</style>
