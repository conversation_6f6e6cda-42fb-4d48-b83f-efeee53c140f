<template>
  <kt-popup
      @close="close"
      ref="ktPopup">
    <view :class="clazz.box">
      <view class="box-title">
        {{$i18n.zhToGlobal("可见类型")}}
      </view>
      <scroll-view
          class="box"
          style="height: 400rpx"
          :scroll-y="true">
        <view class="menu-box">
          <view
              v-for="item in enums.getVisibleTypeList()"
              class="menu-item"
              @click="select(item)"
              :class="{
                'menu-item-selected': selected === item.code
              }"
          >{{item.name}}</view>
        </view>
      </scroll-view>
    </view>
  </kt-popup>
</template>

<script>
import enums from "../../../libs/enums";
export default {
  data() {
    return {
      enums,
      show: false,
      selected: 'public',
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      }
    };
  },
  mounted() {
  },
  methods: {
    open(selected) {
      this.selected = selected;
      this.show = true;
      this.$refs.ktPopup.open();
    },
    close() {
      this.show = false;
      this.$refs.ktPopup.close();
    },
    select(item) {
      this.selected = item.code;
      this.$emit('select', item.code);
      this.close();
    },
  },
}
</script>

<style lang="scss" scoped>
.box{
  bottom: 0;
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  .box-title{
    padding: 20rpx;
    font-size: 28rpx;
    color: #333333;
    text-align: center;
    font-weight: bold;
  }
}

.menu-box{
  padding: 20rpx;
  box-sizing: border-box;
  .menu-item{
    padding: 20rpx;
    border: 3px solid #F0F0F0;
    text-align: center;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    color: #666666;
  }
  .menu-item-selected{
    border: 3px solid #333333;
    background-color: #333333;
    color: #FFFFFF;
  }
}

.box-mode-device-pc{
  position: fixed;
  border-radius: 20rpx;
  // 超出不显示
  overflow: hidden;
  width: 400px;
  left:50%;
  bottom: 50%;
  padding: 30rpx;
  box-sizing: border-box;
  transform: translate(-50%,50%);
}
</style>
