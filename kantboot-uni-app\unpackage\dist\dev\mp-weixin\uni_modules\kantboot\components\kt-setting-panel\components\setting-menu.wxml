<view data-event-opts="{{[['tap',[['toClick']]]]}}" class="{{['data-v-8f704674',clazz.inBox]}}" bindtap="__e"><view class="in-box-item data-v-8f704674"><view class="in-box-item-box data-v-8f704674"><image class="in-box-item-icon data-v-8f704674" src="{{icon}}"></image><view class="in-box-item-text data-v-8f704674">{{title}}</view></view><view class="in-box-item-content data-v-8f704674">{{''+content+''}}<block wx:if="{{isRight}}"><image class="in-box-item-arrow-icon data-v-8f704674" src="{{$root.g0}}"></image></block><block wx:if="{{isSwitch}}"><kt-switch bind:input="__e" vue-id="50b43d00-1" value="{{switchValue}}" data-event-opts="{{[['^input',[['__set_model',['','switchValue','$event',[]]]]]]}}" class="data-v-8f704674" bind:__l="__l"></kt-switch></block></view></view></view>