<view class="container data-v-39000224"><view style="position:relative;" class="data-v-39000224"><wechat-mp-login vue-id="31fe793c-1" agree="{{bodyData.agree}}" data-ref="wechatMpLogin" class="data-v-39000224 vue-ref" bind:__l="__l"></wechat-mp-login><type-select bind:change="__e" vue-id="31fe793c-2" data-event-opts="{{[['^change',[['changeType']]]]}}" class="data-v-39000224" bind:__l="__l"></type-select><view style="height:10px;" class="data-v-39000224"></view><view class="input-box data-v-39000224"><scroll-view style="{{'height:'+('calc(100vh - 600rpx)')+';'}}" scroll-y="{{true}}" class="data-v-39000224"><view class="data-v-39000224"><view class="select-box data-v-39000224"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['select-item','data-v-39000224',(bodyData.genderCode==='male')?'select-item-active':'']}}" bindtap="__e">{{''+$root.g0+''}}<block wx:if="{{bodyData.genderCode==='male'}}"><image class="box-select-icon data-v-39000224" src="{{$root.g1}}"></image></block></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['select-item','data-v-39000224',(bodyData.genderCode==='female')?'select-item-active':'']}}" bindtap="__e">{{''+$root.g2+''}}<block wx:if="{{bodyData.genderCode==='female'}}"><image class="box-select-icon data-v-39000224" src="{{$root.g3}}"></image></block></view></view></view><username-input vue-id="31fe793c-3" body-data="{{bodyData}}" data-event-opts="{{[['^addUserLog',[['addUserLog']]],['^change',[['changeUsername']]]]}}" bind:addUserLog="__e" bind:change="__e" class="data-v-39000224" bind:__l="__l"></username-input><view style="height:30rpx;" class="data-v-39000224"></view><password-input vue-id="31fe793c-4" body-data="{{bodyData}}" data-event-opts="{{[['^addUserLog',[['addUserLog']]],['^change',[['changePassword']]]]}}" bind:addUserLog="__e" bind:change="__e" class="data-v-39000224" bind:__l="__l"></password-input><view style="height:30rpx;" class="data-v-39000224"></view><email-input vue-id="31fe793c-5" body-data="{{bodyData}}" data-event-opts="{{[['^addUserLog',[['addUserLog']]],['^change',[['changeEmail']]]]}}" bind:addUserLog="__e" bind:change="__e" class="data-v-39000224" bind:__l="__l"></email-input><project-meet-set-user-info-panel vue-id="31fe793c-6" has-submit-button="{{false}}" data-event-opts="{{[['^change',[['changeBodyData']]]]}}" bind:change="__e" class="data-v-39000224" bind:__l="__l"></project-meet-set-user-info-panel></scroll-view></view><block wx:if="{{$root.g4}}"><view style="height:30rpx;" class="data-v-39000224"></view></block><view style="height:30rpx;" class="data-v-39000224"></view><kt-button vue-id="31fe793c-7" is-open-box-shadow="{{false}}" data-ref="nextButton" data-event-opts="{{[['^click',[['toRegister']]]]}}" bind:click="__e" class="data-v-39000224 vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g5+" "+"➠"+''}}</kt-button><view style="height:20rpx;" class="data-v-39000224"></view><view style="text-align:center;" class="data-v-39000224"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="to-back-btn data-v-39000224" bindtap="__e">{{$root.g6}}</view></view><view style="height:20rpx;" class="data-v-39000224"></view></view><login-agreement bind:change="__e" vue-id="31fe793c-8" data-event-opts="{{[['^change',[['changeAgree']]]]}}" class="data-v-39000224" bind:__l="__l"></login-agreement></view>