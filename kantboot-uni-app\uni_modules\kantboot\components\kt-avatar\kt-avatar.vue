<template>
  <view
      :style="{
			'width':size,
			'height':size,
		}"
      class="content">

<!--    <kt-image-->
<!--      borderRadius="55%"-->
<!--        :src="$kt.file.byPath('icon/loading.svg')"-->
<!--        class="t-head loading-img" mode="aspectFill"></kt-image>-->


    <kt-image
        :style="{
      'border': border,
        }"
    borderRadius="55%"
    :fileId="fileId"
        :src="src"
        @load="bodyImageLoad"
        class="t-head"
        :class="{
            opacity: isBodyImageLoad ? 1 : 0
          }"
        mode="aspectFill"></kt-image>

    <view
        v-if="online"
        class="radius radius-line"></view>
  </view>
</template>

<script>
export default {
  name: "avatar",
  props: {
    fileId: {
      type: String,
      default: ''
    },
    src: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: '100rpx'
    },
    border: {
      type: String,
      default: '5rpx solid rgba(118,118,118,.3)'
    },
    // 是否在线
    online: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      // 默认图片
      isBodyImageLoad: false
    };
  },
  methods: {
    bodyImageLoad() {
        this.isBodyImageLoad = true
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  display: inline-block;
  border-radius: 50%;
}

.t-head {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 55%;
  top:0;
  left:0;
}

.radius {
  position: absolute;
  background-color: black;
  width: 20rpx;
  height: 20rpx;
  border: 5rpx solid white;
  border-radius: 50%;
  bottom: 0rpx;
  right: 0rpx;
}

.radius-line {
  background-color: rgb(8, 241, 117);
}

.radius-no-line {
  background-color: rgb(155, 156, 156);
}

.loading-img{
  opacity: 0.9;
  // 动画
  animation: loading 1s linear infinite;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}


</style>