@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-54aa5416 {
  position: relative;
  display: inline-block;
}
.user-info-card.data-v-54aa5416 {
  position: relative;
  display: inline-block;
  border-radius: 20rpx;
  overflow: hidden;
  background-color: #FFFFFF;
  width: 320rpx;
  height: 400rpx;
  box-shadow: 0 10rpx 15rpx rgba(118, 118, 118, 0.1);
}
.user-info-card .user-info-card-avatar.data-v-54aa5416 {
  width: 100%;
  height: 100%;
}
.user-info-card .user-info-card-info.data-v-54aa5416 {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 2;
  padding: 15rpx;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
}
.user-info-card .user-info-card-info .dto.data-v-54aa5416 {
  background-color: #08d68b;
  width: 10rpx;
  height: 10rpx;
  border-radius: 10rpx;
}
.user-info-card .user-info-card-info .user-info-card-info-ng.data-v-54aa5416 {
  text-align: left;
  color: #FFFFFF;
  font-size: 23rpx;
}
.user-info-card .user-info-card-info .user-info-card-info-ng .user-info-card-na-nickname.data-v-54aa5416 {
  display: inline-block;
  max-width: calc(100% - 40rpx);
  overflow: hidden;
  text-overflow: ellipsis;
}
.user-info-card .user-info-card-info .user-info-card-info-ng .user-info-card-na-gender.data-v-54aa5416 {
  vertical-align: top;
  display: inline-block;
  font-size: 20rpx;
  width: 35rpx;
  border-radius: 20rpx;
  text-align: center;
  margin-left: 10rpx;
  background-color: #3c9cff;
}
.user-info-card .user-info-card-info .user-info-card-info-ng .user-info-card-na-gender-male.data-v-54aa5416 {
  background-color: #3c9cff;
}
.user-info-card .user-info-card-info .user-info-card-info-ng .user-info-card-na-gender-female.data-v-54aa5416 {
  background-color: #ff5b5b;
}
.container-mode-device-pc .user-info-card.data-v-54aa5416 {
  border: 4rpx solid rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  box-shadow: none;
  cursor: pointer;
}
.container-mode-device-pc .user-info-card.data-v-54aa5416:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
