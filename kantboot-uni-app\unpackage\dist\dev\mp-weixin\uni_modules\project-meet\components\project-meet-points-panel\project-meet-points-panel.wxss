@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.header.data-v-3eec80af {
  position: relative;
  padding: 0 30rpx 30rpx 30rpx;
}
.header .header_bottom.data-v-3eec80af {
  position: absolute;
  top: 0rpx;
  right: 20rpx;
  border-radius: 16rpx;
  padding-left: 30rpx;
  font-size: 26rpx;
}
.bottom.data-v-3eec80af {
  padding: 0 30rpx 150rpx 30rpx;
}
.bottom .bottom_title.data-v-3eec80af {
  font-size: 32rpx;
  font-weight: bold;
  padding-bottom: 20rpx;
}
.bottom .bottom_list.data-v-3eec80af {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.bottom .bottom_list_item.data-v-3eec80af {
  width: 219rpx;
  height: 219rpx;
  box-sizing: border-box;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.7) 100%);
  border-radius: 25rpx;
  color: #222;
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}
.bottom .active_class.data-v-3eec80af {
  background: linear-gradient(to bottom, #fc4847 0%, rgba(255, 153, 0, 0.1) 100%);
  border: 1px solid #fc4847;
  color: #FFFFFF;
  text-shadow: 0 0 10rpx #fc4847;
}
.bottom .bottom_text_box.data-v-3eec80af {
  width: 200rpx;
  height: 100%;
  display: flex;
  flex-flow: column;
  justify-content: center;
  align-items: center;
}
.bottom .bottom_text_top.data-v-3eec80af {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}
.bottom .bottom_text_bottom.data-v-3eec80af {
  font-size: 40rpx;
  font-weight: bold;
}
.bottom .bottom_buttom.data-v-3eec80af {
  font-size: 26rpx;
}
.bottom .bottom_buttom .bottom_buttom_class.data-v-3eec80af {
  margin-top: 50rpx;
}
.panel-mode-device-pc .bottom_list.data-v-3eec80af {
  display: flex;
  flex-wrap: wrap;
  padding: 0 34rpx;
}
.panel-mode-device-pc .bottom_list_item.data-v-3eec80af {
  border: 1px solid #eeeeee;
  flex: 0 0 30%;
  cursor: pointer;
}
.panel-mode-device-pc.data-v-3eec80af {
  padding: 0 100px 0 100px;
  box-sizing: border-box;
}
.panel-mode-device-pc .bottom_buttom_class.data-v-3eec80af {
  padding: 0 200px 0 200px;
}
