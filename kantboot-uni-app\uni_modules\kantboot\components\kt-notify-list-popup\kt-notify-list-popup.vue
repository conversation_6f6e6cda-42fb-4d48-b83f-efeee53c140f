<template>
  <view>
    <kt-popup
    ref="ktPopup"
    @close="close()"
    >
      <view :class="clazz.box">
        <view class="box-title">{{$i18n.zhToGlobal("系统通知")}}</view>
        <scroll-view
            class="scroll-view"
            :scroll-y="true"
        >
          <kt-notify-list-panel
          v-if="show"
          ref="ktNotifyListPanel"
          ></kt-notify-list-panel>
        </scroll-view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      show:false,
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      }
    };
  },
  mounted() {
  },
  methods: {
    open() {
      this.show=true;
      setTimeout(()=>{
        this.$refs.ktPopup.open();
      },1);
    },
    close(){
      this.show=false;
    }
  },
}
</script>

<style lang="scss" scoped>
.box{
  padding: 20rpx 40rpx 20rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
  .box-title{
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
  .scroll-view{
    height: calc(100vh - 400rpx);
  }
}

.box-mode-device-pc{
  position: fixed;
  top:50%;
  left:50%;
  transform: translate(-50%,-50%);
  border-radius: 20rpx;
  width: 900rpx;
  .scroll-view{
    height: 600px;
  }

}
</style>
