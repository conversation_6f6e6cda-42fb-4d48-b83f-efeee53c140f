<template>

  <view
      class="container">
    <kt-nav-bar
        :title="$i18n.zhToGlobal('社区组件')"
    ></kt-nav-bar>

    <view class="box-box">
      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('前提')">
        <kt-markdown markdown="需要引入我之前写的kantboot组件中."></kt-markdown>
      </kt-box>

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('帖子权限设置组件（kt-community-post-permission-setting-box）')">
        <kt-box class="in-kt-box"
        :title="$i18n.zhToGlobal('默认模式')"
        >
          <kt-community-post-permission-setting-box>
          </kt-community-post-permission-setting-box>
        </kt-box>

        <kt-box
        class="in-kt-box"
        :title="$i18n.zhToGlobal('自定义用户弹框和用户卡片')"
        >
          <kt-community-post-permission-setting-box
              @openNotVisible="$refs.userAccountInterrelationPopupOfNotVisible.setIdSelectedList($event);$refs.userAccountInterrelationPopupOfNotVisible.open();"
              ref="fpCommunityPostPermissionSettingBox"
              :custom-user-info-card="true"
          >
            <!-- 可见范围->部分可见 的用户显示组件插槽 -->
            <template v-slot:userInfoCardOfPartVisible="{userAccountId}">
              {{userAccountId}}
            </template>
            <!-- 可见范围->不可见 的用户显示组件插槽 -->
            <template v-slot:userInfoCardOfNotVisible="{userAccountId}">
              {{userAccountId}}
            </template>
          </kt-community-post-permission-setting-box>
          <user-account-interrelation-popup
              :is-select-to-close="false"
              @idSelectedListSelect="$refs.fpCommunityPostPermissionSettingBox.selectNotIdSelect($event)"
              ref="userAccountInterrelationPopupOfNotVisible"
          ></user-account-interrelation-popup>

        </kt-box>

        <kt-markdown
            markdown="因为很多项目展示用户方式不一样，所以我提供了一个弹框和用户的卡片的插槽，`可见范围`区域需插槽插入自定义用户弹框组件和用户的卡片组件。"
        ></kt-markdown>
      </kt-box>


      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('帖子卡片组件（kt-community-post-card）')">

        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('默认模式')">
          <kt-community-post-card
              :post-id="'***************'"
          ></kt-community-post-card>
        </kt-box>

        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('无底部操作按钮模式')">
          <kt-community-post-card
              :post-id="'***************'"
              :has-bottom-operation="false"
          ></kt-community-post-card>
        </kt-box>

        <kt-box
            class="in-kt-box"
            :title="$i18n.zhToGlobal('底部插槽')">
          <kt-community-post-card
              :post-id="'***************'">
            <template v-slot:bottom>
              <view>
                <kt-button>
                  底部插槽
                </kt-button>
              </view>

            </template>
          </kt-community-post-card>
        </kt-box>


      </kt-box>


      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('帖子面板组件（kt-community-post-panel）')">
        <kt-box class="in-kt-box" :title="$i18n.zhToGlobal('内部卡片显示底部操作')">
          <u-divider></u-divider>
          <kt-community-post-panel
              height="900rpx"
              @cardDotClick="cardDotClick"
              @cardClick="cardClick"
          ></kt-community-post-panel>
          <u-divider></u-divider>
        </kt-box>
        <kt-box class="in-kt-box" :title="$i18n.zhToGlobal('内部卡片不显示底部操作')">
          <u-divider></u-divider>
          <kt-community-post-panel
              height="900rpx"
              :has-card-bottom-operation="false"
              @cardDotClick="cardDotClick"
              @cardClick="cardClick">
          </kt-community-post-panel>
          <u-divider></u-divider>
        </kt-box>

      </kt-box>


      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('帖子发布（kt-community-post-push）')">

        <kt-box class="in-kt-box" :title="$i18n.zhToGlobal('显示权限设置')">
          <kt-community-post-push
              @openNotVisible="$refs.notVisiblePopupInPushPanel.setIdSelectedList($event);$refs.notVisiblePopupInPushPanel.open();"
              ref="fpCommunityPostPermissionSettingBox">
            <!-- 可见范围->部分可见 的用户显示组件插槽 -->
            <template v-slot:user-account-of-part-visible="{userAccountId}">
              <user-info-card
                  :user-account-id="userAccountId">
              </user-info-card>
            </template>
            <!-- 可见范围->不可见 的用户显示组件插槽 -->
            <template v-slot:user-account-of-not-visible="{userAccountId}">
              <user-info-card
                  :user-account-id="userAccountId"
              ></user-info-card>
            </template>
          </kt-community-post-push>
          <user-account-interrelation-popup
              :is-select-to-close="false"
              @idSelectedListSelect="$refs.fpCommunityPostPermissionSettingBox.selectNotIdSelect($event)"
              ref="notVisiblePopupInPushPanel"
          ></user-account-interrelation-popup>
        </kt-box>

        <kt-box class="in-kt-box" :title="$i18n.zhToGlobal('不显示权限设置')">
          <kt-community-post-push
              :is-show-permission-setting="false"
              @openNotVisible="$refs.notVisiblePopupInPushPanel.setIdSelectedList($event);$refs.notVisiblePopupInPushPanel.open();"
              ref="fpCommunityPostPermissionSettingBox">
            <!-- 可见范围->部分可见 的用户显示组件插槽 -->
            <template v-slot:user-account-of-part-visible="{userAccountId}">
              <user-info-card
                  :user-account-id="userAccountId">
              </user-info-card>
            </template>
            <!-- 可见范围->不可见 的用户显示组件插槽 -->
            <template v-slot:user-account-of-not-visible="{userAccountId}">
              <user-info-card
                  :user-account-id="userAccountId"
              ></user-info-card>
            </template>
          </kt-community-post-push>
          <user-account-interrelation-popup
              :is-select-to-close="false"
              @idSelectedListSelect="$refs.fpCommunityPostPermissionSettingBox.selectNotIdSelect($event)"
              ref="notVisiblePopupInPushPanel"
          ></user-account-interrelation-popup>

        </kt-box>


      </kt-box>

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('帖子举报')">
        <kt-community-post-report></kt-community-post-report>
      </kt-box>

      <kt-box
          class="kt-box"
          :title="$i18n.zhToGlobal('帖子操作弹窗（kt-community-post-operate-popup）')">

        <kt-box>

          <kt-button
              @click='$refs.fpCommunityPostOperatePopup.open({id:"***************"})'
          >{{ $i18n.zhToGlobal("打开帖子操作弹窗") }}
          </kt-button>

        </kt-box>
        <kt-community-post-operate-popup
            ref="fpCommunityPostOperatePopup"
        ></kt-community-post-operate-popup>
      </kt-box>


    </view>

  </view>
</template>

<script>

export default {
  data() {
    return {
      ktImageSelectDemoValue: []
    };
  },
  methods: {
    cardDotClick(post) {
      uni.showToast({
        title: this.$i18n.zhToGlobal("点击了帖子操作按钮:" + post.id),
        icon: "none",
      })
    },
    cardClick(post) {
      uni.showToast({
        title: this.$i18n.zhToGlobal("点击了帖子:" + post.id),
        icon: "none",
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  box-sizing: border-box;
  text-align: center;

  .box-box {
    display: inline-block;
    width: 100%;
    max-width: 700px;
  }
}

.kt-box {
  text-align: left;
  border-radius: 10rpx;
  margin-bottom: 50rpx;
  box-shadow: 0 0 10rpx rgba(118, 118, 118, .3);

  .in-kt-box {
    border: 1rpx solid #eee;
    border-radius: 10rpx;
    margin-bottom: 30rpx;
  }
}
</style>
