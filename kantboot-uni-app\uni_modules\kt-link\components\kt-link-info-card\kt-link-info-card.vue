<template>
  <view>
    <view>

      <kt-box>
        <kt-user-info-card
        :user-info="{
          id:linkInfo.params.wechatId,
          fileIdOfAvatar:linkInfo.params.fileIdOfAvatar,
          nickname:linkInfo.params.nickname,
          introduction:linkInfo.params.description|| '-',
          isGenderSecret:true
        }"
        ></kt-user-info-card>
      </kt-box>
      <kt-box>
        <image
          :src="$kt.file.visit(linkInfo.params.fileIdOfQrcode)"
          mode="aspectFill">
        </image>
      </kt-box>
      <kt-box style="font-size: 26rpx">
        <view style="text-align: center">
          {{$i18n.zhToGlobal("微信号")}}{{": "}}<text>{{linkInfo.params.wechatId}}</text>
        </view>
        <view style="height: 10rpx"></view>
        <view style="text-align: center">
          {{$i18n.zhToGlobal("长按3秒识别")}}
        </view>
      </kt-box>

    </view>
  </view>
</template>

<script>
export default {
  props:{
    linkId:{
      type: String|Number,
      default: ''
    }
  },
  data() {
    return {
      linkInfo:{
        "description": null,
        "fileIdOfImage": 669847699910661,
        "gmtCreate": 1747239630871,
        "gmtModified": 1747239630871,
        "id": 676262825287685,
        "paramsJsonStr": "{\"fileIdOfAvatar\":669847699910661,\"wechatId\":\"dengddengdd\",\"nickname\":\"1111\",\"description\":\"1111\",\"fileIdOfQrcode\":669847699910661}",
        "title": "https://www.baidu.com",
        "type": "personalWechat",
        "params":{
          "fileIdOfAvatar": 669847699910661,
          "wechatId": "dengddengdd",
          "nickname": "测试001",
          "description": "哈哈哈哈",
          "fileIdOfQrcode": 669847699910661
        }
      },
    };
  },
  watch: {
    linkId: {
      handler(val) {
        this.getLinkInfo();
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getLinkInfo();
  },
  methods: {
    getLinkInfo(){
      this.$kt.request.post("/fp-link-web/link/getById",{
        data:{
          id:this.linkId
        }
      }).then((res)=>{
        let data = res.data;
        this.linkInfo = {
          ...data,
          params: JSON.parse(data.paramsJsonStr)
        };
      }).catch()
    }
  },
}
</script>

<style lang="scss" scoped>
.avatar-box {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}
.link-info-box {
}
</style>
