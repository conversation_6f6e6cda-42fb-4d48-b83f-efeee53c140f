<template>
  <view :class="clazz.ktFormat">

    <template v-for="(item,index) in data">

      <view v-if="item.type === 'text'"
            class="kt-format-content">
        <text class="kt-format-content-text">{{ item.content }}</text>
      </view>

      <view v-else-if="item.type === 'image:id'"
            class="kt-format-content">
        <view>
          <image
              class="kt-format-content-image"
              mode="widthFix"
              @click.stop="showImage('['+item.content+']',0)"
              :src="$kt.file.visit(item.content)"></image>
        </view>
      </view>

      <view v-else-if="item.type === 'images:id'"
            class="kt-format-content">

        <view
            v-if="typeImagesIdList(item.content).length===1"
            style="width: 100%">
          <image
              :src="$kt.file.visit(typeImagesIdList(item.content)[0])"
              mode="widthFix"
              @click.stop="showImage(item.content,0)"
              class="kt-format-content-image"
          ></image>
        </view>

          <view
              v-for="(imageItem,imageIndex) in typeImagesIdList(item.content)"
              v-if="typeImagesIdList(item.content).length===2"
              style="
              aspect-ratio: 1 / 1;
              width: calc(50% - 50rpx);
              border-radius: 20rpx;
              margin-right: 10rpx;
              display: inline-block">
            <image
                :src="$kt.file.visit(imageItem)"
                mode="aspectFill"
                class="kt-format-content-image"
                @click.stop="showImage(item.content,imageIndex)"
            >
            </image>
          </view>
          <view
              v-for="(imageItem,imageIndex) in typeImagesIdList(item.content)"
              v-if="typeImagesIdList(item.content).length>=3"
              style="
              aspect-ratio: 1 / 1;
              width: calc(33.3%);
              padding: 5rpx;
              box-sizing: border-box;
              display: inline-block">
            <image
                :src="$kt.file.visit(imageItem)"
                mode="aspectFill"
                class="kt-format-content-image"
                @click.stop="showImage(item.content,imageIndex)"
            >
            </image>
          </view>

      </view>

      <view v-else-if="item.type === 'video:id'"
        class="kt-format-content">
        <view
              :style="{
                position: 'relative',
                width: '100%',
                aspectRatio: item.ratio || '16 / 9',
              }"
        >
          <video
          style="width: 100%;height: 100%;border-radius: 20rpx"
          :src="$kt.file.visit(item.content)"
          ></video>
        </view>
      </view>


      <view v-else-if="item.type === 'postShare'"
            class="kt-format-content">
        <view
            :style="{
                position: 'relative',
                width: '100%',
              }">
          <view
          @click.stop="postCardClick({id: item.content})"
          v-if="item.content"
          >
            <kt-community-post-card
                @userClick="postUserCardClick"
                :post-id="item.content"
                :has-dot="false"
                :has-bottom-operation="false"
            ></kt-community-post-card>
          </view>
        </view>
      </view>



    </template>

  </view>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => {
        return [{
          type: "text",
          content: "测试001"
        }, {
          type: "image:id",
          content: "658887105785861"
        }, {
          type: "images:id",
          content: "[658887105785861,658887105785861]"
        },
        {
          type: "text",
          content: "测试001"
        },
        {
          type: "images:id",
          content: "[658887105785861,658887105785861]"
        },
        {
          type: "images:id",
          content: "[658887105785861,658887105785861,658887105785861]"
        },
        {
          type: "video:id",
          content: "668444281610245",
          ratio: "16 / 9"
        }];
      }
    }
  },
  data() {
    return {
      clazz:{
        ktFormat: this.$kt.style.toggleClass("kt-format"),
      }
    };
  },
  methods: {
    postCardClick(item){
      this.$emit('postCardClick', item);
    },
    postUserCardClick(item){
      this.$emit('postUserCardClick', item);
    },
    typeImagesIdList(itemContent) {
      try {
        return JSON.parse(itemContent);
      } catch (e) {
        return [];
      }
    },
    showImage(itemContent, index) {
      let imagesIdList = this.typeImagesIdList(itemContent);
      let urls = [];
      for (let i = 0; i < imagesIdList.length; i++) {
        urls.push(this.$kt.file.visit(imagesIdList[i]));
      }
      uni.previewImage({
        current: index,
        urls: urls
      });
    }
  }
}
</script>

<style lang="scss" scoped>

.kt-format {
  width: 100%;
  text-align: left;

  .kt-format-content {
    padding: 10rpx;
    box-sizing: border-box;

    .kt-format-content-text {
      font-size: 32rpx;
      color: #333;
      margin: 10rpx 0;
      letter-spacing: 2rpx;
    }

    .kt-format-content-image-by-image-id {
      width: 100%;
      max-height: 500rpx;
    }
  }
}


.kt-format-content-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

.kt-format-mode-device-pc{
  .kt-format-content {
    padding: 10rpx;
    box-sizing: border-box;

    .kt-format-content-text {
      font-size: 19px;
      color: #333;
      margin: 10rpx 0;
      letter-spacing: 2px;
    }
  }
}

</style>
