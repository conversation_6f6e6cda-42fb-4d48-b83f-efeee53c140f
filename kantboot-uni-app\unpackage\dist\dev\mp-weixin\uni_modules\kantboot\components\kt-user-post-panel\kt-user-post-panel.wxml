<view class="data-v-41008e7c"><view class="data-v-41008e7c"><block wx:if="{{loading}}"><u-loading-icon vue-id="1077e084-1" mode="circle" size="70" class="data-v-41008e7c" bind:__l="__l"></u-loading-icon></block><block wx:if="{{$root.g0}}"><view class="no-data data-v-41008e7c"><view class="data-v-41008e7c"><image class="no-data-image data-v-41008e7c" src="{{$root.g1}}"></image></view><view class="no-data-text data-v-41008e7c">{{$root.g2+''}}</view></view></block><block wx:for="{{list}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['cardClick',['$0'],[[['list','',__i0__,'id']]]]]]]}}" bindtap="__e" class="data-v-41008e7c"><kt-community-post-card vue-id="{{'1077e084-2-'+__i0__}}" post="{{item}}" is-forbid-forward="{{false}}" has-dot="{{false}}" data-event-opts="{{[['^userClick',[['userClick']]]]}}" bind:userClick="__e" class="data-v-41008e7c" bind:__l="__l"></kt-community-post-card><kt-community-post-operate-popup vue-id="{{'1077e084-3-'+__i0__}}" data-ref="communityPostOperatePopup" class="data-v-41008e7c vue-ref-in-for" bind:__l="__l"></kt-community-post-operate-popup></view></block></view></view>