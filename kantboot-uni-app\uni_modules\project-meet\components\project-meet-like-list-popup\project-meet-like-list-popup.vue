<template>
  <view>
    <kt-popup
        ref="ktPopup">
      <view
          :class="clazz.box"
      >
        <view style="height: 50rpx"></view>
        <project-meet-like-list-panel
            v-if="show"
            @close="close()"
        ></project-meet-like-list-panel>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box"),
      },
      show:false
    };
  },
  methods: {
    open(){
      this.show = true;
      this.$refs.ktPopup.open();
    },
    close(){
      this.show = false;
    },
  },
}
</script>

<style lang="scss" scoped>
.box{
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
}
.box-mode-device-pc {
  text-align: center;
  .card{
    position: relative;
    width: 700rpx;
    display: inline-block;
    margin-bottom: 20rpx;
  }
  position: fixed;
  width: 800px;
  border-radius: 20rpx;
  padding: 20rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
