<template>
	<view>
    <view id="headerInUserAccountInterrelation">
      <kt-nav-bar
          :title="$i18n.zhToGlobal('用户关系')"></kt-nav-bar>
    </view>

    <view class="box">
      <kt-user-account-interrelation-panel
          ref="userAccountInterrelationPanel"
          :user-account-id="userAccountId"
          :init-code="initCode"
          @select="toUserInfoPage"
          :height="'calc(100vh - '+headerHeight+'rpx - 160rpx)'"
      ></kt-user-account-interrelation-panel>

    </view>
  </view>
</template>

<script>
  export default {
		data() {
			return {
				headerHeight: 0,
        userAccountId: '',
        initCode: ''
			}
		},
    onLoad(options) {
      // 获取用户id
      this.userAccountId = options.userAccountId;
      if(options.code) {
        console.log(options.code,"options.code")
        this.initCode = options.code
      }
    },
    mounted() {
      this.getHeight()
    },
		methods: {
      toUserInfoPage(userAccount){
        this.$kt.router.navTo('/pages/project-make-friends-pages/user-info/user-info',{"userAccountId":userAccount.id})
      },
      getHeight() {
        this.$nextTick(() => {
          uni.createSelectorQuery()
            .select('#headerInUserAccountInterrelation')
            .boundingClientRect((res) => {
              this.headerHeight = res.height
            }).exec()
        })
      },
			
		}
	}
</script>

<style lang="scss" scoped>
.box{
  padding: 30rpx;
  box-sizing: border-box;
}
</style>
