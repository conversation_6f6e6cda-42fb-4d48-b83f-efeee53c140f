<view hidden="{{!(show)}}" class="{{['data-v-22aa6b24',clazz.box]}}"><view class="box-logo data-v-22aa6b24"><image class="logo data-v-22aa6b24" src="{{$root.g0}}" mode="aspectFit"></image></view><block wx:if="{{false}}"><kt-button class="button data-v-22aa6b24 vue-ref" bind:click="__e" vue-id="32a1f750-1" data-ref="loginByCodeButton" data-event-opts="{{[['^click',[['loginByCode']]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><image class="button-icon data-v-22aa6b24" src="{{$root.g1}}"></image>{{''+$root.g2+''}}<view class="button-into-tip data-v-22aa6b24">{{''+"➠"+''}}</view></kt-button></block><kt-button data-custom-hidden="{{!(!agree)}}" class="button data-v-22aa6b24 vue-ref" bind:click="__e" vue-id="32a1f750-2" data-ref="loginByPhoneButtonTips" data-event-opts="{{[['^click',[['tipAgree']]]]}}" bind:__l="__l" vue-slots="{{['default']}}">{{''+$root.g3+''}}<view class="button-into-tip data-v-22aa6b24">{{''+"➠"+''}}</view></kt-button><kt-button data-custom-hidden="{{!(agree)}}" class="button data-v-22aa6b24 vue-ref" style="position:relative;" vue-id="32a1f750-3" data-ref="loginByPhoneButton" bind:__l="__l" vue-slots="{{['default']}}"><view class="data-v-22aa6b24"></view>{{''+$root.g4+''}}<view class="button-into-tip data-v-22aa6b24">{{''+"➠"+''}}</view><button style="position:absolute;z-index:999;width:100%;height:100%;top:0;left:0;opacity:0;" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['loginByPhone',['$event']]]]]}}" bindgetphonenumber="__e" class="data-v-22aa6b24">123</button></kt-button><view style="height:20rpx;" class="data-v-22aa6b24"></view><view style="text-align:center;" class="data-v-22aa6b24"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="little-btn data-v-22aa6b24" bindtap="__e">{{$root.g5}}</view></view></view>