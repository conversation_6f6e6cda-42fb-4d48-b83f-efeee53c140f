<template>
  <kt-nav-bar
  :indexPath="indexPath"
  :title="title"
  :is-has-i18n="isHasI18n"
  :icon="icon"
  ></kt-nav-bar>
</template>

<script>
export default {
  props:{
    indexPath:{
      type:String,
      default:"/pages/project-acm-pages/body/body"
    },
    title:{
      type:String,
      default:""
    },
    isHasI18n:{
      type:Boolean,
      default:false
    },
    icon:{
      type:String,
      default:null
    }
  },
  data() {
    return {};
  }
}
</script>

<style lang="scss">

</style>
