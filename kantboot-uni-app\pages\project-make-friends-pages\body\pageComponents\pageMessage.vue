<script>
export default {
  name: "pageChat",
  data() {
    return {
      // 组件高度
      height: 0,
      // 组件高度
      navBarHeight: 0,
      // 是否加载完成
      isLoad: false,
      scrollTop: 0,
      list: [],
      clazz: {
        container: this.$kt.style.toggleClass("container"),
        bg: this.$kt.style.toggleClass("bg")
      },
      dialogIdSelected: {},
      footerHeight: 0,
      // 初始化代码，用于搜索页面的参数传递
      initCode: '',
      // 控制搜索菜单显示
      showSearchMenu: false,
    };
  },
  mounted() {
    // 获取导航栏高度
    // this.navBarHeight = this.$refs.navBar.getHeight();
    // 获取#navBarInMessage的高度
    this.$kt.event.on('changeTabbar', () => {
      setTimeout(() => {
        try {
          uni.createSelectorQuery()
            .in(this)
            .select("#navBarInMessage")
            .boundingClientRect((res) => {
              this.navBarHeight = res.height;
              console.log(res, "pageMessage");
            }).exec();
        } catch (e) {
          console.log(e, "pageMessage 错误的高度获取");
        }

      }, 50);
    })

  },
  created() {
    // 监听登录成功事件
    this.$kt.event.on("login:success", () => {
      this.isLogin = true;
      this.getInitList();
    });
  },
  methods: {
    toSearch() {
      this.$kt.router.navTo("/pages/project-make-friends-pages/user-search/user-search", {
        code: this.initCode
      });
    },
    // 显示搜索菜单
    showSearchOptions() {
      this.showSearchMenu = true;
    },
    // 隐藏搜索菜单
    hideSearchMenu() {
      this.showSearchMenu = false;
    },
    // 处理搜索选项点击
    handleSearchClick() {
      this.hideSearchMenu();
      this.toSearch();
    },
    // 处理扫一扫点击
    handleScanClick() {
      this.hideSearchMenu();
      this.toScan();
    },
    // 扫一扫功能
    toScan() {
      console.log("开始扫码");
      // 打开微信扫一扫
      uni.scanCode({
        onlyFromCamera: true,
        scanType: ['qrCode'],
        success: (res) => {
          // 处理扫码结果
          console.log("扫码结果：", res);
          if (res.result) {
            // 解析扫码结果
            let result = JSON.parse(res.result);
            if (result.operateCode === 'user-qrcode') {
              this.$kt.router.navTo('/pages/project-make-friends-pages/user-info/user-info?userAccountId=' + result.userAccountId);
            } else if (result.operateCode === 'member-partner') {
              // 处理协作组邀请二维码
              this.joinGroupByQrCode(result.invitationGroupId);
            }
          }
        },
        fail: (err) => {
          console.error("扫码失败：", err);
        }
      });
    },
    // 通过二维码加入协作组
    joinGroupByQrCode(invitationGroupId) {
      console.log("加入协作组：", invitationGroupId);

      // 显示加载提示
      uni.showLoading({
        title: this.$i18n.zhToGlobal('正在加入协作组...')
      });

      // 调用后端接口加入协作组
      this.$kt.request.post('/project-make-friends-web/invitationRelation/joinGroupByQrCode', {
        data: {
          invitationGroupId: invitationGroupId
        }
      }).then((res) => {
        uni.hideLoading();
        if (res.success) {
          uni.showToast({
            title: this.$i18n.zhToGlobal('加入协作组成功'),
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: res.message || this.$i18n.zhToGlobal('加入协作组失败'),
            icon: 'none'
          });
        }
      }).catch((err) => {
        uni.hideLoading();
        console.error("加入协作组失败：", err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('加入协作组失败'),
          icon: 'none'
        });
      });
    },
    init() {
      // projectMakeFriendsPageMessageFooter
      uni.createSelectorQuery()
        .in(this)
        .select("#projectMakeFriendsPageMessageFooter")
        .boundingClientRect((res) => {
          this.footerHeight = res.height;
          console.log(res, "pageMessage");
        }).exec();
    },
    send(param) {
      this.$refs.sendInput.toLoading();
      // /fp-community-web/post/push
      this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf", {
        data: {
          items: [param],
          dialogId: this.dialogIdSelected
        }
      }).then((res) => {
        this.$refs.sendInput.clear();
        this.$refs.sendInput.toNone();
      }).catch((res) => {

      })
    },
    cardClick(item) {
      if (this.$kt.style.detectDeviceType() === 'pc') {
        this.dialogIdSelected = item.id;
        this.$nextTick(() => {
          this.init();
        })
        return;
      }
      console.log(JSON.stringify(item) + "item.id")
      this.$kt.router.navTo('/pages/project-make-friends-pages/chat-dialog/chat-dialog?dialogId=' + item.id);
    }
  }
}
</script>

<template>
  <view :class="clazz.container">
    <view :class="clazz.bg"></view>
    <view id="navBarInMessage" class="box-header">
      <kt-nav-bar ref="navBar" :icon="$kt.file.byPath('tabbar/message-selected.svg')"
        :title="$i18n.zhToGlobal('消息')"></kt-nav-bar>
    </view>

    <view class="second-box" style="position: relative;padding: 25rpx;">
      <image style="width: 40rpx;height: 40rpx;position: absolute;top:50%;transform: translateY(-50%);right: 140rpx;"
        class="second-icon" @click="toSearch()" :src="$kt.file.byPath('icon/search.svg')"></image>
      <image style="width: 50rpx;height: 50rpx;position: absolute;top:50%;transform: translateY(-50%);right: 80rpx;"
        class="second-icon" @click="showSearchOptions()" :src="$kt.file.byPath('icon/addRound.svg')"></image>
    </view>

    <!-- 搜索菜单弹窗 -->
    <view v-if="showSearchMenu" class="search-menu-overlay" @click="hideSearchMenu()">
      <view class="search-menu" @click.stop>
        <view class="search-menu-item" @click="handleSearchClick()">
          <image class="search-menu-icon" :src="$kt.file.byPath('icon/search.svg')"></image>
          <text class="search-menu-text">{{ $i18n.zhToGlobal('搜索') }}</text>
        </view>
        <view class="search-menu-item" @click="handleScanClick()">
          <image class="search-menu-icon" :src="$kt.file.byPath('icon/scan.svg')"></image>
          <text class="search-menu-text">{{ $i18n.zhToGlobal('扫一扫') }}</text>
        </view>
      </view>
    </view>
    <view class="kt-chat-list-panel-box">
      <kt-chat-list-panel :height="'calc(100vh - ' + navBarHeight + 'px - 20rpx - 50rpx)'" @cardClick="cardClick">
      </kt-chat-list-panel>
    </view>
    <view v-if="$kt.style.detectDeviceType() === 'pc'" class="kt-chat-dialog-panel-box">
      <kt-chat-dialog-panel :height="'calc(100vh - ' + navBarHeight + 'px - ' + footerHeight + 'px - 20rpx - 50rpx)'"
        :dialog-id="dialogIdSelected">
      </kt-chat-dialog-panel>
      <view id="projectMakeFriendsPageMessageFooter" class="footer">

        <kt-send-input :has-voice="false" @change="init" @send="send" mode="panel" ref="sendInput">

        </kt-send-input>

      </view>
    </view>



    <kt-no-login wechat-login-method="loginByCode" ref="noLogin"></kt-no-login>

  </view>


</template>

<style scoped lang="scss">
.box {
  padding: 20rpx;
  box-sizing: border-box;

}

.bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  // 从白到黑的渐变，从上到下
  //background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}

.bg-mode-color-scheme-light {
  //background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
  background-color: #FFFFFF;
}

.bg-mode-color-scheme-dark {
  background-color: #191919;
}

.in-box {
  position: relative;
  height: 170rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .in-box-user-info-card {
    position: absolute;
    width: 100%;
    top: 15rpx;
    left: 0
  }
}

.container-mode-color-scheme-dark {
  .in-box {
    border-bottom: 1rpx solid #404a56;
  }

  .second-icon {
    // 颜色反转
    filter: invert(1);
  }
}

.container-mode-device-pc {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;

  .box {
    .in-box {
      position: relative;
      width: 100%;
      height: 160rpx;

      .in-box-user-info-card {
        position: absolute;
        width: 100%;
        top: 15rpx;
        left: -450rpx
      }
    }
  }

  .header-box {
    width: 100%;
  }
}


.second-box {
  width: 100%;

  .second-box-box {
    width: calc(100% - 100rpx);
    // 不换行
    white-space: nowrap;
    // 超出滚动
    overflow-x: auto;

    .second-box-item {
      display: inline-block;
      font-size: 32rpx;
      letter-spacing: 3rpx;
      margin-right: 5rpx;
      padding: 10rpx 20rpx;
      border-radius: 10rpx;
    }

    .second-box-item-selected {
      font-weight: bold;
    }
  }
}

.container-mode-device-pc {
  .kt-chat-list-panel-box {
    display: inline-block;
    width: 600rpx;
  }

  .kt-chat-dialog-panel-box {
    display: inline-block;
    width: calc(100% - 600rpx);
    color: #000000;
    vertical-align: top;
    // 渐变，从白到#f0f0f0
    background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
  }
}

.footer {
  background-color: #FFFFFF;
}

/* 搜索菜单样式 */
.search-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.search-menu {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 20rpx;
  margin: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  min-width: 300rpx;
}

.search-menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-radius: 10rpx;
  transition: background-color 0.2s;
}

.search-menu-item:hover {
  background-color: #f5f5f5;
}

.search-menu-item:active {
  background-color: #e5e5e5;
}

.search-menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.search-menu-text {
  font-size: 32rpx;
  color: #333333;
}

/* 暗色模式下的搜索菜单样式 */
.container-mode-color-scheme-dark {
  .search-menu {
    background-color: #2c2c2c;
  }

  .search-menu-item:hover {
    background-color: #404040;
  }

  .search-menu-item:active {
    background-color: #505050;
  }

  .search-menu-text {
    color: #ffffff;
  }

  .search-menu-icon {
    filter: invert(1);
  }
}
</style>
