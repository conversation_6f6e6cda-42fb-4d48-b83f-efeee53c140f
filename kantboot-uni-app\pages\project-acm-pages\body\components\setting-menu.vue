<template>
    <view
        @click="toClick()"
        :class="clazz.inBox">
      <view class="in-box-item">
        <view class="in-box-item-box">
          <image
              class="in-box-item-icon"
              :src="icon"
          ></image>
          <view class="in-box-item-text">{{title}}</view>
        </view>
        <view class="in-box-item-content">
          {{content}}
          <image
              v-if="isRight"
              class="in-box-item-arrow-icon"
              :src="$kt.file.byPath('icon/arrowRight.svg')"
          ></image>
          <kt-switch
              v-if="isSwitch"
          v-model="switchValue"
          ></kt-switch>
        </view>
      </view>
  </view>
</template>

<script>
export default {
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    isRight: {
      type: Boolean,
      default: false
    },
    isSwitch: {
      type: <PERSON>olean,
      default: false
    },
    switchValue: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      clazz:{
        inBox:this.$kt.style.toggleClass('in-box')
      }
    };
  },
  methods: {
    toClick() {
      this.$emit('click');
    }
  },
}
</script>

<style lang="scss" scoped>
.in-box{
  color: $kt-color-primary-light;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx;
  border-radius: 10rpx;
  .in-box-item{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 80rpx;
    .in-box-item-box{
      display: flex;
      flex-direction: row;
      align-items: center;
      .in-box-item-icon{
        width: 40rpx;
        height: 40rpx;
        margin-right: 20rpx;
      }
      .in-box-item-text{
        font-size: 28rpx;
      }
    }
  }
  .in-box-item-arrow-icon{
    width: 30rpx;
    height: 30rpx;
    margin-top: 10rpx;
  }

}

.in-box:active{
  opacity: .8;
}

.in-box-item-content{
  font-size: 28rpx;

}

.in-box-mode-color-scheme-light{
  color: $kt-color-primary-light;
}

.in-box-mode-color-scheme-dark{
  color: $kt-color-primary-dark;
  .in-box-item-icon{
    // 颜色反转
    filter: invert(100%);
  }
  .in-box-item-arrow-icon{
    filter: invert(100%);
  }
}


</style>
