<template>
  <view>

  </view>
</template>

<script>
export default {
  onLoad(options) {
    console.log('onLoad', options);
    this.init(options);
    uni.navigateTo({
      url: '/pages/project-make-friends-pages/body/body'
    })
  },
  data() {
    return {};
  },
  created() {
  },
  methods: {
    init(options){
      if(options.userAccountIdOfInviter&&!this.$kt.userAccount.getIsLogin()){
        this.$kt.userAccount.setUserAccountIdOfInviter(options.userAccountIdOfInviter);
      }
    }
  },
}
</script>

<style lang="scss">

</style>
