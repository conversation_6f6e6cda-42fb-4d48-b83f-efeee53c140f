<template>
  <view>
    <kt-popup
        ref="ktPopup">
      <view class="box">
        <view class="box-title">{{$i18n.zhToGlobal("职业选择")}}</view>
        <view style="height: 30rpx"></view>
        <view>

          <scroll-view
              :scroll-y="true"
              style="height: calc(100vh - 300rpx);width: 300rpx;display: inline-block">
            <view>
              <view
                  class="bl-box"
                  v-for="item in list"
                  :key="item.id"
                  :class="{
              'bl-box-item-selected': parentSelected.id === item.id
            }"
                  @click="parentSelect(item)"
              >
                <view class="bl-box-item">{{ item.name }}</view>
              </view>
            </view>
          </scroll-view>

          <scroll-view
              :scroll-y="true"
              style="height: calc(100vh - 300rpx);width: calc(100% - 300rpx);display: inline-block">
            <view>
              <view
                  class="bl-box"
                  v-for="item in parentSelected.children"
                  :key="item.id"
                  :class="{
              'bl-box-item-selected': selected.id === item.id
            }"
                  @click="select(item)"
              >
                <view class="bl-box-item">{{ item.name }}</view>
              </view>
            </view>
          </scroll-view>

        </view>

      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list:[],
      parentSelected: {
        id:null
      },
      selected: {
        id: null
      }
    };
  },
  mounted() {
    // this.open();
  },
  methods: {
    getList() {
      // /project-make-friends-web/admin/jobType/getAllParentIsNull
      this.$request.get('/project-make-friends-web/admin/jobType/getAllParentIsNull').then(res => {
        this.list = res.data;
        if(this.list.length > 0) {
          this.parentSelected = this.list[0];
        } else {
          this.parentSelected = {id: null};
          this.selected = {id: null};
        }
      }).catch(err => {
      })
    },
    parentSelect(item) {
      this.parentSelected = item;
    },
    select(item) {
      this.selected = item;
      // /project-make-friends-web/userAccount/setJobTypeId
      this.$request.post('/project-make-friends-web/userAccount/setJobTypeId', {data: {jobTypeId: item.id}}).then(res => {
        this.$kt.userAccount.requestSelf();
        tihs.close();
      }).catch(err => {
        this.close();
      });
    },
    open() {
      this.parentSelected = {
        id:null
      };
      this.selected = {
        id:null
      };
      this.$refs.ktPopup.open();
      this.getList();
    },
    close() {
      this.$refs.ktPopup.close();
    },

  },
}
</script>

<style lang="scss" scoped>
.box{
  padding: 20rpx 40rpx 20rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
  .box-title{
    font-weight: bold;
  }
}

.bl-box {
  text-align: left;
  width: 100%;
  .bl-box-item {
    padding: 20rpx;
    font-size: 28rpx;
    color: #333;
    border-bottom: 1px solid #eee;
    // 超出省略
    overflow: hidden;
  }
}

.bl-box-item-selected {
  background-color: rgba(0,0,0,.2);
}
</style>
