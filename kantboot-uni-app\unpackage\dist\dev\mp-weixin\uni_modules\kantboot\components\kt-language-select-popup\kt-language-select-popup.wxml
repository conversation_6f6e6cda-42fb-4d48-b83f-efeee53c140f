<kt-popup vue-id="fc70deb8-1" data-ref="ktPopup" class="data-v-292cf6bc vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-292cf6bc',clazz.box]}}"><view class="title data-v-292cf6bc"><image class="title-icon data-v-292cf6bc" src="{{$root.g0}}"></image>{{''+$root.g1+''}}<view class="top-left data-v-292cf6bc"><slot name="topLeft"></slot></view></view><view class="scroll-view data-v-292cf6bc" style="{{'height:'+('calc(100vh - '+'500rpx)')+';'}}"><kt-language-select-panel vue-id="{{('fc70deb8-2')+','+('fc70deb8-1')}}" reset="{{reset}}" data-event-opts="{{[['^select',[['select']]]]}}" bind:select="__e" class="data-v-292cf6bc" bind:__l="__l"></kt-language-select-panel></view></view></kt-popup>