<template>
  <view class="box">
    <swiper
      class="carousel"
      indicator-dots
      autoplay
      interval="3000"
      duration="500"
      :style="{
        height: height,
      }"
      @change="handleSwiperChange"
    >
      <swiper-item v-for="(item, index) in list" :key="index">
        <view class="carousel-image-box"
        :style="{
          padding: currentIndex === index ? '0' : '20rpx',
          borderRadius: borderRadius,
        }"
        >
          <image
              class="carousel-image"
              :style="{
                borderRadius: borderRadius
              }"
              :src="$kt.file.visit(item.fileIdOfImage)"
              mode="aspectFill"
              @click="handleClick(item)"
          />
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import $kt from "@/uni_modules/kantboot";

export default {
  props: {
    typeCode: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: '400rpx'
    },
    borderRadius: {
      type: String,
      default: '10rpx'
    },
  },
  data() {
    return {
      list: [],
      errInfo: {},
      currentIndex: 0, // 新增变量
      $kt,
    };
  },
  watch: {
    typeCode: {
      handler(val, oldVal) {
        if (val === oldVal) {
          return;
        }
        this.getListByTypeCode();
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getListByTypeCode();
  },
  methods: {
    getListByTypeCode() {
      this.$kt.request.post("/fp-carousel-web/carousel/getByTypeCode", {
        data: {
          typeCode: this.typeCode
        }
      }).then((res) => {
        this.list = res.data;
      }).catch((err) => {
        this.errInfo = err;
      });
    },
    handleClick(item) {
      if (item.linkUrl) {
        uni.navigateTo({
          url: item.linkUrl
        });
      }
    },
    handleSwiperChange(event) {
      this.currentIndex = event.detail.current; // 更新当前索引
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.carousel {
  width: 100%;
  height: 400rpx;
}

.carousel-image-box{
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.current-index {
  text-align: center;
  margin-top: 10rpx;
  font-size: 28rpx;
  color: #333;
}
</style>
