@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.modal.data-v-77a7a97c {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 500rpx;
  height: auto;
  background-color: #fff;
  border-radius: 10px;
}
.modal .modal-header.data-v-77a7a97c {
  padding: 20rpx;
  text-align: center;
}
.modal .modal-title.data-v-77a7a97c {
  font-size: 34rpx;
  font-weight: bold;
}
.modal .modal-content.data-v-77a7a97c {
  padding: 20rpx 20rpx 0 20rpx;
  text-align: center;
  letter-spacing: 1rpx;
}
.modal .modal-content .modal-content-text.data-v-77a7a97c {
  font-size: 30rpx;
  color: #333;
}
.modal .modal-split.data-v-77a7a97c {
  height: 1px;
  background-color: #e0e0e0;
  margin: 20rpx 0;
}
.modal .modal-footer.data-v-77a7a97c {
  padding: 20rpx;
  text-align: center;
}
.modal .modal-footer .modal-button.data-v-77a7a97c {
  display: inline-block;
  padding: 10rpx 20rpx;
  margin: 0 10rpx;
  border-radius: 5px;
  cursor: pointer;
  font-size: 28rpx;
  letter-spacing: 2rpx;
}
.modal-footer.data-v-77a7a97c:active {
  opacity: 0.5;
}
.modal-mode-color-scheme-dark.data-v-77a7a97c {
  background-color: #393939;
}
.modal-mode-color-scheme-dark .modal-header.data-v-77a7a97c {
  color: #eeeeee;
}
.modal-mode-color-scheme-dark .modal-content .modal-content-text.data-v-77a7a97c {
  color: #bbb;
}
.modal-mode-color-scheme-dark .modal-split.data-v-77a7a97c {
  background-color: #444;
}
.modal-mode-color-scheme-dark .modal-footer .modal-button.data-v-77a7a97c {
  color: #fff;
}
