<template>
  　　<view>
        <view class="bg"></view>
        <div id="svgaBox"></div>
  　　</view>
</template>
<script>
// npm install svgaplayerweb —save
　import SVGA from 'svgaplayerweb'
　　export default {
　　data() {
　　　　return {
　　　　　　player:{},
　　　　　　parser:{}
　　　　}
　　},
　　mounted() {
　　　　// 动态引入类库
　　　　const script = document.createElement('script')
　　　　script.src = SVGA
　　　　document.head.appendChild(script)
　　　　script.onload = this.ready();
  this.$refs.ktPopup.open();
　　},
　　methods: {
　　　　ready(){
　　　　this.player = new SVGA.Player(document.getElementById('svgaBox'));
　　　　this.parser = new SVGA.Parser(document.getElementById('svgaBox'));
　　　　//注意，这里必须是服务器地址，否则报错
　　　　this.parser.load('https://papaqd.aaarfyh.com/functional-file-web/file/visit/683254448803845',
　　　　　　(videoItem)=> {
　　　　　　　　this.player.loops = 1; // 设置循环播放次数是1
　　　　　　　　this.player.setVideoItem(videoItem);
　　　　　　　　this.player.startAnimation();
　　　　　　　　// 动画播放至某帧后回调
　　　　　　　　this.player.onFrame((i)=>{
　　　　　　　　　　// console.log("播放到第"+i+"帧")
　　　　　　　　})
　　　　　　　　// 动画播放至某进度后回调
　　　　　　　　this.player.onPercentage((i)=>{
　　　　　　　　　　// console.log("播放到"+(i*100)+"%")
　　　　　　　　})
　　　　　　　　// 动画播放结束回调
　　　　　　　　this.player.onFinished(res=>{
　　　　　　　　　　console.log("动画结束");
　　　　　　　　　　// 清空画布
　　　　　　　　　　this.player.clear()
　　　　　　　　})

　　}
　　);
　　}
　　}
};
</script>
<style>
.bg{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-color: rgba(0, 0, 0, 0.2);
}

#svgaBox{
  position: fixed;
  width: 100%;
  //height: 100%;
  left: 0;
  top: 0;
}
</style>