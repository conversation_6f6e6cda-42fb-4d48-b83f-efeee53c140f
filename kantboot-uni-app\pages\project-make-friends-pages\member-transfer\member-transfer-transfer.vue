<template>
  <view>
    <view class="header" id="headerInMemberTransfer">
      <kt-nav-bar :title="$i18n.zhToGlobal('编辑转移会员')" :showBack="true"></kt-nav-bar>
    </view>
    <view class="container">
      <!-- 用户信息展示区 -->
      <view class="user-info-card" v-if="userInfo">
        <view class="user-avatar-container">
          <image class="user-avatar"
            :src="userInfo.fileIdOfAvatar ? $kt.file.visit(userInfo.fileIdOfAvatar) : $kt.file.byPath('kantboot/icon/default-avatar.png')"
            mode="aspectFill"></image>
        </view>
        <view class="user-info-row">
          <view class="info-label">{{ $i18n.zhToGlobal('用户名') }}</view>
          <view class="info-value">{{ userInfo.nickname || $i18n.zhToGlobal('用户') }}</view>
        </view>
        <view class="user-info-row">
          <view class="info-label">ID</view>
          <view class="info-value">{{ userInfo.id }}</view>
        </view>
        <view class="user-info-row" v-if="userInfo.phone">
          <view class="info-label">{{ $i18n.zhToGlobal('手机号') }}</view>
          <view class="info-value">{{ userInfo.phoneAreaCode }} {{ userInfo.phone }}</view>
        </view>
      </view>

      <!-- 转移会员列表 -->
      <view class="transfer-list-section">
        <view class="section-header">
          <text class="section-title">{{ $i18n.zhToGlobal('转移会员列表') }}</text>
          <view class="add-btn-container">
            <button @tap="showMemberSelector" class="add-btn">{{ $i18n.zhToGlobal('新增') }}</button>
          </view>
        </view>

        <view class="transfer-list">
          <view class="empty-tip" v-if="transferList.length === 0">
            {{ $i18n.zhToGlobal('暂无转移会员，请点击新增') }}
          </view>
          <view class="transfer-item" v-for="(item, index) in transferList" :key="index">
            <view class="member-info">
              <image class="member-avatar"
                :src="item.fileIdOfAvatar ? $kt.file.visit(item.fileIdOfAvatar) : '/static/default-avatar.png'"
                mode="aspectFill"></image>
              <view class="member-details">
                <view class="member-nickname">{{ item.nickname || $i18n.zhToGlobal('用户') }}</view>
                <view class="member-id">ID: {{ item.id }}</view>
              </view>
            </view>
            <view class="delete-btn" @click="removeMember(index)">
              <u-icon name="trash" color="#fa3534" size="28"></u-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 确定按钮 -->
      <view class="confirm-btn-area">
        <button class="confirm-btn" @click="confirmTransfer" :disabled="transferList.length === 0">
          {{ $i18n.zhToGlobal('确认转移') }}
        </button>
      </view>
    </view>

    <!-- 会员选择弹窗 -->
    <project-make-friends-transfer-popup ref="memberSelector"
      @confirm="onMembersSelected"></project-make-friends-transfer-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userId: '',
      userInfo: null,
      transferList: [],
      isLoading: false
    }
  },
  onLoad(options) {
    if (options.userId) {
      this.userId = options.userId;
      this.getUserInfo();
    } else {
      uni.showToast({
        title: this.$i18n.zhToGlobal('用户ID不存在'),
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      this.isLoading = true;
      this.$request.post("/user-account-web/userAccount/getById", {
        data: {
          id: this.userId
        }
      }).then(res => {
        if (res.state === 2000) {
          this.userInfo = res.data;
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('获取用户信息失败'),
            icon: 'none'
          });
        }
        this.isLoading = false;
      }).catch(err => {
        console.error(err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
        this.isLoading = false;
      });
    },

    // 显示会员选择弹窗
    showMemberSelector() {
      this.$refs.memberSelector.open(this.userInfo.id);
    },

    // 处理选择的会员
    onMembersSelected(selectedMembers) {
      if (selectedMembers && selectedMembers.length > 0) {
        // 合并会员列表，去重
        const newList = [...this.transferList];
        selectedMembers.forEach(member => {
          const exists = newList.some(item => item.id === member.id);
          if (!exists) {
            newList.push(member);
          }
        });
        this.transferList = newList;
      }
    },

    // 移除会员
    removeMember(index) {
      uni.showModal({
        title: this.$i18n.zhToGlobal('提示'),
        content: this.$i18n.zhToGlobal('确定要移除该会员吗？'),
        success: (res) => {
          if (res.confirm) {
            this.transferList.splice(index, 1);
          }
        }
      });
    },

    // 确认转移
    confirmTransfer() {
      if (this.transferList.length === 0) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('请先添加转移会员'),
          icon: 'none'
        });
        return;
      }
      uni.showLoading({
        title: this.$i18n.zhToGlobal('转移中...'),
        mask: true
      });

      this.isLoading = true;
      const memberIds = this.transferList.map(item => item.id);

      this.$request.post("/project-make-friends-web/userTransferConsent/transfer", {
        data: {
          fromUserId: this.userId,
          toUserIds: memberIds
        }
      }).then(res => {
        if (res.state === 2000) {
          uni.showToast({
            title: this.$i18n.zhToGlobal('转移成功'),
            icon: 'success'
          });
          setTimeout(() => {
            uni.navigateBack({
              delta: 2
            });
          }, 500);
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('转移失败'),
            icon: 'none'
          });
        }
        this.isLoading = false;
        uni.hideLoading();
      }).catch(err => {
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
        this.isLoading = false;
        uni.hideLoading();
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 30rpx;
}

.user-info-card {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .user-avatar-container {
    display: flex;
    justify-content: center;
    margin-bottom: 30rpx;

    .user-avatar {
      width: 150rpx;
      height: 150rpx;
      border-radius: 50%;
    }
  }

  .user-info-row {
    display: flex;
    margin-bottom: 15rpx;

    .info-label {
      width: 300rpx;
      font-size: 28rpx;
      color: #666;
    }

    .info-value {
      flex: 1;
      font-size: 28rpx;
      font-weight: bold;
    }
  }
}

.transfer-list-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
    }

    .add-btn-container {
      .add-btn {
        background-color: #000000;
        height: 60rpx;
        line-height: 60rpx;
        color: #ffffff;
        font-size: 28rpx;

        &[disabled] {
          background-color: #cccccc;
          color: #ffffff;
          opacity: 0.6;
        }
      }
    }
  }

  .transfer-list {
    .empty-tip {
      text-align: center;
      color: #999;
      font-size: 28rpx;
      padding: 40rpx 0;
    }

    .transfer-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .member-info {
        display: flex;
        align-items: center;
        flex: 1;

        .member-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .member-details {
          .member-nickname {
            font-size: 30rpx;
            margin-bottom: 6rpx;
          }

          .member-id {
            font-size: 26rpx;
            color: #666;
          }
        }
      }

      .delete-btn {
        padding: 10rpx;
      }
    }
  }
}

.confirm-btn-area {
  padding: 30rpx 0;

  .confirm-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background-color: #000000;
    color: #ffffff;
    font-size: 32rpx;
    border-radius: 44rpx;

    &[disabled] {
      background-color: #cccccc;
    }
  }
}
</style>