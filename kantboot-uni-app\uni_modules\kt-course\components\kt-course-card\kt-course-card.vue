<template>
  <view
      @click="$emit('click')"
      :class="clazz.card">
    <image class="cover" :src="$kt.file.visit(info.fileIdOfCover)" mode="aspectFill"></image>
    <view class="content">
      <view class="title">{{ info.title }}</view>
      <view class="description">{{ info.description }}</view>

      <view class="price"
      v-if="info.price">
        <text
        :class="{
          'price-old': info.isLineThroughPrice
        }"
        >{{"￥"}}{{ info.price }}</text>
      </view>

    </view>
  </view>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {
          title: '一起来学习吧',
          description: '这是一个课程卡片',
          fileIdOfCover: '666896986198021',
          // 价格是否中划线
          isLineThroughPrice: false,
        }
      }
    },
  },
  data() {
    return {
      clazz:{
        card: this.$kt.style.toggleClass("card"),
      }
    }
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.card {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  background-color: #FFFFFF;
}

.cover {
  width: 100%;
  height: 200rpx;
}

.content {
  padding: 15rpx;
}

.title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8px;
  // 不换行
  white-space: nowrap;
  // 超出省略号
  overflow: hidden;
  text-overflow: ellipsis;
}

.description {
  font-size: 24rpx;
  color: #666;
  height: 80rpx;
  // 最多两行
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.price {
  text-align: right;
  font-size: 28rpx;
  font-weight: bold;
  color: #FF0000;
  .price-old{
    // 中划线
    text-decoration: line-through;
    opacity: .5;
  }
}

.card-mode-color-scheme-dark {
  background-color: rgba(0,0,0,0);
  .content {
    .title {
      color: #e9e9e9;
    }
    .description {
      color: #b9b9b9;
    }
    background-color: #212932;
    .price{
      .price-old{
        color: #b9b9b9;
        opacity: 1;
      }
      color: #fef0f0;
    }
  }
}
</style>
