<template>
  <u-popup :show="show" mode="bottom" :bgColor="'rgba(0,0,0,0)'" @close="close">
    <view class="popup">
      <view class="popup-title">{{$i18n.zhToGlobal('筛选条件')}}</view>
      <scroll-view style="max-height: 70vh;" scroll-y class="filter-scroll">
        <!-- 性别选择 -->
        <view class="filter-section">
          <view class="filter-label">{{$i18n.zhToGlobal('性别')}}</view>
          <view class="gender-box">
            <view class="gender-item" :class="{'gender-item-selected':genderCode=='male'}" @click="genderCode='male'">{{$i18n.zhToGlobal('男')}}</view>
            <view class="gender-item" :class="{'gender-item-selected':genderCode=='female'}" @click="genderCode='female'">{{$i18n.zhToGlobal('女')}}</view>
            <view class="gender-item" :class="{'gender-item-selected':genderCode==''}" @click="genderCode=''">{{$i18n.zhToGlobal('不限')}}</view>
          </view>
        </view>
        <!-- 职业选择 -->
        <view class="filter-section">
          <view class="filter-label">{{$i18n.zhToGlobal('职业')}}</view>
          <view class="filter-value" @click="openJobTypePopup">
            {{ jobTypeNames.length ? jobTypeNames.join(', ') : $i18n.zhToGlobal('请选择') }}
          </view>
          <job-multi-type-popup ref="jobMultiTypePopup" @select="onJobTypeSelect" />
        </view>
        <!-- 常住地选择 -->
        <view class="filter-section">
          <view class="filter-label">{{$i18n.zhToGlobal('常住地')}}</view>
          <view class="filter-value" @click="openAreaPopup">{{ areaName || $i18n.zhToGlobal('请选择') }}</view>
        </view>
        <!-- 年龄区间选择 -->
        <view class="filter-section">
          <view class="filter-label">{{$i18n.zhToGlobal('年龄区间')}}</view>
          <view class="filter-value">
            <SliderRange
              v-model="ageRange"
              :min="minAge"
              :max="maxAge"
              :step="1"
              :barHeight="5"
              :blockSize="24"
              :backgroundColor="'#e9e9e9'"
              :activeColor="'#1aad19'"
              :blockColor="'#fff'"
              @change="onSliderRangeChange"
            />
            <view class="age-range-text">{{ ageRange[0] }} - {{ ageRange[1] }}</view>
          </view>
        </view>
        <!-- 爱好多选 -->
        <view class="filter-section">
          <view class="filter-label">{{$i18n.zhToGlobal('兴趣爱好')}}</view>
          <view class="filter-value">
            <view class="tag-list">
              <view v-for="item in interestsList" :key="item.id" class="tag-item" :class="{selected: interestsSelected.includes(item.id)}" @click="toggleInterest(item.id)">
                {{item.name}}
              </view>
            </view>
          </view>
        </view>
        <!-- 特点多选 -->
        <view class="filter-section">
          <view class="filter-label">{{$i18n.zhToGlobal('个人特点')}}</view>
          <view class="filter-value">
            <view class="tag-list">
              <view v-for="item in characteristicsList" :key="item.id" class="tag-item" :class="{selected: characteristicsSelected.includes(item.id)}" @click="toggleCharacteristic(item.id)">
                {{item.name}}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
      <view class="popup-actions">
        <kt-button type="primary" @click="onConfirm">{{$i18n.zhToGlobal('确定')}}</kt-button>
      </view>
      <!-- 常住地弹窗 -->
      <kt-state-area-address-select-popup ref="areaPopup" @select="onAreaSelect" />
    </view>
  </u-popup>
</template>
<script>
import makeFriends from '@/uni_modules/project-make-friends/libs/load';
import SliderRange from './SliderRange.vue';
import JobTypePopup from './project-make-friends-user-info-panel/components/job-type-popup.vue';
import JobMultiTypePopup from './project-make-friends-user-info-panel/components/job-multi-type-popup.vue';
export default {
  components: { SliderRange, JobTypePopup,JobMultiTypePopup },
  data() {
    return {
      show: false,
      areaCode: '',
      areaName: '',
      minAge: 18,
      maxAge: 60,
      ageRange: [18, 35],
      genderCode: '',
      jobTypeIds: [],
      jobTypeNames: [],
      interestsList: [],
      interestsSelected: [],
      characteristicsList: [],
      characteristicsSelected: [],
    };
  },
  mounted() {
    this.interestsList = makeFriends.getInterests();
    this.characteristicsList = makeFriends.getCharacteristics();
  },
  methods: {
    openJobTypePopup() {
      this.$refs.jobMultiTypePopup.open(this.jobTypeIds);
    },
    onJobTypeSelect(list) {
      this.jobTypeIds = list.map(item => item.id);
      this.jobTypeNames = list.map(item => item.name);
    },
    open(params = {}) {
      this.show = true;
      this.areaCode = params.birthStateAreaAddressCode || '';
      this.areaName = params.areaName || '';
      this.ageRange = params.ageRange || [18, 35];
      this.genderCode = params.genderCode || '';
      this.jobTypeIds = params.jobTypeIds || [];
      this.jobTypeNames = params.jobTypeNames || [];
      this.interestsSelected = params.interests || [];
      this.characteristicsSelected = params.characteristics || [];
    },
    close() {
      this.show = false;
    },
    openAreaPopup() {
      this.$refs.areaPopup.open();
    },
    onAreaSelect(item) {
      this.areaCode = item.fullCode;
      this.areaName = (item.parent && item.parent.parent ? item.parent.parent.name + '/' : '') + (item.parent ? item.parent.name + '/' : '') + (item.name || '');
    },
    toggleInterest(id) {
      const idx = this.interestsSelected.indexOf(id);
      if (idx > -1) this.interestsSelected.splice(idx, 1);
      else this.interestsSelected.push(id);
    },
    toggleCharacteristic(id) {
      const idx = this.characteristicsSelected.indexOf(id);
      if (idx > -1) this.characteristicsSelected.splice(idx, 1);
      else this.characteristicsSelected.push(id);
    },
    onSliderRangeChange(val) {
      this.ageRange = val;
    },
    onConfirm() {
      this.show = false;
      this.$emit('confirm', {
        birthStateAreaAddressCode: this.areaCode,
        ageRange: this.ageRange,
        genderCode: this.genderCode,
        jobTypeIds: this.jobTypeIds,
        jobTypeNames: this.jobTypeNames,
        interests: this.interestsSelected,
        characteristics: this.characteristicsSelected,
        areaName: this.areaName,
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.popup {
  padding: 20rpx 40rpx 20rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
}
.popup-title {
  padding: 20rpx;
  font-size: 34rpx;
  font-weight: bold;
  text-align: left;
  letter-spacing: 2rpx;
}
.filter-section {
  margin-bottom: 30rpx;
}
.filter-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.filter-value {
  min-height: 40rpx;
  font-size: 28rpx;
  color: #333;
}
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}
.tag-item {
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 10rpx 24rpx;
  color: #333;
  font-size: 26rpx;
  margin-bottom: 10rpx;
  cursor: pointer;
}
.tag-item.selected {
  background: #333;
  color: #fff;
}
.age-range-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #888;
}
.popup-actions {
  margin-top: 20rpx;
  text-align: center;
}
.gender-box {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  margin-top: 10rpx;
}
.gender-item {
  font-size: 28rpx;
  color: #333;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  cursor: pointer;
}
.gender-item-selected {
  background: #333;
  color: #fff;
}
</style>
