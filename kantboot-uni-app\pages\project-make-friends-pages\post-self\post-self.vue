<template>
  <view>
    <view id="headerInPostSelf">
      <project-acm-nav-bar
          :is-has-i18n="false"
          :title="$kt.i18n.zhToGlobal('我的朋友圈')">
      </project-acm-nav-bar>
    </view>

    <view class="bg"></view>
    <kt-community-post-panel
        :height="'calc(100vh - '+headerHeight+'px)'"
        @cardClick="cardClick"
        @cardDotClick="cardDotClick"
        is-self
        show-audit-status
    >
    </kt-community-post-panel>

    <kt-community-post-operate-popup
        :remove-text="$kt.i18n.zhToGlobal('删除')"
        :report-text="$kt.i18n.zhToGlobal('举报')"
        :has-permission-setting="false"
        ref="communityPostOperatePopup">
    </kt-community-post-operate-popup>

  </view>
</template>

<script>
export default {
  data() {
    return {
      headerHeight: 0,
    };
  },
  mounted() {
    // 获取header的高度
    uni.createSelectorQuery().in(this).select('#headerInPostSelf').boundingClientRect((rect) => {
      console.log(rect,"获取headerHeight的高度")
      this.headerHeight = rect.height;
    }).exec()
  },
  methods: {
    cardDotClick(post){
      if(!this.$kt.userAccount.getIsLogin()){
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.$refs.communityPostOperatePopup.open(post);
    },
    cardClick(post){
      // 审核必须通过才能查看
      if(post.auditStatus !== "pass"){
        return;
      }
      this.$kt.router.navTo("/pages/project-acm/post-detail/post-detail",{
        postId: post.id
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.bg{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  // 渐变
  background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}
</style>
