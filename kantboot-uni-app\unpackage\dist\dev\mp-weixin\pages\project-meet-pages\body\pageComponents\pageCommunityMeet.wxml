<view class="{{['data-v-23cefd60',clazz.container]}}"><view class="{{['data-v-23cefd60',clazz.bg]}}"><block wx:if="{{$root.g0==='mobile'}}"><image class="bg-image data-v-23cefd60" mode="aspectFill" src="{{$root.g1}}"></image></block><block wx:if="{{$root.g2==='mobile'}}"><image class="bg-image-2 data-v-23cefd60" mode="aspectFill" src="{{$root.g3}}"></image></block><block wx:if="{{$root.g4==='mobile'}}"><image class="bg-image-3 data-v-23cefd60" mode="aspectFill" src="{{$root.g5}}"></image></block><view class="bg-bg data-v-23cefd60"></view></view><view style="width:100%;top:0;left:0;" id="pageHomeHeader" class="data-v-23cefd60"><view class="data-v-23cefd60"><kt-status-bar-height vue-id="56112fa6-1" background-color="rgba(0,0,0,0)" class="data-v-23cefd60" bind:__l="__l"></kt-status-bar-height></view><view class="data-v-23cefd60"><view class="split data-v-23cefd60"></view><view class="second-box data-v-23cefd60" style="position:relative;padding:11rpx;"><view class="second-box-box data-v-23cefd60"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['second-box-item','data-v-23cefd60',(selected==='friend')?'second-box-item-selected':'']}}" bindtap="__e">{{$root.g6+''}}</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['second-box-item','data-v-23cefd60',(selected==='privateFriend')?'second-box-item-selected':'']}}" bindtap="__e">{{$root.g7+''}}</view></view><block wx:if="{{false}}"><image class="second-icon data-v-23cefd60" style="width:40rpx;height:40rpx;position:absolute;top:50%;transform:translateY(-50%);right:50rpx;" src="{{$root.g8}}"></image></block></view></view></view><view class="community-post-box data-v-23cefd60"><swiper style="{{'height:'+($root.m0?'calc(100vh - '+pageHomeHeaderHeight+'px - 50px)':'calc(100vh - '+pageHomeHeaderHeight+'px - 100rpx)')+';'}}" current-item-id="{{selected}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e" class="data-v-23cefd60"><swiper-item item-id="friend" class="data-v-23cefd60"><project-meet-post-panel vue-id="56112fa6-2" isForbidForward="{{false}}" is-forbid-collect="{{false}}" card-background="{{cardBackground}}" height="{{$root.m1?'calc(100vh - '+pageHomeHeaderHeight+'px - 50px)':'calc(100vh - '+pageHomeHeaderHeight+'px - 100rpx)'}}" data-ref="communityPostPanel" data-event-opts="{{[['^cardUserClick',[['toUserInfoPage']]],['^cardClick',[['cardClick']]],['^cardDotClick',[['cardDotClick']]]]}}" bind:cardUserClick="__e" bind:cardClick="__e" bind:cardDotClick="__e" class="data-v-23cefd60 vue-ref" bind:__l="__l"></project-meet-post-panel></swiper-item><swiper-item item-id="privateFriend" class="data-v-23cefd60"><project-meet-post-panel vue-id="56112fa6-3" mode="follow" card-background="{{cardBackground}}" height="{{$root.m2?'calc(100vh - '+pageHomeHeaderHeight+'px - 50px)':'calc(100vh - '+pageHomeHeaderHeight+'px - 100rpx)'}}" data-ref="communityPostPanel" data-event-opts="{{[['^cardUserClick',[['toUserInfoPage']]],['^cardClick',[['cardClick']]],['^cardDotClick',[['cardDotClick']]]]}}" bind:cardUserClick="__e" bind:cardClick="__e" bind:cardDotClick="__e" class="data-v-23cefd60 vue-ref" bind:__l="__l"></project-meet-post-panel></swiper-item></swiper><block wx:if="{{$root.g9==='mobile'}}"><view data-event-opts="{{[['tap',[['toPush',['$event']]]]]}}" class="push-box data-v-23cefd60" bindtap="__e">{{''+$root.g10+''}}</view></block><kt-community-post-operate-popup vue-id="56112fa6-4" data-ref="communityPostOperatePopup" class="data-v-23cefd60 vue-ref" bind:__l="__l"></kt-community-post-operate-popup></view><community-post-popup vue-id="56112fa6-5" data-ref="communityPostPopup" class="data-v-23cefd60 vue-ref" bind:__l="__l"></community-post-popup><project-meet-post-detail-popup vue-id="56112fa6-6" height="calc(100vh - 500rpx)" data-ref="ktCommunityPostDetailPopup" class="data-v-23cefd60 vue-ref" bind:__l="__l"></project-meet-post-detail-popup><project-meet-user-info-popup vue-id="56112fa6-7" data-ref="usPopup" class="data-v-23cefd60 vue-ref" bind:__l="__l"></project-meet-user-info-popup></view>