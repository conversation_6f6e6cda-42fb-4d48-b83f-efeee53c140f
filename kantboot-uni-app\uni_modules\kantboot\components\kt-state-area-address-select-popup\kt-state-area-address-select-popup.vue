<template>
  <view>
    <kt-popup
    ref="ktPopup"
    >
      <view :class="clazz.box">
        <kt-state-area-address-select-panel
            ref="ktStateAreaAddressSelectPanel"
            @select="select"
        ></kt-state-area-address-select-panel>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz: {
        box: this.$kt.style.toggleClass("box"),
      }
    };
  },
  methods: {
    open() {
      this.$refs.ktPopup.open();
    },
    close() {
      this.$refs.ktPopup.close();
    },
    select(item) {
      this.$emit('select', item);
      this.close();
    }

  },
}
</script>

<style lang="scss" scoped>
.box {
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 20rpx 20rpx 20rpx;
}

.box-mode-device-pc{
  border-radius: 20rpx;
}
</style>
