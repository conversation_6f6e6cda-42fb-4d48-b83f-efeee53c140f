<template>
  <kt-popup ref="ktPopup">
    <view :class="clazz.box">
      <view class="title">
        <image
            class="title-icon"
            :src="$kt.file.byPath('kantboot/icon/language.svg')"></image>
        {{$i18n.zhToGlobal("语言选择")}}
        <view class="top-left">
          <slot name="topLeft"></slot>
        </view>
      </view>
      <view
          class="scroll-view"
          :style="{
      'height':'calc(100vh - '+'500rpx)'
    }">
        <kt-language-select-panel
            @select="select"
            :reset="reset">
        </kt-language-select-panel>
      </view>

    </view>
  </kt-popup>
</template>

<script>
export default {
  props: {
    reset: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass("box")
      }
    };
  },
  mounted() {
    // #ifdef H5
    // document.getElementsByClassName("top_header")[0].style.display = "none"
    // #endif
  },
  methods: {
    open() {
      this.$refs.ktPopup.open();
    },
    close() {
      this.$refs.ktPopup.close();
    },
    select(language) {
      this.$emit("select", language);
    }
  }
}
</script>

<style lang="scss" scoped>
.box{
  position: relative;
  padding: 20rpx 0 40rpx 0;
  // 超出不显示
  overflow: hidden;
  text-align: left;
  border-radius: 30rpx 30rpx  0 0;

  .title{
    .title-icon{
      width: 50rpx;
      height: 50rpx;
      vertical-align: top;
      margin-right: 10rpx;
    }
    padding:  10rpx 20rpx 15rpx 20rpx;
    border-radius: 30rpx;

  }
}

.box-mode-device-pc{
  .title{
  }
  border-radius: 20rpx;
}

.box-mode-color-scheme-light{
  background-color: #FFFFFF;
  .title{
    color: #333333;
  }
}

.box-mode-color-scheme-dark{
  background-color: #191919;
  .title{
    color: #FFFFFF;
  }
}

// 滚动条不显示
.scroll-view{
  // 滚动条不显示
  -webkit-overflow-scrolling: touch;
  overflow: hidden;
  -webkit-overflow-scrolling: auto;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }
}

.top-left{
  position: absolute;
  top: 30rpx;
  right: 20rpx;
}
</style>
