<template>
  <view>
    <kt-popup
        @close="close"
        :zIndex="zIndex"
        ref="ktPopup">
      <view
      :class="clazz.box"
      >
        <project-meet-register-panel
            @registerSuccess="registerSuccess"
            :border-radius="isPc()?'20rpx':'20rpx 20rpx 0 0'"
        ></project-meet-register-panel>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  name: "kt-login-popup",
  components: {
  },
  props:{
    zIndex:{
      type: Number,
      default: 999999999
    },
  },
  data(){
    return {
      clazz:{
        box : this.$kt.style.toggleClass("box"),
      }
    }
  },
  mounted(){
    this.$kt.event.on("login:success",()=>{
      this.close();
    });
  },
  methods:{
    isPc(){
      return this.$kt.style.detectDeviceType() === "pc";
    },
    registerSuccess(){
      this.$emit('registerSuccess');
      this.close();
    },
    open(){
      this.$refs.ktPopup.open();
    },
    close(){
      this.$refs.ktPopup.close();
      this.$emit('close');
    },

  },
}
</script>

<style lang="scss" scoped>
.box-mode-device-pc{
  position: fixed;
  width: 500px;
  top: 50%;
  left:50%;
   transform: translate(-50%,-50%);
}
</style>