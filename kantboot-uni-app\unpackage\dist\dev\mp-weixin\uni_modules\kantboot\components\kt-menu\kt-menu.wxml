<view data-event-opts="{{[['tap',[['toClick']]]]}}" class="{{['data-v-97fec1ac',clazz.inBox]}}" bindtap="__e"><view class="in-box-item data-v-97fec1ac"><view class="in-box-item-box data-v-97fec1ac"><image class="in-box-item-icon data-v-97fec1ac" src="{{icon}}"></image><view class="in-box-item-text data-v-97fec1ac">{{title}}</view></view><view class="in-box-item-content data-v-97fec1ac">{{''+content+''}}<block wx:if="{{isRight}}"><image class="in-box-item-arrow-icon data-v-97fec1ac" src="{{$root.g0}}"></image></block><block wx:if="{{isSwitch}}"><kt-switch bind:input="__e" vue-id="67d871c8-1" value="{{switchValue}}" data-event-opts="{{[['^input',[['__set_model',['','switchValue','$event',[]]]]]]}}" class="data-v-97fec1ac" bind:__l="__l"></kt-switch></block></view></view></view>