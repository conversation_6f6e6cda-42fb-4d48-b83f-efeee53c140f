<view class="data-v-1e2f03e9"><scroll-view class="scroll-view data-v-1e2f03e9" style="{{'height:'+(height)+';'}}" scroll-y="{{true}}" refresher-triggered="{{refresherTriggered}}" refresher-enabled="{{true}}" show-scrollbar="{{false}}" lower-threshold="{{$root.g0}}" data-event-opts="{{[['refresherrefresh',[['onRefresherrefresh']]],['scrolltolower',[['getAlert']]]]}}" bindrefresherrefresh="__e" bindscrolltolower="__e"><view class="box data-v-1e2f03e9"><block wx:if="{{$root.g1}}"><view class="no-data data-v-1e2f03e9"><view class="data-v-1e2f03e9"><image class="no-data-image data-v-1e2f03e9" src="{{$root.g2}}"></image></view><view class="no-data-text data-v-1e2f03e9">{{$root.g3+''}}</view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['cardClick',['$0'],[[['list','',__i0__]]]]]]]}}" catchtap="__e" class="data-v-1e2f03e9"><kt-community-post-card vue-id="{{'b09c0b86-1-'+__i0__}}" is-forbid-forward="{{false}}" is-forbid-collect="{{false}}" background="{{cardBackground}}" show-audit-status="{{showAuditStatus}}" post="{{item.$orig}}" has-bottom-operation="{{hasCardBottomOperation}}" data-event-opts="{{[['^dotClick',[['cardDotCLick']]],['^userClick',[['cardUserClick']]]]}}" bind:dotClick="__e" bind:userClick="__e" class="data-v-1e2f03e9" bind:__l="__l" vue-slots="{{['bottom']}}"><view slot="bottom"><slot name="cardBottom"></slot><scoped-slots-cardBottom postId="{{item.$orig.id}}" post="{{item.$orig}}" class="scoped-ref" bind:__l="__l"></scoped-slots-cardBottom></view></kt-community-post-card><view style="height:40rpx;" class="data-v-1e2f03e9"></view></view></block><block wx:if="{{loading}}"><u-loading-icon vue-id="b09c0b86-2" mode="circle" size="60" class="data-v-1e2f03e9" bind:__l="__l"></u-loading-icon></block><block wx:if="{{noMore}}"><view style="text-align:center;color:#999999;font-size:24rpx;" class="data-v-1e2f03e9">{{''+$root.g4+''}}</view></block><view style="height:80rpx;" class="data-v-1e2f03e9"></view></view></scroll-view></view>