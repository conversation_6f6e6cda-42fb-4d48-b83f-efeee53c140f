<template>
  <view>
    <kt-popup ref="ktPopup">
      <view
          :class="clazz.box"
      >
        <view
            class="my-gold"
        >
          {{ $i18n.zhToGlobal("我的金币") }}{{ ": " }}
          <text class="my-gold-text">{{ self.projectMeetGold || '0' }}</text>
        </view>
        <view class="title">
          <image
              class="title-icon"
              :src="$kt.file.byPath('projectMeet/icon/gold.png')"
          ></image>
          {{ $i18n.zhToGlobal("金币转赠") }}
        </view>
        <view>
          <view style="height: 30rpx"></view>

          <view class="input-box">
            <input
                v-model="requestParams.number"
                type="text"
                id="goldTransferInput"
                ref="intInput"
                pattern="\d*"
                class="input-input"
                @input="input"
                :placeholder="$i18n.zhToGlobal('输入金币数量')"
            ></input>
          </view>
          <view style="height: 30rpx"></view>
          <view>
            <kt-button
                ref="ktButton"
                @click="toTransfer()"
            >{{ $i18n.zhToGlobal("确定") }}
            </kt-button>
          </view>
          <view style="height: 30rpx"></view>
          <view style="text-align: center">
            <view
                @click="toRecharge"
                class="recharge-btn">{{$i18n.zhToGlobal("充值金币")}}</view>
          </view>
          <view style="height: 30rpx"></view>
        </view>
      </view>
    </kt-popup>

    <project-meet-gold-popup
        v-if="self.id"
        @paySuccess="getSelf"
        :user-account-id="self.id"
        ref="projectMeetGoldPopup"
    ></project-meet-gold-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz: {
        box: this.$kt.style.toggleClass("box"),
      },
      requestParams: {
        userAccountIdOfGetter: "",
        projectMeetOrderId: "",
        number: ""
      },
      self: {
        id:"",
        projectMeetGold: "0"
      },

    };
  },
  mounted() {
    // this.open();
  },
  watch: {},
  methods: {
    toRecharge() {
      this.$refs.projectMeetGoldPopup.open();
    },
    createOrder() {
      setTimeout(()=>{
        this.$refs.ktButton.loading(null,999999);
      },1);
      this.$request.post("/project-meet-web/goldRecharge/generateTransferOrderId", {
        data: {
          userAccountIdOfGetter: this.requestParams.userAccountIdOfGetter,
        },
      }).then((res) => {
        this.requestParams.projectMeetOrderId = res.data;
        this.$refs.ktButton.toNone();
      });
    },
    input(e) {
      this.requestParams.number = e.detail.value;
      if (!this.requestParams.number) {
        return;
      }
      this.requestParams.number = parseInt(this.requestParams.number);
      try {
        this.$forceUpdate();
      } catch (e) {

      }
    },
    getSelf() {
      this.$kt.userAccount.requestSelf().then((res) => {
        this.self.id = res.data.id;
        this.self.projectMeetGold = res.data.projectMeetGold;
        try {
          this.$forceUpdate();
        } catch (e) {
        }
      });
    },
    open(userAccountIdOfGetter) {
      this.requestParams.userAccountIdOfGetter = userAccountIdOfGetter;
      this.getSelf();
      this.createOrder();
      this.$refs.ktPopup.open();
    },
    close() {
      this.$refs.ktPopup.close();
    },
    toTransfer() {
      if (!this.requestParams.number) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("未输入金额"));
        return;
      }
      // 如果小于0
      if (this.requestParams.number < 0) {
        this.$refs.ktButton.error(this.$i18n.zhToGlobal("金额格式不正确"));
        return;
      }
      if (this.self.projectMeetGold < this.requestParams.number) {
        this.$refs.ktButton.error("余额不足");
        return;
      }
      this.$refs.ktButton.loading(null, 999999);
      // /project-meet-web/goldRecharge/transfer
      this.$request.post("/project-meet-web/goldRecharge/transfer",{
        data:{
          projectMeetOrderId: this.requestParams.projectMeetOrderId,
          number: this.requestParams.number,
        }
      }).then((res)=>{
        this.getSelf();
        this.close();
        this.$refs.ktButton.success(this.$i18n.zhToGlobal("转赠成功"));
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.box {
  position: relative;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;

  .title {
    font-size: 32rpx;
    font-weight: bold;

    .title-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .input-box {
    .input-input {
      width: 100%;
      height: 100rpx;
      border-radius: 20rpx;
      border: 1px solid #E5E5E5;
      padding-left: 20rpx;
      box-sizing: border-box;
      text-align: center;
    }
  }

  .my-gold {
    position: absolute;
    top: 24rpx;
    right: 10rpx;
    font-size: 26rpx;
  }
}

.box-mode-device-pc {
  border-radius: 20rpx;
}

.recharge-btn{
  display: inline-block;
  font-size: 26rpx;
}

.recharge-btn:active{
  opacity: .8;
}

</style>
