<template>
  <view>

    <kt-box :title="$i18n.zhToGlobal('举报选项')">

      <view class="option-box">
        <view style="text-align: center">
          <u-loading-icon
              style="display: inline-block"
              v-if="optionsLoading"
              mode="circle"
              size="80"
          ></u-loading-icon>
        </view>
        <view
            v-for="(item, index) in options"
            class="option-item"
            @click="optionIdSelected = item.id"
            :class="{
              'option-item-active': optionIdSelected === item.id,
            }"
        >
          {{item.name}}
        </view>
      </view>
    </kt-box>

    <kt-box :title="$i18n.zhToGlobal('举报描述')">
      <view class="textarea-box">
        <textarea
            class="textarea"
            v-model="textContent"
            :placeholder="$i18n.zhToGlobal('输入举报描述')"
        ></textarea>
      </view>
    </kt-box>

    <kt-box :title="$i18n.zhToGlobal('图片')">
      <view class="textarea-box">
        <kt-image-select
            v-model="imageContent"
            file-group-code="fp"></kt-image-select>
      </view>
    </kt-box>

    <kt-box
    v-show="hasConfirmButton"
    >
      <kt-button
          @click="report"
          ref="confirmButton"
      :is-open-box-shadow="false"
      >{{$i18n.zhToGlobal("确认举报")}}</kt-button>
    </kt-box>

  </view>
</template>

<script>
import api from "@/uni_modules/kt-community/libs/api";
export default {
  props:{
    postId:{
      type: String || Number,
      default: ""
    },
    /**
     * 是否显示确定按钮
     */
    hasConfirmButton: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      api,
      options:[],
      optionIdSelected:null,
      optionsLoading:false,
      textContent: "",
      imageContent:[]

    };
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      this.optionsLoading = true;
      await this.api.getPostReportOptions().then((res)=>{
        this.options = res.data;
      });
      this.optionsLoading = false;
    },
    report() {
      if (this.optionIdSelected === null) {
        this.$refs.confirmButton.error(this.$i18n.zhToGlobal("未选择举报选项"));
        this.$emit("reportFail", {
          errMsg: this.$i18n.zhToGlobal("未选择举报选项")
        });
        return;
      }
      this.$refs.confirmButton.loading(null, 99999);
      this.$emit("reportLoading");
      this.api.reportPost({
        postId: this.postId,
        postReportOptionId: this.optionIdSelected,
        textContent:JSON.stringify(this.textContent),
      }).then((res)=>{
        this.$refs.confirmButton.success(res.msg);
        this.$emit("reportSuccess", res);
      }).catch((err)=>{
        this.$refs.confirmButton.error(err.errMsg);
        this.$emit("reportFail", err);
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.option-box {
  padding: 20rpx;
  border-radius: 10rpx;
  // 1行3个
  //display: flex;
  //flex-wrap: wrap;
  //justify-content: space-between;
  //align-items: center;
  text-align: center;
  .option-item{
    display: inline-block;
    margin: 1%;
    width: 30%;
    text-align: center;
    font-size: 30rpx;
    padding: 15rpx;
    color: #333333;
    border: 1px solid #E5E5E5;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    box-sizing: border-box;
    cursor: pointer;
  }
  .option-item-active{
    background-color: #F0F0F0;
    border: 1px solid #f0f0f0;
  }
}

.textarea-box{
  border-radius: 10rpx;
  .textarea{
    width: 100%;
    height: 200rpx;
    border: 1px solid #E5E5E5;
    border-radius: 10rpx;
    padding: 20rpx;
    font-size: 30rpx;
    color: #333333;
    line-height: 50rpx;
    box-sizing: border-box;
  }
}
</style>
