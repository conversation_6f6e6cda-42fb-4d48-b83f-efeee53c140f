<view class="{{[clazz.box]}}" style="width:100%;"><view style="position:relative;"><swiper style="height:520rpx;"><swiper-item><block wx:if="{{userAccount.fileIdOfAvatar}}"><image style="width:100%;height:550rpx;" mode="aspectFill" src="{{$root.g0}}"></image></block><block wx:else><image style="width:100%;height:550rpx;" mode="aspectFill" src="{{$root.g1}}"></image></block></swiper-item><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__"><swiper-item><image style="width:100%;height:750rpx;" mode="aspectFill" src="{{item.g2}}"></image></swiper-item></block></swiper><block wx:if="{{$root.m0}}"><view class="online-box">{{$root.g3+" "+$root.m1+$root.g4+''}}</view></block><block wx:if="{{$root.m2}}"><view class="online-box offline-box">{{$root.g5+" "+$root.m3+$root.g6}}</view></block></view><view class="body"><view class="body_title"><view class="body_title_left"><view class="body_title_name">{{userAccount.nickname+''}}<block wx:if="{{isSvip}}"><view class="tag-v tag-v-svip">SVIP</view></block><block wx:if="{{ktPopup&&!isSvip}}"><view class="tag-v tag-v-vip">VIP</view></block></view><block wx:if="{{userAccount.genderCode==='male'}}"><view class="body_title_age body_title_age_male"><view style="margin-left:5rpx;">♂</view><view style="margin-right:5rpx;">{{$root.g7}}</view></view></block><block wx:if="{{userAccount.genderCode==='femele'}}"><view class="body_title_age body_title_age_female"><view style="margin-left:5rpx;">♀</view><view style="margin-right:5rpx;">{{$root.g8}}</view></view></block></view><block wx:if="{{userAccount.genderCode==='male'}}"><view data-event-opts="{{[['tap',[['openSetRemarkPopup',['$event']]]]]}}" class="body_title_right" bindtap="__e"><view>{{$root.g9}}</view><view>:</view><block wx:if="{{!remark}}"><view style="margin-left:5rpx;">{{$root.g10}}</view></block><block wx:if="{{remark}}"><view style="margin-left:5rpx;">{{remark}}</view></block></view></block></view><view class="body_info"><view style="padding-right:20rpx;padding-left:7rpx;"><block wx:if="{{userAccount.countryCn}}"><view>{{$root.g11}}</view></block></view></view><view class="body_adjective"><view class="body_adjective_title"><view class="body_adjective_icn"></view><view class="body_adjective_text">{{$root.g12+''}}</view></view><view class="body_adjective_body"><block wx:if="{{$root.g13}}"><view class="no-data-text">{{$root.g14+''}}</view></block><block wx:for="{{userAccount.interestsIds}}" wx:for-item="interest" wx:for-index="__i1__"><block wx:if="{{$root.g15}}"><view class="adjective_body_icon tag-v-v">{{''+(interestMap[interest]?interestMap[interest].name:"")+''}}</view></block></block></view></view><view class="body_adjective"><view class="body_adjective_title"><view class="body_adjective_icn"></view><view class="body_adjective_text">{{$root.g16}}</view></view><view class="body_adjective_body"><block wx:if="{{$root.g17}}"><view class="no-data-text">{{$root.g18+''}}</view></block><block wx:for="{{$root.l1}}" wx:for-item="expectedRelationship" wx:for-index="__i2__"><block wx:if="{{$root.g19}}"><view class="adjective_body_icon">{{''+(expectedRelationshipMap[expectedRelationship.$orig+""]?expectedRelationshipMap[expectedRelationship.$orig+""].name:expectedRelationship.g20)+''}}</view></block></block></view></view><view class="body_adjective"><view class="body_adjective_title"><view class="body_adjective_icn"></view><view class="body_adjective_text">{{$root.g21+''}}</view></view><view class="body_adjective_body"><block wx:if="{{userAccount.introduction}}"><view>{{''+userAccount.introduction+''}}</view></block><block wx:else><view class="no-data-text">{{''+$root.g22+''}}</view></block></view></view><view data-event-opts="{{[['tap',[['toUserPost',['$0'],['userAccountId']]]]]}}" class="body_adjective" bindtap="__e"><view class="body_adjective_title"><view class="body_adjective_icn"></view><view class="body_adjective_text">{{$root.g23+''}}</view><view style="flex:1;display:flex;justify-content:flex-end;padding-right:20rpx;"><image style="width:25rpx;" src="{{$root.g24}}" mode="widthFix" alt></image></view></view></view><view style="height:200rpx;"></view></view><view class="{{['foot_box',(bottomFixed)?'foot_box_fixed':'']}}"><view style="flex:0.5;display:flex;align-items:center;justify-content:center;"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="foot_btn" bindtap="__e"><block wx:if="{{userAccount.genderCode==='male'}}"><text>{{$root.g25+''}}</text></block><block wx:if="{{userAccount.genderCode==='female'}}"><text>{{$root.g26+''}}</text></block></view></view><view style="flex:0.5;display:flex;align-items:center;justify-content:center;"><view data-event-opts="{{[['tap',[['toPrivateChat',['$event']]]]]}}" class="foot_btn" bindtap="__e">{{''+$root.g27+''}}</view></view></view><project-meet-vgp-popup class="vue-ref" vue-id="a559a95e-1" user-account-id="{{userAccountId}}" data-ref="projectMeetVgpPopup" bind:__l="__l"></project-meet-vgp-popup><kt-set-remark-popup class="vue-ref" bind:confirm="__e" vue-id="a559a95e-2" data-ref="ktSetRemarkPopup" data-event-opts="{{[['^confirm',[['getRemark']]]]}}" bind:__l="__l"></kt-set-remark-popup><kt-user-post-popup class="vue-ref" vue-id="a559a95e-3" data-ref="ktUserPostPopup" bind:__l="__l"></kt-user-post-popup></view>