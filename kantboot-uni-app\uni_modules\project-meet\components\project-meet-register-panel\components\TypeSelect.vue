<template>
  <view
      v-show="false"
      :class="clazz.typeBox">
    <view
        class="type-box-btn"
        :class="{
            'type-box-btn-selected': param.typeCode === 'login'
          }"
        @click="selectType('login')">
      {{ $i18n.zhToGlobal("登录") }}
      <view
          v-if="param.typeCode === 'login'"
          class="type-box-line"></view>

    </view>
    <view
        class="type-box-btn"
        :class="{'type-box-btn-selected': param.typeCode === 'register'}"
        @click="selectType('register')">
      {{ $i18n.zhToGlobal("注册") }}
      <view
          v-if="param.typeCode === 'register'"
          class="type-box-line"></view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        typeBox: this.$kt.style.toggleClass("type-box"),
      },
      param: {
        typeCode: "login",
      }
    };
  },
  created() {
    this.$emit("change", this.param);
  },
  methods: {
    selectType(e) {
      this.param.typeCode = e;
      this.$emit("change", this.param);
    }
  }
}
</script>

<style lang="scss" scoped>

.type-box {
  margin-top: 10rpx;
  position: relative;
  margin-bottom: 10rpx;

  .type-box-btn {
    position: relative;
    display: inline-block;
    font-size: 28rpx;
    text-align: center;
    width: 50%;
    color: #999999;
    .type-box-line{
      position: absolute;
      width: 100rpx;
      height: 5rpx;
      background-color: #000000;
      bottom: -10rpx;
      border-radius: 10rpx;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .type-box-btn:active {
    opacity: .8;
  }

  .type-box-btn-selected {
    color: #000000;
  }

}

.type-box-mode-color-scheme-light {
  background-color: #fff;
  color: #000;
  .type-box-btn-selected {
    color: #000000;
  }
  .type-box-line {
    background-color: #000000;
  }
}

.type-box-mode-color-scheme-dark {
  color: #fff;
  .type-box-btn {
    color: #fff;
    .type-box-line {
      background-color: #fff;
    }
  }
  .type-box-btn-selected {
    color: #fff;
  }

}

</style>
