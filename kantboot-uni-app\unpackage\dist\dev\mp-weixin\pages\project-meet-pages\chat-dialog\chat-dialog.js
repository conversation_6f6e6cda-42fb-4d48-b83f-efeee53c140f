(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/project-meet-pages/chat-dialog/chat-dialog"],{

/***/ 343:
/*!********************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/main.js?{"page":"pages%2Fproject-meet-pages%2Fchat-dialog%2Fchat-dialog"} ***!
  \********************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _chatDialog = _interopRequireDefault(__webpack_require__(/*! ./pages/project-meet-pages/chat-dialog/chat-dialog.vue */ 344));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_chatDialog.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 344:
/*!***********************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/pages/project-meet-pages/chat-dialog/chat-dialog.vue ***!
  \***********************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _chat_dialog_vue_vue_type_template_id_006f6cc7_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chat-dialog.vue?vue&type=template&id=006f6cc7&scoped=true& */ 345);
/* harmony import */ var _chat_dialog_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chat-dialog.vue?vue&type=script&lang=js& */ 347);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _chat_dialog_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _chat_dialog_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _chat_dialog_vue_vue_type_style_index_0_id_006f6cc7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chat-dialog.vue?vue&type=style&index=0&id=006f6cc7&lang=scss&scoped=true& */ 349);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _chat_dialog_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _chat_dialog_vue_vue_type_template_id_006f6cc7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _chat_dialog_vue_vue_type_template_id_006f6cc7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "006f6cc7",
  null,
  false,
  _chat_dialog_vue_vue_type_template_id_006f6cc7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/project-meet-pages/chat-dialog/chat-dialog.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 345:
/*!******************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/pages/project-meet-pages/chat-dialog/chat-dialog.vue?vue&type=template&id=006f6cc7&scoped=true& ***!
  \******************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_template_id_006f6cc7_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat-dialog.vue?vue&type=template&id=006f6cc7&scoped=true& */ 346);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_template_id_006f6cc7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_template_id_006f6cc7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_template_id_006f6cc7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_template_id_006f6cc7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 346:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/pages/project-meet-pages/chat-dialog/chat-dialog.vue?vue&type=template&id=006f6cc7&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    ktNavBar: function () {
      return Promise.all(/*! import() | uni_modules/kantboot/components/kt-nav-bar/kt-nav-bar */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/kantboot/components/kt-nav-bar/kt-nav-bar")]).then(__webpack_require__.bind(null, /*! @/uni_modules/kantboot/components/kt-nav-bar/kt-nav-bar.vue */ 650))
    },
    ktChatDialogPanel: function () {
      return Promise.all(/*! import() | uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel")]).then(__webpack_require__.bind(null, /*! @/uni_modules/kantboot/components/kt-chat-dialog-panel/kt-chat-dialog-panel.vue */ 1112))
    },
    projectMeetGoldTransferCard: function () {
      return __webpack_require__.e(/*! import() | uni_modules/project-meet/components/project-meet-gold-transfer-card/project-meet-gold-transfer-card */ "uni_modules/project-meet/components/project-meet-gold-transfer-card/project-meet-gold-transfer-card").then(__webpack_require__.bind(null, /*! @/uni_modules/project-meet/components/project-meet-gold-transfer-card/project-meet-gold-transfer-card.vue */ 1119))
    },
    ktSendInput: function () {
      return Promise.all(/*! import() | uni_modules/kantboot/components/kt-send-input/kt-send-input */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/kantboot/components/kt-send-input/kt-send-input")]).then(__webpack_require__.bind(null, /*! @/uni_modules/kantboot/components/kt-send-input/kt-send-input.vue */ 781))
    },
    projectMeetVgpPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/project-meet/components/project-meet-vgp-popup/project-meet-vgp-popup */ "uni_modules/project-meet/components/project-meet-vgp-popup/project-meet-vgp-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/project-meet/components/project-meet-vgp-popup/project-meet-vgp-popup.vue */ 1126))
    },
    projectMeetGiftPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/project-meet/components/project-meet-gift-popup/project-meet-gift-popup */ "uni_modules/project-meet/components/project-meet-gift-popup/project-meet-gift-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/project-meet/components/project-meet-gift-popup/project-meet-gift-popup.vue */ 1016))
    },
    projectMeetGoldTransferPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/project-meet/components/project-meet-gold-transfer-popup/project-meet-gold-transfer-popup */ "uni_modules/project-meet/components/project-meet-gold-transfer-popup/project-meet-gold-transfer-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/project-meet/components/project-meet-gold-transfer-popup/project-meet-gold-transfer-popup.vue */ 1133))
    },
    projectMeetUserInfoPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/project-meet/components/project-meet-user-info-popup/project-meet-user-info-popup */ "uni_modules/project-meet/components/project-meet-user-info-popup/project-meet-user-info-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/project-meet/components/project-meet-user-info-popup/project-meet-user-info-popup.vue */ 1140))
    },
    projectMeetPointsPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/project-meet/components/project-meet-points-popup/project-meet-points-popup */ "uni_modules/project-meet/components/project-meet-points-popup/project-meet-points-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/project-meet/components/project-meet-points-popup/project-meet-points-popup.vue */ 1147))
    },
    projectMeetVipPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/project-meet/components/project-meet-vip-popup/project-meet-vip-popup */ "uni_modules/project-meet/components/project-meet-vip-popup/project-meet-vip-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/project-meet/components/project-meet-vip-popup/project-meet-vip-popup.vue */ 1154))
    },
    projectMeetGoldPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/project-meet/components/project-meet-gold-popup/project-meet-gold-popup */ "uni_modules/project-meet/components/project-meet-gold-popup/project-meet-gold-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/project-meet/components/project-meet-gold-popup/project-meet-gold-popup.vue */ 1161))
    },
    ktSetRemarkPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/kantboot/components/kt-set-remark-popup/kt-set-remark-popup */ "uni_modules/kantboot/components/kt-set-remark-popup/kt-set-remark-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/kantboot/components/kt-set-remark-popup/kt-set-remark-popup.vue */ 1168))
    },
    ktLanguageSelectPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/kantboot/components/kt-language-select-popup/kt-language-select-popup */ "uni_modules/kantboot/components/kt-language-select-popup/kt-language-select-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/kantboot/components/kt-language-select-popup/kt-language-select-popup.vue */ 706))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 =
    _vm.dialog.id &&
    _vm.dialog.userAccountId &&
    !_vm.isCustomerService &&
    _vm.userAccount.genderCode === "female"
      ? _vm.$i18n.zhToGlobal("为她充值")
      : null
  var g1 =
    _vm.dialog.id &&
    _vm.dialog.userAccountId &&
    !_vm.isCustomerService &&
    _vm.userAccount.genderCode === "male"
      ? _vm.$i18n.zhToGlobal("为他充值")
      : null
  var g2 =
    _vm.dialog.id &&
    _vm.dialog.userAccountId &&
    _vm.self.isSubAccount &&
    !_vm.isCustomerService
      ? _vm.$i18n.zhToGlobal("修改备注")
      : null
  var g3 =
    !_vm.isCustomerService && !_vm.self.isSubAccount
      ? _vm.$kt.file.byPath("projectMeet/icon/gold.png")
      : null
  var g4 = !_vm.isCustomerService
    ? _vm.$kt.file.byPath("kantboot/icon/language.svg")
    : null
  var g5 =
    !_vm.isCustomerService && !_vm.targetLanguage.code
      ? _vm.$i18n.zhToGlobal("不翻译")
      : null
  var g6 = _vm.$i18n.enToGlobal("Cancel translation")
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      return _vm.$refs.projectMeetVgpPopup.open()
    }
    _vm.e1 = function ($event) {
      return _vm.$refs.projectMeetVgpPopup.open()
    }
    _vm.e2 = function ($event) {
      return _vm.$refs.setRemarkPopup.open(_vm.dialog.userAccountId)
    }
    _vm.e3 = function ($event) {
      return _vm.$refs.ktLanguageSelectPopup.open()
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
        g5: g5,
        g6: g6,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 347:
/*!************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/pages/project-meet-pages/chat-dialog/chat-dialog.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat-dialog.vue?vue&type=script&lang=js& */ 348);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 348:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/pages/project-meet-pages/chat-dialog/chat-dialog.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 41));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 43));
var ProjectMeetGiftCard = function ProjectMeetGiftCard() {
  __webpack_require__.e(/*! require.ensure | pages/project-make-friends-pages/project-meet-gift-card/project-meet-gift-card */ "pages/project-make-friends-pages/project-meet-gift-card/project-meet-gift-card").then((function () {
    return resolve(__webpack_require__(/*! @/pages/project-make-friends-pages/project-meet-gift-card/project-meet-gift-card.vue */ 1175));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    ProjectMeetGiftCard: ProjectMeetGiftCard
  },
  data: function data() {
    return {
      title: this.$i18n.zhToGlobal("对话"),
      self: {
        isSubAccount: false
      },
      isCustomerService: false,
      dialogId: "",
      headerHeight: "",
      footerHeight: "",
      dialog: {},
      userAccount: {},
      targetLanguage: {
        code: ""
      },
      token: "",
      ifa: true,
      unreadCount: 0
    };
  },
  onLoad: function onLoad(options) {
    var _this = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              if (!_this.getUrlParameter("token")) {
                _context.next = 6;
                break;
              }
              _context.next = 3;
              return _this.$kt.userAccount.requestSelf();
            case 3:
              _this.self = _this.$kt.userAccount.getSelf();
              _this.ifa = false;
              setTimeout(function () {
                _this.ifa = true;
              }, 100);
            case 6:
              _this.dialogId = options.dialogId;
              _this.userAccountId = options.userAccountId;
              if (!_this.userAccountId) {
                _context.next = 11;
                break;
              }
              _context.next = 11;
              return _this.getChatByUserAccountId();
            case 11:
              _this.getById();

              // this.$refs.projectMeetGiftPopup.open();
            case 12:
            case "end":
              return _context.stop();
          }
        }
      }, _callee);
    }))();
  },
  onUnload: function onUnload() {
    var _this2 = this;
    this.ifa = false;
    setTimeout(function () {
      _this2.ifa = true;
    }, 10);
  },
  mounted: function mounted() {
    var _this3 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
      return _regenerator.default.wrap(function _callee2$(_context2) {
        while (1) {
          switch (_context2.prev = _context2.next) {
            case 0:
              _this3.$kt.event.on("FunctionalChatDialogMessage.handleRelationship", function (res) {
                // alert(JSON.stringify(res));
                _this3.unreadCount = res.userAccountRelationship.unreadCount;
                setTimeout(function () {
                  if (!_this3.unreadCount) {
                    _this3.title = _this3.$i18n.zhToGlobal("对话");

                    // // 获取现在的标题-uniapp
                    uni.setNavigationBarTitle({
                      title: _this3.$i18n.zhToGlobal("对话")
                    });
                  } else {
                    _this3.title = _this3.$i18n.zhToGlobal("新消息") + "（" + _this3.unreadCount + "）";

                    // // 获取现在的标题-uniapp
                    uni.setNavigationBarTitle({
                      title: _this3.$i18n.zhToGlobal("新消息") + "（" + _this3.unreadCount + "）"
                    });
                  }
                }, 1000);
                _this3.$kt.event.on("translate:balanceNotEnough", function () {
                  _this3.$refs.projectMeetPointsPopup.open(_this3.self.id);
                });
              });
              _context2.next = 3;
              return _this3.$kt.userAccount.requestSelf();
            case 3:
              _this3.self = _this3.$kt.userAccount.getSelf();

              // 获取navBarPostDetail的高度
              _this3.$nextTick(function () {
                uni.createSelectorQuery().select('#projectMeetPagesHeader').boundingClientRect(function (res) {
                  _this3.headerHeight = res.height;
                }).exec();
                uni.createSelectorQuery().select('#projectMeetPageFooter').boundingClientRect(function (res) {
                  _this3.footerHeight = res.height;
                }).exec();
              });
            case 5:
            case "end":
              return _context2.stop();
          }
        }
      }, _callee2);
    }))();
  },
  methods: {
    getUrlParameter: function getUrlParameter(name) {
      name = name.replace(/[\[\]]/g, "\\$&");
      var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
        results = regex.exec(window.location.href);
      if (!results) return null;
      if (!results[2]) return '';
      return decodeURIComponent(results[2].replace(/\+/g, " "));
    },
    selectLanguage: function selectLanguage(res) {
      this.targetLanguage = res;
      this.$refs.ktLanguageSelectPopup.close();
    },
    toUnTranslate: function toUnTranslate() {
      this.targetLanguage = {
        code: ""
      };
      this.$refs.ktLanguageSelectPopup.close();
    },
    getIsCustomerService: function getIsCustomerService() {
      var _this4 = this;
      this.$request.post("/functional-chat-web/dialog/isCustomerService", {
        data: {
          userAccountId: this.dialog.userAccountId
        }
      }).then(function (res) {
        _this4.isCustomerService = res.data;
      }).catch(function (res) {
        _this4.isCustomerService = false;
      });
    },
    userCardClick: function userCardClick(userAccount) {
      if (this.$kt.style.detectDeviceType() === 'pc') {
        this.$refs.usPopup.open(userAccount.id);
        // this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info");
      } else {
        this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info?userAccountId=" + userAccount.id);
      }
    },
    openGoldPopup: function openGoldPopup() {
      this.$refs.projectMeetGoldTransferPopup.open(this.userAccount.id);
    },
    chooseGift: function chooseGift() {
      this.$refs.projectMeetGiftPopup.open(this.userAccount.id);
    },
    change: function change() {
      var _this5 = this;
      // 获取navBarPostDetail的高度
      this.$nextTick(function () {
        uni.createSelectorQuery().select('#projectMeetPagesHeader').boundingClientRect(function (res) {
          _this5.headerHeight = res.height;
        }).exec();
        uni.createSelectorQuery().select('#projectMeetPageFooter').boundingClientRect(function (res) {
          _this5.footerHeight = res.height;
        }).exec();
      });
      setTimeout(function () {
        uni.createSelectorQuery().select('#projectMeetPagesHeader').boundingClientRect(function (res) {
          _this5.headerHeight = res.height;
        }).exec();
        uni.createSelectorQuery().select('#projectMeetPageFooter').boundingClientRect(function (res) {
          _this5.footerHeight = res.height;
        }).exec();
      }, 100);
      setTimeout(function () {
        uni.createSelectorQuery().select('#projectMeetPagesHeader').boundingClientRect(function (res) {
          _this5.headerHeight = res.height;
        }).exec();
        uni.createSelectorQuery().select('#projectMeetPageFooter').boundingClientRect(function (res) {
          _this5.footerHeight = res.height;
        }).exec();
      }, 1000);
    },
    // /functional-chat-web/chatDialog/getByUserAccountId
    getChatByUserAccountId: function getChatByUserAccountId() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.next = 2;
                return _this6.$request.post("/functional-chat-web/dialog/getOneToOneByUserAccountId", {
                  data: {
                    userAccountId: _this6.userAccountId
                  }
                }).then(function (res) {
                  _this6.dialog = res.data;
                  _this6.dialogId = res.data.id;
                }).catch(function (err) {
                  _this6.$kt.toast.error(err);
                });
              case 2:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    send: function send(param) {
      var _this7 = this;
      if (this.targetLanguage.code) {
        param.identifyCode = "auto";
        param.targetLanguageCode = this.targetLanguage.code;
      }
      this.$refs.sendInput.toLoading();
      // /fp-community-web/post/push
      this.$kt.request.post("/functional-chat-web/dialogMessage/sendMessageBySelf", {
        data: {
          items: [param],
          dialogId: this.dialogId
        }
      }).then(function (res) {
        _this7.$refs.sendInput.clear();
        _this7.$refs.sendInput.toNone();
        _this7.$kt.userAccount.requestSelf();
      }).catch(function (res) {
        if (res.stateCode === 'balanceNotEnough') {
          setTimeout(function () {
            _this7.$refs.projectMeetPointsPopup.open(_this7.self.id);
          }, 1000);
        }
        if (res.stateCode === 'onlyVipCanSendImage' || res.stateCode === 'onlyVipCanSendVideo') {
          setTimeout(function () {
            _this7.$refs.projectMeetVipPopup.open(_this7.self.id);
          }, 1000);
        }
        if (res.stateCode === 'goldNotEnough') {
          setTimeout(function () {
            _this7.$refs.projectMeetGoldPopup.open(_this7.self.id);
          }, 1000);
        }
        uni.showToast({
          title: res.errMsg,
          icon: "none"
        });
        _this7.$refs.sendInput.toNone();
      });
    },
    dialogLoad: function dialogLoad(dialog) {
      var _this8 = this;
      this.dialog = dialog;
      this.$kt.userAccount.getById(dialog.userAccountId).then(function (userAccount) {
        _this8.userAccount = userAccount;
      });
      this.getIsCustomerService();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 349:
/*!*********************************************************************************************************************************************************************************************!*\
  !*** E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/pages/project-meet-pages/chat-dialog/chat-dialog.vue?vue&type=style&index=0&id=006f6cc7&lang=scss&scoped=true& ***!
  \*********************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_style_index_0_id_006f6cc7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat-dialog.vue?vue&type=style&index=0&id=006f6cc7&lang=scss&scoped=true& */ 350);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_style_index_0_id_006f6cc7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_style_index_0_id_006f6cc7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_style_index_0_id_006f6cc7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_style_index_0_id_006f6cc7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_chat_dialog_vue_vue_type_style_index_0_id_006f6cc7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 350:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-uni-app/pages/project-meet-pages/chat-dialog/chat-dialog.vue?vue&type=style&index=0&id=006f6cc7&lang=scss&scoped=true& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[343,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/project-meet-pages/chat-dialog/chat-dialog.js.map