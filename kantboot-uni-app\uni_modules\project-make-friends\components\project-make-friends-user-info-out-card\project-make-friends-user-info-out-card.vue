<template>
    <view :class="clazz.container">
        <view class="user-info-card">
            
            <view style="vertical-align: top;
            display: inline-block;
            width: calc(100% - 150rpx);">
                <view>

                <view class="user-info-card-nickname">{{ userInfo.nickname || $i18n.zhToGlobal("无昵称") }}</view>
                
                <view v-if="userInfo.gmtBirthday&&!userInfo.isAgeSecret" class="user-info-card-age">
                   <view v-if="$i18n.getLanguageCode()==='zh_CN'||$i18n.getLanguageCode()==='zh_HK'||$i18n.getLanguageCode()==='zh_MO'||$i18n.getLanguageCode()==='zh_TW'">
                        {{ $kt.date.getAge(userInfo.gmtBirthday) }}{{ $i18n.zhToGlobal("岁") }}
                   </view>
                    <view v-else>
                       {{$i18n.zhToGlobal("年龄")}}{{":"}}{{ $kt.date.getAge(userInfo.gmtBirthday) }}
                   </view>
                </view>
                
                <view class="user-info-card-gender">
                    <image
                    v-if="userInfo.genderCode === 'male'"
                    class="user-info-card-gender-icon"
                    :src="$kt.file.byPath('icon/male.svg')"></image>
                    <image
                    v-else
                    class="user-info-card-gender-icon"
                    :src="$kt.file.byPath('icon/female.svg')"></image>
                </view>

                </view>

                <view style="margin-top: 20rpx;">
                     <view class="user-info-card-intro">
                        {{makeFriends.getJobTypeNameById(userInfo.jobTypeId) || $i18n.zhToGlobal("无职业") }}
                       {{ "|" }}
                       <view v-if="userInfo.birthStateAreaAddress" style="display: inline-block;margin-left: 10rpx;margin-right: 10rpx">
                         <text v-if="userInfo.birthStateAreaAddress.name">
                           {{userInfo.birthStateAreaAddress.name}}
                         </text>
                       </view>
                       <text
                           v-if="userInfo.vegetarian"
                       >{{ "|" }}</text>
                       <view v-if="userInfo.vegetarian" style="display: inline-block;">
                         <view
                             class="tag-v-1"
                             v-if="$i18n.getLanguageCode() === 'zh_CN' || $i18n.getLanguageCode() === 'zh_HK' || $i18n.getLanguageCode() === 'zh_MO' || $i18n.getLanguageCode() === 'zh_TW'">
                           {{ "素食" + $kt.math.sub(currentYear, userInfo.vegetarianStartYear) + "年" }}
                         </view>
                         <view class="tag-v-1" v-else>{{ $i18n.zhToGlobal("素食年份") }}{{ ": " }}<view
                             style="display: inline-block;font-weight: bold;margin-left: 8rpx;">{{
                             $kt.math.sub(currentYear, userInfo.vegetarianStartYear) }}</view>
                         </view>
                       </view>

                    </view>
                </view>


                <view style="margin-top: 20rpx;">
                    <!-- 个性签名 -->
                     <view class="user-info-card-intro" style="position: relative;">
                        <view style="display: inline-block;width: 32rpx;">
                            <image
                                class="user-info-card-intro-icon"
                              :src="$kt.file.byPath('icon/intro.svg')"
                            style="width: 30rpx;opacity: .7;height: 30rpx;margin-top: 6rpx;"
                        ></image>
                        </view>
                        
                        <view class="intro-in" style="display: inline-block;width: calc(100% - 50rpx);vertical-align: top;">
                            {{ userInfo.introduction || $i18n.zhToGlobal("无个性签名") }}
                        </view>

                    </view>
                </view>

                <view style="height: 20rpx;"></view>
        <view>
            <view>
                <view
                v-for="item in getCharacteristics(userInfo)"
                class="tag tag-v">
                    {{ makeFriends.getCharacteristicNameById(item.characteristicId) }}
                </view>
            </view>
        </view>
        <view style="height: 20rpx;"></view>
        <!-- 个人简介 -->
        <view class="user-info-card-intro personal-introduction" style="position: relative;">
                {{$i18n.zhToGlobal("个人简介")}}{{": "}}{{ userInfo.personalIntroduction || $i18n.zhToGlobal("无个人简介") }}
        </view>


            </view>

            <view style="display: inline-block;width: 130rpx;">
                <kt-avatar 
                size="120rpx"
                :src="$kt.file.visit(userInfo.fileIdOfAvatar)">
                </kt-avatar>
            </view>
        </view>

    </view>
</template>

<script>
import makeFriends from '../../libs/load'

export default {
    props:{
        userInfo:{
            type:Object,
            default:()=>{
                return {}
            }
        }
    },
    data(){
        return {
            makeFriends,
            clazz:{
                container: this.$kt.style.toggleClass("container"),
            },
          currentYear: new Date().getFullYear(),
        }
    },
    methods:{
        getCharacteristics(userInfo){
            // 获取前3个特征
            let characteristics = userInfo.characteristics || [];
            let characteristicsList = [];
            for(let i=0;i<characteristics.length;i++){
                if(i>2){
                    break;
                }
                characteristicsList.push(characteristics[i]);
            }
            return characteristicsList;
        }
    }
}
</script>
<style lang="scss" scoped>
.user-info-card{
}
.user-info-card-nickname{
    display: inline-block;
    font-weight: bold;
    font-size: 36rpx;
    letter-spacing: 1px;
    vertical-align: top;
}
.user-info-card-age{
    display: inline-block;
    font-size: 28rpx;
    vertical-align: top;
    // 绿色
    color: #07c160;
    // 透明绿
    background-color: rgba(7, 193, 96, 0.1);
    padding: 5rpx 10rpx;
    border-radius: 10rpx;
    margin-left: 15rpx;
}
.user-info-card-gender{
    display: inline-block;
    margin-left: 25rpx;
    vertical-align: top;
    margin-top: 5rpx;
    width: 30rpx;
    height: 30rpx;
    .user-info-card-gender-icon{
        width: 100%;
        height: 100%;
    }
}
.user-info-card-intro{
    font-size: 28rpx;
    color: #999;
    letter-spacing: 1px;
}


.tag-v {
		background-color: rgba(255, 244, 240, 1);
		display: inline-block;
		vertical-align: top;
		margin-right: 20rpx;
		height: 50rpx;
		line-height: 50rpx;
		margin-bottom: 20rpx;
		padding: 0 20rpx 0 20rpx;
		border-radius: 10rpx;
		font-size: 28rpx;
		color: rgba(245, 172, 54, 1);

}
.intro-in{
    // 超出隐藏
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.personal-introduction{
    // 超出隐藏，2行
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.container-mode-color-scheme-dark{
  .user-info-card{
    .user-info-card-intro-1{
      color: #cccccc;
    }
    .user-info-card-nickname{
      color: #cccccc;
    }
    .user-info-card-age{
      color: #07c160;
      background-color: rgba(7, 193, 96, 0.1);
    }
    .user-info-card-intro{
      color: #999;
      .user-info-card-intro-icon{
        filter: invert(1);
      }
    }
  }
}

.tag-v-1 {
  display: inline-block;
  vertical-align: top;
  margin-left: 10rpx;

}


</style>