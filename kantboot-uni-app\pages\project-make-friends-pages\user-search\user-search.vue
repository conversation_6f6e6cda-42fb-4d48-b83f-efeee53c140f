<template>
  <view>
    <view
        class="header"
        id="headerInInvitePersons">
      <kt-nav-bar
          :title="$i18n.zhToGlobal('用户搜索')"></kt-nav-bar>
      <!-- 搜索框 -->
      <view class="box-search">
        <view class="box-search-input">
          <input
              class="input"
              type="text"
              v-model="keyword"
              @input="input"
              @blur="search"
              @change="search"
              :placeholder="$i18n.zhToGlobal('输入搜索内容')"
          ></input>
          <view
              v-if="false"
              @click="search"
              class="btn">{{$i18n.zhToGlobal("搜索")}}</view>
        </view>
      </view>

    </view>
    <view>
      <view class="box">
        <view
        v-if="keyword2"
        >
          <project-make-friends-recommend-panel
              :keyword="keyword2"
              :height="'calc(100vh - '+headerHeight+'px)'"
          ></project-make-friends-recommend-panel>
        </view>
      </view>
    </view>

  </view>
</template>

<script>

export default {
  data() {
    return {
      list: [],
      listFilter: '',
      keyword: '',
      headerHeight: 0,
      keyword2:''
    };
  },
  mounted() {
    // this.getListBySelf();
    this.getHeight();
  },
  methods: {
    search(){
      this.keyword2 = this.keyword;
      console.log(this.keyword2)
    },
    getHeight() {
      this.$nextTick(() => {
        uni.createSelectorQuery()
            .select('#headerInInvitePersons')
            .boundingClientRect((res) => {
              this.headerHeight = res.height
            }).exec()
      })
    },
    input(e) {
      this.keyword=e.detail.value;
      this.filterLists();
      this.search();
    },
    // 过滤
    filterLists() {
      if (this.keyword) {
        this.listFilter = this.list.filter(user => {
          return user.nickname.includes(this.keyword) || user.introduction.includes(this.keyword);
        });
        return;
      }
      this.listFilter = this.list;

    },
// /user-account-web/userAccountInvite/getAllInviterBySelf
    getListBySelf() {
      this.$kt.request.post('/user-account-web/userAccountInvite/getAllInviterBySelf', {
        data: {}
      }).then((res) => {
        this.list = res.data;
        // 根据gmtCreate时间倒序
        this.list.sort((a, b) => b.gmtCreate - a.gmtCreate);
        this.filterLists();
      }).catch((err) => {
        uni.showToast({
          title: this.$i18n.zhToGlobal('获取邀请人失败'),
          icon: 'none'
        });
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.box{
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;
  .box-item{
    margin-bottom: 60rpx;
    .box-item-time{
      font-size: 28rpx;
      color: #999999;
      margin-top: 10rpx;
    }
  }

}
.box-search{
  position: relative;
  width: 100%;
  background-color: #F0F0F0;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  .input{
    width: 100%;
  }
  .btn{
    position: absolute;
    right: 20rpx;
    top: 10rpx;
    padding: 10rpx 20rpx;
    z-index:10;
  }
  .btn:active{
    opacity: .5;
  }
}
.box-item:active{
  opacity: .7;
}
.header{
  padding: 20rpx;
  box-sizing: border-box;
}
</style>
