<view class="data-v-9b645b2e"><kt-popup vue-id="d1260886-1" data-ref="weightPopup" class="data-v-9b645b2e vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-9b645b2e',clazz.box]}}"><view class="box-title data-v-9b645b2e">{{''+$root.g0}}</view><view class="pop_class data-v-9b645b2e"><block wx:for="{{weightArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['weightSelect',['$0'],[[['weightArr','',index]]]]]]]}}" class="{{['class_item','data-v-9b645b2e',(value===item.value)?'class_item-active':'']}}" bindtap="__e">{{item.value}}</view></block></view></view></kt-popup></view>