<view hidden="{{!(false)}}" class="{{['data-v-4d761552',clazz.typeBox]}}"><view data-event-opts="{{[['tap',[['selectType',['login']]]]]}}" class="{{['type-box-btn','data-v-4d761552',(param.typeCode==='login')?'type-box-btn-selected':'']}}" bindtap="__e">{{''+$root.g0+''}}<block wx:if="{{param.typeCode==='login'}}"><view class="type-box-line data-v-4d761552"></view></block></view><view data-event-opts="{{[['tap',[['selectType',['register']]]]]}}" class="{{['type-box-btn','data-v-4d761552',(param.typeCode==='register')?'type-box-btn-selected':'']}}" bindtap="__e">{{''+$root.g1+''}}<block wx:if="{{param.typeCode==='register'}}"><view class="type-box-line data-v-4d761552"></view></block></view></view>