<template>
  <view>
    <kt-popup ref="ktPopup">
      <view :class="clazz.box">
        <view class="box-title">
          {{ $i18n.zhToGlobal("搜索用户") }}
        </view>
        <view style="height: 20rpx"></view>
        <view class="box-search-input">
          <input type="text"
                 :placeholder="$i18n.zhToGlobal('根据用户名搜索')"
                 v-model="keyword"
                @input="input"
                 @blur="inputBlur"
          ></input>
        </view>
        <view>
          <view style="height: 100rpx"></view>
          <u-loading-icon
          v-if="loading"
          mode="circle"
          ></u-loading-icon>
          <u-empty
              v-else-if="keyword&&userAccountList.length===0"
          :content="$i18n.zhToGlobal('未找到用户')"
          ></u-empty>
          <view
              class="user-card-box"
              v-else>
            <view v-for="(userAccount,index) in userAccountList"
                  :key="index"
                  @click="toUserInfoPage(userAccount)"
                  class="kt-user-info-card-box">

              <kt-user-info-card
                  :user-info="userAccount"
              ></kt-user-info-card>
            </view>
          </view>
          <view style="height: 100rpx"></view>
        </view>
      </view>
    </kt-popup>
    <project-meet-user-info-popup
        ref="usPopup"
    ></project-meet-user-info-popup>

  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz:{
        box: this.$kt.style.toggleClass('box')
      },
      keyword:"",
      loading:false,
      userAccountList:[],
      notExist:false,
      searchFinally:false,
      self:{
        genderCode:'male'
      },
      // 搜索结果的keyword
      searchKeyword: "",
    };
  },
  mounted() {
    this.self = this.$kt.userAccount.getSelf();
  },
  methods: {
    isPc() {
    return this.$kt.style.detectDeviceType() === 'pc';
    },
    toUserInfoPage(userAccount) {
      if (this.isPc()) {
        this.$refs.usPopup.open(userAccount.id);
        // this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info");
      } else {
        this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info?userAccountId=" + userAccount.id);
      }
    },
    inputBlur(){
      if(this.keyword!==this.searchKeyword){
        this.inputSearch();
      }
    },
    inputSearch(){
      // /user-account-web/userAccount/getByUsername
      this.loading = true;
      if(this.keyword===""){
        this.userAccountList=[];
        this.loading=false;
        return;
      }
      this.$request.post("/user-account-web/userAccount/getByUsernameVague",{
        data:{username:this.keyword}
      }).then(res=>{
        let userAccountList = res.data;
        if(!userAccountList){
          userAccountList=[]
        }
        for(let i=0;i<userAccountList.length;i++){
          let userAccount = userAccountList[i];
          if(!userAccount.genderCode){
          // 删除
            userAccountList.splice(i,1);
            i--;
            continue;
          }
          if(userAccount.genderCode==='male'&&this.self.genderCode==='male'){
            // 删除
            userAccountList.splice(i,1);
            i--;
            continue;
          }else if(userAccount.genderCode==='female'&&this.self.genderCode==='female'){
            // 删除
            userAccountList.splice(i,1);
            continue;
          }
        }
        this.userAccountList = userAccountList;
        this.searchKeyword = this.keyword+"";
        this.loading=false;
      }).catch(err=>{
        this.loading=false;
      })
    },
    input(e){
      this.keyword = e.detail.value;
      this.inputSearch();

    },
    open(){
      if(this.keyword){
        this.inputSearch();
      }else{
        this.userAccountList=[]
      }
      this.$refs.ktPopup.open();
    },
    close(){
      this.$refs.ktPopup.close();
    }
  },
}
</script>

<style lang="scss">
.box{
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 20rpx;
  box-sizing: border-box;
  .box-title {
    font-size: 28rpx;
    color: #333333;
    text-align: center;
    font-weight: bold;
  }
  .box-search-input {
    width: 100%;
    border-radius: 40rpx;
    background-color: #f0f0f0;
    padding: 20rpx;
    box-sizing: border-box;
  }
}

.box-mode-device-pc{
  border-radius: 20rpx;
}

.user-card-box{
  // 滚动
  overflow-y: scroll;
  max-height: calc(100vh - 700rpx);

  .kt-user-info-card-box{
    margin-bottom: 40rpx;
  }
}
</style>
