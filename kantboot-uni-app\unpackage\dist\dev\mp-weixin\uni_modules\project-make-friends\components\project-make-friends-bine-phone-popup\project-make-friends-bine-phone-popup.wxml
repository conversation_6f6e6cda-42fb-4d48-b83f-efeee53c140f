<view class="data-v-513d364a"><kt-popup vue-id="532bd808-1" overlayClose="{{false}}" zIndex="{{999999999}}" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-513d364a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-513d364a"><view data-event-opts="{{[['tap',[['closePop',['$event']]]]]}}" class="close-btn data-v-513d364a" bindtap="__e">×</view><view class="title data-v-513d364a">{{$root.g0}}</view><block wx:if="{{isLogin&&self.id}}"><scroll-view style="height:calc(100vh - 500rpx);" scroll-y="{{true}}" class="data-v-513d364a"><view class="in-box data-v-513d364a"><view class="in-box-input data-v-513d364a"><image class="in-box-input-icon data-v-513d364a" src="{{$root.g1}}"></image><input class="in-box-input-input data-v-513d364a" length="11" placeholder="{{$root.g2}}" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['requestParams']],['e0',['$event']]]]]}}" value="{{requestParams.phone}}" bindinput="__e"/></view></view><view class="in-box data-v-513d364a"><view class="in-box-input data-v-513d364a"><image class="in-box-input-icon data-v-513d364a" src="{{$root.g3}}"></image><block wx:if="{{!passwordShow}}"><input class="in-box-input-input data-v-513d364a" type="password" placeholder="{{$root.g4}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['requestParams']],['e1',['$event']]]]]}}" value="{{requestParams.password}}" bindinput="__e"/></block><block wx:if="{{passwordShow}}"><input class="in-box-input-input data-v-513d364a" type="text" placeholder="{{$root.g5}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['requestParams']],['e2',['$event']]]]]}}" value="{{requestParams.password}}" bindinput="__e"/></block><block wx:if="{{!passwordShow}}"><image class="in-box-input-icon data-v-513d364a" src="{{$root.g6}}"></image></block><block wx:if="{{passwordShow}}"><image class="in-box-input-icon data-v-513d364a" src="{{$root.g7}}"></image></block><view data-event-opts="{{[['tap',[['passwordShowChange']]]]}}" class="in-box-input-show-change data-v-513d364a" bindtap="__e"></view></view></view><view class="in-box data-v-513d364a"><view class="in-box-input data-v-513d364a"><image class="in-box-input-icon data-v-513d364a" src="{{$root.g8}}"></image><block wx:if="{{!passwordConfirmShow}}"><input class="in-box-input-input data-v-513d364a" type="password" placeholder="{{$root.g9}}" data-event-opts="{{[['input',[['__set_model',['','passwordConfirm','$event',[]]],['e3',['$event']]]]]}}" value="{{passwordConfirm}}" bindinput="__e"/></block><block wx:if="{{passwordConfirmShow}}"><input class="in-box-input-input data-v-513d364a" type="text" placeholder="{{$root.g10}}" data-event-opts="{{[['input',[['__set_model',['','passwordConfirm','$event',[]]],['e4',['$event']]]]]}}" value="{{passwordConfirm}}" bindinput="__e"/></block><block wx:if="{{!passwordConfirmShow}}"><image class="in-box-input-icon data-v-513d364a" src="{{$root.g11}}"></image></block><block wx:if="{{passwordConfirmShow}}"><image class="in-box-input-icon data-v-513d364a" src="{{$root.g12}}"></image></block><view data-event-opts="{{[['tap',[['passwordConfirmShowChange']]]]}}" class="in-box-input-show-change data-v-513d364a" bindtap="__e"></view></view><view style="height:50rpx;" class="data-v-513d364a"></view><view class="common-info-text data-v-513d364a">{{$root.g13+" "+"("+$root.g14+")"}}</view><project-make-friends-user-info-panel vue-id="{{('532bd808-2')+','+('532bd808-1')}}" user-account-id="{{self.id}}" show-relation="{{false}}" show-user-post="{{false}}" class="data-v-513d364a" bind:__l="__l"></project-make-friends-user-info-panel></view></scroll-view></block><view style="height:30rpx;" class="data-v-513d364a"></view><kt-button bind:click="__e" vue-id="{{('532bd808-3')+','+('532bd808-1')}}" data-ref="ktButton" data-event-opts="{{[['^click',[['toBind']]]]}}" class="data-v-513d364a vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{''+$root.g15+''}}</kt-button></view></kt-popup></view>