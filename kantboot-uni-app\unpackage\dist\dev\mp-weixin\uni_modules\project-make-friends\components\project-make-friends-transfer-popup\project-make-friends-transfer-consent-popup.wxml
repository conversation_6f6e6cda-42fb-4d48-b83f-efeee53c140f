<view class="data-v-15b9d56f"><kt-popup vue-id="bcfd4d12-1" overlayClose="{{false}}" zIndex="{{999999999}}" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-15b9d56f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-15b9d56f"><view data-event-opts="{{[['tap',[['closePop',['$event']]]]]}}" class="close-btn data-v-15b9d56f" bindtap="__e">×</view><view class="title data-v-15b9d56f">{{$root.g0}}</view><block wx:if="{{transferData}}"><view class="transfer-info data-v-15b9d56f"><view class="info-section data-v-15b9d56f"><view class="section-title data-v-15b9d56f">{{$root.g1}}</view><view class="user-card data-v-15b9d56f"><image class="user-avatar data-v-15b9d56f" src="{{transferData.originalInviterUserAccount&&transferData.originalInviterUserAccount.fileIdOfAvatar?$root.g2:'/static/default-avatar.png'}}" mode="aspectFill"></image><view class="user-details data-v-15b9d56f"><view class="user-nickname data-v-15b9d56f">{{transferData.originalInviterUserAccount?$root.g3:''}}</view><view class="user-id data-v-15b9d56f">{{"ID: "+(transferData.originalInviterUserAccount?transferData.originalInviterUserAccount.id:'')}}</view><block wx:if="{{transferData.originalInviterUserAccount&&transferData.originalInviterUserAccount.phone}}"><view class="user-phone data-v-15b9d56f">{{''+transferData.originalInviterUserAccount.phoneAreaCode+" "+transferData.originalInviterUserAccount.phone+''}}</view></block></view></view></view><view class="info-section data-v-15b9d56f"><view class="section-title data-v-15b9d56f">{{$root.g4}}</view><view class="user-card data-v-15b9d56f"><image class="user-avatar data-v-15b9d56f" src="{{transferData.targetInviterUserAccount&&transferData.targetInviterUserAccount.fileIdOfAvatar?$root.g5:'/static/default-avatar.png'}}" mode="aspectFill"></image><view class="user-details data-v-15b9d56f"><view class="user-nickname data-v-15b9d56f">{{transferData.targetInviterUserAccount?$root.g6:''}}</view><view class="user-id data-v-15b9d56f">{{"ID: "+(transferData.targetInviterUserAccount?transferData.targetInviterUserAccount.id:'')}}</view><block wx:if="{{transferData.targetInviterUserAccount&&transferData.targetInviterUserAccount.phone}}"><view class="user-phone data-v-15b9d56f">{{''+transferData.targetInviterUserAccount.phoneAreaCode+" "+transferData.targetInviterUserAccount.phone+''}}</view></block></view></view></view><view class="info-section data-v-15b9d56f"><view class="section-title data-v-15b9d56f">{{$root.g7}}</view><view class="time-text data-v-15b9d56f">{{$root.m0}}</view></view></view></block><view class="footer data-v-15b9d56f"><view class="buttons data-v-15b9d56f"><button class="reject-btn data-v-15b9d56f" disabled="{{processing}}" data-event-opts="{{[['tap',[['handleReject',['$event']]]]]}}" bindtap="__e">{{''+(processing&&currentAction==='reject'?$root.g8:$root.g9)+''}}</button><button class="approve-btn data-v-15b9d56f" disabled="{{processing}}" data-event-opts="{{[['tap',[['handleApprove',['$event']]]]]}}" bindtap="__e">{{''+(processing&&currentAction==='approve'?$root.g10:$root.g11)+''}}</button></view></view></view></kt-popup></view>