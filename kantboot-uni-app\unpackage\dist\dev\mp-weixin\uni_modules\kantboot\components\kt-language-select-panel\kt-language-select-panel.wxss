@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content.data-v-745ab7a2 {
  text-align: left;
  padding: 30rpx;
  box-sizing: border-box;
}
.box.data-v-745ab7a2 {
  position: relative;
  padding: 30rpx;
  font-size: 30rpx;
  border: 1rpx solid #f0f0f0;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.box .box-select-icon.data-v-745ab7a2 {
  position: absolute;
  right: 0;
  top: 0;
  width: 60rpx;
  height: 60rpx;
  border-radius: 0 10rpx 0 0;
}
.box .box-name.data-v-745ab7a2 {
  color: #000000;
  font-weight: bold;
}
.box .box-code.data-v-745ab7a2 {
  color: #666666;
}
.box .box-i18n-name.data-v-745ab7a2 {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  font-size: 28rpx;
  color: #666666;
  display: inline-block;
  float: right;
}
.box-mode-device-pc.data-v-745ab7a2 {
  cursor: pointer;
}
.box-mode-color-scheme-light.data-v-745ab7a2 {
  border: 1rpx solid #f0f0f0;
}
.box-mode-color-scheme-light .box-name.data-v-745ab7a2 {
  color: #000000;
}
.box-mode-color-scheme-light .box-code.data-v-745ab7a2 {
  color: #666666;
}
.box-mode-color-scheme-light .box-i18n-name.data-v-745ab7a2 {
  color: #666666;
}
.box-mode-color-scheme-dark.data-v-745ab7a2 {
  border: none;
  background-color: #292929;
}
.box-mode-color-scheme-dark .box-name.data-v-745ab7a2 {
  color: #F8F8F8;
}
.box-mode-color-scheme-dark .box-code.data-v-745ab7a2 {
  color: #F8F8F8;
}
.box-mode-color-scheme-dark .box-i18n-name.data-v-745ab7a2 {
  color: #F8F8F8;
  opacity: 0.5;
}
.box-mode-color-scheme-dark .box-select-icon.data-v-745ab7a2 {
  -webkit-filter: invert(100%);
          filter: invert(100%);
}
.box.data-v-745ab7a2:active {
  opacity: 0.8;
}
.btn.data-v-745ab7a2 {
  position: fixed;
  z-index: 100;
  bottom: 0;
  width: 100%;
  padding: 30rpx;
  left: 0;
  text-align: center;
  color: #f0f0f0;
  letter-spacing: 5rpx;
  box-sizing: border-box;
  background-color: #15161c;
}
.btn.data-v-745ab7a2:active {
  background-color: #15161c;
  color: #F8F8F8;
}
.title.data-v-745ab7a2 {
  font-size: 32rpx;
  color: #000000;
  font-weight: bold;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  text-align: center;
}
