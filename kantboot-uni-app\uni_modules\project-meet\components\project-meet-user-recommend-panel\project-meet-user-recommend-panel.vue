<template>
  <view>
    <scroll-view :class="clazz.userRecommendPanel" :scroll-y="true"
                 :scroll-with-animation="true"
                 @scrolltolower="getAfter"
                 :show-scrollbar="false"
                 :refresher-triggered="refresherTriggered"
                 :refresher-enabled="false"
                 @refresherrefresh="onRefresherrefresh"
                 :lower-threshold="$kt.util.rpxToPx(500)"
                 :style="{height}">

      <view
          class="meet-user-match-card-box">
        <project-meet-user-match-card
        ref="userMatchCard"
        @toLogin="toLogin()"
        ></project-meet-user-match-card>
      </view>

      <view class="user-list-box">
        <view
            v-for="item in userList"
            :class="clazz.userBox">
          <project-meet-user-info-out-card
              class="card-in-user-box"
              :userInfo="item"
              @userClick="toUserInfoPage(item)"
          ></project-meet-user-info-out-card>

        </view>
        <u-loading-icon
            v-if="alfterLoading"
            mode="circle"
            size="50rpx"
        ></u-loading-icon>
        <view v-if="isBottom"
              style="text-align: center; color: #999999; font-size: 24rpx;">
          {{ $kt.i18n.zhToGlobal('没有更多了') }}
        </view>
        <view style="height: 140rpx;"></view>
      </view>
    </scroll-view>


    <project-meet-user-info-popup
    ref="usPopup"
    ></project-meet-user-info-popup>



  </view>

</template>

<script>
export default {
  props: {
    height: {
      type: String,
      default: "100vh"
    },
    keyword: {
      type: String,
      default: ""
    },
    // 是否含有匹配
    isHasMatch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isLoading: false,
      refresherEnabled: true,
      refresherTriggered: true,
      userList: [],
      userListInStart:[],
      requestParam: {
        maxId: 0,
        minId: 0,
      },
      alfterLoading: false,
      rowLength: 20,
      // 是否见底了
      isBottom: false,
      clazz: {
        userRecommendPanel: this.$kt.style.toggleClass("user-recommend-panel"),
        userBox: this.$kt.style.toggleClass("user-box"),
      }
    }
  },
  watch: {
    keyword(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.getInitList();
      }
    }
  },
  async created() {
    this.getInitList();
    // 监听登录
    this.$kt.event.on("login:success", () => {
      this.getInitList();
    });
  },
  mounted() {
    this.$kt.event.on('changeTabbar', () => {
      this.$refs.usPopup.close();
    });
  },
  methods: {
    toLogin(){
      this.$emit("toLogin");
      // this.$refs.userMatchCard.toLogin();
    },
    getUserListInStart(){
      // 获取userList开始的两个
      this.userListInStart = this.userList.slice(0, 2);
    },
    async onRefresherrefresh() {
      if (this.refreshing) {
        // this.refresherTriggered = false;
        return;
      }
      this.isLoading = true;
      this.refresherTriggered = true;
      await this.getInitList();
      setTimeout(() => {
        this.refresherTriggered = false;
      }, 1000);
    },
    /**
     * 计算最大id和最小id
     */
    calculateMaxAndMinId() {
      let maxId = 0;
      let minId = 0;
      for (let i = 0; i < this.userList.length; i++) {
        let item = this.userList[i];
        if (i === 0) {
          maxId = item.id;
          minId = item.id;
        } else {
          if (item.id > maxId) {
            maxId = item.id;
          }
          if (item.id < minId) {
            minId = item.id;
          }
        }
      }
      this.requestParam.maxId = minId;
      this.requestParam.minId = maxId;
    },

    /**
     * 是否已存在
     */
    isExist(id) {
      for (let i = 0; i < this.userList.length; i++) {
        let item = this.userList[i];
        if (item.id === id) {
          return true;
        }
      }
      return false;
    },
    pushUserList(userAccount) {
      // 先判断是否存在
      if (this.isExist(userAccount.id)) {
        return;
      }
      this.userList.push(userAccount);
    },
    isPc() {
      return this.$kt.style.detectDeviceType() === 'pc';
    },
    toUserInfoPage(userAccount) {
      if (this.isPc()) {
        this.$refs.usPopup.open(userAccount.id);
        // this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info");
      } else {
        this.$kt.router.navTo("/pages/project-meet-pages/user-info/user-info?userAccountId=" + userAccount.id);
      }
    },
    // /project-make-friends-web/userAccount/getRecommendList
    getInitList() {
      // 先从存储中获取
      let userList = this.$kt.storage.get('userRecommendList');
      if (userList) {
        this.userList = userList;
        this.calculateMaxAndMinId();
      }

      this.$request.post('/project-meet-web/userAccount/getRecommendList',{
        data: {
          keyword: this.keyword,
        }
      }).then(res => {
        this.userList = res.data;
        this.calculateMaxAndMinId();
        // 添加进存储
        this.$kt.storage.set('userRecommendList', res.data);
        this.getUserListInStart();
      }).catch(err => {
      });
    },
    getAfter() {
      if (this.isBottom) {
        return;
      }
      if (this.alfterLoading) {
        return;
      }
      this.alfterLoading = true;
      this.$request.post("/project-meet-web/userAccount/getRecommendList", {data: {maxId: this.requestParam.maxId,
        keyword: this.keyword
        }})
          .then(res => {
            let list = res.data;
            if (list.length < this.rowLength) {
              this.alfterLoading = false;
              this.isBottom = true;
              return;
            }
            for (let i = 0; i < list.length; i++) {
              let item = list[i];
              this.pushUserList(item);
            }
            this.calculateMaxAndMinId();
            this.alfterLoading = false;
          });
    },

  }
}
</script>


<style lang="scss" scoped>
// 取消显示滚动条
.user-recommend-panel {
  overflow: hidden;

  &::-webkit-scrollbar {
    display: none;
  }
}

// 滚动条不显示
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

// 不显示滚动条
.user-recommend-panel::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

// 滚动
.user-recommend-panel::-webkit-scrollbar-thumb {
  background-color: #fff;
  border-radius: 0;
}

.user-list-box {
  position: relative;
  width: 100%;
  text-align: center;

  .user-box {
    display: inline-block;
    margin: 30rpx;
  }
}

.meet-user-match-card-box {
  padding: 20rpx 30rpx 20rpx 30rpx;
  box-sizing: border-box
}

.user-recommend-panel-mode-device-pc {
  position: relative;

  .meet-user-match-card-box {
    top:-10rpx;
    left: 167rpx;
    width: calc(100% - 300rpx);
    margin-left: 150rpx;
    z-index:3;
  }
  .user-box{
    margin: 30rpx;
  }
  .card-in-user-box{
    margin: 30rpx;
  }
}


</style>