import request from "../request";
import storage from "../storage";
import event from "../event";

let result = {};

result.requestSelf = ()=> {
    // 获取用户自身信息
    return new Promise((resolve, reject) => {
        request.send({
            // 检测到未登录，不自动跳转登录页
            isJumpLogin:false,
            uri: "/user-account-web/userAccount/getSelf",
            stateSuccess: (res) => {
                console.debug("获取用户自身信息成功", res);
                // 保存用户信息到缓存
                storage.set("userAccount:self", res.data);
                // 设置成已登录
                storage.set("userAccount:isLogin", true);
                event.emit("userAccount:getSelf", res.data);
                resolve(res);
            },
            stateFail: (res) => {
                console.log(res,"err")
                if(res?.stateCode==="notLogin"){
                    // 删除用户信息
                    storage.remove("userAccount:self");
                    // 设置成未登录
                    storage.set("userAccount:isLogin", false);
                }
                // 如果网络错误
                if(res?.stateCode==="networkError"){
                    uni.showToast({
                        title: "Network Error",
                        icon: "none"
                    });
                }

                resolve(res);
            }
        })
    });
}

result.requestById = (userAccountId)=> {
    // 获取用户信息
    return new Promise((resolve, reject) => {
        request.send({
            uri: "/user-account-web/userAccount/getById",
            data: {
                id: userAccountId
            },
            stateSuccess: (res) => {
                console.debug("获取用户信息成功", res);
                // 存入缓存中
                storage.setEx("userAccount:getById:"+res.data.id, res.data,1000*60*20);
                event.emit("userAccount:getById", res.data);
                resolve(res.data);
            },
            stateFail: (res) => {
                console.log(res,"err")
                // 如果网络错误
                if(res?.stateCode==="networkError"){
                    uni.showToast({
                        title: "Network Error",
                        icon: "none"
                    });
                }
                reject(res);
            }
        })
    });
}

result.getById = (userAccountId)=> {
    return new Promise((resolve, reject) => {
        // 先从缓存中获取
        let userAccount = storage.get("userAccount:getById:"+userAccountId);
        if(userAccount){
            resolve(userAccount);
            return;
        }
        // 如果没有，则请求
        result.requestById(userAccountId).then((res)=>{
            resolve(res);
        }).catch((res)=>{
            reject(res);
        });
    });
}


/**
 * 设置自身信息
 */
result.setSelf = function(self) {
    storage.set("userAccount:self", self);
}

/**
 * 获取自身信息
 */
result.getSelf = function() {
    return storage.get("userAccount:self");
}


/**
 * 是否在登录状态
 */
result.getIsLogin = function() {
    return !!storage.get("userAccount:isLogin");
}

/**
 * 设置登录状态
 */
result.setIsLogin = function(isLogin) {
    storage.set("userAccount:isLogin", isLogin);
}

/**
 * 设置邀请人id
 */
result.setUserAccountIdOfInviter = (userAccountIdOfInviter)=> {
    storage.set("userAccount:userAccountIdOfInviter", userAccountIdOfInviter);
}

/**
 * 获取邀请人id
 */
result.getUserAccountIdOfInviter = ()=> {
    return storage.get("userAccount:userAccountIdOfInviter");
}

result.removeUserAccountIdOfInviter = ()=> {
    storage.remove("userAccount:userAccountIdOfInviter");
}

/**
 * 设置邀请人
 * @param userAccountIdOfInviter 邀请人id
 * @returns {Promise<unknown>}
 */
result.setInviter = (userAccountIdOfInviter)=> {
    if(!userAccountIdOfInviter){
        if(!result.getUserAccountIdOfInviter()){
            return Promise.resolve();
        }
        userAccountIdOfInviter = result.getUserAccountIdOfInviter();
    }
    return new Promise((resolve, reject) => {
        request.send({
            uri: "/user-account-web/userAccountInvite/setInviterSelf",
            data: {
                userAccountIdOfInviter
            },
            stateSuccess: (res) => {
                resolve(res);
            },
            stateFail: (res) => {
                reject(res);
            }
        })
    });
}


export default result;