<template>
  <view :class="clazz.content">
    <view>
      <setting-menu-box
          v-if="isLogin&&hasAccount"
          :title="$i18n.zhToGlobal('账户')">
        <setting-menu
            v-if="self.phoneAreaCode && self.phone && hasPhone"
            :icon="$kt.file.byPath('icon/phone.svg')"
            :title="$i18n.zhToGlobal('手机号')"
            :content="'+'+self.phoneAreaCode + ' ' + self.phone"
        ></setting-menu>
        <setting-menu
            v-else-if="self.phone && hasPhone"
            :icon="$kt.file.byPath('icon/phone.svg')"
            :title="$i18n.zhToGlobal('手机号')"
            :content="self.phone"></setting-menu>
        <setting-menu
            v-else-if="hasPhone"
            :icon="$kt.file.byPath('icon/phone.svg')"
            :title="$i18n.zhToGlobal('手机号')"
            :content="$i18n.zhToGlobal('未绑定')"
        ></setting-menu>
        <setting-menu
            v-if="self.email && hasEmail"
            :icon="$kt.file.byPath('icon/email.svg')"
            :title="$i18n.zhToGlobal('邮箱')"
            :content="self.email"
        ></setting-menu>
        <setting-menu
            v-else-if="hasEmail"
            :icon="$kt.file.byPath('icon/email.svg')"
            :title="$i18n.zhToGlobal('邮箱')"
            :content="$i18n.zhToGlobal('未绑定')"></setting-menu>
        <setting-menu
            :icon="$kt.file.byPath('kantboot/icon/key.svg')"
            @click="$refs.ktChangePasswordPopup.open()"
            :title="$i18n.zhToGlobal('密码')"
            :content="$i18n.zhToGlobal('修改密码')">
        </setting-menu>

        <setting-menu
            v-if="hasDataManage"
            :is-right="true"
            :icon="$kt.file.byPath('icon/dataManage.svg')"
            :title="$i18n.zhToGlobal('数据管理')"
        ></setting-menu>
      </setting-menu-box>

      <setting-menu-box
          v-if="hasApp"
          :title="$i18n.zhToGlobal('应用')">
        <setting-menu
            v-if="hasLanguage"
            @click="$kt.router.toLanguageSelect()"
            :icon="$kt.file.byPath('kantboot/icon/language.svg')"
            :title="$i18n.zhToGlobal('语言')"
            :content="$i18n.getLanguageName()"
        ></setting-menu>
        <setting-menu
            v-if="hasColorMode"
            @click="$kt.router.toColorModeSelect()"
            :icon="$kt.file.byPath('icon/color.svg')"
            :title="$i18n.zhToGlobal('颜色模式')"
            :content="colorSchemeContent"
        ></setting-menu>
      </setting-menu-box>

      <setting-menu-box
          v-if="hasAbout"
          :title="$i18n.zhToGlobal('关于')">
        <setting-menu
            v-if="hasUserAgreement"
            @click="toUserAgreement"
            :is-right="true"
            :icon="$kt.file.byPath('icon/agreement.svg')"
            :title="$i18n.zhToGlobal('用户协议')"
        ></setting-menu>
        <setting-menu
            v-if="hasPrivacyAgreement"
            @click="toPrivacyAgreement"
            :is-right="true"
            :icon="$kt.file.byPath('icon/agreement.svg')"
            :title="$i18n.zhToGlobal('隐私协议')"
        ></setting-menu>
        <setting-menu
            :icon="$kt.file.byPath('icon/color.svg')"
            :title="$i18n.zhToGlobal('版本信息')"
            :content="bodyData.appVersion"
        ></setting-menu>
      </setting-menu-box>

      <setting-menu-box>
        <setting-menu
            v-if="isLogin"
            @click="$refs.ktModal.open()"
            :icon="$kt.file.byPath('kantboot/icon/logout.svg')"
            :title="$i18n.zhToGlobal('退出登录')"
        ></setting-menu>
        <setting-menu
            v-else
            @click="$kt.router.toLogin()"
            :icon="$kt.file.byPath('kantboot/icon/login.svg')"
            :title="$i18n.zhToGlobal('前往登录')">
        </setting-menu>
      </setting-menu-box>


    </view>
    <view style="height: 100rpx"></view>

    <kt-change-password-popup
        :no-has-old-password="noHasOldPassword"
    ref="ktChangePasswordPopup"></kt-change-password-popup>

    <kt-modal
        :title="$i18n.zhToGlobal('提示')"
        :content="$i18n.zhToGlobal('是否确定退出登录')"
        @confirm="toLogout"
        :confirm-text="$i18n.zhToGlobal('确定')"
        ref="ktModal">
    </kt-modal>
  </view>
</template>

<script>

import SettingMenuBox from "./components/setting-menu-box.vue";
import SettingMenu from "./components/setting-menu.vue";

export default {
  props: {
    /**
     * 是否有账户信息
     */
    hasAccount: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有手机信息
     */
    hasPhone: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有邮箱信息
     */
    hasEmail: {
      type: Boolean,
      default: true
      },
    /**
     * 是否有修改密码
     */
     hasChangePassword: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有数据管理
     */
    hasDataManage: {
      type: Boolean,
      default: true
    },

    /**
     * 是否有应用信息
     */
    hasApp: {
      type: Boolean,
      default: true
    },

    /**
     * 是否有语言信息
     */
    hasLanguage: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有颜色模式信息
     */
    hasColorMode: {
      type: Boolean,
      default: true
    },

    /**
     * 是否有关于信息
     */
    hasAbout: {
      type: Boolean,
      default: true
    },


    /**
     * 是否有用户协议
     */
    hasUserAgreement: {
      type: Boolean,
      default: true
    },
    /**
     * 是否有隐私协议
     */
    hasPrivacyAgreement: {
      type: Boolean,
      default: true
    },
    /**
     * 使用者协议网址
     */
    userAgreementUrl: {
      type: String,
      default: ""
    },
    /**
     * 隐私协议网址
     */
    privacyAgreementUrl: {
      type: String,
      default: ""
    },
    noHasOldPassword: {
      type: Boolean,
      default: false
    },
  },
  components: {SettingMenu, SettingMenuBox},
  data() {
    return {
      i18n: this.$i18n,
      isLogin: this.$kt.userAccount.getIsLogin(),
      self:{id:null},
      clazz: {
        inBox: this.$kt.style.toggleClass("in-box"),
        content: this.$kt.style.toggleClass("content"),
        back: this.$kt.style.toggleClass("back"),
      },
      colorSchemeContent:this.$i18n.zhToGlobal("跟随系统"),
      bodyData:{
        appVersion: "",
      },
    };
  },
  async onLoad() {
    this.clazz = {
      inBox: this.$kt.style.toggleClass("in-box"),
      content: this.$kt.style.toggleClass("content"),
      back: this.$kt.style.toggleClass("back"),
    };
    await new Promise(resolve => {
      setTimeout(() => {
        resolve();
      }, 3000);
    });
  },
  created() {
    this.getVersion();
    this.self = this.$kt.userAccount.getSelf();
    this.$kt.event.on('login:success', ()=>{
      this.isLogin = this.$kt.userAccount.getIsLogin();
      this.self = this.$kt.userAccount.getSelf();
    });
    this.colorSchemeContent = this.$i18n.zhToGlobal("跟随系统");
    let colorScheme =this.$kt.style.getMode().colorScheme;
    if(colorScheme==="auto"){
      this.colorSchemeContent = this.$i18n.zhToGlobal("跟随系统");
    }else if (colorScheme === "light") {
      this.colorSchemeContent = this.$i18n.zhToGlobal("光亮模式");
    } else if (colorScheme === "dark") {
      this.colorSchemeContent = this.$i18n.zhToGlobal("暗黑模式");
    }
  },
  methods: {
    toLogout() {
      try{
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "logout",
            operationCode: "logout",
            safeInputContent: "退出登录" + " 账号: "+ this.self.username,
            inputContent: "退出登录 邮箱: "+this.self.email
                +" 账号: "+ this.self.username,
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "退出登录",
            levelCode: "success",
            levelText: "成功",
          }
        });
      }catch (e) {

      }
      setTimeout(()=>{
        this.isLogin = false;
        this.$kt.userAccount.setIsLogin(false);
        this.$kt.storage.remove("token");

        uni.reLaunch({
          url: this.$kt.router.config.intoPath
        });

      },500);
    },
    getVersion(){
      // 获取微信小程序版本号
      uni.getSystemInfo({
        success: (res) => {
          this.bodyData.appVersion = res.appVersion;
        }
      });
    },
    /**
     * 前往用户协议
     */
    toUserAgreement() {
      if (this.userAgreementUrl) {
        // #ifdef MP-WEIXIN
        this.$kt.router.toWebview(this.userAgreementUrl+"?languageCode="+this.$kt.i18n.getLanguageCode(),{
          title: this.$i18n.zhToGlobal("用户协议"),
        });
        // #endif
        // #ifdef H5
        window.open(this.userAgreementUrl+"?languageCode="+this.$kt.i18n.getLanguageCode());
        // #endif
        // #ifdef APP-PLUS
        plus.runtime.openURL(this.userAgreementUrl+"?languageCode="+this.$kt.i18n.getLanguageCode());
        // #endif
        return;
      }
        uni.showToast({
          title: this.$i18n.zhToGlobal("用户协议未设置"),
          icon: "none"
        });
    },
    /**
     * 前往隐私协议
     */
    toPrivacyAgreement() {
      if (this.privacyAgreementUrl) {
        // #ifdef MP-WEIXIN
        this.$kt.router.toWebview(this.privacyAgreementUrl + "?languageCode=" + this.$kt.i18n.getLanguageCode(), {
          title: this.$i18n.zhToGlobal("隐私协议"),
        });
        // #endif
        // #ifdef H5
        window.open(this.privacyAgreementUrl + "?languageCode=" + this.$kt.i18n.getLanguageCode());
        // #endif
        // #ifdef APP-PLUS
        plus.runtime.openURL(this.privacyAgreementUrl + "?languageCode=" + this.$kt.i18n.getLanguageCode());
        // #endif
        return;
      }
      uni.showToast({
        title: this.$i18n.zhToGlobal("隐私协议未设置"),
        icon: "none"
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.back-mode-color-scheme-light {
  background-color: #f5f5f5;
}

.back-mode-color-scheme-dark {
  background-color: #191919;
}

</style>
