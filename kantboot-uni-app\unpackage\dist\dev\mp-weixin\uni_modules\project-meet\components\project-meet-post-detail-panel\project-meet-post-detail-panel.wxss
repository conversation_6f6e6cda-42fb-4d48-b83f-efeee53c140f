@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-7356a296 {
  position: relative;
  box-sizing: border-box;
}
.kt-community-post-detail-panel.data-v-7356a296 {
  position: relative;
}
.kt-community-post-detail-panel .bottom.data-v-7356a296 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  z-index: 100;
  box-sizing: border-box;
}
.no-data.data-v-7356a296 {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 30rpx;
}
.no-data .no-data-image.data-v-7356a296 {
  width: 100rpx;
  height: 100rpx;
  opacity: 0.6;
}
.box.data-v-7356a296 {
  padding: 20rpx;
  box-sizing: border-box;
}
.box .box-title.data-v-7356a296 {
  font-size: 32rpx;
  color: #333;
}
.box .box-item.data-v-7356a296 {
  width: 100%;
  display: inline-block;
  box-sizing: border-box;
}
.box-mode-color-scheme-dark .box-title.data-v-7356a296 {
  color: #fff;
}
.box-mode-color-scheme-dark .no-data.data-v-7356a296 {
  color: #b9b9b9;
}
.box-mode-color-scheme-dark .no-data-image.data-v-7356a296 {
  -webkit-filter: invert(1);
          filter: invert(1);
}
