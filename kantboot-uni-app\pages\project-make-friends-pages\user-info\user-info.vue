<template>
	<view>
		<kt-nav-bar :title="pageTitle"></kt-nav-bar>
		<view class="back"></view>
		<view class="container">
      <view>
        <project-make-friends-user-info-panel
            v-if="userAccountId"
        :user-account-id="userAccountId"
        ></project-make-friends-user-info-panel>
      </view>
    </view>
	</view>
</template>

<script>
import BirthDatePicker from './components/birth-date-picker.vue';
import CharacteristicPopup from './components/characteristic-popup.vue';
import InterestPopup from './components/interest-popup.vue';
import PersonalIntroductionPopup from './components/personal-introduction-popup.vue';
import UnFollowPopup from './components/un-follow-popup.vue';
import userInfoCardEdit from './components/user-info-card-edit.vue';
import UserInfoPopup from './components/user-info-popup.vue';
import VegetarianPicker from './components/vegetarian-picker.vue';
import makeFriends from '@/uni_modules/project-make-friends/libs/load';

export default {
	components: {
		BirthDatePicker,
		UserInfoPopup,
		userInfoCardEdit,
		VegetarianPicker,
		InterestPopup,
		PersonalIntroductionPopup,
		UnFollowPopup,
		CharacteristicPopup
	},
	data() {
		return {
			loadFinish: false,
			isAgeDateShow: false,
			isBithdayDateShow: false,
			makeFriends,
			// 是否修改素食
			isChangeVegetarian: false,
			// 是否修改出生时间保密
			isChangeAgeSecret: false,

			pageTitle: this.$i18n.zhToGlobal('个人资料'),
			userAccountId: '',
			userAccount: {},
			// 是否是自己
			isSelf: false,
			selfUserAccount: {},
			// 当前年份
			currentYear: new Date().getFullYear(),
			isFollow: false,
			isLogin: false,
			theme:"",
			// 是否准备关注
			isFollowing: false,
			// 是否准备聊天
			isChatting: false,
      interrelation:{
        followCount: 0,
        followedCount: 0,
        mutualFollowCount: 0
      }
		}
	},
	async onLoad(options) {
		this.init(options);
		this.$kt.event.on("userAccount:getSelf", () => {
			console.log("userAccount:getSelf");
			if(this.isSelf){
				this.userAccount = this.$kt.userAccount.getSelf();		
			}
		});
		this.$kt.event.on("login:success", () => {
			this.init(options);
			if(this.isFollowing){
				this.requestFollow();
				return;
			}
			if(this.isChatting){
				setTimeout(()=>{
					this.toChat();
				},300);
				return;
			}
		});


		// this.$refs.userInfoPopup.open(this.userAccount);
	},
	methods: {
    toUserPost(userAccountId){
      this.$kt.router.navTo('/pages/project-make-friends-pages/user-post/user-post?userAccountId='+userAccountId);
    },
    toPostSelf(){
      this.$kt.router.navTo('/pages/project-make-friends-pages/post-self/post-self');
    },
    /**
     * 前往用户关系
     */
    toInterrelation(code){
      this.$kt.router.navTo('/pages/project-make-friends-pages/user-account-interrelation/user-account-interrelation?code='+code+"&userAccountId="+this.userAccountId);
    },
		toChat(){
			this.isChatting = true;
			if(!this.isLogin){
				this.$refs.ktLoginPopup.open();
				return;
			}
			this.isChatting = false;
			if (this.userAccountId == this.selfUserAccount.id) {
				uni.showToast({
					title: this.$i18n.zhToGlobal('不能私信自己'),
					icon: 'none'
				});
				return;
			}
			this.$kt.router.navTo("/pages/project-make-friends-pages/chat-dialog/chat-dialog", {
				userAccountId: this.userAccountId
			});
		},
		loginPopupCloseHandle() {
			setTimeout(()=>{
				this.isLogin = this.$kt.userAccount.getIsLogin();
				if(!this.isLogin){
					this.isFollowing = false;
				}
			},3000);
		},
		async init(options){
			this.theme = uni.getSystemInfoSync().theme;
		this.userAccountId = options.userAccountId;
		this.isSelf = false;
		this.isFollow = false;
		this.isLogin = this.$kt.userAccount.getIsLogin();
		console.log(this.$kt.userAccount.getIsLogin(),"isLogin");
		if(this.$kt.userAccount.getIsLogin()){
			this.selfUserAccount = this.$kt.userAccount.getSelf();
			if (this.userAccountId == this.selfUserAccount.id) {
				this.isSelf = true;
			}
		}

		this.loadFinish = false;
		if (this.isSelf) {
			this.pageTitle = this.$i18n.zhToGlobal('我的资料');
			this.userAccount = this.selfUserAccount;
			await this.getInterrelationSelf();
		} else {
			this.pageTitle = this.$i18n.zhToGlobal('个人资料');
			await this.getUserAccountById();
			await this.getInterrelationByUserAccountId();
			await this.getInterrelationIsFollow();
		}
		this.loadFinish = true;
		},
		unFollow(){
			this.isFollow = false;
			this.getInterrelationByUserAccountId();
			this.getInterrelationIsFollow();
		},
		// /user-interrelation-web/interrelation/isFollow
		async getInterrelationIsFollow() {
			if(!this.isLogin){
				return;
			}
			await this.$request.post('/user-interrelation-web/interrelation/isFollow', {
				data: { userAccountId: this.userAccountId }
			}).then(res => {
				this.isFollow = res.data;
			}).catch(err => {
				uni.showToast({
					title: err.errMsg,
					icon: 'none'
				});
			});
		},
		// /user-interrelation-web/interrelation/getByUserAccountId
		async getInterrelationByUserAccountId() {
			await this.$request.post('/user-interrelation-web/interrelation/getByUserAccountId', {
				data: { userAccountId: this.userAccountId }
			}).then(res => {
				this.interrelation = res.data;
			}).catch(err => {
				uni.showToast({
					title: err.errMsg,
					icon: 'none'
				});
			});
		},
		// /user-interrelation-web/interrelation/getSelf
		async getInterrelationSelf() {
			await this.$request.post('/user-interrelation-web/interrelation/getBySelf').then(res => {
				this.interrelation = res.data;
			}).catch(err => {
				uni.showToast({
					title: err.errMsg,
					icon: 'none'
				});
			});
		},

		requestFollow() {
			this.isFollowing = true;
			if(!this.isLogin){
				this.$refs.ktLoginPopup.open();
				return;
			}
			if (this.userAccountId == this.selfUserAccount.id) {
				uni.showToast({
					title: this.$i18n.zhToGlobal('不能关注自己'),
					icon: 'none'
				});
				return;
			}
			this.isFollow = true;

			this.$request.post('/user-interrelation-web/interrelation/follow', {
				data: { userAccountId: this.userAccountId }
			}).then(res => {
				this.getInterrelationByUserAccountId();
				this.getInterrelationIsFollow();
				this.isFollowing = false;
				// this.$kt.userAccount.requestSelf();
			}).catch(err => {
				uni.showToast({
					title: err.errMsg,
					icon: 'none'
				});
			});
		},
		getInterrelation(userAccount){
			let interrelation = {
				followCount: 0,
				followedCount: 0,
				mutualFollowCount: 0

			}
			if(this.interrelation){
				interrelation = this.interrelation;
				if(!interrelation.mutualFollowCount) {
					interrelation.mutualFollowCount = 0;
				}
				if(!interrelation.followCount) {
					interrelation.followCount = 0;
				}
				if(!interrelation.followedCount) {
					interrelation.followedCount = 0;
				}
			}
			return interrelation;
		},
		// /user-account-web/userAccount/getById
		async getUserAccountById() {

      await this.$kt.userAccount.getById(this.userAccountId).then(res => {
          this.userAccount = res;
      }).catch(err => {
      });
      await this.$kt.userAccount.requestById(this.userAccountId).then(res => {
          this.userAccount = res;
      }).catch(err => {
      });

		},
		openChangeAgeSecret(){
			this.isChangeAgeSecret = true;
		},
		changeAgeSecret(){
			this.userAccount.isAgeSecret = !this.userAccount.isAgeSecret;
			// /project-make-friends-web/userAccount/setAgeSecret
			this.$request.post('/project-make-friends-web/userAccount/setIsAgeSecret', {
				data: { isAgeSecret: this.userAccount.isAgeSecret }
			}).then(res => {
				this.$kt.userAccount.requestSelf();
			})
			this.isChangeAgeSecret = false;
		},
		birthDateConfirm(value){
		},
		/**
		 * 打开兴趣
		 */
		openInterestPopup(){
			this.$refs.interestPopup.open(this.userAccount.interests);
		},
		/**
		 * 打开个人特点
		 */
		openCharacteristicPopup(){
			this.$refs.characteristicPopup.open(this.userAccount.characteristics);
		},
		vegetarianYearSelect(year) {
			console.log(year, "---");
			this.$request.post('/project-make-friends-web/userAccount/setVegetarianStartYear', {
				data: { yearNumber: year }
			}).then(res => {
				this.$kt.userAccount.requestSelf();
			}).catch(err => {
				uni.showToast({
					title: err.errMsg,
					icon: 'none'
				});
			});
		},
		openChangeVegetarian() {
			this.isChangeVegetarian = true;
		},
		changeVegetarian() {
			this.userAccount.vegetarian = !this.userAccount.vegetarian;
			// /project-make-friends-web/userAccount/setVegetarian
			this.$request.post('/project-make-friends-web/userAccount/setVegetarian', {
				data: { vegetarian: this.userAccount.vegetarian }
			}).then(res => {
				this.$kt.userAccount.requestSelf();
			});
			this.isChangeVegetarian = false;
		},
		openVegetarianPicker() {
			console.log("openVegetarianPicker");
			this.$refs.vegetarianPicker.open();
		}

	}
}
</script>

<style lang="scss" scoped>
.icon-img{
	width: 40rpx;
	height: 40rpx;
}
.back {
	height: 100%;
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: -1;
	background-color: #f5f5f5;
}


// 暗黑模式
@media (prefers-color-scheme: dark) {
	.back {
		background-color: #000000;
	}
}

.container {
	padding: 30rpx;
	box-sizing: border-box;
}

.box {
	position: relative;
	background-color: #ffffff;
	border-radius: 30rpx;
	padding: 30rpx;
	box-sizing: border-box;
	margin-bottom: 30rpx;

	.box-title {
		font-size: 30rpx;
		font-weight: bold;
	}
}


.box-data {
	text-align: center;
	margin-top: 10rpx;
	padding: 0 30rpx;

	.box-data-item {
		display: inline-block;
		width: 33%;
		vertical-align: top;

		.box-data-item-num {
			font-size: 30rpx;
			font-weight: bold;
		}

		.box-data-item-text {
			font-size: 24rpx;
			color: #999999;
		}
	}

}

.box-data-2 {
	text-align: left;
	margin-top: 10rpx;
	padding: 10rpx 0 10rpx 0rpx;

	.tag-v {
		background-color: rgba(255, 244, 240, 1);
		display: inline-block;
		vertical-align: top;
		margin-right: 20rpx;
		height: 50rpx;
		line-height: 50rpx;
		margin-bottom: 20rpx;
		padding: 0rpx 20rpx 0rpx 20rpx;
		border-radius: 10rpx;
		font-size: 28rpx;
		color: rgba(245, 172, 54, 1);

	}
}

.tag-btn:active{
	opacity: .7;
}

.box-btn-box {
	text-align: center;
	margin-top: 10rpx;

	.box-btn {
		display: inline-block;
		width: 45%;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #f5f5f5;
		border-radius: 20rpx;
		font-size: 28rpx;
		color: #333333;
		text-align: center;
		margin-right: 10rpx;
	}

	.box-fllow-btn{
		// background-color: #333333;
		background-color: $kt-color-primary;
		color: #ffffff;
	}
}

.box-btn:active{
	opacity: .7;
}

</style>
