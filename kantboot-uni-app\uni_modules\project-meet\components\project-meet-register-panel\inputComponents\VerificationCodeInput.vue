<template>
  <view
      :class="clazz.loginInputBox">
    <view class="icon-box">

      <view class="icon">
        <image class="icon-img"
               :src="$kt.file.byPath('icon/sharp.svg')"
               mode="widthFix">
        </image>
      </view>

    </view>

    <view class="input-box"
          style="width: calc(100% - 180rpx)"
    >
      <input
          autofocus
          @input="inputInput"
          class="input"
          v-model="param.verificationCode"
          :adjust-position="false"
          :placeholder="$i18n.zhToGlobal('验证码')"
          type="text"></input>
    </view>

    <view
        v-if="!sendParam.sending&&sendParam.sendTime<=0"
        @click="sendVerificationCode()"
        class="icon-box icon-box-btn">

      <view class="icon">
        <view style="font-size: 20rpx">
          <image
              :src="$kt.file.byPath('icon/send.svg')"
              mode="widthFix"
              style="width: 50rpx"
              class="icon-img"
          ></image>
        </view>
      </view>


    </view>

    <view
        v-if="sendParam.sendTime>0"
        class="icon-box icon-box-btn">

      <view class="icon">
        <view style="font-size: 28rpx">
          <text>{{ sendParam.sendTime }}</text>
        </view>
      </view>


    </view>

    <view
        v-if="sendParam.sending&&sendParam.sendTime<=0"
        @click="sendVerificationCode()"
        class="icon-box icon-box-btn">

      <view class="icon">
        <view style="font-size: 20rpx">
          <image
              :src="$kt.file.byPath('icon/loading.svg')"
              mode="widthFix"
              style="width: 50rpx"
              class="icon-img icon-img-loading"
          ></image>
        </view>
      </view>


    </view>


  </view>

</template>

<script>
import operate from "../js/operate";
export default {
  props: ["bodyData"],
  data() {
    return {
      clazz:{
        loginInputBox: this.$kt.style.toggleClass("login-input-box"),
      },
      sendParam:{
        sending: false,
        sendTime: 0,
        sendDateTime: 0,
      },
      param: {
        verificationCode: ""
      }
    };
  },
  created() {
    this.initSendParam();
  },
  methods: {
    inputInput(e) {
      this.param.verificationCode = e.detail.value;
      this.$emit("change", this.param);
    },
    sendVerificationCode(){
      if(this.sendParam.sending){
        return;
      }
      if(this.sendParam.sendTime>0){
        return;
      }
      this.sendParam.sending = true;
      operate.sendVerificationCode(this.bodyData).then(res=>{
        this.sendParam.sending = false;
        this.setSendDateTime();
        this.startCountDown(60);
      }).catch(err=>{
        this.sendParam.sending = false;
      });
    },
    startCountDown(sendTime){
      this.sendParam.sendTime = sendTime;
      let inter = setInterval(()=>{
        this.sendParam.sendTime--;
        if(this.sendParam.sendTime<=0){
          clearInterval(inter);
        }
      },1000);
    },
    setSendDateTime(){
      this.$kt.storage.set("login:sendDateTime",new Date().getTime());
    },
    initSendParam(){
      this.sendParam.sending = true;
      let sendDateTime = this.$kt.storage.get("login:sendDateTime");
      if(sendDateTime==null||!sendDateTime){
        this.sendParam.sendTime = 0;
        this.sendParam.sending = false;
        return;
      }
      if(sendDateTime-(-(1000*60))>new Date().getTime()){
        this.sendParam.sendTime = parseInt(60-(new Date().getTime()-sendDateTime)/1000);
        this.sendParam.sending = false;
        this.startCountDown(this.sendParam.sendTime);
        return;
      }
      this.sendParam.sendTime = 0;
      this.sendParam.sending = false;
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../css/loginInput";
</style>
