@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-513d364a {
  padding: 50rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
}
.box .title.data-v-513d364a {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.box .in-box.data-v-513d364a {
  padding: 20rpx;
}
.box .in-box .in-box-input.data-v-513d364a {
  position: relative;
  background-color: #f0f0f0;
  padding: 30rpx;
  border-radius: 20rpx;
}
.box .in-box .in-box-input .in-box-input-icon.data-v-513d364a {
  width: 40rpx;
  height: 40rpx;
  display: inline-block;
  vertical-align: middle;
}
.box .in-box .in-box-input .in-box-input-input.data-v-513d364a {
  position: relative;
  vertical-align: top;
  display: inline-block;
  letter-spacing: 2rpx;
  margin-left: 10rpx;
  width: calc(100% - 100rpx);
  border: none;
  font-size: 28rpx;
  color: #333333;
  z-index: 2;
}
.box .in-box .in-box-input .in-box-input-show-change.data-v-513d364a {
  position: absolute;
  right: 0;
  top: 0;
  width: 100rpx;
  height: 100%;
  opacity: 0.1;
  z-index: 99999;
}
.common-info-text.data-v-513d364a {
  font-size: 28rpx;
  color: #666666;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.close-btn.data-v-513d364a {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #666;
  z-index: 10;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  -webkit-user-select: none;
          user-select: none;
  outline: none;
}
.close-btn.data-v-513d364a:hover,
.close-btn.data-v-513d364a:active {
  background: #f5f5f5;
  color: #666;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  -webkit-transform: none;
          transform: none;
}

