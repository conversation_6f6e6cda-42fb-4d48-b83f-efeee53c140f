<template>
  <kt-box class="form-item"
          :title="$i18n.zhToGlobal('小程序信息')">
    <kt-box class="in-form-item">
      <kt-box
          :title="$i18n.zhToGlobal('小程序头像')"
          class="form-item">
        <kt-image-select
            v-model="fileIdsOfAvatar"
            :count="1"
            file-group-code="fp"
        ></kt-image-select>
        <view

            class="form-item-param">{{"params.fileIdOfAvatar"}}</view>

      </kt-box>

      <kt-box
          :title="$i18n.zhToGlobal('小程序路径')"
          class="form-item">
        <view class="form-item-input">
          <input
              v-model="params.path"
              type="text"
              :placeholder="$i18n.zhToGlobal('输入小程序路径,为空则默认打开首页')"
              class="input"/>
        </view>
        <view class="form-item-param">{{"params.path"}}</view>
      </kt-box>

      <kt-box
          :title="$i18n.zhToGlobal('小程序APPID')"
          class="form-item">
        <view class="form-item-input">
          <input
              v-model="params.appid"
              type="text"
              :placeholder="$i18n.zhToGlobal('输入小程序APPID')"
              class="input"/>
        </view>
        <view class="form-item-param">{{"params.appid"}}</view>
      </kt-box>


      <kt-box
          :title="$i18n.zhToGlobal('小程序原始ID')"
          class="form-item">
        <view class="form-item-input">
          <input
              v-model="params.originalId"
              type="text"
              :placeholder="$i18n.zhToGlobal('输入小程序原始ID')"
              class="input"/>
        </view>
        <view
            v-if="showParams"
            class="form-item-param">{{"params.originalId"}}</view>
      </kt-box>
    </kt-box>
  </kt-box>
</template>

<script>
export default {
  props:{
    showParams: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileIdsOfAvatar: [],
      fileIdsOfQrcode: [],
      params: {
        fileIdOfAvatar: '',
        path: '',
        appid: '',
        originalId: ''
      }
    };
  },
  watch: {
    fileIdsOfAvatar: {
      handler(val) {
        if (val.length === 0) {
          this.params.fileIdOfAvatar = '';
          return;
        }
        this.params.fileIdOfAvatar = val[0];
        this.changeParams();
      },
      immediate: true,
      deep: true
    },
    params: {
      handler(val) {
        this.changeParams();
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.changeParams();
  },
  methods: {
    changeParams() {
      this.$emit('changeParams', this.params);
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../css/kt-link-operation-card.scss";
</style>
