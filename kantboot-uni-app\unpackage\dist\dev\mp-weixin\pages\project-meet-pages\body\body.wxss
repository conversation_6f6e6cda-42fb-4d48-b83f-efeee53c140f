@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.bg.data-v-af4b67ea {
  position: fixed;
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
  z-index: -1;
}
.bg-mode-color-scheme-light.data-v-af4b67ea {
  background-color: #ffffff;
}
.bg-mode-color-scheme-dark.data-v-af4b67ea {
  background-color: #191919;
}
.show.data-v-af4b67ea {
  opacity: 1;
  z-index: 10;
}
.hide.data-v-af4b67ea {
  opacity: 0;
  z-index: -1;
}
.header.data-v-af4b67ea {
  display: none;
}
.header-mode-device-pc.data-v-af4b67ea {
  display: block;
  border-bottom: 1px solid #e5e5e5;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.8);
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  text-align: center;
  z-index: 999999;
}
.view-box-mode-device-pc.data-v-af4b67ea {
  position: fixed;
  width: 1200px;
  height: 100vh;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.view-box-mode-device-pc .view-center.data-v-af4b67ea {
  width: 600px;
}
.view-box-mode-device-pc .view-right.data-v-af4b67ea {
  position: absolute;
  display: inline-block;
  width: 360px;
  height: 100%;
  right: 0;
  top: 0;
  z-index: 10;
}
.view-box-mode-device-pc .view-right-mode-color-scheme-light.data-v-af4b67ea {
  background-color: #ffffff;
}
.view-box-mode-device-pc .view-right-mode-color-scheme-dark.data-v-af4b67ea {
  background-color: #191919;
}
.view-box-meet-mode-device-pc.data-v-af4b67ea {
  position: fixed;
  width: 1200px;
  height: 100vh;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.view-box-meet-mode-device-pc .view-center.data-v-af4b67ea {
  width: 960px;
}
.view-box-meet-mode-device-pc .view-right.data-v-af4b67ea {
  position: absolute;
  display: inline-block;
  width: 5px;
  height: 100%;
  right: 0;
  top: 0;
  z-index: 99;
}
.view-box-meet-mode-device-pc .view-right-mode-color-scheme-light.data-v-af4b67ea {
  background-color: #ffffff;
}
.view-box-meet-mode-device-pc .view-right-mode-color-scheme-dark.data-v-af4b67ea {
  background-color: #191919;
}
.bg-self-banned.data-v-af4b67ea {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  z-index: 9999999;
}
.bg-self-banned .box1.data-v-af4b67ea {
  position: absolute;
  padding: 60rpx;
  box-sizing: border-box;
  left: 0;
  width: 100%;
  top: 100rpx;
}
.bg-self-banned .box1 .logo.data-v-af4b67ea {
  width: 200px;
  height: 200px;
  display: block;
  margin: 0 auto;
}
.bg-self-banned .box1 .text.data-v-af4b67ea {
  text-align: center;
  font-size: 32rpx;
  color: #666666;
  margin-top: 20rpx;
}
