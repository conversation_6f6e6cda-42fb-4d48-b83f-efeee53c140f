<template>
  <kt-box class="form-item"
          :title="$i18n.zhToGlobal('跳转网页配置')">
    <kt-box class="in-form-item">
      <kt-box
          :title="$i18n.zhToGlobal('H5链接')"
          class="form-item">
        <view class="form-item-input">
          <input
              type="text"
              placeholder="输入H5链接"
              v-model="params.url"
              class="input"/>
        </view>
        <view
            v-if="showParams"
            class="form-item-param">{{"params.url"}}</view>

      </kt-box>
    </kt-box>
  </kt-box>
</template>

<script>
export default {
  props: {
    showParams: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      params:{
        url: ''
      }
    };
  },
  watch: {
    params: {
      handler(val) {
        this.changeParams();
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.changeParams();
  },
  methods: {
    changeParams() {
      this.$emit('changeParams', this.params);
    }
  },
}
</script>

<style lang="scss" scoped>
@import "../css/kt-link-operation-card.scss";
</style>
