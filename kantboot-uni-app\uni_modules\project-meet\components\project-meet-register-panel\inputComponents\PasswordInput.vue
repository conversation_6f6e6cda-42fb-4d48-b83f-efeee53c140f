<template>
  <view
      :class="clazz.loginInputBox">
    <view class="icon-box">

      <view class="icon">
        <image class="icon-img" :src="$kt.file.byPath('icon/key.svg')" mode="widthFix"></image>
      </view>

    </view>
    <view
        style="width: calc(100% - 180rpx)"
        class="input-box">
      <input
          @blur="addUserLog"
          @input="changePassword"
          class="input"
          :adjust-position="false"
          :placeholder="$i18n.zhToGlobal('密码')"
          :type="passwordType"></input>

    </view>

    <view
        v-if="passwordType==='password'"
        @click="passwordType = 'text'"

        class="icon-box">

      <view class="icon">
        <view style="font-size: 20rpx">
          <image
              :src="$kt.file.byPath('icon/eye.svg')"
              mode="widthFix"
              class="icon-img"
          ></image>
        </view>
      </view>

    </view>

    <view
        v-if="passwordType==='text'"
        @click="passwordType = 'password'"
        class="icon-box">

      <view class="icon">
        <view style="font-size: 20rpx">
          <image
              :src="$kt.file.byPath('icon/eyeOff.svg')"
              mode="widthFix"
              class="icon-img"
          ></image>
        </view>
      </view>

    </view>

  </view>
</template>

<script>
export default {
  props: ["bodyData"],
  data() {
    return {
      clazz:{
        loginInputBox: this.$kt.style.toggleClass("login-input-box")
      },
      param: {
        password: "",
      },
      passwordType: "password"
    };
  },
  methods:{
    addUserLog(){
      this.$emit("addUserLog");
    },
    changePassword(e){
      this.param.password = e.detail.value
      this.$emit("change",this.param)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../css/loginInput";
</style>
