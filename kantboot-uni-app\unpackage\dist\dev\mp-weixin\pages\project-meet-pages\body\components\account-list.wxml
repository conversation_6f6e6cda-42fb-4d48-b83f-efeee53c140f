<view class="data-v-e1efdd84"><view class="box data-v-e1efdd84"><block wx:for="{{subAccounts}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['selectAccount',['$0'],[[['subAccounts','',__i0__]]]]]]]}}" class="{{['box-item','data-v-e1efdd84',(item.id===selected)?'box-item-selected':'']}}" bindtap="__e">{{''+item.username+''}}<block wx:if="{{item.messageUnreadCount}}"><view class="unread-count data-v-e1efdd84">{{item.messageUnreadCount+''}}</view></block></view></block></view></view>