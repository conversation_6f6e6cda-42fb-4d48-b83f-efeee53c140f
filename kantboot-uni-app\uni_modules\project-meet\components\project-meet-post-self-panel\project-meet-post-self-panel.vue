<template>
  <view>
    <kt-community-post-panel
        :height="'calc(100vh - '+headerHeight+'px)'"
        @cardClick="cardClick"
        @cardDotClick="cardDotClick"
        :is-forbid-forward="false"
        :isForbidCollect="false"
        is-self
        show-audit-status
    >
    </kt-community-post-panel>

    <kt-community-post-operate-popup
        :remove-text="$kt.i18n.zhToGlobal('删除')"
        :report-text="$kt.i18n.zhToGlobal('举报')"
        :has-permission-setting="false"
        ref="communityPostOperatePopup">
    </kt-community-post-operate-popup>

  </view>
</template>

<script>
export default {
  props:{
    headerHeight:{
      type: Number,
      default: 0
    }
  },
  data() {
    return {
    };
  },
  mounted() {
  },
  methods: {
    cardDotClick(post){
      if(!this.$kt.userAccount.getIsLogin()){
        this.$refs.ktLoginPopup.open();
        return;
      }
      this.$refs.communityPostOperatePopup.open(post);
    },
    cardClick(post){
      // 审核必须通过才能查看
      if(post.auditStatus !== "pass"){
        return;
      }
      this.$kt.router.navTo("/pages/project-acm/post-detail/post-detail",{
        postId: post.id
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.bg{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  // 渐变
  background: linear-gradient(to bottom, #ffffff 300rpx, #f0f0f0 100%);
}
</style>
