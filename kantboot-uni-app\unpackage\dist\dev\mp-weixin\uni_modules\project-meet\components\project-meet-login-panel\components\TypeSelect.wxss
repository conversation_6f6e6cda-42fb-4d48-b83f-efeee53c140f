@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.type-box.data-v-50ce7c50 {
  margin-top: 10rpx;
  position: relative;
  margin-bottom: 10rpx;
}
.type-box .type-box-btn.data-v-50ce7c50 {
  position: relative;
  display: inline-block;
  font-size: 28rpx;
  text-align: center;
  width: 50%;
  color: #999999;
}
.type-box .type-box-btn .type-box-line.data-v-50ce7c50 {
  position: absolute;
  width: 100rpx;
  height: 5rpx;
  background-color: #000000;
  bottom: -10rpx;
  border-radius: 10rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.type-box .type-box-btn.data-v-50ce7c50:active {
  opacity: 0.8;
}
.type-box .type-box-btn-selected.data-v-50ce7c50 {
  color: #000000;
}
.type-box-mode-color-scheme-light.data-v-50ce7c50 {
  background-color: #fff;
  color: #000;
}
.type-box-mode-color-scheme-light .type-box-btn-selected.data-v-50ce7c50 {
  color: #000000;
}
.type-box-mode-color-scheme-light .type-box-line.data-v-50ce7c50 {
  background-color: #000000;
}
.type-box-mode-color-scheme-dark.data-v-50ce7c50 {
  color: #fff;
}
.type-box-mode-color-scheme-dark .type-box-btn.data-v-50ce7c50 {
  color: #fff;
}
.type-box-mode-color-scheme-dark .type-box-btn .type-box-line.data-v-50ce7c50 {
  background-color: #fff;
}
.type-box-mode-color-scheme-dark .type-box-btn-selected.data-v-50ce7c50 {
  color: #fff;
}
