<view class="data-v-0bd3b587"><kt-popup vue-id="6ea37c5f-1" data-ref="ktPopup" class="data-v-0bd3b587 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['data-v-0bd3b587',clazz.box]}}"><view class="box-title data-v-0bd3b587">{{''+$root.g0}}</view><view class="pop_class data-v-0bd3b587"><block wx:for="{{heightArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['heightSelect',['$0'],[[['heightArr','',index]]]]]]]}}" class="{{['class_item','data-v-0bd3b587',(value===item.value)?'class_item-active':'']}}" bindtap="__e">{{item.value}}</view></block></view></view></kt-popup></view>