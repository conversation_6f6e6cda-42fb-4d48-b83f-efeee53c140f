<template>
  <view :class="clazz.card"
        :style="{
            borderRadius: borderRadius,
            background: background ? background : null,
        }"
  >

    <view v-show="isLoading">
      <view style="height: 100rpx"></view>

      <u-loading-icon
          v-if="isLoading"
          mode="circle"
          :size="50"
      ></u-loading-icon>

    </view>


    <view v-show="!isLoading">
      <view
      v-show="showAuditStatus"
      >
        <view class="dot"
        :class="{
            'dot-audit-auditing': postData.auditStatus === 'auditing',
            'dot-audit-pass': postData.auditStatus === 'pass',
            'dot-audit-fail': postData.auditStatus === 'fail'
        }"></view>
        <view class="dot-text">
          <view
              v-if="postData.auditStatus === 'auditing'"
              class="dot-text-auditing"
          >{{$i18n.zhToGlobal("审核中")}}</view>
          <view
              v-if="postData.auditStatus === 'pass'"
              class="dot-text-pass"
          >{{$i18n.zhToGlobal("审核通过")}}</view>
          <view
              v-if="postData.auditStatus === 'fail'"
              class="dot-text-fail"
          >{{$i18n.zhToGlobal("审核不通过")}}</view>
        </view>
        <view class="dot-fail-reason">
          <view
              v-if="postData.auditStatus === 'fail'"
              class="dot-text-fail"
          >{{$i18n.zhToGlobal("原因")}}：{{postData.auditFailReason}}</view>
        </view>
      </view>

      <view
          class="in-user-card">
        <view
            @click.stop="userClick()"
            class="in-user-card-avatar-box">
          <image
              class="in-user-card-avatar"
              v-if="userAccountOfUploader.fileIdOfAvatar"
              :src="$kt.file.visit(userAccountOfUploader.fileIdOfAvatar)"
              mode="aspectFit"
          ></image>
          <image
              v-else
              class="in-user-card-avatar"
              :src="$kt.file.byPath('image/logo.png')"
              mode="aspectFit"></image>
        </view>
        <view class="in-user-card-info-box">
          <view
              class="in-user-card-info-box-nickname"
          >{{ userAccountOfUploader.nickname }}
            <view
                v-if="isSelf"
                class="tag">
              {{$i18n.zhToGlobal("我")}}
            </view>
            <view
              v-if="userAccountOfUploader.isInnerStaff"
              class="tag">
              {{$i18n.zhToGlobal("内部员工")}}
            </view>
            <view
                v-if="false"
            class="f-btn"
            >{{"+"}}{{" "}}{{$i18n.zhToGlobal("关注")}}</view>
            <image
                v-if="hasDot"
                @click.stop="dotClick()"
                class="card-three-dot"
                :src="$kt.file.byPath('icon/threeDot.svg')"
            ></image>
          </view>

          <view
              class="in-user-card-info-box-time"
          >{{ $kt.date.toReadable(postData.gmtCreate) }}
          </view>
        </view>

      </view>

      <view class="in-card">
        <kt-format
            @postUserCardClick="postUserCardClick"
            @postCardClick="postCardClick"
            :data="postData.items"
        ></kt-format>
      </view>
      <!-- !isRelationshipSelfLoading&& -->
      <view
          v-if="hasBottomOperation"
          class="in-footer-card">
        <!-- 点赞 -->
        <view
            v-if="!postData.isForbidLike"
            @click.stop="toLike()"
            class="in-footer-card-item">
          <image
              v-if="relationshipSelf.isLike"
              class="in-footer-card-item-icon in-footer-card-item-icon-active"
              :src="$kt.file.byPath('icon/likeActive.svg')"></image>
          <image
              v-else
              class="in-footer-card-item-icon"
              :src="$kt.file.byPath('icon/like.svg')"></image>
          <view class="in-footer-card-item-number">
            {{ getRelationship(postData).likeCount }}
          </view>
        </view>
        <!-- 评论 -->
        <view
            v-if="!postData.isForbidComment"
            class="in-footer-card-item">
          <image
              class="in-footer-card-item-icon"
              :src="$kt.file.byPath('icon/comment.svg')"></image>
          <view class="in-footer-card-item-number">
            {{ getRelationship(postData).commentCount }}
          </view>
        </view>
        <!-- 收藏 -->
        <view
            v-if="!postData.isForbidCollect&&isForbidCollect"
            class="in-footer-card-item" @click.stop="toCollect()">
          <image
              v-if="relationshipSelf.isCollect"
              class="in-footer-card-item-icon in-footer-card-item-icon-active"
              :src="$kt.file.byPath('icon/collectionActive.svg')"></image>
          <image
              v-else
              class="in-footer-card-item-icon"
              :src="$kt.file.byPath('icon/collection.svg')"></image>
          <view class="in-footer-card-item-number">
            {{ getRelationship(postData).collectCount }}
          </view>
        </view>

        <view
            v-if="!postData.isForbidForward&&isForbidForward&&isNoHasPostShareType(postData)"
            @click.stop="toForward()"
            class="in-footer-card-item">
          <image
              class="in-footer-card-item-icon"
              :src="$kt.file.byPath('icon/forward.svg')"></image>
        </view>

      </view>


      <view class="in-footer-card">

        <slot name="bottom"
        :postId="postData.id"
              :post="postData"
        ></slot>

      </view>

    </view>


    <kt-community-post-share-popup
    ref="communityPostSharePopup"
    ></kt-community-post-share-popup>

  </view>
</template>

<script>
import communityApi from "@/uni_modules/kt-community/libs/api";
import communityEvent from "@/uni_modules/kt-community/libs/event";

export default {
  props: {
    background:{
      type: String,
      default: '#fff'
    },
    /**
     * 是否显示审核状态
     */
    showAuditStatus: {
      type: Boolean,
      default: false
    },
    /**
     * 帖子ID
     */
    postId: {
      type: String,
      default: ''
    },
    /**
     * 帖子
     */
    post: {
      type: Object,
      default: () => {
        return {
          /**
           * 用户的账号ID
           */
          userAccountIdOfUploader: '',
          /**
           * 帖子ID
           */
          id: '',
          /**
           * 帖子创建时间
           */
          gmtCreate: '',
          /**
           * 帖子内容，须遵循 kt-format
           */
          items: []
        }
      }
    },
    /**
     * 卡片弧度
     */
    borderRadius: {
      type: String,
      default: '20rpx'
    },
    /**
     * 背景是否透明
     */
    transparent: {
      type: Boolean,
      default: false
    },
    /**
     * 是否显示底部
     */
    hasBottomOperation: {
      type: Boolean,
      default: true
    },
    hasDot: {
      type: Boolean,
      default: true
    },
    isForbidForward: {
      type: Boolean,
      default: true
    },
    isForbidCollect: {
      type: Boolean,
      default: true
    },
    // 转发默认点击
    forwardDefaultClick: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      clazz: {
        card: this.$kt.style.toggleClass("card"),
      },
      api: communityApi,
      event: communityEvent,
      /**
       * 帖子发布者的的用户账号信息
       */
      userAccountOfUploader: {
        nickname: '',
        avatarIdOfFile: ''
      },
      /**
       * 当前用户与帖子的关系
       */
      relationshipSelf: {
        /**
         * 是否点赞
         */
        isLike: false,
        /**
         * 是否收藏
         */
        isCollect: false,
      },
      /**
       * 帖子是否正在点赞的状态（为了防止重复点击）
       */
      isLiking: false,
      /**
       * 帖子是否正在收藏中（为了防止重复点击）
       */
      isCollecting: false,
      /**
       * 是否正在加载中
       */
      isLoading: false,
      /**
       * 是否正在加载用户关系
       */
      isRelationshipSelfLoading: false,
      /**
       * 是否是当前用户的帖子
       */
      isSelf: false,
      postData:{}
    };
  },
  watch: {
    postId: {
      handler(newVal) {
        if (newVal) {
          // 如果有postId，则根据postId获取帖子
          this.getPostById(this.postId);
        }
      },
      immediate: true
    },
    post: {
      handler(newVal) {
        this.getRelationshipSelf();
        this.postData = newVal;
      },
      immediate: true
    },

  },
  async created() {
    if (this.postId) {
      this.isLoading = true;
      // 如果有postId，则根据postId获取帖子
      await this.getPostById(this.postId);

      this.isLoading = false;
    }

    this.$kt.userAccount.getById(this.post.userAccountIdOfUploader).then(res => {
      this.userAccountOfUploader = res;
      let self = this.$kt.userAccount.getSelf();
      let isLogin = this.$kt.userAccount.getIsLogin();
      if (isLogin && self && self.id === this.post.userAccountIdOfUploader) {
        this.isSelf = true;
      } else {
        this.isSelf = false;
      }
    }).catch(err => {
    });

    // 监听当前对帖子的点赞或收藏的关系状态改变
    this.event.onRelationshipSelfChange((data) => {
      if (data.postId + "" === this.post.id + "") {
        this.post = data.post;
        this.postData = data.post;
        this.relationshipSelf = data.relationshipSelf;
        try {
          // 重新渲染
          this.$forceUpdate();
        } catch (e) {
        }
      }
    });
  },

  mounted() {
  },
  methods: {
    isNoHasPostShareType(post){
      for (let i = 0; i < post.items.length; i++) {
        if (post.items[i].type === "postShare") {
          return false;
        }
      }
      return true;
    },
    postUserCardClick(userAccount) {
      // 触发用户卡片点击事件
      this.$emit("userClick", userAccount);
    },
    postCardClick(post) {
      // 触发帖子卡片点击事件
      this.$emit("postClick", post);
    },
    userClick() {
      // 触发用户卡片点击事件
      this.$emit("userClick", this.userAccountOfUploader);
    },
    toForward() {
      if(this.forwardDefaultClick){
        this.$refs.communityPostSharePopup.open(this.postData.id);
      }
      this.$emit("forwardClick", this.postData);
    },
    /**
     * 右上角的点击事件
     */
    dotClick() {
      this.$emit("dotClick", this.post)
    },

    /**
     * 点赞
     */
    toLike() {
      if (this.isLiking) {
        // 正在点赞中
        return;
      }

      // 反转当前用户对帖子的点赞状态（为了快速显示出点赞效果，在请求后端前，先反转点赞状态）
      this.relationshipSelf.isLike = !this.relationshipSelf.isLike;
      // 获取点赞的参数
      let requestParams = {
        // 获取帖子的ID
        postId: this.post.id,
        // 获取当前用户对帖子的点赞状态
        isLike: this.relationshipSelf.isLike
      }



      // 设置正在点赞中
      this.isLiking = true;
      this.api.toPostLike(requestParams).then(res => {
        // 重新获取当前用户与帖子的关系
        this.getRelationshipSelf();
        // 重新获取帖子
        this.getPostById(this.post.id);
        // 设置为不在点赞中
        this.isLiking = false;
      }).catch(err => {
        // 设置为不在点赞中
        this.isLiking = false;
        // 反转回当前用户对帖子的点赞状态
        this.relationshipSelf.isLike = !this.relationshipSelf.isLike;
        // 该处对应的错误提示
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        });
      });
    },
    /**
     * 收藏
     */
    toCollect() {
      if (this.isCollecting) {
        // 正在收藏中
        return;
      }

      // 设置正在收藏中
      this.isCollecting = true;

      // 反转当前用户对帖子的收藏状态（为了快速显示出收藏效果，在请求后端前，先反转收藏状态）
      this.relationshipSelf.isCollect = !this.relationshipSelf.isCollect;
      // 获取收藏的参数
      let requestParams = {
        // 获取帖子的ID
        postId: this.post.id,
        // 获取当前用户对帖子的收藏状态
        isCollect: this.relationshipSelf.isCollect
      }
      this.api.toPostCollect(requestParams).then(res => {
        // 重新获取当前用户与帖子的关系
        this.getRelationshipSelf();
        // 重新获取帖子
        this.getPostById(this.post.id);
        // 设置为不在收藏中
        this.isCollecting = false;
      }).catch(err => {
        // 设置为不在收藏中
        this.isCollecting = false;
        // 反转回当前用户对帖子的收藏状态
        this.relationshipSelf.isCollect = !this.relationshipSelf.isCollect;
        // 该处对应的错误提示
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        });
      });

    },

    // 获取关系
    getRelationshipSelf() {
      // 如果没有传入postId，则使用当前post的id
      if (!this.hasBottomOperation) {
        // 如果不显示操作按钮，则不获取关系
        return;
      }

      if (this.isRelationshipSelfLoading) {
        return;
      }

      if(!this.post.id){
        return;
      }

      this.isRelationshipSelfLoading = true;

      this.api.getPostRelationshipBySelf({
        postId: this.post.id
      }).then(res => {
        // 获取当前用户与帖子的关系
        this.relationshipSelf = res.data;
        if (!this.relationshipSelf.isCollect) {
          this.relationshipSelf.isCollect = false;
        }
        if (!this.relationshipSelf.isLike) {
          this.relationshipSelf.isLike = false;
        }

        // 当前事件参数
        let eventParams = {
          postId: this.post.id,
          post: this.post,
          relationshipSelf: this.relationshipSelf
        };

        // 触发帖子关系变动状态
        this.event.emitRelationshipSelfChange(eventParams);

        this.isRelationshipSelfLoading = false;
      }).catch(err => {
        this.isRelationshipSelfLoading = false;
      });

    },
    // 根据id获取帖子
    async getPostById(id) {
      if (!id) {
        // 如果没有传入id，则使用当前post的id
        id = this.post.id;
      }

      await this.api.getPostById({
        id
      }).then((res) => {
        this.post = res.data;
        this.postData = res.data;
        try{
          // 重新渲染
          this.$forceUpdate();
        }catch (e) {

        }
        let self = this.$kt.userAccount.getSelf();
        let isLogin = this.$kt.userAccount.getIsLogin();
        if (isLogin && self && self.id === this.post.userAccountIdOfUploader) {
          this.isSelf = true;
        } else {
          this.isSelf = false;
        }
        this.$kt.userAccount.getById(this.post.userAccountIdOfUploader).then(res => {
          this.userAccountOfUploader = res;
        }).catch(err => {
        });
        this.getRelationshipSelf();
      });


    },

    /**
     * 获取帖子与当前用户的关系
     * @param post 帖子
     * @returns {
     *           collectCount: number,
     *           likeCount: number,
     *           commentCount: number
     *          }
     */
    getRelationship(post) {
      let relationship = {
        // 点赞数量
        likeCount: 0,
        // 评论数量
        commentCount: 0,
        // 收藏数量
        collectCount: 0,
      };
      if (post.relationship) {
        if (post.relationship.likeCount) {
          relationship.likeCount = post.relationship.likeCount;
        }
        if (post.relationship.commentCount) {
          relationship.commentCount = post.relationship.commentCount;
        }
        if (post.relationship.collectCount) {
          relationship.collectCount = post.relationship.collectCount;
        }
      }
      return relationship;
    },
  }
}
</script>

<style lang="scss" scoped>
.card {
  position: relative;

  .card-three-dot {
    display: inline-block;
    width: 30rpx;
    height: 30rpx;
    margin-right: -30rpx;
    float: right;
  }

  .card-three-dot:active {
    opacity: 0.5;
  }
}

.in-user-card {
  position: relative;
  text-align: left;
  margin: 10rpx;


  .in-user-card-avatar-box {
    display: inline-block;
    width: 75rpx;
    height: 75rpx;
    border-radius: 50%;

    .in-user-card-avatar {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }

  .in-user-card-info-box {
    position: relative;
    display: inline-block;
    margin-left: 20rpx;
    width: calc(100% - 95rpx);
    vertical-align: top;


    .in-user-card-info-box-nickname {
      font-size: 28rpx;
    }

    .in-user-card-info-box-time {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.card {
  //background-color: #fff;
  padding: 30rpx;
  border-radius: 30rpx;
}

.in-card {
  width: 100%;
  text-align: left;
}

.in-card-content-box-text {
  font-size: 32rpx;
  color: #333;
  margin: 10rpx 0;
  letter-spacing: 2rpx;
}

.in-footer-card {
  text-align: left;
  margin-top: 20rpx;

  .in-footer-card-item {
    display: inline-block;
    margin-right: 20rpx;

    .in-footer-card-item-icon {
      display: inline-block;
      width: 30rpx;
      height: 30rpx;
      margin-right: 10rpx;
    }

    .in-footer-card-item-number {
      display: inline-block;
      font-size: 28rpx;
      color: #333;
      vertical-align: top;
      margin-right: 10rpx;
    }
  }
}

.in-footer-footer-card {
  margin-top: 20rpx;
  padding: 30rpx;
  border: 1px solid #eee;
  border-radius: 20rpx;

  .in-footer-footer-card-title {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
  }
}


.tag {
  display: inline-block;
  background-color: #eee;
  color: #333;
  font-size: 24rpx;
  padding: 2rpx 15rpx 2rpx 15rpx;
  border-radius: 20rpx;
  margin-left: 10rpx;
}

.card-mode-color-scheme-dark {
  background-color: #212932;

  .card-three-dot {
    filter: invert(1);
  }

  .in-card {
    color: #e9e9e9;
  }

  .in-card-content-box-text {
    color: #e9e9e9;
  }

  .in-user-card {
    .in-user-card-info-box {


      .in-user-card-info-box-nickname {
        color: #e9e9e9;
      }
    }
  }


  .in-footer-card {

    .in-footer-card-item {

      .in-footer-card-item-icon {
        // 颜色反转
        filter: invert(1);
      }

      .in-footer-card-item-number {
        color: #F8F8F8;
      }
    }
  }
}


.dot {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  margin-top: 15rpx;
}

.dot-audit-auditing {
  background-color: #FF9800;
}

.dot-audit-pass {
  background-color: #4CAF50;
}

.dot-audit-fail {
  background-color: #F44336;
}

.dot-text {
  position: relative;
  display: inline-block;
  vertical-align: top;
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;

  .dot-text-auditing {
    color: #FF9800;
  }

  .dot-text-pass {
    color: #4CAF50;
  }

  .dot-text-fail {
    color: #F44336;
  }
}

.dot-fail-reason{
  position: relative;
  display: inline-block;
  vertical-align: top;
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
  float: right;
  margin-top: -5rpx;
}

.f-btn{
  position: absolute;
  border: 2px solid #333;
  color: #333;
  border-radius: 30rpx;
  padding: 5rpx 20rpx;
  font-size: 24rpx;
  top:-10rpx;
  right:10rpx;
}

.f-btn:active{
  opacity: 0.5;
}

.card-mode-device-pc{
  .f-btn{
    cursor: pointer;
  }
}
</style>
