<template>
  <view>
    <kt-popup
    ref="ktPopup"
    @close="close" @confirm="confirm">
      <view :class="clazz.popup">
        <view class="popup-title">{{ $i18n.zhToGlobal("基本资料") }}</view>
        <view
            v-if="false"
            class="picker">
          <view
              class="bl-box">
            <view style="text-align: center;">
              <!-- #ifndef MP-WEIXIN -->
              <view @click="selectAvatar()" style="display: inline-block;position: relative">
                <kt-avatar
                    class="avatar"
                    size="220rpx"
                    :src="viewSrc?viewSrc:$kt.file.visit(userAccount.fileIdOfAvatar)"
                ></kt-avatar>
              </view>
              <!-- #endif -->

              <!-- #ifdef MP-WEIXIN -->
              <view style="display: inline-block;position: relative">
                <kt-avatar
                    class="avatar"
                    size="220rpx"
                    :src="viewSrc?viewSrc:$kt.file.visit(userAccount.fileIdOfAvatar)"
                ></kt-avatar>
                <button
                    open-type="chooseAvatar"
                    @chooseavatar="chooseAvatarInMpWeixin"
                    style="width: calc(100% + 10rpx);height: 100%;position: absolute;top:0;left:0;opacity: 0"
                >微信头像
                </button>
              </view>
              <!-- #endif -->

            </view>
            <view style="height: 30rpx;"></view>
            <!-- 昵称 -->
            <view
                v-if="false"
                style="padding: 0 50rpx 0 50rpx;box-sizing: border-box;">
              <input
                  disabled
                  class="input nickname-input"
                  @blur="nicknameBlur"
                  @focus="nicknameFocus"
                  type="text"
                  :adjust-position="false"
                  :placeholder="$i18n.zhToGlobal('昵称')"
                  v-model="userAccount.nickname"
              ></input>


            </view>
          </view>
          <view
              v-if="false"
              style="height: 30px;"></view>
          <!-- 性别 -->
          <view
          v-if="false"
          >
            <view class="gender-box">
              <view class="gender-item" :class="{'gender-item-selected':userAccount.genderCode=='male'}"
                    :style="userAccount.genderCode=='male'?'background-color: #409EFF;':''"
                    @click="userAccount.genderCode='male'">{{ $i18n.zhToGlobal("男") }}
              </view>
              <view class="gender-item" :class="{'gender-item-selected':userAccount.genderCode=='female'}"
                    :style="userAccount.genderCode=='female'?'background-color: rgba(255,0,0,.6);':''"
                    @click="userAccount.genderCode='female'">{{ $i18n.zhToGlobal("女") }}
              </view>
            </view>
          </view>
          <view
              v-if="!isNicknameFocus"
              style="height: 30px;"></view>
          <!-- 个性签名 -->
          <view
              v-if="!isNicknameFocus"
              style="padding: 0 50rpx 0 50rpx;">
            <view class="input-box" style="position: relative">
              <!-- #ifndef MP-WEIXIN -->
              <textarea maxlength="100"
                                :adjust-position="false"
                                class="input textarea"
                                type="textarea"
                                :placeholder="$i18n.zhToGlobal('个性签名')"
                                v-model="userAccount.introduction"></textarea>
              <!-- #endif -->
              <!-- #ifdef MP-WEIXIN -->
              <textarea
                  v-if="false"
                  maxlength="100"
                        :adjust-position="false"
                        class="input textarea"
                        type="nickname"
                        :placeholder="$i18n.zhToGlobal('个性签名')"
                        v-model="userAccount.introduction"></textarea>
              <!-- #endif -->

            </view>
          </view>

        </view>

        <view
        class="picker"
        style="text-align: left"
        >
          <scroll-view
          style="height: calc(100vh - 600rpx);"
          scroll-y
          >
            <project-meet-set-user-info-panel
            :has-submit-button="false"
            @change="changeUserInfo"
            ref="setUserInfoPanel"
            ></project-meet-set-user-info-panel>

          </scroll-view>
        </view>


        <view style="height: 10px;"></view>
        <kt-button
            v-if="!isNicknameFocus"
            @click="submit()"
            ref="confirmBtn"
        >{{ $i18n.zhToGlobal("确定") }}
        </kt-button>
        <view style="height: 10px;"></view>
        <kt-keyboard-size
            ref="keyboardSize"
        ></kt-keyboard-size>
      </view>
    </kt-popup>
  </view>

</template>

<script>
import KtPopup from '../../../../uni_modules/kantboot/components/kt-popup/kt-popup.vue';

export default {
  components: {KtPopup},
  data() {
    return {
      show: false,
      userAccount: {},
      viewSrc: '',
      isNicknameFocus: false,
      clazz:{
        popup:this.$kt.style.toggleClass("popup")
      }
    }
  },
  mounted() {
  },
  methods: {
    changeUserInfo(e){
      this.userAccount = e;
    },
    nicknameBlur() {
      this.isNicknameFocus = false;
    },
    nicknameFocus() {
      if( this.$kt.style.detectDeviceType() !== "pc"){
        this.isNicknameFocus = true;

      }
    },
    selectAvatar() {
      console.log('selectAvatar');
      // this.$kt.image.toImageClip().then(res=>{
      // }).catch(err=>{
      // });
      // uniapp选择图片
      uni.chooseImage({
        count: 1, //默认9
        success: (res) => {
          // console.log(res);
          // this.imgList = res.tempFilePaths;
          // console.log(this.imgList);
          console.log(res.tempFilePaths[0]);
          this.$kt.image.toImageClip(res.tempFilePaths[0], 300, 300).then(res => {
            this.viewSrc = res;
          }).catch(err => {
          });

        }
      })
    },
    chooseAvatarInMpWeixin(e) {
      this.viewSrc = e.detail.avatarUrl;
    },
    async submit() {
      this.$refs.confirmBtn.loading(null, 999999)
      if (this.viewSrc) {
        await new Promise((resolve, reject) => {
          this.$request.uploadFile(
              {
                // filePath:this.viewSrc,
                data: {
                  file: this.viewSrc,
                  groupCode: 'userAvatar',
                },
                stateSuccess: res => {
                  console.log(res);
                  this.userAccount.fileIdOfAvatar = res.data.id;
                  resolve("");
                },
                stateError: err => {
                  console.log(err);
                  reject("");
                }
              });
        });

      }
      await this.$request.post('/project-meet-web/userAccount/setUserInfo', {
        data: this.userAccount
      }).then(res => {
        this.$kt.userAccount.requestSelf();
        this.$refs.confirmBtn.success(res.msg);
        this.close();
      }).catch(err => {
        this.$refs.confirmBtn.error(res.errMsg);
      })
    },
    open(userAccount) {
      this.viewSrc = '';
      this.userAccount = JSON.parse(JSON.stringify(userAccount));
      if(!this.userAccount.interestsIds){
        this.userAccount.interestsIds = [];
      }
      if(!this.userAccount.expectedRelationshipIds){
        this.userAccount.expectedRelationshipIds = [];
      }
      this.show = true;
      setTimeout(()=>{
        this.$refs.setUserInfoPanel.setBodyData(this.userAccount);
      },100);

      this.$refs.ktPopup.open();
    },
    close() {
      this.show = false;
      this.$refs.ktPopup.close();
    },
    confirm() {
      this.close();
      this.$emit('confirm', this.selectedYear);
    },
    bindYearChange(e) {
      this.selectedYearIndex = e.detail.value;
      this.selectedYear = this.years[this.selectedYearIndex];
    }
  }
}
</script>

<style lang="scss" scoped>
.avatar:active {
  transform: scale(0.9);
}

.popup {
  padding: 20rpx 40rpx 20rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
}

.popup-title {
  padding: 20rpx;
  font-size: 34rpx;
  font-weight: bold;
  text-align: left;
  letter-spacing: 2rpx;
}

.picker {
  text-align: center;
  padding: 10rpx;
  box-sizing: border-box;
}

.bl-box {
  text-align: left;
  width: 100%;

  .bl-box-item {
    display: inline-block;
    margin: 10rpx;
    padding: 28rpx;
    border-radius: 20rpx;
    color: #333;
    box-sizing: border-box;
    background-color: #f5f5f5;

  }

  .bl-box-item-selected {
    background-color: #333;
    color: #fff;
  }
}

.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  font-size: 28rpx;
  text-align: center;
  letter-spacing: 2rpx;
}

.textarea {
  width: 100%;
  min-height: 150rpx;
  padding: 20rpx;
  box-sizing: border-box;
  text-align: left;
}

.gender-box {
  .gender-item {
    padding: 20rpx;
    box-sizing: border-box;
    border-radius: 30rpx;
    display: inline-block;
    width: calc(50% - 40rpx);
    color: #333;
    margin-left: 20rpx;
    background-color: #f5f5f5;
  }

  .gender-item-selected {
    color: #fff;
    background-color: #333;
  }
}
.popup-mode-device-pc{
  border-radius: 20rpx;
}
.nickname-input{
  // 禁止手势
  cursor: not-allowed;
}
</style>

