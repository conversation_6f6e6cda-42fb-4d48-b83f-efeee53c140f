<view class="{{['data-v-4468a16e',clazz.loginInputBox]}}"><view class="icon-box data-v-4468a16e"><view class="icon data-v-4468a16e"><image class="icon-img data-v-4468a16e" src="{{$root.g0}}" mode="widthFix"></image></view></view><view class="input-box data-v-4468a16e" style="width:calc(100% - 180rpx);"><input class="input data-v-4468a16e" adjust-position="{{false}}" placeholder="{{$root.g1}}" type="{{passwordType}}" data-event-opts="{{[['blur',[['addUserLog',['$event']]]],['input',[['changePassword',['$event']]]]]}}" bindblur="__e" bindinput="__e"/></view><block wx:if="{{passwordType==='password'}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="icon-box data-v-4468a16e" bindtap="__e"><view class="icon data-v-4468a16e"><view style="font-size:20rpx;" class="data-v-4468a16e"><image class="icon-img data-v-4468a16e" src="{{$root.g2}}" mode="widthFix"></image></view></view></view></block><block wx:if="{{passwordType==='text'}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="icon-box data-v-4468a16e" bindtap="__e"><view class="icon data-v-4468a16e"><view style="font-size:20rpx;" class="data-v-4468a16e"><image class="icon-img data-v-4468a16e" src="{{$root.g3}}" mode="widthFix"></image></view></view></view></block></view>