@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box.data-v-7d622d22 {
  padding: 20rpx;
  box-sizing: border-box;
}
.bg.data-v-7d622d22 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.bg-mode-color-scheme-light.data-v-7d622d22 {
  background-color: #FFFFFF;
}
.bg-mode-color-scheme-dark.data-v-7d622d22 {
  background-color: #191919;
}
.in-box.data-v-7d622d22 {
  position: relative;
  height: 170rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.in-box .in-box-user-info-card.data-v-7d622d22 {
  position: absolute;
  width: 100%;
  top: 15rpx;
  left: 0;
}
.in-box .unread-count.data-v-7d622d22 {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  background-color: rgba(255, 0, 0, 0.7);
  color: #FFFFFF;
  font-size: 25rpx;
  padding: 0 10rpx 0 10rpx;
  border-radius: 10rpx;
}
.container-mode-color-scheme-dark .in-box.data-v-7d622d22 {
  border-bottom: 1rpx solid #404a56;
}
.container-mode-device-pc.data-v-7d622d22 {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;
}
.container-mode-device-pc .box .in-box.data-v-7d622d22 {
  position: relative;
  width: 100%;
  height: 160rpx;
  cursor: pointer;
}
.container-mode-device-pc .box .in-box .in-box-user-info-card.data-v-7d622d22 {
  position: absolute;
  width: 100%;
  top: 15rpx;
  left: -450rpx;
}
.container-mode-device-pc .header-box.data-v-7d622d22 {
  width: 100%;
}
.in-box-selected.data-v-7d622d22 {
  background-color: #cccccc;
}
