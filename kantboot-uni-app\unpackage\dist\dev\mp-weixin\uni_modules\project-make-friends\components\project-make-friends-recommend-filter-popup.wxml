<u-popup vue-id="b321f8b8-1" show="{{show}}" mode="bottom" bgColor="rgba(0,0,0,0)" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-aaf09cbc" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup data-v-aaf09cbc"><view class="popup-title data-v-aaf09cbc">{{$root.g0}}</view><scroll-view class="filter-scroll data-v-aaf09cbc" style="max-height:70vh;" scroll-y="{{true}}"><view class="filter-section data-v-aaf09cbc"><view class="filter-label data-v-aaf09cbc">{{$root.g1}}</view><view class="gender-box data-v-aaf09cbc"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['gender-item','data-v-aaf09cbc',(genderCode=='male')?'gender-item-selected':'']}}" bindtap="__e">{{$root.g2}}</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['gender-item','data-v-aaf09cbc',(genderCode=='female')?'gender-item-selected':'']}}" bindtap="__e">{{$root.g3}}</view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="{{['gender-item','data-v-aaf09cbc',(genderCode=='')?'gender-item-selected':'']}}" bindtap="__e">{{$root.g4}}</view></view></view><view class="filter-section data-v-aaf09cbc"><view class="filter-label data-v-aaf09cbc">{{$root.g5}}</view><view data-event-opts="{{[['tap',[['openJobTypePopup',['$event']]]]]}}" class="filter-value data-v-aaf09cbc" bindtap="__e">{{''+($root.g6?$root.g7:$root.g8)+''}}</view><job-multi-type-popup bind:select="__e" vue-id="{{('b321f8b8-2')+','+('b321f8b8-1')}}" data-ref="jobMultiTypePopup" data-event-opts="{{[['^select',[['onJobTypeSelect']]]]}}" class="data-v-aaf09cbc vue-ref" bind:__l="__l"></job-multi-type-popup></view><view class="filter-section data-v-aaf09cbc"><view class="filter-label data-v-aaf09cbc">{{$root.g9}}</view><view data-event-opts="{{[['tap',[['openAreaPopup',['$event']]]]]}}" class="filter-value data-v-aaf09cbc" bindtap="__e">{{$root.g10}}</view></view><view class="filter-section data-v-aaf09cbc"><view class="filter-label data-v-aaf09cbc">{{$root.g11}}</view><view class="filter-value data-v-aaf09cbc"><slider-range vue-id="{{('b321f8b8-3')+','+('b321f8b8-1')}}" min="{{minAge}}" max="{{maxAge}}" step="{{1}}" barHeight="{{5}}" blockSize="{{24}}" backgroundColor="#e9e9e9" activeColor="#1aad19" blockColor="#fff" value="{{ageRange}}" data-event-opts="{{[['^change',[['onSliderRangeChange']]],['^input',[['__set_model',['','ageRange','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-aaf09cbc" bind:__l="__l"></slider-range><view class="age-range-text data-v-aaf09cbc">{{ageRange[0]+" - "+ageRange[1]}}</view></view></view><view class="filter-section data-v-aaf09cbc"><view class="filter-label data-v-aaf09cbc">{{$root.g12}}</view><view class="filter-value data-v-aaf09cbc"><view class="tag-list data-v-aaf09cbc"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['toggleInterest',['$0'],[[['interestsList','id',item.$orig.id,'id']]]]]]]}}" class="{{['tag-item','data-v-aaf09cbc',(item.g13)?'selected':'']}}" bindtap="__e">{{''+item.$orig.name+''}}</view></block></view></view></view><view class="filter-section data-v-aaf09cbc"><view class="filter-label data-v-aaf09cbc">{{$root.g14}}</view><view class="filter-value data-v-aaf09cbc"><view class="tag-list data-v-aaf09cbc"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['toggleCharacteristic',['$0'],[[['characteristicsList','id',item.$orig.id,'id']]]]]]]}}" class="{{['tag-item','data-v-aaf09cbc',(item.g15)?'selected':'']}}" bindtap="__e">{{''+item.$orig.name+''}}</view></block></view></view></view></scroll-view><view class="popup-actions data-v-aaf09cbc"><kt-button vue-id="{{('b321f8b8-4')+','+('b321f8b8-1')}}" type="primary" data-event-opts="{{[['^click',[['onConfirm']]]]}}" bind:click="__e" class="data-v-aaf09cbc" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g16}}</kt-button></view><kt-state-area-address-select-popup bind:select="__e" vue-id="{{('b321f8b8-5')+','+('b321f8b8-1')}}" data-ref="areaPopup" data-event-opts="{{[['^select',[['onAreaSelect']]]]}}" class="data-v-aaf09cbc vue-ref" bind:__l="__l"></kt-state-area-address-select-popup></view></u-popup>