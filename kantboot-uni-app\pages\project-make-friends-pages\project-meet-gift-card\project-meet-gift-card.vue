<template>
  <view>
    <view
        class="card"
        @click="openProjectMeetGiftShow"
    >
      <view>
        <image
            class="gift-card-cover"
            :src="$kt.file.visit(gift.fileIdOfCover)"
        ></image>
      </view>
      <view class="gift-card-text">
        {{ $i18n.zhToGlobal("点击查看") }}
      </view>


    </view>

    <project-meet-gift-show
    ref="projectMeetGiftShow"
    ></project-meet-gift-show>

  </view>
</template>

<script>
export default {
  props:{
    giftId: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      gift: {},
    };
  },
  created() {
    this.getById();
  },
  methods: {
    openProjectMeetGiftShow() {
      this.$refs.projectMeetGiftShow.open(this.gift.fileIdOfEffect);
    },
    async getById() {
      this.$request.post("/project-meet-web/gift/getById", {
        data:{id: this.giftId},
      }).then((res) => {
          this.gift = res.data;
      }).catch((err) => {
      });
    },
    open() {
      this.$refs.projectMeetGiftPopup.open();
    },
  },
}
</script>

<style lang="scss" scoped>
.card{
  border:3rpx solid #F0F0F0;
  border-radius: 20rpx;
  padding: 10rpx;
  box-sizing: border-box;
}
.card:active {
  opacity: .8;
}
.gift-card-cover {
  width: 300rpx;
  height: 300rpx;
  object-fit: cover;
}
.gift-card-text {
  text-align: center;
  font-size: 28rpx;
  color: #666666;
  margin-top: 20rpx;
}
</style>
