@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.popup.data-v-aaf09cbc {
  padding: 20rpx 40rpx 20rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
}
.popup-title.data-v-aaf09cbc {
  padding: 20rpx;
  font-size: 34rpx;
  font-weight: bold;
  text-align: left;
  letter-spacing: 2rpx;
}
.filter-section.data-v-aaf09cbc {
  margin-bottom: 30rpx;
}
.filter-label.data-v-aaf09cbc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.filter-value.data-v-aaf09cbc {
  min-height: 40rpx;
  font-size: 28rpx;
  color: #333;
}
.tag-list.data-v-aaf09cbc {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}
.tag-item.data-v-aaf09cbc {
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 10rpx 24rpx;
  color: #333;
  font-size: 26rpx;
  margin-bottom: 10rpx;
  cursor: pointer;
}
.tag-item.selected.data-v-aaf09cbc {
  background: #333;
  color: #fff;
}
.age-range-text.data-v-aaf09cbc {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #888;
}
.popup-actions.data-v-aaf09cbc {
  margin-top: 20rpx;
  text-align: center;
}
.gender-box.data-v-aaf09cbc {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  margin-top: 10rpx;
}
.gender-item.data-v-aaf09cbc {
  font-size: 28rpx;
  color: #333;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  cursor: pointer;
}
.gender-item-selected.data-v-aaf09cbc {
  background: #333;
  color: #fff;
}
