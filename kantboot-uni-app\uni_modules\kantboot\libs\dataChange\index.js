import request from "../request";
import storage from "../storage";

/**
 * 根据key获取uuid
 * @param key key
 * @returns {Promise<unknown>}
 */
let getUuidByKey =  (key)=> {
    return new Promise((resolve, reject) => {
        request.send({
            uri: "/util-data-change-web/dataChange/getUuidByKey",
            data: {
                key: key
            },
            stateSuccess: (res) => {
                console.debug("获取uuid成功", res.data);
                resolve(res.data);
            },
            stateFail: (res) => {
                reject();
            }
        })
    });
}

/**
 * 检查是否改变了数据
 */
let checkDataChange =  (key)=> {
    let value = getDataChange(key);
    return new Promise((resolve, reject) => {
        getUuidByKey(key).then((res) => {
            // 如果不相等，说明数据改变了
            if (value !== res) {
                console.debug("数据改变了", value, res);
                setDataChange(key, res);
                // storage.set("DataChange:" + key, res);
                resolve({
                    isChange: true,
                    uuid: res
                });
                return;
            }
            resolve({
                isChange: false,
                uuid: res
            });
        }).catch((err) => {
            console.error("检查数据是否改变错误", err);
            resolve({
                isChange: false,
                uuid: null
            });
        });
    })
}

/**
 * 设置数据改变
 */
let setDataChange = function (key,uuid) {
    storage.set("DataChange:" + key, uuid);
}

let getDataChange = function (key) {
    return storage.get("DataChange:" + key);
}

export default {
    getUuidByKey,
    checkDataChange,
    setDataChange,
    getDataChange
}