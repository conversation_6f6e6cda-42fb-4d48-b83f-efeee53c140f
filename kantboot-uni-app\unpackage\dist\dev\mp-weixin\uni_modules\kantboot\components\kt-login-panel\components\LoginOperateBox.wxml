<view class="container data-v-3a17dffd"><view style="position:relative;" class="data-v-3a17dffd"><wechat-mp-login vue-id="08e4a0a9-1" wechat-login-method="{{wechatLoginMethod}}" agree="{{bodyData.agree}}" data-ref="wechatMpLogin" class="data-v-3a17dffd vue-ref" bind:__l="__l"></wechat-mp-login><type-select bind:change="__e" vue-id="08e4a0a9-2" data-event-opts="{{[['^change',[['changeType']]]]}}" class="data-v-3a17dffd" bind:__l="__l"></type-select><view style="height:10px;" class="data-v-3a17dffd"></view><view class="input-box data-v-3a17dffd"><email-and-phone-input vue-id="08e4a0a9-3" body-data="{{bodyData}}" data-event-opts="{{[['^change',[['changeMethod']]]]}}" bind:change="__e" class="data-v-3a17dffd" bind:__l="__l"></email-and-phone-input><view style="height:30rpx;" class="data-v-3a17dffd"></view><block wx:if="{{bodyData.typeCode==='verificationCode'}}"><verification-code-input vue-id="08e4a0a9-4" body-data="{{bodyData}}" data-event-opts="{{[['^change',[['changeVerificationCode']]]]}}" bind:change="__e" class="data-v-3a17dffd" bind:__l="__l"></verification-code-input></block><block wx:if="{{bodyData.typeCode==='password'}}"><password-input vue-id="08e4a0a9-5" body-data="{{bodyData}}" data-event-opts="{{[['^change',[['changePassword']]]]}}" bind:change="__e" class="data-v-3a17dffd" bind:__l="__l"></password-input></block><block wx:if="{{type===1}}"><view class="login-input-box data-v-3a17dffd"><view class="icon-box data-v-3a17dffd"><view class="icon data-v-3a17dffd"><image class="icon-img data-v-3a17dffd" src="{{$root.g0}}" mode="widthFix"></image></view></view><view class="input-box data-v-3a17dffd" style="width:calc(100% - 120rpx);"><input class="input data-v-3a17dffd" adjust-position="{{false}}" placeholder="{{$root.g1}}" type="{{passwordType}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['param']],['e0',['$event']]]]]}}" value="{{param.password}}" bindinput="__e"/></view><block wx:if="{{passwordType==='password'}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="icon-box data-v-3a17dffd" bindtap="__e"><view class="icon data-v-3a17dffd"><view style="font-size:20rpx;" class="data-v-3a17dffd"><image class="icon-img data-v-3a17dffd" src="{{$root.g2}}" mode="widthFix"></image></view></view></view></block><block wx:if="{{passwordType==='text'}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="icon-box data-v-3a17dffd" bindtap="__e"><view class="icon data-v-3a17dffd"><view style="font-size:20rpx;" class="data-v-3a17dffd"><image class="icon-img data-v-3a17dffd" src="{{$root.g3}}" mode="widthFix"></image></view></view></view></block></view></block></view><block wx:if="{{$root.g4}}"><view style="height:30rpx;" class="data-v-3a17dffd"></view></block><view style="height:30rpx;" class="data-v-3a17dffd"></view><kt-button vue-id="08e4a0a9-6" is-open-box-shadow="{{false}}" data-ref="nextButton" data-event-opts="{{[['^click',[['toLogin']]]]}}" bind:click="__e" class="data-v-3a17dffd vue-ref" bind:__l="__l" vue-slots="{{['default']}}">{{$root.g5+" "+"➠"+''}}</kt-button><view style="height:20rpx;" class="data-v-3a17dffd"></view><view style="text-align:center;" class="data-v-3a17dffd"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="to-back-btn data-v-3a17dffd" bindtap="__e">{{$root.g6}}</view></view><view style="height:20rpx;" class="data-v-3a17dffd"></view></view><login-agreement bind:change="__e" vue-id="08e4a0a9-7" data-event-opts="{{[['^change',[['changeAgree']]]]}}" class="data-v-3a17dffd" bind:__l="__l"></login-agreement></view>