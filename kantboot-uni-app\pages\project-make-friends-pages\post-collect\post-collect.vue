<template>
  <view>
    <view
        class="header"
        id="headerInPostCollect">
      <kt-nav-bar
          :title="$i18n.zhToGlobal('收藏的帖子')"></kt-nav-bar>
      <!-- 搜索框 -->
      <view class="search-box">
        <view class="search-box-input">
          <input
              v-model="keyword"
              @input="input"
              :placeholder="$i18n.zhToGlobal('搜索帖子')"
              :placeholder-style="'color: #999999; font-size: 28rpx;'"
              :border-radius="'50rpx'"
              :height="'60rpx'"
              :padding="'0 30rpx'"
              :background="'#F5F5F5'"
              @confirm="input"
          ></input>
        </view>
      </view>
    </view>

    <view :style="{height: headerHeight + 'px'}"></view>

    <view v-if="loading">
      <view style="height: 100rpx"></view>
      <u-loading-icon
          mode="circle"
          :size="50"
      ></u-loading-icon>
    </view>
    <view
    v-if="!loading && posts.length === 0"
    >
      <view style="height: 100rpx"></view>
      <u-empty
          :image="$kt.file.byPath('icon/empty.svg')"
          :text="$i18n.zhToGlobal('暂无收藏的帖子')"
          :height="'calc(100vh - '+headerHeight+'rpx - 160rpx)'"></u-empty>
    </view>

    <view class="box">
      <view
      class="in-box"
          v-for="post in postsOfFilter"
      >
        <kt-community-post-card
        :post="post"
        ></kt-community-post-card>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      headerHeight: 0,
      userAccountId: '',
      initCode: '',
      posts: [],
      loading: true,
      postsOfFilter: [],
      keyword: '',
    }
  },
  onLoad(options) {
    // 获取用户id
    this.userAccountId = options.userAccountId;
    if(this.userAccountId === undefined || this.userAccountId === null || this.userAccountId === '') {
      this.userAccountId = this.$kt.userAccount.getSelf().id;
    }
    this.getAllCollectPosts();
    if(options.code) {
      console.log(options.code,"options.code")
      this.initCode = options.code
    }
  },
  mounted() {
    this.getHeight();
  },
  methods: {
    input(e) {
      this.keyword = e.detail.value
      this.filterPosts();
    },
    getAllCollectPosts() {
      this.loading = true;
      this.$kt.request.post('/fp-community-web/post/getAllCollectPosts', {
        data:{userAccountId: this.userAccountId}
      }).then((res) => {
          this.posts = res.data;
          this.filterPosts();
          this.loading = false;
      }).catch((err) => {
        uni.showToast({
          title: this.$i18n.zhToGlobal('获取收藏的帖子失败'),
          icon: 'none'
        });
      });
    },
    /**
     * 过滤帖子
     */
    filterPosts() {
      if (!this.keyword) {
        this.postsOfFilter = this.posts;
      } else {
        this.postsOfFilter = [];
        for (let i = 0; i < this.posts.length; i++) {
          let post = this.posts[i];
          let postItems = post.items;
          for  (let j = 0; j < postItems.length; j++) {
            let postItem = postItems[j];
            postItem.id = null;
          }
          if (JSON.stringify(postItems).includes(this.keyword)) {
            this.postsOfFilter.push(post);
          }
        }
      }
    },
    toUserInfoPage(userAccount){
      this.$kt.router.navTo('/pages/project-make-friends-pages/user-info/user-info',{"userAccountId":userAccount.id})
    },
    getHeight() {
      this.$nextTick(() => {
        uni.createSelectorQuery()
            .select('#headerInPostCollect')
            .boundingClientRect((res) => {
              this.headerHeight = res.height
            }).exec()
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #FFFFFF;
  z-index: 1000;
}
.box{
  padding: 30rpx;
  box-sizing: border-box;
}

.in-box {
  margin-bottom: 30rpx;
}

.search-box {
  top: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background-color: #FFFFFF;
  z-index: 1000;
  padding: 30rpx;
  box-sizing: border-box;

  .search-box-input {
    width: 100%;
    position: relative;
    border-radius: 50rpx;
    background-color: #F5F5F5;
    box-sizing: border-box;
    padding: 10rpx;
    padding-left: 20rpx;
  }
}

</style>
