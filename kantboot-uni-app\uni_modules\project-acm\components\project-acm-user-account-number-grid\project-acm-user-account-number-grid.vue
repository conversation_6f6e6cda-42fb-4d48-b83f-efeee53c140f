<template>
  <view class="box">
    <u-grid :col="2">
      <u-grid-item>
        <view class="num">{{"￥"}}{{"0"}}</view>
        <view class="text">{{$i18n.zhToGlobal("可提现")}}</view>
      </u-grid-item>
      <u-grid-item>
        <view class="num">{{"0"}}</view>
        <view class="text">{{$i18n.zhToGlobal("我的团队")}}</view>
      </u-grid-item>
    </u-grid>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  }
}
</script>

<style lang="scss" scoped>
.box{
  padding: 20rpx;
  box-sizing: border-box;
}
.num {
  font-size: 28rpx;
  color: #000;
  font-weight: bold;
}
.text {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}
</style>
