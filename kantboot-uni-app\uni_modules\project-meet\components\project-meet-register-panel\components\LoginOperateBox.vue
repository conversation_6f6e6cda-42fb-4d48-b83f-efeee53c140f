<template>
  <view class="container">

  <view
      style="position: relative">
    <!-- #ifdef MP-WEIXIN -->
    <wechat-mp-login
        ref="wechatMpLogin"
    :agree="bodyData.agree"
    ></wechat-mp-login>
    <!-- #endif -->
    <type-select @change="changeType"></type-select>

    <view style="height: 10px"></view>
    <view class="input-box">
      <scroll-view
        :scroll-y="true"
        :style="{
          height: 'calc(100vh - 600rpx)'
        }"
      >

     <view>
       <view class="select-box">
         <view class="select-item"
               @click="bodyData.genderCode = 'male';addUserLog()"
               :class="{
              'select-item-active': bodyData.genderCode === 'male'
              }"
         >
           {{$i18n.zhToGlobal("我是一名男性，正在寻找一名女性")}}
           <image
               v-if="bodyData.genderCode === 'male'"
               class="box-select-icon"
               :src="$kt.file.byPath('icon/yesLeftTop.svg')"></image>
         </view>
         <view class="select-item"
                @click="bodyData.genderCode = 'female';addUserLog()"
         :class="{
              'select-item-active': bodyData.genderCode === 'female'
              }"
         >
           {{$i18n.zhToGlobal("我是一名女性，正在寻找一名男性")}}
           <image
               v-if="bodyData.genderCode === 'female'"
               class="box-select-icon"
               :src="$kt.file.byPath('icon/yesLeftTop.svg')"></image>

         </view>
       </view>
     </view>

      <username-input
          @addUserLog="addUserLog"
          :body-data="bodyData"
          @change="changeUsername"></username-input>

      <view style="height: 30rpx"></view>

      <password-input
          @addUserLog="addUserLog"
          :body-data="bodyData"
          @change="changePassword"></password-input>

      <view style="height: 30rpx"></view>

      <email-input
          @addUserLog="addUserLog"
      :body-data="bodyData"
      @change="changeEmail"
      ></email-input>


        <project-meet-set-user-info-panel
            @change="changeBodyData"
            :has-submit-button="false"
        ></project-meet-set-user-info-panel>

      </scroll-view>


    </view>

    <view
        v-if="$i18n.getLanguageCode()==='fi_FI'||$i18n.getLanguageCode()==='bg_BG'||$i18n.getLanguageCode()==='fr_BE'"
        style="height: 30rpx"
    ></view>

    <view style="height: 30rpx"></view>
    <kt-button
        @click="toRegister()"
        ref="nextButton"
        :is-open-box-shadow="false">{{ $i18n.zhToGlobal("注册") }}{{ " " }}{{"➠"}}
    </kt-button>
    <view style="height: 20rpx"></view>

    <!-- #ifdef MP-WEIXIN -->
    <view style="text-align: center">
      <view
          @click="$refs.wechatMpLogin.open()"
          class="to-back-btn">{{ $i18n.zhToGlobal("返回") }}</view>
    </view>
    <view style="height: 20rpx"></view>
    <!-- #endif -->
  </view>

    <login-agreement
    @change="changeAgree"
    ></login-agreement>


  </view>
</template>

<script>
import LoginAgreement from "../../../components/project-meet-login-panel/components/LoginAgreement.vue";
import TypeSelect from "./TypeSelect.vue";
import VerificationCodeInput from "../inputComponents/VerificationCodeInput.vue";
import PasswordInput from "../inputComponents/PasswordInput.vue";
import $kt from "@/uni_modules/kantboot";
import operate from "../js/operate";
import WechatMpLogin
  from "./WechatMpLogin.vue";
import UsernameInput
  from "../inputComponents/UsernameInput.vue";
import ConfirmPasswordInput
  from "@/uni_modules/project-meet/components/project-meet-register-panel/inputComponents/ConfirmPasswordInput.vue";
import EmailInput
  from "@/uni_modules/project-meet/components/project-meet-register-panel/inputComponents/EmailInput.vue";
import result from "../js/operate";

export default {
  components: {
    EmailInput,
    ConfirmPasswordInput,
    WechatMpLogin,
    LoginAgreement,
    TypeSelect,
    VerificationCodeInput,
    PasswordInput,
    UsernameInput
  },
  data() {
    return {
      passwordType:"password",
      bodyData: {
        phoneAreaCode: "86",
        typeCode: "verificationCode",
        methodCode: "email",
        to:"",
        email:"",
        phone: "",
        password: "",
        // 确认密码
        confirmPassword: "",
        verificationCode: "",
        agree:false,
        genderCode: "male",
              fileIdOfAvatar:"",
              nickname:"",
              introduction:"",
              // 兴趣爱好的ids
              interestsIds:[],
              // 期待的关系的ids
              expectedRelationshipIds:[]
      },
      clazz:{
        tips: this.$kt.style.toggleClass("tips"),
      }

    }
  },
  created() {
  },
  mounted() {
    $kt.event.on("nextButtonInLogin:success",(res)=>{
      this.$refs.nextButton.success(res.msg);
    });
    $kt.event.on("nextButtonInLogin:error",(err)=>{
      this.$refs.nextButton.error(err.errMsg);
    });

  },
  methods: {
    addUserLog() {
      this.$request.post("/project-meet-web/userLog/add",{
        data:{
          typeCode: "register",
          safeInputContent: "账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
             +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
          inputContent: "账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
          + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
          ,
          operationCode: "registerInput",
          sceneCode: this.$kt.style.detectDeviceType(),
          operationText: "注册中",
          levelCode: "info",
          levelText: "普通信息",
        }
      });

    },
    changeType(e) {
      this.bodyData.typeCode = e.typeCode;
    },
    changeBodyData(e){
      this.bodyData.fileIdOfAvatar = e.fileIdOfAvatar;
       this.bodyData.introduction = e.introduction;
      this.bodyData.interestsIds = e.interestsIds;
       this.bodyData.expectedRelationshipIds = e.expectedRelationshipIds;
       this.bodyData.gmtBirthday = e.gmtBirthday;
       this.bodyData.height = e.height;
       this.bodyData.weight = e.weight;
       this.bodyData.countryCn = e.countryCn;
    },
    changeMethod(e){
      this.bodyData.methodCode = e.methodCode;
      this.bodyData.to = e.to;
      if(e.methodCode==="email"){
        this.bodyData.email = e.email;
      }else{
        this.bodyData.phone = e.phone;
        this.bodyData.phoneAreaCode = e.phoneAreaCode;
      }
    },
    changeUsername(e){
      this.bodyData.username = e.to;
    },
    changeVerificationCode(e){
      this.bodyData.verificationCode = e.verificationCode;
    },
    changePassword(e){
      this.bodyData.password = e.password
    },
    changeConfirmPassword(e){
      this.bodyData.confirmPassword = e.confirmPassword;
    },
    changeEmail(e){
      this.bodyData.email = e.email;
    },
    changeAgree(e){
      this.bodyData.agree = e.agree;
    },
    /**
     * 检测邮箱格式
     */
    checkEmail(email) {
      const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return emailPattern.test(email);
    },

    toRegister(){
      if(!this.bodyData.agree){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未同意协议"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "未同意协议 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                +  "性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "未同意协议 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }
      if(!this.bodyData.username){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未输入用户名"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "未输入用户名 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "未输入用户名 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });

        return;
      }
      // 请输入邮箱
      if(!this.bodyData.email){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未输入邮箱"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "未输入邮箱 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "未输入邮箱 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }
      if(!this.bodyData.password){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未输入密码"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "未输入密码 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
               +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "未输入密码 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }
      if(!this.checkEmail(this.bodyData.email)){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("邮箱格式错误"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "邮箱格式错误 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "邮箱格式错误 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }
      // 如果没有填入生日
      if(!this.bodyData.gmtBirthday){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未输入生日"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "未输入生日 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
               +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "未输入生日 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }

      // 如果没填身高
      if(!this.bodyData.height){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未输入身高"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "未输入身高 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "未输入身高 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }
      // 如果没填体重
      if(!this.bodyData.weight){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未输入体重"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "未输入体重 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
               +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "未输入体重 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }
      // 如果没填国家
      if(!this.bodyData.countryCn){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未选择国家或地区"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "未选择国家或地区 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "未选择国家或地区 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }
      // 如果没有选择兴趣爱好
      if(!this.bodyData.interestsIds || this.bodyData.interestsIds.length==0){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未选择兴趣爱好"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "未选择兴趣爱好 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "未选择兴趣爱好 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }
      // 如果没有选择期待的关系
      if(!this.bodyData.expectedRelationshipIds || this.bodyData.expectedRelationshipIds.length==0){
        this.$refs.nextButton.error(this.$i18n.zhToGlobal("未选择期待的关系"));
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "未选择期待的关系 账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
               +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "未选择期待的关系 "+"账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        return;
      }

      let errCodeToMsgMap = {
        "usernameAlreadyExists":"用户名已存在",
        "serverError":"服务器错误",
      };

      this.bodyData.phoneAreaCode="";
      this.bodyData.phone="";
      this.$refs.nextButton.loading(null,99999);
      this.$request.post("/project-meet-web/userAccount/register",{
         data:this.bodyData,
      }).then((res)=>{
        this.$refs.nextButton.success(res.msg);
        // this.$emit('registerSuccess', res.data);
        // this.$kt.event.emit("projectMeet:registerSuccess",this.bodyData);
        this.$kt.request.setToken(res.data.token);
        this.$kt.userAccount.setSelf(res.data.userAccount);
        this.$kt.userAccount.setIsLogin(true);

        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "注册成功 " +
                "账号是否被封禁: " +(this.bodyData.isBanned?"是":"否")+
                "账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "注册成功 "+
                "账号是否被封禁: " +(this.bodyData.isBanned?"是":"否")+
                "账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerClickButNoFinish",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }
        });
        // 发送登录成功事件
        this.$kt.event.emit("login:success");
      }).catch((err)=>{
        this.$request.post("/project-meet-web/userLog/add",{
          data:{
            typeCode: "register",
            safeInputContent: "注册失败 " +errCodeToMsgMap[err.stateCode]+" "+
                "账号: "+this.bodyData.username+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
               +" 性别："+(this.bodyData.genderCode ==='male'?"男":"女"),
            inputContent: "注册失败 " +errCodeToMsgMap[err.stateCode]+" "+
                "账号: "+this.bodyData.username+" 密码: "+this.bodyData.password+" 协议："+(this.bodyData.agree?"勾选":"未勾选")
                + " 邮箱: "+this.bodyData.email+" 性别："+(this.bodyData.genderCode ==='male'?"男":"女")
            ,
            operationCode: "registerFail",
            sceneCode: this.$kt.style.detectDeviceType(),
            operationText: "点击注册，但是未完成注册",
            levelCode: "info",
            levelText: "普通信息",
          }});
        this.$refs.nextButton.error(err.errMsg);
      })

    },
  },
}
</script>

<style lang="scss" scoped>
@import "../css/loginInput";
.container{
  position: relative;
}
.to-back-btn{
  color: #666666;
  font-size: 30rpx;
  letter-spacing: 2rpx;
}

.select-box{
  width: 100%;
  .select-item{
    position: relative;
    border: 5rpx solid #eeeeee;
    border-radius: 10rpx;
    color: #AAAAAA;
    font-size: 28rpx;
    padding: 20rpx;
    margin-bottom: 30rpx;
    box-sizing: border-box;
    cursor: pointer;
  }
  .select-item-active{
    border: 5rpx solid #333333;
    color: #333333;
  }
  .box-select-icon{
    position: absolute;
    right: -2rpx;
    top: -2rpx;
    width: 60rpx;
    height: 60rpx;
    border-radius: 0 10rpx 0 0;
  }
}
</style>
