<view class="data-v-75b0b7ec"><kt-popup vue-id="40654388-1" overlayClose="{{false}}" zIndex="{{999999999}}" data-ref="ktPopup" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-75b0b7ec vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-75b0b7ec"><view data-event-opts="{{[['tap',[['closePop',['$event']]]]]}}" class="close-btn data-v-75b0b7ec" bindtap="__e">×</view><view class="title data-v-75b0b7ec">{{$root.g0}}</view><view class="content-area data-v-75b0b7ec"><view class="input-section data-v-75b0b7ec"><view class="section-title data-v-75b0b7ec">{{$root.g1}}</view><view class="input-container data-v-75b0b7ec"><input class="project-name-input data-v-75b0b7ec" placeholder="{{$root.g2}}" maxlength="{{50}}" data-event-opts="{{[['input',[['__set_model',['','projectName','$event',[]]],['onInput',['$event']]]]]}}" value="{{projectName}}" bindinput="__e"/></view><view class="tips data-v-75b0b7ec">{{''+$root.g3+''}}</view></view></view><view class="footer data-v-75b0b7ec"><view class="buttons data-v-75b0b7ec"><button class="cancel-btn data-v-75b0b7ec" disabled="{{processing}}" data-event-opts="{{[['tap',[['closePop',['$event']]]]]}}" bindtap="__e">{{''+$root.g4+''}}</button><button class="confirm-btn data-v-75b0b7ec" disabled="{{$root.g5}}" data-event-opts="{{[['tap',[['handleConfirm',['$event']]]]]}}" bindtap="__e">{{''+(processing?$root.g6:$root.g7)+''}}</button></view></view></view></kt-popup></view>