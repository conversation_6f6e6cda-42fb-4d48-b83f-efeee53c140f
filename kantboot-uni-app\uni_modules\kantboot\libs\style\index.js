import storage from "@/uni_modules/kantboot/libs/storage";
import event from "@/uni_modules/kantboot/libs/event";

let result = {};

/**
 * 模式
 */
result.mode = {
    // 是否暗黑模式，auto: 自动，dark: 暗黑模式，light: 明亮模式
    colorScheme: "auto",
    // 设备模式（不根据系统返回，根据大小与宽高比例）， pc、pad、mobile，watch、tv
    device: 'pc',
    // 横屏还是竖屏，portrait:竖屏，landscape: 横屏
    orientation: 'portrait',
    // 根据字体大小，-1、0、1、2、3、4、5、6
    fontSize: '0',
    // 根据语言编码
    languageCode: 'zh_CN',
};

result.getMode = function () {
    let modeStorage = storage.get("kt.style.mode");
    if (modeStorage) {
        result.mode = modeStorage;
        return result.mode;
    } else {
        // 设置默认值
        storage.set("kt.style.mode", result.mode);
        return result.mode;
    }
}

result.setMode = function (mode) {
    storage.set("kt.style.mode", mode);
    return result.mode;
}

result.detectDeviceType = () => {
    const systemInfo = uni.getSystemInfoSync();
    const screenWidth = systemInfo.screenWidth;
    const platform = systemInfo.platform.toLowerCase();

    // 判断电视（大屏且可能横屏，或明确平台标识）
    // if (screenWidth >= 1920 || platform.includes('tv')) {
    //     return 'tv';
    // }
    // 原有逻辑
    if (screenWidth < 768) {
        return 'mobile';
    }
    if (screenWidth < 992) {
        return 'pad';
    }
    return 'pc';
}


result.getDarkOrLight = function (darkOrLight) {
    // 获取暗黑模式，获取系统设置
    if (darkOrLight === undefined || darkOrLight === "auto") {
        // #ifdef MP-WEIXIN
        return uni.getSystemInfoSync().hostTheme;
        // #endif
        // #ifdef H5
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        // #endif
    }
    return darkOrLight;
}

result.toggleClass = function (...clazz) {

    let mode = result.getMode();

    let classNameList1 = clazz;
    let classNameList2 = [];
    // 根据暗黑模式来决定是否添加夜间模式的类名
    classNameList2 = clazz.map(item => {
        return item + '-mode-color-scheme-' + result.getDarkOrLight(mode.colorScheme)
    });

    // 根据设备模式添加设备模式的类名
    let classNameList3 = [];
    classNameList3 = clazz.map(item => {
        // console.log('result.detectDeviceType()', result.detectDeviceType());
        return item + '-mode-device-' + result.detectDeviceType();

    });

    let classNameList4 = [];
    // 根据横屏还是竖屏添加横屏或者竖屏的类名
    if (mode.orientation) {
        classNameList4 = clazz.map(item => {
            return item + '-mode-orientation-' + mode.orientation
        });
    }

    // 根据文字大小添加类名
    let classNameList5 = clazz.map(item => {
        return item + '-mode-font-size-' + mode.fontSize
    });

    // 根据语言编码添加类名
    let classNameList6 = clazz.map(item => {
        return item + '-mode-language-code-' + mode.languageCode
    });

    // let classNameList = classNameList1 + ' ' + classNameList2 + ' ' + classNameList3 + ' ' + classNameList4 + ' ' + classNameList5;
    // classNameList = classNameList.replace(/\s+/g, ' ').trim();
    // return classNameList;
    let map = {};
    for (let i = 0; i < clazz.length; i++) {
        let item = clazz[i];
        map[item] = true;
    }
    for (let i = 0; i < classNameList1.length; i++) {
        let item = classNameList1[i];
        map[item] = true;
    }
    for (let i = 0; i < classNameList2.length; i++) {
        let item = classNameList2[i];
        map[item] = true;
    }
    for (let i = 0; i < classNameList3.length; i++) {
        let item = classNameList3[i];
        // console.log('item----', item);
        map[item] = true;
    }
    for (let i = 0; i < classNameList4.length; i++) {
        let item = classNameList4[i];
        map[item] = true;
    }
    for (let i = 0; i < classNameList5.length; i++) {
        let item = classNameList5[i];
        map[item] = true;
    }
    for (let i = 0; i < classNameList6.length; i++) {
        let item = classNameList6[i];
        map[item] = true;
    }
    let classNameList = Object.keys(map).join(' ');
    return classNameList;
}

/**
 * 根据字体大小，返回一个计算后的值
 */
result.toggleFontSize = (fontSize) => {
    let mode = result.mode;
    // 比例对应
    let ratio = {
        '-1': 0.8,
        '0': 1,
        '1': 1.2,
        '2': 1.4,
        '3': 1.6,
        '4': 1.8,
        '5': 2,
        '6': 2.2
    }
    return `calc(${ratio[fontSize]} * ${ratio[mode.fontSize + '']})`;
}

/**
 * 根据颜色，返回一个计算后的值
 */
result.toggleColor = (color) => {
    let mode = result.mode;
    if (!mode.dark) {
        return color;
    }
}

export default result;