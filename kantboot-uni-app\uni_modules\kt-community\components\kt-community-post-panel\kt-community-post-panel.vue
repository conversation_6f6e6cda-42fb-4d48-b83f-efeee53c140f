<template>
  <view>
    <scroll-view
        scroll-y
        :style="{
            'height':height
        }"
        :refresher-triggered="refresherTriggered"
        :refresher-enabled="true"
        @refresherrefresh="onRefresherrefresh()"
        class="scroll-view"
        @scrolltolower="getAlert()"
        :show-scrollbar="false"
        :lower-threshold="$kt.util.rpxToPx(500)">
      <view class="box">

        <!-- 空空如也 -->
        <view v-if="list.length === 0&&!loading"
              class="no-data"
        >
          <view>
            <image
                class="no-data-image"
                :src="$kt.file.byPath('icon/book.svg')"
            ></image>
          </view>
          <view
              class="no-data-text"
          >{{ $i18n.zhToGlobal("空空如也") }}
          </view>

        </view>
        <view
            @click.stop="cardClick(item)"
            v-for="item in list">
          <kt-community-post-card
              :is-forbid-forward="isForbidForward"
              :background="cardBackground"
              :show-audit-status="showAuditStatus"
              :is-forbid-collect="isForbidCollect"
              :post="item"
              :forward-default-click="forwardDefaultClick"
              :has-bottom-operation="hasCardBottomOperation"
              @dotClick="cardDotCLick"
              @userClick="cardUserClick"
              @postClick="postClick"
              @forwardClick="forwardClick"
          >
            <template v-slot:bottom>
              <slot name="cardBottom" :postId="item.id" :post="item"></slot>
            </template>
          </kt-community-post-card>
          <view style="height: 40rpx"></view>
        </view>
        <u-loading-icon
            v-if="loading"
            mode="circle"
            size="60"
        ></u-loading-icon>
        <view v-if="noMore"
              style="text-align: center; color: #999999; font-size: 24rpx;">
          {{$kt.i18n.zhToGlobal('没有更多了')}}
        </view>
        <view style="height: 80rpx"></view>

      </view>

    </scroll-view>
<!--    <community-post-popup-->
<!--        ref="communityPostPopup"-->
<!--    ></community-post-popup>-->

  </view>

</template>

<script>
import communityEvent from "@/uni_modules/kt-community/libs/event";

export default {
  name: "fp-community-post-panel",
  props: {
    mode:{
      type: String,
      default: "normal"
    },
    cardBackground: {
      type: String,
      default: "#FFFFFF"
    },
    isForbidForward: {
      type: Boolean,
      default: true
    },
    isForbidCollect: {
      type: Boolean,
      default: true
    },

    /**
     * 是否是自己的帖子
     */
    isSelf:{
      type: Boolean,
      default: false
    },
    /**
     * 是否显示审核状态
     */
    showAuditStatus: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: "100vh"
    },
    rowSize:{
      type: Number,
      default: 20
    },
    hasCardBottomOperation:{
      type: Boolean,
      default: true
    },
    forwardDefaultClick: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      event:communityEvent,
      refresherTriggered: false,
      refresherEnabled: true,
      // 刷新中
      refreshing: false,
      requestParam: {
        maxId: 0,
        minId: 0
      },
      list: [],
      loading: true,
      // 没有更多了
      noMore: false,
    };
  },
  created() {
    this.getInitList();
    // 监听帖子发布
    this.event.onPostPush((item)=>{
      // 重新获取数据
      this.getInitList();
    });
    // 监听帖子删除
    this.event.onPostRemove((item)=>{
      // 重新获取数据
      this.getInitList();
    });
  },
  methods: {
    forwardClick(post) {
      this.$emit("forwardClick", post);
    },
    postClick(post) {
      this.$emit("cardClick", post);
    },
    refresh() {
      this.getInitList();
    },
    /**
     * 卡片点击的事件
     * @param post 帖子数据
     */
    cardClick(post){
      this.$emit("cardClick", post);
    },
    /**
     * 用户点击的事件
     * @param user 用户数据
     */
    cardUserClick(user) {
      this.$emit("cardUserClick", user);
    },
    /**
     * 卡片右上角的三个点点击的的事件
     * @param post 帖子数据
     */
    cardDotCLick(post) {
      this.$emit("cardDotClick", post);
    },
    async onRefresherrefresh() {
      if (this.refreshing) {
        this.refresherTriggered = false;
        return;
      }
      this.refreshing = true;
      await this.getInitList();
      this.refresherTriggered = true;
      setTimeout(() => {
        this.refresherTriggered = false;
        this.refreshing = false;
      }, 500);
    },
    // 获取最大id和最小id
    getMaxAndMinId() {
      let maxId = 0;
      let minId = 0;
      for (let i = 0; i < this.list.length; i++) {
        let item = this.list[i];
        if (item.id > maxId) {
          maxId = item.id;
        }
        if (item.id < minId || minId === 0) {
          minId = item.id;
        }
      }
      this.requestParam.maxId = minId;
      this.requestParam.minId = maxId;
    },
    // /fp-community-web/post/getList
    async getInitList() {
      this.loading = true;
      await this.$kt.request.post("/fp-community-web/post/getList", {
        data: {
          isSelf: this.isSelf,
          mode:this.mode
        }
      }).then(async (res) => {
        this.list = [];
        this.$nextTick(() => {
          this.list = res.data;
        });
        // relationship
        this.loading = false;
        this.getMaxAndMinId();
        
        // 重新渲染
        try{
          this.$forceUpdate();
        }catch (e) {
          
        }
      }).catch(err => {
        this.loading = false;
        this.$kt.message.error(err.errMsg);
      });
    },
    async getAlert(){
      if(this.noMore){
        return;
      }
      if (this.loading) {
        return;
      }
      this.loading = true;
      await this.$kt.request.post("/fp-community-web/post/getList", {
        data: {
          maxId: this.requestParam.maxId,
          isSelf: this.isSelf,
          mode:this.mode
        }
      }).then(async (res) => {
        if(!res.data){
          this.noMore;
        }
        if(res.data.length<this.rowSize){
          this.noMore = true;
        }
        this.list = [...this.list, ...res.data];
        this.loading = false;
        this.getMaxAndMinId();
      }).catch(err => {
        this.loading = false;
        this.$kt.message.error(err.errMsg);
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.scroll-view {
  width: 100%;

  &::-webkit-scrollbar {
    display: none;
  }
}

// 不显示滚动条
.scroll-view::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

.scroll-view::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

// 滚动
.scroll-view::-webkit-scrollbar-thumb {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

.box {
  padding: 20rpx;
  box-sizing: border-box;
}

.no-data {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 200rpx;

  .no-data-image {
    width: 100rpx;
    height: 100rpx;
    opacity: .6;
  }
}


</style>
