<template>
	<view 
	@click="click()"
	class="edit-box">
		<image class="edit-icon" :src="$kt.file.byPath('icon/setting.svg')"></image>
	</view>
</template>

<script>
export default {
	data() {
		return {
		}
	},
	onLoad(options) {
	},
	methods: {
		click(){
			this.$emit('click')
		}
	}
}
</script>

<style lang="scss" scoped>
.edit-box {
  cursor: pointer;
	width: 20px;
	height: 20px;
	z-index: 3;
	.edit-icon {
		width: 100%;
		height: 100%;
	}
}

.edit-box:active {
	transform: scale(0.9);
}
</style>
