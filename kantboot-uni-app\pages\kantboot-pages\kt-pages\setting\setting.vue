<template>
    <view class="content">
      <kt-nav-bar
          :is-i18n="false"
      :title="$i18n.zhToGlobal('设置')"
      ></kt-nav-bar>
      <view :class="clazz.back"></view>
      <view>
        <setting-menu-box
            v-if="isLogin"
            :title="$i18n.zhToGlobal('账户')">
          <setting-menu
              v-if="self.phoneAreaCode && self.phone"
              :icon="$kt.file.byPath('icon/phone.svg')"
              :title="$i18n.zhToGlobal('手机号')"
              :content="'+'+self.phoneAreaCode + ' ' + self.phone"
          ></setting-menu>
          <setting-menu
            v-else-if="self.phone"
              :icon="$kt.file.byPath('icon/phone.svg')"
              :title="$i18n.zhToGlobal('手机号')"
              :content="self.phone"></setting-menu>
          <setting-menu
            v-else
            :icon="$kt.file.byPath('icon/phone.svg')"
            :title="$i18n.zhToGlobal('手机号')"
            :content="$i18n.zhToGlobal('未绑定')"
          ></setting-menu>
          <setting-menu
              v-if="self.email"
              :icon="$kt.file.byPath('icon/email.svg')"
              :title="$i18n.zhToGlobal('邮箱')"
              :content="self.email"
          ></setting-menu>
          <setting-menu
              v-else
              :icon="$kt.file.byPath('icon/email.svg')"
              :title="$i18n.zhToGlobal('邮箱')"
              :content="$i18n.zhToGlobal('未绑定')"></setting-menu>
          <setting-menu
              :is-right="true"
              :icon="$kt.file.byPath('icon/dataManage.svg')"
              :title="$i18n.zhToGlobal('数据管理')"
          ></setting-menu>
        </setting-menu-box>

        <setting-menu-box :title="$i18n.zhToGlobal('应用')">
          <setting-menu
              @click="$kt.router.toLanguageSelect()"
              :icon="$kt.file.byPath('kantboot/icon/language.svg')"
              :title="$i18n.zhToGlobal('语言')"
              :content="$i18n.getLanguageName()"
          ></setting-menu>
          <setting-menu
              @click="$kt.router.toColorModeSelect()"
              :icon="$kt.file.byPath('icon/color.svg')"
              :title="$i18n.zhToGlobal('颜色模式')"
              :content="colorSchemeContent"
          ></setting-menu>
        </setting-menu-box>

        <setting-menu-box :title="$i18n.zhToGlobal('关于')">
          <setting-menu
              :is-right="true"
              :icon="$kt.file.byPath('icon/agreement.svg')"
              :title="$i18n.zhToGlobal('用户协议')"
          ></setting-menu>
          <setting-menu
              :is-right="true"
              :icon="$kt.file.byPath('icon/agreement.svg')"
              :title="$i18n.zhToGlobal('隐私协议')"
          ></setting-menu>
          <setting-menu
            :icon="$kt.file.byPath('icon/color.svg')"
            :title="$i18n.zhToGlobal('版本信息')"
            :content="bodyData.appVersion"
          ></setting-menu>
        </setting-menu-box>

        <setting-menu-box>
          <setting-menu
              v-if="isLogin"
              @click="$refs.ktModal.open()"
              :icon="$kt.file.byPath('kantboot/icon/logout.svg')"
              :title="$i18n.zhToGlobal('退出登录')"
          ></setting-menu>
          <setting-menu
              v-else
              @click="$kt.router.toLogin()"
              :icon="$kt.file.byPath('kantboot/icon/login.svg')"
              :title="$i18n.zhToGlobal('前往登录')">
          </setting-menu>
        </setting-menu-box>


      </view>
      <view style="height: 100rpx"></view>

      <kt-modal
          :title="$i18n.zhToGlobal('提示')"
          :content="$i18n.zhToGlobal('是否确定退出登录')"
          @confirm="toLogout"
          ref="ktModal">
      </kt-modal>
    </view>
  </template>
  
  <script>
  
  import SettingMenuBox from "./components/setting-menu-box.vue";
  import SettingMenu from "./components/setting-menu.vue";

  export default {
    components: {SettingMenu, SettingMenuBox},
    data() {
      return {
        i18n: this.$i18n,
        isLogin: this.$kt.userAccount.getIsLogin(),
        self:{id:null},
        clazz: {
          inBox: this.$kt.style.toggleClass("in-box"),
          content: this.$kt.style.toggleClass("content"),
          back: this.$kt.style.toggleClass("back"),
        },
        colorSchemeContent:this.$i18n.zhToGlobal("跟随系统"),
        bodyData:{
          appVersion: "",
        },
      };
    },
    async onLoad() {
      this.clazz = {
        inBox: this.$kt.style.toggleClass("in-box"),
        content: this.$kt.style.toggleClass("content"),
        back: this.$kt.style.toggleClass("back"),
      };
      await new Promise(resolve => {
        setTimeout(() => {
          resolve();
        }, 3000);
      });
    },
    created() {
      this.getVersion();
      this.self = this.$kt.userAccount.getSelf();
      this.$kt.event.on('login:success', ()=>{
        this.isLogin = this.$kt.userAccount.getIsLogin();
        this.self = this.$kt.userAccount.getSelf();
      });
      this.colorSchemeContent = this.$i18n.zhToGlobal("跟随系统");
      let colorScheme =this.$kt.style.getMode().colorScheme;
      if(colorScheme==="auto"){
        this.colorSchemeContent = this.$i18n.zhToGlobal("跟随系统");
      }else if (colorScheme === "light") {
        this.colorSchemeContent = this.$i18n.zhToGlobal("光亮模式");
      } else if (colorScheme === "dark") {
        this.colorSchemeContent = this.$i18n.zhToGlobal("暗黑模式");
      }
    },
    methods: {
      toLogout() {
        try{
          this.$request.post("/project-meet-web/userLog/add",{
            data:{
              typeCode: "logout",
              operationCode: "logout",
              safeInputContent: "退出登录" + " 账号: "+ this.getUsernameText(),
              inputContent: "退出登录 邮箱: "+this.self.email
                  +" 账号: "+ this.self.username,
              sceneCode: this.$kt.style.detectDeviceType(),
              operationText: "退出登录",
              levelCode: "success",
              levelText: "成功",
            }
          });
        }catch (e) {

        }
        this.isLogin = false;
        this.$kt.userAccount.setIsLogin(false);
        this.$kt.storage.remove("token");

        uni.reLaunch({
          url: this.$kt.router.config.intoPath
        });
      },
      getVersion(){
        // 获取微信小程序版本号
        uni.getSystemInfo({
          success: (res) => {
            this.bodyData.appVersion = res.appVersion;
          }
        });
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
.back {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  // 从#ffffff到#f5f5f5渐变，从上到下，第300rpx开始
  z-index: -1;
  background-color: #f5f5f5;
 }

 .back-mode-color-scheme-light {
   background-color: #f5f5f5;
 }

 .back-mode-color-scheme-dark {
   background-color: #191919;
 }

  </style>
  